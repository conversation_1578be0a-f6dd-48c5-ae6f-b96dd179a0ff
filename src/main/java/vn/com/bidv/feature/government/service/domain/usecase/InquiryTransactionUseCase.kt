package vn.com.bidv.feature.government.service.domain.usecase

import vn.com.bidv.feature.government.service.data.GovernmentServiceRepository
import vn.com.bidv.feature.government.service.data.governmentservice.model.InquiryCustomsDutyReq
import vn.com.bidv.feature.government.service.domain.model.DataListInquiryCustomsDutyDMO
import vn.com.bidv.feature.government.service.domain.model.InquiryCustomsDutyDMO
import vn.com.bidv.sdkbase.data.LocalRepository
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class InquiryTransactionUseCase @Inject constructor(
    private val governmentServiceRepository: GovernmentServiceRepository,
    private val localRepository: LocalRepository
) {
    suspend fun inquiryTransaction(taxId: String, declarationNo: String?, year: Int?): DomainResult<DataListInquiryCustomsDutyDMO> {
        val result = governmentServiceRepository.inquiryTransaction(
            InquiryCustomsDutyReq(
                taxCode = taxId,
                declarationNo = declarationNo,
                declarationYear = year?.toString()
            )
        )
        return result.convert {
            DataListInquiryCustomsDutyDMO(
                items = this.items?.map {
                    InquiryCustomsDutyDMO(
                        eiTypeCode = it.eiTypeCode,
                        taxTypeCode = it.taxTypeCode,
                        ccCode = it.ccCode,
                        chapterCode = it.chapterCode,
                        ecCode = it.ecCode,
                        amount = it.amount,
                        ccy = it.ccy,
                        declarationDate = it.declarationDate,
                        declarationNo = it.declarationNo,
                        transDesc = it.transDesc,
                        payerType = it.payerType,
                        ccName = it.ccName,
                        chapterName = it.chapterName,
                        ecName = it.ecName,
                        eiTypeName = it.eiTypeName,
                        taxTypeName = it.taxTypeName,
                        treasuryCode = it.treasuryCode,
                        treasuryName = it.treasuryName,
                        admAreaCode = it.admAreaCode,
                        admAreaName = it.admAreaName,
                        revAccCode = it.revAccCode,
                        revAccName = it.revAccName,
                        revAuthCode = it.revAuthCode,
                        revAuthName = it.revAuthName,
                        payerTypeName = it.payerTypeName,
                    )
                } ?: listOf()
            )
        }
    }
}