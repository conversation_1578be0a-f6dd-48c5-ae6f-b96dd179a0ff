package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.main.tabcontent.pendingtransaction

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.common.ui.BaseMVIScreen
import vn.com.bidv.designsystem.component.datadisplay.badge.IBankBadgeLabel
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelSize
import vn.com.bidv.designsystem.component.dataentry.datacard.IBankDataCard
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.listwithloadmorev2.ListAutoLoadMoreViewModel
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.ItemCardCommon
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.ListTransactionBaseReducer
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.ListTransactionBaseScreen
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.model.ActionType
import vn.com.bidv.feature.government.service.constants.Constants
import vn.com.bidv.feature.government.service.model.DeleteRoot
import vn.com.bidv.feature.government.service.domain.model.TxnPendingListDMO
import vn.com.bidv.feature.government.service.model.ListCustomsDutiesRuleFilter
import vn.com.bidv.feature.government.service.model.DeleteTransactionFlowPopupArguments
import vn.com.bidv.feature.government.service.navigation.GovernmentServiceRoute
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.main.tabcontent.pendingtransaction.advsearch.AdvSearchScreen
import vn.com.bidv.sdkbase.data.ReloadFunctionKey
import vn.com.bidv.sdkbase.data.ReloadKey
import vn.com.bidv.sdkbase.data.ReloadModuleKey
import vn.com.bidv.sdkbase.utils.TransactionStatusBase
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants
import vn.com.bidv.sdkbase.utils.exts.dateToString
import vn.com.bidv.sdkbase.utils.exts.toDate
import vn.com.bidv.sdkbase.utils.formatAmount
import vn.com.bidv.localization.R as RLocal
import vn.com.bidv.designsystem.R
@Composable
fun ListPendingCustomsDutiesScreen(
    navController: NavHostController, lazyListState: LazyListState = rememberLazyListState()
) {
    val viewModel: ListPendingCustomsDutiesViewModel = hiltViewModel()
    val loadViewModel: LoadListPendingCustomsDutiesViewModel = hiltViewModel()

    BaseMVIScreen(
        viewModel = viewModel,
        renderContent = { uiState, onEvent ->
            ListPendingTransactionScreenContent(
                viewModel = viewModel,
                uiState = uiState,
                onEvent = onEvent,
                loadListViewModel = loadViewModel,
                listState = lazyListState,
                navController = navController,
                onDeleteButtonClick = { transactions ->
                    navController.navigate(
                        GovernmentServiceRoute.DeleteTransactionFlowPopupRoute(
                            DeleteTransactionFlowPopupArguments(
                                txnIds = transactions.mapNotNull { it.txnId },
                                deleteRoot = DeleteRoot.ListScreen,
                            )
                        )
                    )
                })
        },
        handleSideEffect = null,
    )
}

@Composable
private fun ListPendingTransactionScreenContent(
    viewModel: ListPendingCustomsDutiesViewModel,
    loadListViewModel: ListAutoLoadMoreViewModel<TxnPendingListDMO, ListCustomsDutiesRuleFilter>,
    listState: LazyListState = rememberLazyListState(),
    navController: NavHostController,
    uiState: ListTransactionBaseReducer.ListTransactionBaseViewState<TxnPendingListDMO, ListCustomsDutiesRuleFilter>,
    onEvent: (ListTransactionBaseReducer.ListTransactionBaseViewEvent<TxnPendingListDMO, ListCustomsDutiesRuleFilter>) -> Unit,
    onDeleteButtonClick: (List<TxnPendingListDMO>) -> Unit,
) {
    ListTransactionBaseScreen(
        viewModel = viewModel,
        navController = navController,
        lazyListState = listState,
        loadListViewModel = loadListViewModel,
        resIdTextTransactionType = RLocal.string.d_giao_dich,
        advSearchContent = {
            AdvSearchScreen(searchRuleFilter = it)
        },
        shouldShowCreateButton = !uiState.listDataState.any { it.isChecked },
        listAction = listOf(
            ActionType.Delete, ActionType.Push_Approval, ActionType.Create_Transaction
        ),
        handleAction = { type, transactions ->
            when (type) {
                ActionType.Delete -> {
                    onDeleteButtonClick(
                        transactions.map { it.data }
                    )
                }

                ActionType.Push_Approval -> TODO()

                ActionType.Create_Transaction -> {
                    navController.navigate(GovernmentServiceRoute.CustomsFeeInfoMainRoute.route)
                }

                else -> {/*do nothing*/
                }
            }
        },
        reloadKey = ReloadKey(ReloadModuleKey.GOVERNMENT_SERVICE, ReloadFunctionKey.LIST_PENDING),
        ruleFilterDefault = ListCustomsDutiesRuleFilter(),
        maxLengthSearchBox = Constants.MAX_LENGTH_TEXT_INPUT_70,
    ) { model, onEventListView ->
        ItemCardCommon(
            transactionsDMO = model,
            uiState = uiState,
            onEvent = onEvent,
            onClickItem = {
                navController.navigate(
                    GovernmentServiceRoute.PendingTransactionDetailRoute.createRoute(
                        model.data.txnId ?: ""
                    )
                )
            },
            navController = navController,
            listAction = listOf(
                ActionType.Push_Approval,
                ActionType.EDIT,
                ActionType.Print_Document,
                ActionType.Delete,
            ),
            handleAction = { type ->
                when (type) {
                    ActionType.Push_Approval -> TODO()

                    ActionType.EDIT -> TODO()

                    ActionType.Print_Document -> TODO()

                    ActionType.Delete -> {
                        onDeleteButtonClick(listOf(model.data))
                    }

                    else -> {/*do nothing*/
                    }
                }
            },
            contentHeader = { modelHeader ->
                ItemCardHeader(modelHeader.data)
            },
            contentBody = { modelBody ->
                ItemCardBody(modelBody.data)
            },
            onEventListView = onEventListView,
        )
    }
}

@Composable
fun ItemCardHeader(
    data: TxnPendingListDMO,
) {
    val style = LocalTypography.current
    val colorScheme = LocalColorScheme.current

    val status = when (data.status) {
        "INIT" -> TransactionStatusBase.INIT
        "REJECTED" -> TransactionStatusBase.REJECTED
        else -> {
            TransactionStatusBase.UNKNOWN
        }
    }

    Row(
        modifier = Modifier.padding(horizontal = IBSpacing.spacingXs),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            modifier = Modifier
                .weight(1f, fill = false)
                .padding(start = IBSpacing.spacingXs),
            text = data.txnId ?: "--",
            style = style.titleTitle_s,
            color = colorScheme.contentMainPrimary,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
        )

        IBankBadgeLabel(
            modifier = Modifier.padding(start = 8.dp),
            title = stringResource(status.statusResourceId),
            badgeSize = LabelSize.M,
            badgeColor = status.color
        )
    }

}

@Composable
fun ItemCardBody(
    data: TxnPendingListDMO,
) {
    val style = LocalTypography.current
    val colorScheme = LocalColorScheme.current

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                start = IBSpacing.spacingM, end = IBSpacing.spacingM, bottom = IBSpacing.spacingXs
            )
    ) {
        Text(
            modifier = Modifier.fillMaxWidth(),
            text = data.treasuryName ?: "",
            style = style.titleTitle_s,
            color = colorScheme.contentMainPrimary,
        )

        Spacer(modifier = Modifier.height(IBSpacing.spacingS))

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = IBSpacing.spacing3xs),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                modifier = Modifier,
                text = data.revAccCode ?: "",
                style = style.bodyBody_m,
                color = colorScheme.contentMainTertiary,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            VerticalDivider(
                modifier = Modifier
                    .height(IBSpacing.spacingL)
                    .padding(horizontal = IBSpacing.spacingXs),
                thickness = IBBorderDivider.borderDividerXs,
                color = colorScheme.borderSolidPrimary
            )
            Text(
                modifier = Modifier,
                text = data.revAuthName ?: "",
                style = style.bodyBody_m,
                color = colorScheme.contentMainTertiary,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }

        Spacer(modifier = Modifier.height(IBSpacing.spacingS))

        Row(
            modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                modifier = Modifier.padding(end = IBSpacing.spacing2xs),
                text = data.amount.formatAmount(currCode = "VND"),
                style = style.titleTitle_m,
                color = colorScheme.contentMainPrimary,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            Text(
                modifier = Modifier,
                text = data.ccy ?: "",
                style = style.titleTitle_m,
                color = colorScheme.contentMainPrimary,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            Spacer(modifier = Modifier.width(IBSpacing.spacingS))
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.End,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    modifier = Modifier.size(20.dp),
                    painter = painterResource(id = R.drawable.calendar_outline),
                    contentDescription = "",
                    tint = Color.Unspecified
                )
                Spacer(modifier = Modifier.width(IBSpacing.spacing2xs))
                Text(
                    modifier = Modifier,
                    text = data.createdDate?.toDate(pattern = SdkBaseConstants.DateTimeConstants.INPUT_FORMAT_DD_MM_YYYY_HH_MM_SS_SSSSSS)
                        ?.dateToString() ?: "",
                    style = style.bodyBody_m,
                    color = colorScheme.contentMainTertiary,
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewItemCardHeader() {
    val itemCardContent = TxnPendingListDMO(
        txnId = "GD1122GD",
        amount = "***************",
        ccy = "VND",
        treasuryCode = "711",
        treasuryName = "Kho bạc nhà nước Việt Nam - Chi nhanh ABC",
        createdDate = "30/04/2024",
        status = TransactionStatusBase.REJECTED.statusCode,
        statusName = stringResource(TransactionStatusBase.REJECTED.statusResourceId)
    )

    ItemCardHeader(
        data = itemCardContent
    )
}

@Preview(showBackground = true)
@Composable
private fun PreviewItemCardBody() {
    val data = TxnPendingListDMO(
        txnId = "**********************ăddwdwdwdwdwdwdwdwd22**********************22",
        amount = "***************",
        ccy = "VND",
        treasuryCode = "711",
        treasuryName = "Kho bạc nhà nước Việt Nam",
        createdDate = "30/10/2024",
        status = TransactionStatusBase.REJECTED.statusCode,
        statusName = stringResource(TransactionStatusBase.REJECTED.statusResourceId)
    )

    IBankDataCard(
        modifier = Modifier.padding(horizontal = IBSpacing.spacingM),
        showCheckbox = false,
        isChecked = false,
        onCheckedChange = {},
        cardHeader = {
            ItemCardHeader(data)
        },
        cardContent = {
            ItemCardBody(data)
        },
        cardFooter = {},
    )
}

@Preview(showBackground = true)
@Composable
private fun PreviewItemCardBody2() {
    val data = TxnPendingListDMO(
        txnId = "GD1122GD",
        amount = "***************",
        ccy = "VND",
        treasuryCode = "711",
        treasuryName = "Kho bạc nhà nước Việt Nam",
        createdDate = "30/10/2024",
        status = TransactionStatusBase.REJECTED.statusCode,
        statusName = stringResource(TransactionStatusBase.REJECTED.statusResourceId)
    )

    IBankDataCard(
        modifier = Modifier.padding(horizontal = IBSpacing.spacingM),
        showCheckbox = false,
        isChecked = false,
        onCheckedChange = {},
        cardHeader = {
            ItemCardHeader(data)
        },
        cardContent = {
            ItemCardBody(data)
        },
        cardFooter = {},
    )
}



