package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.dropdown

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import vn.com.bidv.feature.government.service.ui.common.CommonDropDown
import vn.com.bidv.feature.government.service.ui.common.CommonSearchDialog
import vn.com.bidv.feature.government.service.domain.model.TaxTypeDMO
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.AddPaymentItem
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.AddPaymentReducer
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.VNCharacterUtil

@Composable
fun TaxTypeDropDown(
    uiState: AddPaymentReducer.AddPaymentState,
    onEvent: (AddPaymentReducer.AddPaymentEvent) -> Unit,
) {
    CommonDropDown(
        labelText = stringResource(R.string.sac_thue),
        selectedItem = uiState.taxTypeSelected,
        displayTextSelector = {
            if (it?.taxTypeCode?.isNotEmpty() == true && it.taxTypeName?.isNotEmpty() == true)
                "${it.taxTypeCode} - ${it.taxTypeName}"
            else ""
        },
        fieldError = uiState.fieldError[AddPaymentItem.DropDownItem.TaxType],
        onClickEnd = {
            onEvent(AddPaymentReducer.AddPaymentEvent.TaxTypeEvent.ShowTaxTypeBottomSheet(true))
        },
        onClickClear = {
            onEvent(AddPaymentReducer.AddPaymentEvent.TaxTypeEvent.ClearTaxType)
        }
    )
}

@Composable
fun ShowTaxTypeBottomSheet(
    uiState: AddPaymentReducer.AddPaymentState,
    onEvent: (AddPaymentReducer.AddPaymentEvent) -> Unit,
    onValueChange: (AddPaymentItem.DropDownItem.TaxType, TaxTypeDMO?) -> Unit
) {
    if (uiState.showTaxTypeBottomSheet) {
        CommonSearchDialog(
            title = stringResource(R.string.sac_thue),
            itemSelected = uiState.taxTypeSelected,
            listData = uiState.listTaxType,
            showSearchBox = (uiState.listTaxType?.size ?: 0) > 10,
            searchFilter = { item, query ->
                val search = "${item.taxTypeCode}${item.taxTypeName}"
                VNCharacterUtil.removeAccent(search)
                    .contains(VNCharacterUtil.removeAccent(query), ignoreCase = true)
            },
            onDismiss = {
                onEvent(AddPaymentReducer.AddPaymentEvent.TaxTypeEvent.ShowTaxTypeBottomSheet(false))
                onValueChange(AddPaymentItem.DropDownItem.TaxType, it)
            },
            itemTitleSelector = { it.taxTypeName.orEmpty() },
            compareKey = { a, b -> a.taxTypeCode == b?.taxTypeCode }
        )
    }
}
