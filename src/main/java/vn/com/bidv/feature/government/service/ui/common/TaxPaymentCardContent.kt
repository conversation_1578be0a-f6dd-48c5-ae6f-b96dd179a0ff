package vn.com.bidv.feature.government.service.ui.common

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.component.dataentry.IBankCheckBox
import vn.com.bidv.designsystem.component.dataentry.datacard.ButtonAction
import vn.com.bidv.designsystem.component.dataentry.datacard.FooterType
import vn.com.bidv.designsystem.component.dataentry.datacard.IBankCardFooter
import vn.com.bidv.designsystem.component.dataentry.datacard.IBankCardHeader
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.log.BLogUtil

@Composable
fun TaxPaymentCardContent(
    modifier: Modifier = Modifier,
    showCheckbox: Boolean = false,
    checkboxEnabled: Boolean = false,
    isChecked: Boolean,
    onClick: (() -> Unit)? = null,
    onCheckedChange: ((Boolean) -> Unit)? = null,
    headerPaddingValues: PaddingValues = PaddingValues(start = 16.dp, top = 12.dp, bottom = 12.dp),
    cardHeader: @Composable () -> Unit,
    cardFooter: (@Composable () -> Unit)? = null,
    cardContent: @Composable () -> Unit
) {
    val colorScheme = LocalColorScheme.current
    Row(
        modifier = modifier
            .fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clickable(enabled = onClick != null) {
                    onClick?.invoke()
                }
                .clip(shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL))
                .border(
                    width = if (isChecked) IBBorderDivider.borderDividerS else 0.dp,
                    color = if (isChecked) colorScheme.borderBrandSecondary else Color.Transparent,
                    shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL)
                )
                .background(color = colorScheme.bgMainTertiary)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(headerPaddingValues)
            ) {
                if (showCheckbox) {
                    IBankCheckBox(
                        enable = checkboxEnabled,
                        checked = isChecked,
                        modifier = Modifier.padding(end = 8.dp),
                        onCheckedChange = {
                            onCheckedChange?.invoke(it.checked)
                        },
                    )
                }
                cardHeader()
            }
            HorizontalDivider()
            cardContent()
            cardFooter?.let {
                HorizontalDivider()
                it()
            }
        }
    }
}

@Preview
@Composable
fun PreviewDataCard() {
    var isChecked by remember { mutableStateOf(false) }
    TaxPaymentCardContent(
        showCheckbox = true,
        isChecked = isChecked,
        onCheckedChange = {
            if (it != isChecked) {
                isChecked = it
            }
        },
        cardHeader = {
            IBankCardHeader(
                title = "Title",
                subtitle = "Supporting text",
                showDivider = false,
                icon = ImageVector.vectorResource(id = vn.com.bidv.designsystem.R.drawable.information_circle),
            )
        },
        cardContent = {
            Surface(
                modifier = Modifier.padding(
                    vertical = IBSpacing.spacingXs,
                    horizontal = IBSpacing.spacingM
                )
            ) { Text("This is your dynamic custom content") }
        },
        cardFooter = {
            IBankCardFooter(
                footerType = FooterType.ActionButton(
                    buttonLeft = ButtonAction(
                        "Button Left",
                        icon = ImageVector.vectorResource(id = vn.com.bidv.designsystem.R.drawable.send),
                        onClick = {
                            BLogUtil.d("onButtonLeftClick")
                        }
                    ),
                    buttonRight = ButtonAction(
                        "Button Left",
                        icon = ImageVector.vectorResource(id = vn.com.bidv.designsystem.R.drawable.transfer),
                        onClick = {
                            BLogUtil.d("onButtonLeftClick")
                        }
                    )
                )
            )
        },
    )
}