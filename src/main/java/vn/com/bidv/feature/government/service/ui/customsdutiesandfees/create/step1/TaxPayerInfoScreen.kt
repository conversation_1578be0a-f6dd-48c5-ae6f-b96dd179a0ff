package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step1

import android.widget.Toast
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.rememberNavController
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import timber.log.Timber
import vn.com.bidv.common.utils.CollectSideEffect
import vn.com.bidv.designsystem.component.datadisplay.sectionheader.IBankSectionHeader
import vn.com.bidv.designsystem.component.datadisplay.sectionheader.LeadingType
import vn.com.bidv.designsystem.component.dataentry.IBFrameState
import vn.com.bidv.designsystem.component.dataentry.IBankInputFieldBase
import vn.com.bidv.designsystem.component.dataentry.datacard.IBankDataCard
import vn.com.bidv.designsystem.component.feedback.inlinemessage.InlineMessage
import vn.com.bidv.designsystem.component.feedback.inlinemessage.InlineMessageStatus
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.component.navigation.button.IBankLinkButton
import vn.com.bidv.designsystem.component.navigation.button.IBankNormalButton
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonSize
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonType
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankActionBar
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.IBankTheme
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.ModelCheckAble
import vn.com.bidv.feature.government.service.constants.Constants
import vn.com.bidv.feature.government.service.domain.model.InquiryCustomsDutyDMO
import vn.com.bidv.feature.government.service.model.AddPaymentDTO
import vn.com.bidv.feature.government.service.model.BaseTransaction
import vn.com.bidv.feature.government.service.model.SubmissionType
import vn.com.bidv.feature.government.service.model.TaxPayerInfoField
import vn.com.bidv.feature.government.service.navigation.GovernmentServiceRoute
import vn.com.bidv.feature.government.service.ui.common.TaxPaymentCard
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.formatMoney
import vn.com.bidv.designsystem.R as designsystemR

data class InitFailedErrorDialogContent(
    val message: String?,
    val failedInitStep: TaxPayerInfoReducer.InitStep,
)

@Composable
fun TaxPayerInfoScreen(
    navController: NavHostController,
    progressBar: @Composable () -> Unit,
) {
    val context = LocalContext.current
    val viewModel: TaxPayerInfoViewModel = hiltViewModel()

    var showErrorDialog by remember { mutableStateOf<InitFailedErrorDialogContent?>(null) }

    BaseScreen(
        navController = navController,
        viewModel = viewModel,
        topAppBarType = TopAppBarType.Title,
        topAppBarConfig = TopAppBarConfig(
            titleTopAppBar = stringResource(R.string.thong_tin_khoan_nop),
            showHomeIcon = true,
            actionItems = {
                IBankLinkButton(
                    modifier = Modifier,
                    text = stringResource(R.string.mau_giao_dich),
                    leadingIcon = ImageVector.vectorResource(designsystemR.drawable.quan_ly_mau_giao_dich_outline)
                ) {
                    Toast.makeText(
                        context,
                        context.getString(R.string.mau_giao_dich),
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
        ),
        handleSideEffect = {
            when (it) {
                is TaxPayerInfoReducer.TaxPaymentSideEffect.InitScreenFailed -> {
                    showErrorDialog = InitFailedErrorDialogContent(
                        message = it.errorMessage,
                        failedInitStep = it.failedInitStep,
                    )
                }
                else -> {
                    //noop
                }
            }
        }
    ) { viewState, viewEvent ->

        if (viewState.initializeState == TaxPayerInfoReducer.InitializeState.NOT_STARTED) {
            viewEvent(TaxPayerInfoReducer.TaxPaymentViewEvent.InitScreen)
        }

        val totalAmount = remember(viewState) {
            val target = if (viewState.modeDelegate) {
                viewState.onBehalfTaxManualEntries
            } else {
                viewState.businessTaxManualEntries + viewState.fetchedTaxId
            }

            target
                .filter { it.isChecked }
                .sumOf { it.data.getValueAmount().toLong() }
                .toString()
        }
        Column {
            progressBar()
                Column(Modifier
                    .fillMaxHeight()
                    .verticalScroll(rememberScrollState())
                    .weight(1f)) {
                    TaxPayerInfoRadioPicker(
                        modeDelegate = viewState.modeDelegate,
                        Modifier.padding(
                            start = IBSpacing.spacingM,
                            end = IBSpacing.spacingM,
                            top = IBSpacing.spacingM
                        )
                    ) {
                        viewEvent(
                            TaxPayerInfoReducer.TaxPaymentViewEvent.ChangeModeDelegate(
                                modeDelegate = it == SubmissionType.ON_BEHALF
                            )
                        )
                    }
                    TaxPaymentInfoContent(navController, viewState, viewEvent)
                }

                IBankActionBar(
                    buttonPositive = DialogButtonInfo(stringResource(R.string.tiep_tuc)),
                    isVertical = false,
                    leadingIcon = ImageVector.vectorResource(designsystemR.drawable.khoan_phai_thu),
                    title = stringResource(R.string.tong_tien),
                    description = totalAmount.formatMoney("VND", true),
                )
        }
    }

    showErrorDialog?.let {
        IBankModalConfirm(
            modalConfirmType = ModalConfirmType.Error,
            title = stringResource(R.string.loi),
            supportingText = it.message,
            listDialogButtonInfo = listOf(
                DialogButtonInfo(
                    label = stringResource(R.string.thu_lai),
                    onClick = {
                        viewModel.sendEvent(
                            TaxPayerInfoReducer.TaxPaymentViewEvent.RetryInit(it.failedInitStep, )
                        )
                    }
                )
            ),
            onDismissRequest = {
                showErrorDialog = null
            }
        )
    }

    CollectSideEffect(viewModel.subscribeShareData(Constants.INQUIRY_TRANS_SCREEN_SHARE_DATA_KEY)) {
        try {
            val inquiryItems = Gson().fromJson<List<InquiryCustomsDutyDMO>>(
                it.data,
                object : TypeToken<List<InquiryCustomsDutyDMO>>() {}.type
            )
            if (inquiryItems != null) {
                Timber.d(inquiryItems.toString())
                viewModel.sendEvent(
                    TaxPayerInfoReducer.TaxPaymentViewEvent.AddInquiryItems(
                        inquiryItems
                    )
                )
            }
        } catch (e: Exception) {
            Timber.e(e, "Failed to parse inquiry items")
        }
    }

    CollectSideEffect(viewModel.subscribeShareData(Constants.ADD_PAYMENT_DATA)) {
        try {
            val addPaymentData = Gson().fromJson(it.data, AddPaymentDTO::class.java)
            if (addPaymentData != null) {
                Timber.d(addPaymentData.toString())
                viewModel.sendEvent(
                    TaxPayerInfoReducer.TaxPaymentViewEvent.AddManualItems(
                        listOf(
                            addPaymentData
                        )
                    )
                )
            }
        } catch (e: Exception) {
            Timber.e(e, "Failed to parse add payment data")
        }
    }
}

@Composable
fun TaxPaymentInfoContent(
    navController: NavHostController,
    viewState: TaxPayerInfoReducer.TaxPaymentViewState,
    handleEvent: (TaxPayerInfoReducer.TaxPaymentViewEvent) -> Unit,
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current

    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp),
        modifier = Modifier
            .padding(12.dp)
            .fillMaxSize()
    ) {
        TaxPayerInfoSection(viewState) { field, value ->
            handleEvent(TaxPayerInfoReducer.TaxPaymentViewEvent.UpdateBusinessTaxInfo(field, value))
        }

        if (viewState.modeDelegate) {
            DelegatorTaxPayerInfoSection(viewState) { field, value ->
                handleEvent(
                    TaxPayerInfoReducer.TaxPaymentViewEvent.UpdateOnBehalfTaxInfo(
                        field,
                        value
                    )
                )
            }
        }

        IBankDataCard(cardHeader = {
            IBankSectionHeader(
                shLeadingType = LeadingType.Dash(),
                shSectionTitle = stringResource(R.string.danh_sach_thue_can_nop),
                thumbContent = {
                    if (!viewState.modeDelegate) {
                        IBankNormalButton(
                            text = stringResource(R.string.van_tin),
                            size = NormalButtonSize.SM(typography),
                            type = NormalButtonType.SECONDARYGRAY(colorScheme)
                        ) {
                            navController.navigate(
                                GovernmentServiceRoute.InquiryTransactionScreen(taxNumber = viewState.businessTaxInfo.taxId)
                            )
                        }
                    } else {
                        val taxEntriesCalculation = viewState.onBehalfTaxManualEntries

                        if (taxEntriesCalculation.isEmpty()) {
                            IBankNormalButton(
                                text = stringResource(R.string.them),
                                size = NormalButtonSize.SM(typography),
                                type = NormalButtonType.PRIMARY(colorScheme)
                            ) {
                                navController.navigate(GovernmentServiceRoute.AddPaymentRouter.route)
                            }
                        }
                    }
                }
            )
        }, isChecked = false, cardFooter = {}, onCheckedChange = {}, cardContent = {
            Column(
                modifier = Modifier.padding(horizontal = 12.dp),
                verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingS),
            ) {
                val taxEntriesCalculation = if (viewState.modeDelegate) {
                    viewState.onBehalfTaxManualEntries
                } else {
                    (viewState.businessTaxManualEntries + viewState.fetchedTaxId)
                }
                ListTaxPayment(
                    navController,
                    taxEntries = taxEntriesCalculation,
                    modeDelegate = viewState.modeDelegate,
                    onItemCheckedChange = { handleEvent(TaxPayerInfoReducer.TaxPaymentViewEvent.SelectTaxEntry(it)) }
                ) {
                    navController.navigate(GovernmentServiceRoute.AddPaymentRouter.route)
                }
                if (taxEntriesCalculation.isNotEmpty()) {
                    IBankNormalButton(
                        text = stringResource(R.string.them_khoan_nop),
                        type = NormalButtonType.SECONDARYGRAY(colorScheme),
                        modifier = Modifier
                            .padding(bottom = 12.dp)
                            .fillMaxWidth()
                    ) {
                        navController.navigate(GovernmentServiceRoute.AddPaymentRouter.route)
                    }
                }
            }
        })
    }
}

@Composable
private fun TaxPayerInfoSection(
    viewState: TaxPayerInfoReducer.TaxPaymentViewState,
    onTextChange: (field: TaxPayerInfoField, value: String) -> Unit,
) {
    val colorScheme = LocalColorScheme.current
    IBankDataCard(cardHeader = {
        IBankSectionHeader(
            shLeadingType = LeadingType.Dash(),
            shSectionTitle = stringResource(R.string.thong_tin_nguoi_nop_thue),
        )
    }, isChecked = false, cardFooter = {}, onCheckedChange = {}, cardContent = {
        Column(
            verticalArrangement = Arrangement.spacedBy(12.dp),
            modifier = Modifier.padding(bottom = 12.dp, start = 12.dp, end = 12.dp)
        ) {
            IBankInputFieldBase(
                required = true,
                placeholderText = stringResource(R.string.ma_so_thue_nguoi_nop_thue),
                state = if (!viewState.modeDelegate) IBFrameState.DISABLE(colorScheme) else IBFrameState.DEFAULT(
                    colorScheme
                ),
                text = viewState.businessTaxInfo.taxId,
            ) {
                onTextChange(TaxPayerInfoField.TAX_ID, it.text)
            }
            IBankInputFieldBase(
                required = true,
                placeholderText = stringResource(R.string.ho_ten_nguoi_nop_thue),
                text = viewState.businessTaxInfo.name,
                onClickClear = {
                    onTextChange(TaxPayerInfoField.NAME, "")
                }
            ) {
                onTextChange(TaxPayerInfoField.NAME, it.text)
            }
            IBankInputFieldBase(
                required = true,
                placeholderText = stringResource(R.string.dia_chi_nguoi_nop_thue),
                text = viewState.businessTaxInfo.address,
                onClickClear = {
                    onTextChange(TaxPayerInfoField.ADDRESS, "")
                }
            ) {
                onTextChange(TaxPayerInfoField.ADDRESS, it.text)
            }
        }
    })
}

@Composable
private fun DelegatorTaxPayerInfoSection(
    viewState: TaxPayerInfoReducer.TaxPaymentViewState,
    onTextChange: (field: TaxPayerInfoField, value: String) -> Unit,
) {
    val colorScheme = LocalColorScheme.current
    IBankDataCard(
        showCheckbox = false, isChecked = false, cardHeader = {
            IBankSectionHeader(
                shLeadingType = LeadingType.Dash(),
                shSectionTitle = stringResource(R.string.thong_tin_nguoi_nop_thay),
            )
        }, cardFooter = {}, onCheckedChange = {},
        cardContent = {
            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp),
                modifier = Modifier.padding(bottom = 12.dp, start = 12.dp, end = 12.dp)
            ) {
                IBankInputFieldBase(
                    required = true,
                    placeholderText = stringResource(R.string.ma_so_nguoi_nop_thay),
                    state = IBFrameState.DISABLE(colorScheme),
                    text = viewState.onBehalfTaxInfo.taxId,
                ) {
                    onTextChange(TaxPayerInfoField.TAX_ID, it.text)
                }
                IBankInputFieldBase(
                    required = true,
                    placeholderText = stringResource(R.string.ho_ten_nguoi_nop_thay),
                    text = viewState.onBehalfTaxInfo.name,
                    onClickClear = {
                        onTextChange(TaxPayerInfoField.NAME, "")
                    }
                ) {
                    onTextChange(TaxPayerInfoField.NAME, it.text)
                }
                IBankInputFieldBase(
                    required = true,
                    placeholderText = stringResource(R.string.dia_chi_nguoi_nop_thay),
                    text = viewState.onBehalfTaxInfo.address,
                    onClickClear = {
                        onTextChange(TaxPayerInfoField.ADDRESS, "")
                    }
                ) {
                    onTextChange(TaxPayerInfoField.ADDRESS, it.text)
                }
            }
        }
    )
}



@Composable
fun <T : BaseTransaction> ListTaxPayment(
    navController: NavHostController,
    modeDelegate: Boolean,
    taxEntries: List<ModelCheckAble<T>>,
    onItemCheckedChange: (ModelCheckAble<T>) -> Unit,
    onAddTransactionClick: () -> Unit,
) {
    val colorScheme = LocalColorScheme.current
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        if (taxEntries.isEmpty()) {
            if (!modeDelegate) {
                EmptyTaxPaymentsList(onAddTransactionClick)
            }
        } else {
            InlineMessage(
                status = InlineMessageStatus.Info(colorScheme),
                message = stringResource(R.string.quy_khach_chon_toi_da_5_khoan_thue_trong_1_lan_thanh_toan)
            )
            taxEntries.forEach {
                val checkedItems = taxEntries.filter { it.isChecked }
                val isCheckedLimitReached = checkedItems.size >= Constants.MAX_CONCURRENT_PAYMENTS_CHECKED_TAX_PAYER_SCREEN
                TaxPaymentCard(
                    transactionsDMO = it,
                    navController = navController,
                    checkboxEnabled = it.isChecked || !isCheckedLimitReached
                ) {
                    onItemCheckedChange(it)
                }
            }
        }
    }
}

@Composable
private fun EmptyTaxPaymentsList(onAddTransactionClick: () -> Unit) {
    val typography = LocalTypography.current

    Column(
        modifier = Modifier.fillMaxWidth().padding(IBSpacing.spacingM),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingS)
    ) {
        Icon(
            modifier = Modifier.fillMaxSize(0.3f),
            imageVector = ImageVector.vectorResource(id = vn.com.bidv.designsystem.R.drawable.search_no_results_64),
            contentDescription = null,
            tint = Color.Unspecified
        )

        Text(
            text = stringResource(R.string.chua_co_khoan_thue_can_nop),
            style = typography.titleTitle_m,
            textAlign = TextAlign.Center
        )

        Text(
            text = stringResource(R.string.vui_long_van_tin_hoac_them_khoan_nop_de_tiep_tuc),
            style = typography.bodyBody_m,
            textAlign = TextAlign.Center
        )

        IBankNormalButton(
            text = stringResource(R.string.them_khoan_nop),
            onClick = onAddTransactionClick,
            size = NormalButtonSize.SM(typography)
        )
    }
}

@Preview
@Composable
fun PreviewTaxPaymentInfoContent() {
    IBankTheme {
        TaxPaymentInfoContent(
            navController = rememberNavController(),
            viewState = TaxPayerInfoReducer.TaxPaymentViewState(
                fetchedTaxId = Constants.fakeTransactions.map { ModelCheckAble(it) },
                modeDelegate = true,
            ),
            handleEvent = {}
        )
    }

}