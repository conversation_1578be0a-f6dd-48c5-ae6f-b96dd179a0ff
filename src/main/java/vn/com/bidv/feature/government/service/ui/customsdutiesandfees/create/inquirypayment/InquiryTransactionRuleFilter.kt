package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.inquirypayment

import com.google.gson.Gson
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.model.ListTransactionRuleFilter

data class InquiryTransactionRuleFilter(
    override var search: String? = null,
    val taxNumber: String = "",
    val taxDeclarationNo: String = "",
    val taxDeclarationYear: Int? = null,
) : ListTransactionRuleFilter {
    
    override fun getBadgeNumber(): Int {
        var count = 0
        if (taxDeclarationNo.isNotEmpty()) count++
        if (taxDeclarationYear != null) count++
        return count
    }

    override fun <R : ListTransactionRuleFilter> copyGson(): R {
        return try {
            val json = Gson().toJson(this)
            Gson().fromJson(json, this::class.java) as R
        } catch (e: Exception) {
            InquiryTransactionRuleFilter() as R
        }
    }

    override fun <R : ListTransactionRuleFilter> copySearch(search: String?): R {
        return this.copy(search = search) as R
    }
}
