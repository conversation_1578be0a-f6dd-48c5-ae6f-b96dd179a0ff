package vn.com.bidv.feature.government.service.util

import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.model.ActionType

sealed interface GetMenuActionList {
    fun getTopMenuActions(): List<ActionType>
    fun getBottomMenuActions(): List<ActionType>
}

sealed class TransactionMenuActions: GetMenuActionList {
    data object PendingTransactionsScreenActions : TransactionMenuActions() {
        override fun getTopMenuActions(): List<ActionType> = listOf(
            ActionType.EDIT,
            ActionType.Print_Document,
            ActionType.History_Impact,
            ActionType.Delete)

        override fun getBottomMenuActions(): List<ActionType> = listOf(ActionType.Push_Approval)
    }

    data object AllTransactionsScreenActions : TransactionMenuActions() {
        override fun getTopMenuActions(): List<ActionType> = listOf()

        override fun getBottomMenuActions(): List<ActionType> = listOf()
    }
}