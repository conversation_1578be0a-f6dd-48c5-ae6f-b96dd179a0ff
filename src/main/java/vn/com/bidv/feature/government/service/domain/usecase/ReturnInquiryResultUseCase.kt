package vn.com.bidv.feature.government.service.domain.usecase

import com.google.gson.Gson
import vn.com.bidv.feature.government.service.constants.Constants
import vn.com.bidv.feature.government.service.domain.model.InquiryCustomsDutyDMO
import vn.com.bidv.sdkbase.data.LocalRepository
import vn.com.bidv.sdkbase.data.ShareDataDTO
import javax.inject.Inject

class ReturnInquiryResultUseCase @Inject constructor(
    private val localRepository: LocalRepository,
) {
    suspend operator fun invoke(selectedItems: List<InquiryCustomsDutyDMO>) {
        localRepository.shareDataTo(
            Constants.INQUIRY_TRANS_SCREEN_SHARE_DATA_KEY,
            ShareDataDTO(
                Constants.INQUIRY_TRANS_SCREEN_SHARE_DATA_KEY,
                <PERSON>son().toJson(selectedItems)
            )
        )
    }
}