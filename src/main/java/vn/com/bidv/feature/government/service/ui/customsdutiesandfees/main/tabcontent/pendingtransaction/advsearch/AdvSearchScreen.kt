package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.main.tabcontent.pendingtransaction.advsearch

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import vn.com.bidv.common.ui.BaseMVIScreen
import vn.com.bidv.designsystem.component.dataentry.IBFrameExtendState
import vn.com.bidv.designsystem.component.dataentry.IBFrameState
import vn.com.bidv.designsystem.component.dataentry.IBankInputDropdownBaseV2
import vn.com.bidv.designsystem.component.dataentry.IBankInputDropdownTypeData
import vn.com.bidv.designsystem.component.dataentry.IBankInputFieldBase
import vn.com.bidv.designsystem.component.dataentry.IBankInputMoney
import vn.com.bidv.designsystem.component.dataentry.RemoveSpaceFilter
import vn.com.bidv.designsystem.component.dataentry.RemoveVietnameseAccentFilter
import vn.com.bidv.designsystem.component.dataentry.SpecialCharacterFilter
import vn.com.bidv.designsystem.component.datepicker.IBankDateRangePickerDialog
import vn.com.bidv.designsystem.component.datepicker.model.DatePickerConfig
import vn.com.bidv.designsystem.component.feedback.bottomsheet.MultiSearchDialogState
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.theme.IBColorScheme
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.feature.government.service.constants.Constants
import vn.com.bidv.feature.government.service.model.ListCustomsDutiesRuleFilter
import vn.com.bidv.sdkbase.utils.TransactionStatusBase
import vn.com.bidv.sdkbase.utils.exts.dateToString
import java.util.Calendar
import java.util.Date

@Composable
fun AdvSearchScreen(
    listAdvSearchItem: List<AdvSearchItem> = listOf(
        AdvSearchItem.DebitAccount(),
        AdvSearchItem.TaxId(),
        AdvSearchItem.DeclarationNumber(),
        AdvSearchItem.DropDownItem.Status(),
        AdvSearchItem.Date,
        AdvSearchItem.BatchNumber(),
        AdvSearchItem.InputMoney.MinAmount(vn.com.bidv.localization.R.string.tu_so_tien),
        AdvSearchItem.InputMoney.MaxAmount(vn.com.bidv.localization.R.string.den_so_tien),
        AdvSearchItem.DropDownItem.Currency(),
    ),
    searchRuleFilter: ListCustomsDutiesRuleFilter? = null,
) {
    val viewModel: AdvSearchViewModel = hiltViewModel()
    BaseMVIScreen(viewModel = viewModel, renderContent = { uiState, onEvent ->
        AdvSearchScreenContent(
            uiState = uiState,
            onEvent = onEvent,
            listAdvSearchItem = listAdvSearchItem,
            searchRuleFilter = searchRuleFilter,
            onValueChange = { field, value ->
                onEvent(
                    AdvSearchReducer.AdvSearchEvent.ValueTextChanged(
                        minAmount = if (field is AdvSearchItem.InputMoney.MinAmount) value else null,
                        maxAmount = if (field is AdvSearchItem.InputMoney.MaxAmount) value else null,
                        debit = if (field is AdvSearchItem.DebitAccount) value else null,
                        tax = if (field is AdvSearchItem.TaxId) value else null,
                        declaration = if (field is AdvSearchItem.DeclarationNumber) value else null,
                        batch = if (field is AdvSearchItem.BatchNumber) value else null,
                    )
                )
            },
        )
    }, handleSideEffect = {})
}

@Composable
fun AdvSearchScreenContent(
    uiState: AdvSearchReducer.AdvSearchState,
    onEvent: (AdvSearchReducer.AdvSearchEvent) -> Unit,
    listAdvSearchItem: List<AdvSearchItem>,
    searchRuleFilter: ListCustomsDutiesRuleFilter?,
    onValueChange: (field: AdvSearchItem, value: String) -> Unit = { _, _ -> },
) {
    val showMoneyTypeBottomSheet = remember { mutableStateOf(false) }
    val showStatusBottomSheet = remember { mutableStateOf(false) }
    val showDatePicker = remember { mutableStateOf(false) }

    val colorScheme = LocalColorScheme.current
    val (minInputMoneyState, minInputMoneyMessage) = IBFrameState.DEFAULT(colorScheme) to ""
    val (maxInputMoneyState, maxInputMoneyMessage) = if (uiState.invalidAmount) IBFrameState.ERROR(
        colorScheme
    ) to stringResource(
        vn.com.bidv.localization.R.string.so_tien_tu_phai_nho_hon_hoac_bang_so_tien_den
    ) else IBFrameState.DEFAULT(
        colorScheme
    ) to ""
    LaunchedEffect(searchRuleFilter) {
        handleSearchFilter(searchRuleFilter = searchRuleFilter, onEvent = onEvent)
    }
    LaunchedEffect(uiState) {
        searchRuleFilter?.setValueTo(
            search = searchRuleFilter.search,
            startDate = uiState.startDate,
            endDate = uiState.endDate,
            debit = uiState.debit,
            tax = uiState.tax,
            declaration = uiState.declaration,
            batch = uiState.batch,
            minAmount = uiState.minAmount,
            maxAmount = uiState.maxAmount,
            listCurrencySelected = uiState.listCurrencySelected,
            listStatusSelected = uiState.listStatusSelected?.map { it.statusCode },
        )
    }
    LazyColumn(
        modifier = Modifier
            .fillMaxHeight()
            .fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingM)
    ) {
        items(listAdvSearchItem.size) { itemType ->
            when (val item = listAdvSearchItem[itemType]) {
                is AdvSearchItem.InputMoney.MinAmount -> MinInputMoneyItem(
                    uiState = uiState,
                    onEvent = onEvent,
                    onValueChange = onValueChange,
                    inputMoneyState = minInputMoneyState,
                    inputMoneyMessage = minInputMoneyMessage,
                    placeHolderText = stringResource(item.titleRes)
                )

                is AdvSearchItem.InputMoney.MaxAmount -> MaxInputMoneyItem(
                    uiState = uiState,
                    onEvent = onEvent,
                    onValueChange = onValueChange,
                    inputMoneyState = maxInputMoneyState,
                    inputMoneyMessage = maxInputMoneyMessage,
                    placeHolderText = stringResource(item.titleRes)
                )

                is AdvSearchItem.DropDownItem.Currency -> CurrencyDropdownItem(
                    onEvent = onEvent,
                    uiState = uiState,
                    showMoneyTypeBottomSheet = showMoneyTypeBottomSheet
                )

                is AdvSearchItem.DropDownItem.Status -> StatusDropdownItem(
                    onEvent = onEvent,
                    uiState = uiState,
                    showMoneyTypeBottomSheet = showStatusBottomSheet
                )

                is AdvSearchItem.Date -> DatePickerItem(
                    uiState = uiState,
                    onEvent = onEvent,
                    colorScheme = colorScheme,
                    showDatePicker = showDatePicker,
                    placeHolderText = stringResource(item.titleRes)
                )

                is AdvSearchItem.DebitAccount -> CustomerInfoDebitAccountItem(
                    uiState = uiState, onValueChange = onValueChange,
                    placeHolderText = stringResource(item.titleRes)
                )

                is AdvSearchItem.TaxId -> CustomerInfoTaxIdItem(
                    uiState = uiState, onValueChange = onValueChange,
                    placeHolderText = stringResource(item.titleRes)
                )

                is AdvSearchItem.DeclarationNumber -> CustomerInfoDeclarationNumberItem(
                    uiState = uiState, onValueChange = onValueChange,
                    placeHolderText = stringResource(item.titleRes)
                )

                is AdvSearchItem.BatchNumber -> CustomerInfoBatchNumberItem(
                    uiState = uiState, onValueChange = onValueChange,
                    placeHolderText = stringResource(item.titleRes)
                )

                else -> {/*do nothing*/
                }
            }
        }
    }
    ShowCurrencyBottomSheet(
        uiState = uiState,
        showCurrencyBottomSheet = showMoneyTypeBottomSheet,
        data = uiState.listCurrency,
        state = if (uiState.listCurrency.isNullOrEmpty()) {
            MultiSearchDialogState.LOADING
        } else {
            MultiSearchDialogState.CONTENT
        },
        onItemSelected = {
            if (it.size == uiState.listCurrency?.size) {
                onEvent(AdvSearchReducer.AdvSearchEvent.SetCurrencyList(null))
            } else {
                onEvent(AdvSearchReducer.AdvSearchEvent.SetCurrencyList(it))
            }
        })

    ShowStatusBottomSheet(
        uiState = uiState,
        showStatusBottomSheet = showStatusBottomSheet,
        data = uiState.listStatus,
        state = if (uiState.listStatus.isNullOrEmpty()) {
            MultiSearchDialogState.LOADING
        } else {
            MultiSearchDialogState.CONTENT
        },
        onItemSelected = {
            if (it.size == uiState.listStatus?.size) {
                onEvent(AdvSearchReducer.AdvSearchEvent.SetStatusList(null))
            } else {
                onEvent(AdvSearchReducer.AdvSearchEvent.SetStatusList(it))
            }

        })

    ShowDatePickerBottomSheet(showDatePicker = showDatePicker, uiState = uiState, onEvent = onEvent)

    uiState.modalConfirmConfig?.let {
        IBankModalConfirm(
            modalConfirmType = uiState.modalConfirmConfig.modalConfirmType,
            title = uiState.modalConfirmConfig.title,
            supportingText = uiState.modalConfirmConfig.supportingText,
            listDialogButtonInfo = listOf(DialogButtonInfo(label = stringResource(vn.com.bidv.localization.R.string.dong))),
            onDismissRequest = {
                onEvent(AdvSearchReducer.AdvSearchEvent.ShowError(modalConfirm = null))
                showMoneyTypeBottomSheet.value = false
                showStatusBottomSheet.value = false
            })
    }
}

@Composable
fun CustomerInfoDebitAccountItem(
    uiState: AdvSearchReducer.AdvSearchState,
    onValueChange: (field: AdvSearchItem, value: String) -> Unit,
    placeHolderText: String,
) {
    IBankInputFieldBase(
        text = uiState.debit ?: "",
        filters = listOf(SpecialCharacterFilter(), RemoveSpaceFilter()),
        maxLengthText = Constants.MAX_LENGTH_DEBIT_TEXT_INPUT,
        inputType = KeyboardOptions(keyboardType = KeyboardType.Password),
        placeholderText = placeHolderText,
        onClickClear = {
            onValueChange(AdvSearchItem.DebitAccount(), "")
        }) { textFieldValue ->
        onValueChange(AdvSearchItem.DebitAccount(), textFieldValue.text)
    }
}

@Composable
fun CustomerInfoTaxIdItem(
    uiState: AdvSearchReducer.AdvSearchState,
    onValueChange: (field: AdvSearchItem, value: String) -> Unit,
    placeHolderText: String,
) {
    IBankInputFieldBase(
        text = uiState.tax ?: "",
        filters = listOf(SpecialCharacterFilter(), RemoveSpaceFilter()),
        maxLengthText = Constants.MAX_LENGTH_TAX_TEXT_INPUT,
        inputType = KeyboardOptions(keyboardType = KeyboardType.Password),
        placeholderText = placeHolderText,
        onClickClear = {
            onValueChange(AdvSearchItem.TaxId(), "")
        }) { textFieldValue ->
        onValueChange(AdvSearchItem.TaxId(), textFieldValue.text)
    }
}

@Composable
fun CustomerInfoDeclarationNumberItem(
    uiState: AdvSearchReducer.AdvSearchState,
    onValueChange: (field: AdvSearchItem, value: String) -> Unit,
    placeHolderText: String,
) {
    IBankInputFieldBase(
        text = uiState.declaration ?: "",
        maxLengthText = Constants.MAX_LENGTH_DECLARATION_TEXT_INPUT,
        filters = listOf(RemoveSpaceFilter(), RemoveVietnameseAccentFilter()),
        inputType = KeyboardOptions(keyboardType = KeyboardType.Password),
        placeholderText = placeHolderText,
        onClickClear = {
            onValueChange(AdvSearchItem.DeclarationNumber(), "")
        }) { textFieldValue ->
        onValueChange(AdvSearchItem.DeclarationNumber(), textFieldValue.text)
    }
}

@Composable
fun CustomerInfoBatchNumberItem(
    uiState: AdvSearchReducer.AdvSearchState,
    onValueChange: (field: AdvSearchItem, value: String) -> Unit,
    placeHolderText: String,
) {
    IBankInputFieldBase(
        text = uiState.batch ?: "",
        maxLengthText = Constants.MAX_LENGTH_TAX_BATCH_INPUT,
        filters = listOf(SpecialCharacterFilter(), RemoveSpaceFilter()),
        inputType = KeyboardOptions(keyboardType = KeyboardType.Password),
        placeholderText = placeHolderText,
        onClickClear = {
            onValueChange(AdvSearchItem.BatchNumber(), "")
        }) { textFieldValue ->
        onValueChange(AdvSearchItem.BatchNumber(), textFieldValue.text)
    }
}

@Composable
fun DatePickerItem(
    uiState: AdvSearchReducer.AdvSearchState,
    onEvent: (AdvSearchReducer.AdvSearchEvent) -> Unit,
    colorScheme: IBColorScheme,
    showDatePicker: MutableState<Boolean>,
    placeHolderText: String,
) {
    val startDate = uiState.startDate?.dateToString() ?: ""
    val endDate = uiState.endDate?.dateToString() ?: startDate
    val displayDateText = if (uiState.startDate != null) "$startDate - $endDate" else ""

    IBankInputDropdownBaseV2(
        onClickEnd = {
            showDatePicker.value = true
        },
        labelText = placeHolderText,
        typeData = IBankInputDropdownTypeData.Select(text = displayDateText),
        iconEnd = vn.com.bidv.designsystem.R.drawable.calendar_outline,
        state = if (uiState.startDate != null) IBFrameExtendState.FILLED(colorScheme) else IBFrameState.DEFAULT(
            colorScheme
        ),
        onClickClear = {
            onEvent(AdvSearchReducer.AdvSearchEvent.ClearDate)
        })
}

@Composable
fun ShowDatePickerBottomSheet(
    uiState: AdvSearchReducer.AdvSearchState,
    showDatePicker: MutableState<Boolean>,
    onEvent: (AdvSearchReducer.AdvSearchEvent) -> Unit
) {
    if (showDatePicker.value) {
        var startDate by remember { mutableStateOf(uiState.startDate) }
        var endDate by remember { mutableStateOf(uiState.endDate) }
        val baseCalendar = Calendar.getInstance().apply {
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }

        IBankDateRangePickerDialog(
            modifier = Modifier.fillMaxWidth(),
            title = stringResource(id = vn.com.bidv.localization.R.string.chon_khoang_thoi_gian),
            startDateSelected = startDate,
            endDateSelected = endDate,
            negativeButtonText = stringResource(vn.com.bidv.localization.R.string.huy),
            onDismissRequest = { showDatePicker.value = false },
            config = DatePickerConfig.build {
                minDate = baseCalendar.apply { add(Calendar.YEAR, -1) }.time
                maxDate = Date()
                firstDayOfWeek = Calendar.MONDAY
            },
        ) { start, end ->
            startDate = start
            endDate = end
            onEvent(
                AdvSearchReducer.AdvSearchEvent.SelectDate(
                    startDate = startDate, endDate = endDate
                )
            )
        }
    }
}

@Composable
fun CurrencyDropdownItem(
    uiState: AdvSearchReducer.AdvSearchState,
    onEvent: (AdvSearchReducer.AdvSearchEvent) -> Unit,
    showMoneyTypeBottomSheet: MutableState<Boolean>,
) {
    val colorScheme = LocalColorScheme.current
    IBankInputDropdownBaseV2(
        onClickEnd = {
            showMoneyTypeBottomSheet.value = true
            onEvent(
                AdvSearchReducer.AdvSearchEvent.ShowCurrencyBottomSheet
            )
        },
        labelText = stringResource(id = vn.com.bidv.localization.R.string.loai_tien),
        typeData = when {
            uiState.listCurrencySelected.isNullOrEmpty() || uiState.listCurrencySelected.size == uiState.listCurrency?.size -> IBankInputDropdownTypeData.Select(
                stringResource(vn.com.bidv.localization.R.string.tat_ca)
            )

            else -> {
                IBankInputDropdownTypeData.Tags(uiState.listCurrencySelected.map { it })
            }
        },
        showClearButton = false,
        maxTagCount = 5,
        onTagRemoved = { index ->
            val updatedList =
                uiState.listCurrencySelected?.toMutableList()?.apply { removeAt(index) }?.toList()
            onEvent(
                AdvSearchReducer.AdvSearchEvent.SetCurrencyList(
                    listCurrencySelected = if (updatedList.isNullOrEmpty()) uiState.listCurrency else updatedList
                )
            )
        },
        iconEnd = vn.com.bidv.designsystem.R.drawable.arrow_bottom_outline,
        state = if (!uiState.listCurrency.isNullOrEmpty()) IBFrameExtendState.FILLED(
            colorScheme
        ) else IBFrameState.DEFAULT(
            colorScheme
        )
    )
}

@Composable
fun StatusDropdownItem(
    uiState: AdvSearchReducer.AdvSearchState,
    onEvent: (AdvSearchReducer.AdvSearchEvent) -> Unit,
    showMoneyTypeBottomSheet: MutableState<Boolean>,
) {
    val colorScheme = LocalColorScheme.current
    IBankInputDropdownBaseV2(
        onClickEnd = {
            showMoneyTypeBottomSheet.value = true
            onEvent(
                AdvSearchReducer.AdvSearchEvent.ShowStatusBottomSheet
            )
        },
        labelText = stringResource(vn.com.bidv.localization.R.string.trang_thai),
        typeData = when {
            uiState.listStatusSelected.isNullOrEmpty() || uiState.listStatusSelected.size == uiState.listStatus?.size -> IBankInputDropdownTypeData.Select(
                stringResource(vn.com.bidv.localization.R.string.tat_ca)
            )

            else -> {
                IBankInputDropdownTypeData.Tags(uiState.listStatusSelected.map { stringResource(it.statusResourceId) })
            }
        },
        showClearButton = false,
        maxTagCount = 5,
        onTagRemoved = { index ->
            val updatedList =
                uiState.listStatusSelected?.toMutableList()?.apply { removeAt(index) }?.toList()
            onEvent(
                AdvSearchReducer.AdvSearchEvent.SetStatusList(
                    listStatusSelected = if (updatedList.isNullOrEmpty()) uiState.listStatus else updatedList
                )
            )
        },
        iconEnd = vn.com.bidv.designsystem.R.drawable.arrow_bottom_outline,
        state = if (!uiState.listStatus.isNullOrEmpty()) IBFrameExtendState.FILLED(
            colorScheme
        ) else IBFrameState.DEFAULT(
            colorScheme
        )
    )
}

@Composable
fun MinInputMoneyItem(
    uiState: AdvSearchReducer.AdvSearchState,
    onEvent: (AdvSearchReducer.AdvSearchEvent) -> Unit,
    onValueChange: (field: AdvSearchItem, value: String) -> Unit,
    inputMoneyState: IBFrameState,
    inputMoneyMessage: String,
    placeHolderText: String,
) {
    IBankInputMoney(
        state = inputMoneyState,
        helpTextLeft = inputMoneyMessage,
        maxLengthText = Constants.MAX_LENGTH_MONEY_INPUT,
        textValue = uiState.minAmount ?: "",
        placeholderText = placeHolderText,
        onClickClear = {
            onValueChange(AdvSearchItem.InputMoney.MinAmount(), "")
        },
        onAmountChange = {
            onValueChange(AdvSearchItem.InputMoney.MinAmount(), it)
            handleValidInputMoney(minAmount = it, maxAmount = uiState.maxAmount, onEvent = onEvent)
        },
        onSelectCurrency = {},
        showCurrency = false
    )
}

@Composable
fun MaxInputMoneyItem(
    uiState: AdvSearchReducer.AdvSearchState,
    onEvent: (AdvSearchReducer.AdvSearchEvent) -> Unit,
    onValueChange: (field: AdvSearchItem, value: String) -> Unit,
    inputMoneyState: IBFrameState,
    inputMoneyMessage: String,
    placeHolderText: String
) {
    IBankInputMoney(
        state = inputMoneyState,
        helpTextLeft = inputMoneyMessage,
        maxLengthText = Constants.MAX_LENGTH_MONEY_INPUT,
        textValue = uiState.maxAmount ?: "",
        placeholderText = placeHolderText,
        onClickClear = {
            onValueChange(AdvSearchItem.InputMoney.MaxAmount(), "")
        },
        onAmountChange = {
            onValueChange(AdvSearchItem.InputMoney.MaxAmount(), it)
            handleValidInputMoney(minAmount = uiState.minAmount, maxAmount = it, onEvent = onEvent)
        },
        onSelectCurrency = {},
        showCurrency = false
    )
}

private fun handleValidInputMoney(
    minAmount: String?,
    maxAmount: String?,
    onEvent: (AdvSearchReducer.AdvSearchEvent) -> Unit,
) {
    if (!minAmount.isNullOrBlank() && !maxAmount.isNullOrBlank()) {
        val minAmountValue = minAmount.toLongOrNull()
        val maxAmountValue = maxAmount.toLongOrNull()

        if (minAmountValue != null && maxAmountValue != null) {
            onEvent(AdvSearchReducer.AdvSearchEvent.ValidateAmount(minAmountValue > maxAmountValue))
        }
    } else {
        onEvent(AdvSearchReducer.AdvSearchEvent.ValidateAmount(false))
    }
}

private fun handleSearchFilter(
    onEvent: (AdvSearchReducer.AdvSearchEvent) -> Unit,
    searchRuleFilter: ListCustomsDutiesRuleFilter?
) {
    if (searchRuleFilter == null || searchRuleFilter.getBadgeNumber() == 0) {
        onEvent(AdvSearchReducer.AdvSearchEvent.ClearSearchFilterContent)
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewAdvSearchScreenContent() {

    val sampleUiState = AdvSearchReducer.AdvSearchState(
        startDate = Date(),
        endDate = Date(),
        debit = "1234567890",
        tax = "*********",
        declaration = "12",
        batch = "BATCH001",
        minAmount = "100000",
        maxAmount = "5000000",
        listCurrency = listOf(
            "VND",
            "USD",
        ),
        listCurrencySelected = listOf(
            "VND"
        ),
        listStatus = listOf(
            TransactionStatusBase.INIT,
            TransactionStatusBase.REJECTED,
        ),
        listStatusSelected = listOf(TransactionStatusBase.REJECTED),
        invalidAmount = false,
        modalConfirmConfig = null
    )
    val sampleAdvSearchItems = listOf(
        AdvSearchItem.DebitAccount(),
        AdvSearchItem.TaxId(),
        AdvSearchItem.DeclarationNumber(),
        AdvSearchItem.DropDownItem.Status(),
        AdvSearchItem.Date,
        AdvSearchItem.BatchNumber(),
        AdvSearchItem.InputMoney.MinAmount(vn.com.bidv.localization.R.string.tu_so_tien),
        AdvSearchItem.InputMoney.MaxAmount(vn.com.bidv.localization.R.string.den_so_tien),
        AdvSearchItem.DropDownItem.Currency(),
    )

    val onEvent: (AdvSearchReducer.AdvSearchEvent) -> Unit = {}
    val searchRuleFilter: ListCustomsDutiesRuleFilter? = null
    val onValueChange: (field: AdvSearchItem, value: String) -> Unit = { _, _ -> }

    AdvSearchScreenContent(
        uiState = sampleUiState,
        onEvent = onEvent,
        listAdvSearchItem = sampleAdvSearchItems,
        searchRuleFilter = searchRuleFilter,
        onValueChange = onValueChange
    )
}