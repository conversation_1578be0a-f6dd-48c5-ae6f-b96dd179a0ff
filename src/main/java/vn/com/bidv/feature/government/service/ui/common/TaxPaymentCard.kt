package vn.com.bidv.feature.government.service.ui.common

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import androidx.navigation.compose.rememberNavController
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.ModelCheckAble
import vn.com.bidv.feature.government.service.constants.Constants
import vn.com.bidv.feature.government.service.domain.model.InquiryCustomsDutyDMO
import vn.com.bidv.feature.government.service.model.IViewTransaction
import vn.com.bidv.feature.government.service.navigation.GovernmentServiceRoute

@Composable
fun TaxPaymentCard(
    transactionsDMO: ModelCheckAble<out IViewTransaction>,
    navController: NavHostController,
    checkboxEnabled: Boolean = false,
    onCheckedChange: () -> Unit,
) {
    val colorScheme = LocalColorScheme.current
    TaxPaymentCardContent(
        modifier = Modifier
            .clip(shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL))
            .background(colorScheme.bgMainTertiary)
            .border(
                width = 1.dp,
                color = colorScheme.borderMainSecondary,
                shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL)
            ),
        checkboxEnabled = checkboxEnabled,
        cardHeader = {
            ItemCardHeader(transactionsDMO.data)
        },
        cardContent = {
            Column(Modifier.padding(top = 12.dp)) {
                ItemCardBody(transactionsDMO.data)
            }
        },
        showCheckbox = true,
        isChecked = transactionsDMO.isChecked,
        cardFooter = {},
        onCheckedChange = {
            onCheckedChange()
        },
        onClick = {
            navController.navigate(
                GovernmentServiceRoute.TaxPaymentDetailRoute(
                    args = (transactionsDMO.data as InquiryCustomsDutyDMO)
                )
            )
        },
    )
}

@Preview
@Composable
fun PrevTaxPaymentEntryCard() {
    Box(Modifier.background(Color.Black).padding(4.dp)) {
        TaxPaymentCard(
            transactionsDMO = ModelCheckAble(Constants.fakeTransactions[0]),
            navController = rememberNavController(),
            onCheckedChange = {}
        )
    }
}