package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.feature.common.utils.ModalConfirmConfig
import vn.com.bidv.feature.government.service.domain.usecase.GovernmentServiceUseCase
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class AddPaymentViewModel @Inject constructor(
    private val governmentServiceUseCase: GovernmentServiceUseCase
) : ViewModelIBankBase<AddPaymentReducer.AddPaymentState, AddPaymentReducer.AddPaymentEvent, AddPaymentReducer.AddPaymentEffect>(
    initialState = AddPaymentReducer.AddPaymentState(),
    reducer = AddPaymentReducer()
) {
    override fun handleEffect(
        sideEffect: AddPaymentReducer.AddPaymentEffect,
        onResult: (AddPaymentReducer.AddPaymentEvent) -> Unit
    ) {
        when (sideEffect) {
            is AddPaymentReducer.AddPaymentEffect.GetListChapterCode -> {
                callDomain(
                    showLoadingIndicator = false,
                    onSuccess = { result ->
                        onResult(
                            AddPaymentReducer.AddPaymentEvent.ChapterCodeEvent.GetListChapterCodeSuccess(
                                result.data?.items
                            )
                        )
                    }
                ) {
                    governmentServiceUseCase.getListChapterCode()
                }
            }

            is AddPaymentReducer.AddPaymentEffect.GetListEconomicCode -> {
                callDomain(
                    showLoadingIndicator = false,
                    onSuccess = { result ->
                        onResult(
                            AddPaymentReducer.AddPaymentEvent.EconomicCodeEvent.GetListEconomicCodeSuccess(
                                result.data?.items
                            )
                        )
                    },
                ) {
                    governmentServiceUseCase.getListEconomicCode()
                }
            }

            is AddPaymentReducer.AddPaymentEffect.GetListCurrencyType -> {
                onResult(
                    AddPaymentReducer.AddPaymentEvent.CurrencyTypeEvent.GetListCurrencyTypeSuccess(
                        governmentServiceUseCase.getListCurrencyType()
                    )
                )
            }

            is AddPaymentReducer.AddPaymentEffect.GetListTaxType -> {
                callDomain(
                    showLoadingIndicator = false,
                    onSuccess = { result ->
                        onResult(
                            AddPaymentReducer.AddPaymentEvent.TaxTypeEvent.GetListTaxTypeSuccess(
                                result.data?.items
                            )
                        )
                    }
                ) {
                    governmentServiceUseCase.getListTaxType()
                }
            }

            is AddPaymentReducer.AddPaymentEffect.GetListCustomsCurrency -> {
                callDomain(
                    showLoadingIndicator = false,
                    onSuccess = { result ->
                        onResult(
                            AddPaymentReducer.AddPaymentEvent.CustomsCurrencyEvent.GetListCustomsCurrencySuccess(
                                result.data?.items
                            )
                        )
                    }
                ) {
                    governmentServiceUseCase.getListCustomsCurrency()
                }
            }

            is AddPaymentReducer.AddPaymentEffect.GetListTradeType -> {
                callDomain(
                    showLoadingIndicator = false,
                    onSuccess = { result ->
                        onResult(
                            AddPaymentReducer.AddPaymentEvent.TradeTypeEvent.GetListTradeTypeSuccess(
                                result.data?.items
                            )
                        )
                    }
                ) {
                    governmentServiceUseCase.getListTradeType()
                }
            }

            is AddPaymentReducer.AddPaymentEffect.SendDataToMain -> {
                viewModelScope.launch {
                    governmentServiceUseCase.sendDataToMain(sideEffect.addPaymentDTO)
                }
                onResult(AddPaymentReducer.AddPaymentEvent.AddPaymentSuccess)
            }

            is AddPaymentReducer.AddPaymentEffect.NavigateBack -> {
                /*nothing*/
            }
        }
    }
}