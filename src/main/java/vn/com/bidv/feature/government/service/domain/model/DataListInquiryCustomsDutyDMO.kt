package vn.com.bidv.feature.government.service.domain.model

import com.google.gson.annotations.SerializedName
import kotlinx.serialization.Serializable
import vn.com.bidv.feature.government.service.model.BaseTransaction
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants
import java.util.UUID

data class DataListInquiryCustomsDutyDMO(
    val items: List<InquiryCustomsDutyDMO>,
)

@Serializable
data class InquiryCustomsDutyDMO(
    @SerializedName("eiTypeCode")
    override val eiTypeCode: String,

    /* Mã sắc thuế */
    @SerializedName("taxTypeCode")
    override val taxTypeCode: String,

    /* Mã loại tiền hải quan */
    @SerializedName("ccCode")
    override val ccCode: String,

    /* Mã chương */
    @SerializedName("chapterCode")
    override val chapterCode: String,

    /* Mã nội dung kinh tế */
    @SerializedName("ecCode")
    override val ecCode: String,

    /* <PERSON>ố tiền */
    @SerializedName("amount")
    override val amount: String,

    /* Loại tiền tệ của số tiền nhập */
    @SerializedName("ccy")
    override val ccy: String,

    /* Ngày tờ khai */
    @SerializedName("declarationDate")
    override val declarationDate: String,

    /* Số tờ khai */
    @SerializedName("declarationNo")
    override val declarationNo: String,

    /* Diễn giải giao dịch */
    @SerializedName("transDesc")
    override val transDesc: String,

    /* Loại hình người nộp thuế: 0 - Không xác định, 1 - doanh nghiệp, 2 - cá nhân */
    @SerializedName("payerType")
    val payerType: Int,

    /* Tên loại tiền hải quan */
    @SerializedName("ccName")
    val ccName: String? = null,

    /* Tên chương */
    @SerializedName("chapterName")
    val chapterName: String? = null,

    /* Tên nội dung kinh tế */
    @SerializedName("ecName")
    val ecName: String? = null,

    /* Tên loại hình xuất nhập khẩu */
    @SerializedName("eiTypeName")
    val eiTypeName: String? = null,

    /* Tên sắc thuế */
    @SerializedName("taxTypeName")
    val taxTypeName: String? = null,

    /* Mã kho bạc */
    @SerializedName("treasuryCode")
    override val treasuryCode: String? = null,

    /* Tên kho bạc */
    @SerializedName("treasuryName")
    val treasuryName: String? = null,

    /* Mã địa bàn hành chính */
    @SerializedName("admAreaCode")
    override val admAreaCode: String? = null,

    /* Tên địa bàn hành chính */
    @SerializedName("admAreaName")
    val admAreaName: String? = null,

    /* Mã tài khoản thu */
    @SerializedName("revAccCode")
    override val revAccCode: String? = null,

    /* Tên tài khoản thu */
    @SerializedName("revAccName")
    val revAccName: String? = null,

    /* Mã cơ quan thu */
    @SerializedName("revAuthCode")
    override val revAuthCode: String? = null,

    /* Tên cơ quan thu */
    @SerializedName("revAuthName")
    val revAuthName: String? = null,

    @SerializedName("payerTypeName")
    val payerTypeName: String? = null
): BaseTransaction() {
    override val uniqueId = UUID.randomUUID().toString()

    override fun getHeaderString() = declarationNo

    override fun getValueTitle() = "$eiTypeCode - $eiTypeName"

    override fun getValueTaxDescription() = ecName ?: "--"

    override fun getValueAmount() = amount

    override fun getValueCcy() = ccy

    override fun getValueDate(): Pair<String, String> {
        return SdkBaseConstants.DateTimeConstants.FORMAT_YYYY_MM_DD to declarationDate
    }
}