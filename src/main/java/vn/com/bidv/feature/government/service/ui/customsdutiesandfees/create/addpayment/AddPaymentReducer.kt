package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.common.utils.ModalConfirmConfig
import vn.com.bidv.feature.government.service.domain.model.ChapterDMO
import vn.com.bidv.feature.government.service.domain.model.CustomsCurrencyDMO
import vn.com.bidv.feature.government.service.domain.model.EconomicContentDMO
import vn.com.bidv.feature.government.service.domain.model.ExportImportTypeDMO
import vn.com.bidv.feature.government.service.domain.model.TaxTypeDMO
import vn.com.bidv.feature.government.service.model.AddPaymentDTO
import vn.com.bidv.feature.government.service.model.FieldStatus
import java.util.Date

class AddPaymentReducer :
    Reducer<AddPaymentReducer.AddPaymentState, AddPaymentReducer.AddPaymentEvent, AddPaymentReducer.AddPaymentEffect> {

    @Immutable
    data class AddPaymentState(
        val declarationNumber: String? = null,
        val date: Date? = null,
        val listChapterCode: List<ChapterDMO>? = null,
        val chapterCodeSelected: ChapterDMO? = null,
        val showChapterCodeBottomSheet: Boolean = false,
        val listEconomicCode: List<EconomicContentDMO>? = null,
        val economicCodeSelected: EconomicContentDMO? = null,
        val showEconomicCodeBottomSheet: Boolean = false,
        val listCurrencyType: List<String>? = null,
        val currencyTypeSelected: String? = "VND",
        val showCurrencyTypeBottomSheet: Boolean = false,
        val listTaxType: List<TaxTypeDMO>? = null,
        val taxTypeSelected: TaxTypeDMO? = null,
        val showTaxTypeBottomSheet: Boolean = false,
        val listCustomsCurrency: List<CustomsCurrencyDMO>? = null,
        val customsCurrencySelected: CustomsCurrencyDMO? = null,
        val showCustomsCurrencyBottomSheet: Boolean = false,
        val listTradeType: List<ExportImportTypeDMO>? = null,
        val tradeTypeSelected: ExportImportTypeDMO? = null,
        val showTradeTypeBottomSheet: Boolean = false,
        val amount: String? = null,
        val transactionDescription: String? = null,
        val fieldError: Map<AddPaymentItem, FieldStatus> = mutableMapOf(),
    ) : ViewState

    @Immutable
    sealed class AddPaymentEvent : ViewEvent {
        data object AddPaymentSuccess : AddPaymentEvent()
        data object ValidateField : AddPaymentEvent()

        data class ValueTextChange<T>(
            val field: AddPaymentItem,
            val newValue: T?
        ) : AddPaymentEvent()

        data object ClearDate : AddPaymentEvent()

        sealed class ChapterCodeEvent : AddPaymentEvent() {
            data class GetListChapterCodeSuccess(val listChapterCode: List<ChapterDMO>? = null) :
                ChapterCodeEvent()

            data class ShowChapterCodeBottomSheet(val showChapterCodeBottomSheet: Boolean = false) :
                ChapterCodeEvent()

            data object ClearChapterCode : ChapterCodeEvent()
        }

        sealed class EconomicCodeEvent : AddPaymentEvent() {
            data class GetListEconomicCodeSuccess(val listEconomicCode: List<EconomicContentDMO>? = null) :
                EconomicCodeEvent()

            data class ShowEconomicCodeBottomSheet(val showEconomicCodeBottomSheet: Boolean = false) :
                EconomicCodeEvent()

            data object ClearEconomicCode : EconomicCodeEvent()
        }

        sealed class CurrencyTypeEvent : AddPaymentEvent() {
            data class GetListCurrencyTypeSuccess(val listCurrencyType: List<String>? = null) :
                CurrencyTypeEvent()

            data class ShowCurrencyTypeBottomSheet(val showCurrencyTypeBottomSheet: Boolean = false) :
                CurrencyTypeEvent()

            data object ClearCurrencyType : CurrencyTypeEvent()
        }

        sealed class TaxTypeEvent : AddPaymentEvent() {
            data class GetListTaxTypeSuccess(val listTaxType: List<TaxTypeDMO>? = null) :
                TaxTypeEvent()

            data class ShowTaxTypeBottomSheet(val showTaxTypeBottomSheet: Boolean = false) :
                TaxTypeEvent()

            data object ClearTaxType : TaxTypeEvent()
        }

        sealed class CustomsCurrencyEvent : AddPaymentEvent() {
            data class GetListCustomsCurrencySuccess(val listCustomsCurrency: List<CustomsCurrencyDMO>? = null) :
                CustomsCurrencyEvent()

            data class ShowCustomsCurrencyBottomSheet(val showCustomsCurrencyBottomSheet: Boolean = false) :
                CustomsCurrencyEvent()

            data object ClearCustomsCurrency : CustomsCurrencyEvent()
        }

        sealed class TradeTypeEvent : AddPaymentEvent() {
            data class GetListTradeTypeSuccess(val listTradeType: List<ExportImportTypeDMO>? = null) :
                TradeTypeEvent()

            data class ShowTradeTypeBottomSheet(val showTradeTypeBottomSheet: Boolean = false) :
                TradeTypeEvent()

            data object ClearTradeType : TradeTypeEvent()
        }

    }

    @Immutable
    sealed class AddPaymentEffect : SideEffect {
        data object GetListChapterCode : AddPaymentEffect()
        data object GetListEconomicCode : AddPaymentEffect()
        data object GetListCurrencyType : AddPaymentEffect()
        data object GetListTaxType : AddPaymentEffect()
        data object GetListCustomsCurrency : AddPaymentEffect()
        data object GetListTradeType : AddPaymentEffect()
        data object NavigateBack : AddPaymentEffect(), UIEffect
        data class SendDataToMain(val addPaymentDTO: AddPaymentDTO? = null) : AddPaymentEffect()
    }

    override fun reduce(
        previousState: AddPaymentState, event: AddPaymentEvent
    ): Pair<AddPaymentState, AddPaymentEffect?> {

        return when (event) {
            is AddPaymentEvent.AddPaymentSuccess -> {
                previousState to AddPaymentEffect.NavigateBack
            }

            is AddPaymentEvent.ValidateField -> {
                handleValidateField(previousState)
            }

            is AddPaymentEvent.ValueTextChange<*> -> {
                reduceValueTextChange(previousState, event)
            }

            is AddPaymentEvent.ClearDate -> {
                previousState.copy(date = null) to null
            }

            is AddPaymentEvent.ChapterCodeEvent -> reduceChapterCodeEvent(previousState, event)
            is AddPaymentEvent.EconomicCodeEvent -> reduceEconomicCodeEvent(previousState, event)
            is AddPaymentEvent.CurrencyTypeEvent -> reduceCurrencyTypeEvent(previousState, event)
            is AddPaymentEvent.TaxTypeEvent -> reduceTaxTypeEvent(previousState, event)
            is AddPaymentEvent.CustomsCurrencyEvent -> reduceCustomsCurrencyEvent(
                previousState,
                event
            )

            is AddPaymentEvent.TradeTypeEvent -> reduceTradeTypeEvent(previousState, event)

        }
    }

    private fun reduceValueTextChange(
        previousState: AddPaymentState,
        event: AddPaymentEvent.ValueTextChange<*>
    ): Pair<AddPaymentState, AddPaymentEffect?> {
        return when (event.field) {
            is AddPaymentItem.Date -> {
                previousState.copy(date = event.newValue as Date?) to null
            }

            is AddPaymentItem.DeclarationNumber -> {
                previousState.copy(declarationNumber = event.newValue as String?) to null
            }

            is AddPaymentItem.InputMoney -> {
                previousState.copy(amount = event.newValue as String?) to null
            }

            is AddPaymentItem.TransactionDescription -> {
                previousState.copy(transactionDescription = event.newValue as String?) to null
            }

            is AddPaymentItem.DropDownItem.ChapterCode -> {
                previousState.copy(chapterCodeSelected = event.newValue as ChapterDMO?) to null
            }

            is AddPaymentItem.DropDownItem.EconomicCode -> {
                previousState.copy(economicCodeSelected = event.newValue as EconomicContentDMO?) to null
            }

            is AddPaymentItem.DropDownItem.CurrencyType -> {
                previousState.copy(currencyTypeSelected = event.newValue as String?) to null
            }

            is AddPaymentItem.DropDownItem.TaxType -> {
                previousState.copy(taxTypeSelected = event.newValue as TaxTypeDMO?) to null
            }

            is AddPaymentItem.DropDownItem.CustomsCurrency -> {
                previousState.copy(customsCurrencySelected = event.newValue as CustomsCurrencyDMO?) to null
            }

            is AddPaymentItem.DropDownItem.TradeType -> {
                previousState.copy(tradeTypeSelected = event.newValue as ExportImportTypeDMO?) to null
            }
        }
    }

    private fun reduceChapterCodeEvent(
        previousState: AddPaymentState,
        event: AddPaymentEvent.ChapterCodeEvent
    ): Pair<AddPaymentState, AddPaymentEffect?> {
        return when (event) {
            is AddPaymentEvent.ChapterCodeEvent.GetListChapterCodeSuccess -> {
                previousState.copy(listChapterCode = event.listChapterCode) to null
            }

            is AddPaymentEvent.ChapterCodeEvent.ShowChapterCodeBottomSheet -> {
                val effect =
                    if (event.showChapterCodeBottomSheet) AddPaymentEffect.GetListChapterCode else null
                previousState.copy(
                    showChapterCodeBottomSheet = event.showChapterCodeBottomSheet
                ) to effect

            }

            is AddPaymentEvent.ChapterCodeEvent.ClearChapterCode -> {
                previousState.copy(chapterCodeSelected = null) to null
            }
        }
    }

    private fun reduceEconomicCodeEvent(
        previousState: AddPaymentState,
        event: AddPaymentEvent.EconomicCodeEvent
    ): Pair<AddPaymentState, AddPaymentEffect?> {
        return when (event) {
            is AddPaymentEvent.EconomicCodeEvent.GetListEconomicCodeSuccess -> {
                previousState.copy(listEconomicCode = event.listEconomicCode) to null
            }

            is AddPaymentEvent.EconomicCodeEvent.ShowEconomicCodeBottomSheet -> {
                val effect =
                    if (event.showEconomicCodeBottomSheet) AddPaymentEffect.GetListEconomicCode else null
                previousState.copy(
                    showEconomicCodeBottomSheet = event.showEconomicCodeBottomSheet
                ) to effect

            }

            is AddPaymentEvent.EconomicCodeEvent.ClearEconomicCode -> {
                previousState.copy(economicCodeSelected = null) to null
            }
        }
    }

    private fun reduceCurrencyTypeEvent(
        previousState: AddPaymentState,
        event: AddPaymentEvent.CurrencyTypeEvent
    ): Pair<AddPaymentState, AddPaymentEffect?> {
        return when (event) {
            is AddPaymentEvent.CurrencyTypeEvent.GetListCurrencyTypeSuccess -> {
                previousState.copy(listCurrencyType = event.listCurrencyType) to null
            }

            is AddPaymentEvent.CurrencyTypeEvent.ShowCurrencyTypeBottomSheet -> {
                val effect =
                    if (event.showCurrencyTypeBottomSheet) AddPaymentEffect.GetListCurrencyType else null
                previousState.copy(
                    showCurrencyTypeBottomSheet = event.showCurrencyTypeBottomSheet
                ) to effect

            }

            is AddPaymentEvent.CurrencyTypeEvent.ClearCurrencyType -> {
                previousState.copy(currencyTypeSelected = null) to null
            }
        }
    }

    private fun reduceTaxTypeEvent(
        previousState: AddPaymentState,
        event: AddPaymentEvent.TaxTypeEvent
    ): Pair<AddPaymentState, AddPaymentEffect?> {
        return when (event) {
            is AddPaymentEvent.TaxTypeEvent.GetListTaxTypeSuccess -> {
                previousState.copy(listTaxType = event.listTaxType) to null
            }

            is AddPaymentEvent.TaxTypeEvent.ShowTaxTypeBottomSheet -> {
                val effect =
                    if (event.showTaxTypeBottomSheet) AddPaymentEffect.GetListTaxType else null
                previousState.copy(
                    showTaxTypeBottomSheet = event.showTaxTypeBottomSheet
                ) to effect

            }

            is AddPaymentEvent.TaxTypeEvent.ClearTaxType -> {
                previousState.copy(taxTypeSelected = null) to null
            }
        }
    }

    private fun reduceCustomsCurrencyEvent(
        previousState: AddPaymentState,
        event: AddPaymentEvent.CustomsCurrencyEvent
    ): Pair<AddPaymentState, AddPaymentEffect?> {
        return when (event) {
            is AddPaymentEvent.CustomsCurrencyEvent.GetListCustomsCurrencySuccess -> {
                previousState.copy(listCustomsCurrency = event.listCustomsCurrency) to null
            }

            is AddPaymentEvent.CustomsCurrencyEvent.ShowCustomsCurrencyBottomSheet -> {
                val effect =
                    if (event.showCustomsCurrencyBottomSheet) AddPaymentEffect.GetListCustomsCurrency else null
                previousState.copy(
                    showCustomsCurrencyBottomSheet = event.showCustomsCurrencyBottomSheet
                ) to effect

            }

            is AddPaymentEvent.CustomsCurrencyEvent.ClearCustomsCurrency -> {
                previousState.copy(customsCurrencySelected = null) to null
            }

        }
    }

    private fun reduceTradeTypeEvent(
        previousState: AddPaymentState,
        event: AddPaymentEvent.TradeTypeEvent
    ): Pair<AddPaymentState, AddPaymentEffect?> {
        return when (event) {
            is AddPaymentEvent.TradeTypeEvent.GetListTradeTypeSuccess -> {
                previousState.copy(listTradeType = event.listTradeType) to null
            }

            is AddPaymentEvent.TradeTypeEvent.ShowTradeTypeBottomSheet -> {
                val effect =
                    if (event.showTradeTypeBottomSheet) AddPaymentEffect.GetListTradeType else null
                previousState.copy(
                    showTradeTypeBottomSheet = event.showTradeTypeBottomSheet
                ) to effect

            }

            is AddPaymentEvent.TradeTypeEvent.ClearTradeType -> {
                previousState.copy(tradeTypeSelected = null) to null
            }
        }
    }

    private fun handleValidateField(previousState: AddPaymentState): Pair<AddPaymentState, AddPaymentEffect?> {
        val errors = mutableMapOf<AddPaymentItem, FieldStatus>()
        errors[AddPaymentItem.DeclarationNumber] =
            if (previousState.declarationNumber.isNullOrBlank()) FieldStatus.INVALID else FieldStatus.VALID

        errors[AddPaymentItem.InputMoney] =
            if (previousState.amount.isNullOrBlank()) FieldStatus.INVALID else FieldStatus.VALID

        errors[AddPaymentItem.TransactionDescription] =
            if (previousState.transactionDescription.isNullOrBlank()) FieldStatus.INVALID else FieldStatus.VALID

        errors[AddPaymentItem.Date] =
            if (previousState.date == null) FieldStatus.INVALID else FieldStatus.VALID

        errors[AddPaymentItem.DropDownItem.ChapterCode] =
            if (previousState.chapterCodeSelected == null) FieldStatus.INVALID else FieldStatus.VALID

        errors[AddPaymentItem.DropDownItem.EconomicCode] =
            if (previousState.economicCodeSelected == null) FieldStatus.INVALID else FieldStatus.VALID

        errors[AddPaymentItem.DropDownItem.CurrencyType] =
            if (previousState.currencyTypeSelected == null) FieldStatus.INVALID else FieldStatus.VALID

        errors[AddPaymentItem.DropDownItem.TaxType] =
            if (previousState.taxTypeSelected == null) FieldStatus.INVALID else FieldStatus.VALID

        errors[AddPaymentItem.DropDownItem.CustomsCurrency] =
            if (previousState.customsCurrencySelected == null) FieldStatus.INVALID else FieldStatus.VALID

        errors[AddPaymentItem.DropDownItem.TradeType] =
            if (previousState.tradeTypeSelected == null) FieldStatus.INVALID else FieldStatus.VALID

        val isValid = errors.values.all { it == FieldStatus.VALID }
        val effect = if (isValid) {
            val addPaymentDTO = AddPaymentDTO(
                declarationNo = previousState.declarationNumber ?: "--",
                date = previousState.date ?: Date(),
                chapterCodeSelected = previousState.chapterCodeSelected?.chapterCode ?: "--",
                economicCodeSelected = previousState.economicCodeSelected?.ecCode ?: "--",
                currencyTypeSelected = previousState.currencyTypeSelected ?: "--",
                taxTypeSelected = previousState.taxTypeSelected?.taxTypeName ?: "--",
                customsCurrencySelected = previousState.customsCurrencySelected?.ccName ?: "--",
                tradeTypeSelected = previousState.tradeTypeSelected?.eiTypeName ?: "--",
                amount = previousState.amount ?: "--",
                transactionDescription = previousState.transactionDescription ?: "--",
                ecName = previousState.economicCodeSelected?.ecName ?: "--",
                eiTypeName = previousState.tradeTypeSelected?.eiTypeName ?: "--",
            )
            AddPaymentEffect.SendDataToMain(addPaymentDTO)
        } else null
        return previousState.copy(fieldError = errors) to effect
    }
}