package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step1

import timber.log.Timber
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.ModelCheckAble
import vn.com.bidv.feature.government.service.domain.model.InquiryCustomsDutyDMO
import vn.com.bidv.feature.government.service.domain.model.TaxPayerInfoDMO
import vn.com.bidv.feature.government.service.model.BaseTransaction
import vn.com.bidv.feature.government.service.model.TaxPayerInfo
import vn.com.bidv.feature.government.service.model.TaxPayerInfoField

class TaxPayerInfoReducer :
    Reducer<TaxPayerInfoReducer.TaxPaymentViewState, TaxPayerInfoReducer.TaxPaymentViewEvent, TaxPayerInfoReducer.TaxPaymentSideEffect> {
    data class TaxPaymentViewState(
        val initializeState: InitializeState = InitializeState.NOT_STARTED,
        val fetchedTaxId: List<ModelCheckAble<BaseTransaction>> = listOf(),

        val businessTaxManualEntries: List<ModelCheckAble<BaseTransaction>> = listOf(),
        val onBehalfTaxManualEntries: List<ModelCheckAble<BaseTransaction>> = listOf(),
        val modeDelegate: Boolean = false,
        val businessTaxInfo: TaxPayerInfo = TaxPayerInfo("", "", ""),
        val onBehalfTaxInfo: TaxPayerInfo = TaxPayerInfo("", "", ""),
        val defaultTaxPayerInfo: TaxPayerInfo = TaxPayerInfo("", "", ""),
    ) : ViewState

    sealed class TaxPaymentViewEvent : ViewEvent {
        data class ChangeModeDelegate(val modeDelegate: Boolean) : TaxPaymentViewEvent()
        data class UpdateBusinessTaxInfo(val field: TaxPayerInfoField, val value: String) :
            TaxPaymentViewEvent()

        data class UpdateOnBehalfTaxInfo(val field: TaxPayerInfoField, val value: String) :
            TaxPaymentViewEvent()

        data class SelectTaxEntry(val taxEntry: ModelCheckAble<out BaseTransaction>) :
            TaxPaymentViewEvent()

        data class AddInquiryItems(val inquiryItems: List<BaseTransaction>) :
            TaxPaymentViewEvent()

        data class AddManualItems(val manualItems: List<BaseTransaction>) :
            TaxPaymentViewEvent()

        data object InitScreen : TaxPaymentViewEvent()

        data class InitScreenFailed(
            val failedStep: InitStep,
            val errorCode: String?,
            val errorMessage: String?
        ) : TaxPaymentViewEvent()

        data class GetTaxPayerInfoSuccess(
            val taxPayerInfo: TaxPayerInfoDMO
        ) : TaxPaymentViewEvent()

        data class InquiryTransactionSuccessful(
            val transactions: List<InquiryCustomsDutyDMO>
        ) : TaxPaymentViewEvent()

        data class RetryInit(val initStep: InitStep): TaxPaymentViewEvent()
    }

    enum class InitializeState {
        NOT_STARTED, RUNNING, COMPLETED
    }

    sealed interface InitStep {
        data object FetchUserInfo: InitStep
        data class FetchDefaultListInquiry(val taxCode: String): InitStep
    }

    sealed class TaxPaymentSideEffect : SideEffect {
        data object InitScreen : TaxPaymentSideEffect()
        data object ToastAddInquiryItemsSuccessfully : TaxPaymentSideEffect()
        data object ToastAddInquiryItemsDuplicated : TaxPaymentSideEffect()
        data class InitScreenFailed(val errorCode: String?, val errorMessage: String?, val failedInitStep: InitStep) :
            TaxPaymentSideEffect(), UIEffect

        data class RetryInit(val initStep: InitStep) : TaxPaymentSideEffect()

        data class InquiryTransaction(val taxCode: String) : TaxPaymentSideEffect()
    }

    override fun reduce(
        previousState: TaxPaymentViewState,
        event: TaxPaymentViewEvent
    ): Pair<TaxPaymentViewState, TaxPaymentSideEffect?> {
        return when (event) {
            is TaxPaymentViewEvent.InitScreen -> {
                previousState.copy(
                    initializeState = InitializeState.RUNNING
                ) to TaxPaymentSideEffect.InitScreen
            }

            is TaxPaymentViewEvent.ChangeModeDelegate -> {
                if (previousState.modeDelegate != event.modeDelegate) {
                    if (event.modeDelegate) {
                        previousState.copy(
                            modeDelegate = true,
                            businessTaxInfo = TaxPayerInfo("", "", ""),
                            onBehalfTaxInfo = previousState.defaultTaxPayerInfo,
                            onBehalfTaxManualEntries = listOf(),
                        ) to null
                    } else {
                        previousState.copy(
                            modeDelegate = false,
                            businessTaxInfo = previousState.defaultTaxPayerInfo,
                        ) to null
                    }
                } else {
                    previousState to null
                }
            }

            is TaxPaymentViewEvent.UpdateBusinessTaxInfo -> {
                val updatedBusinessTaxInfo =
                    previousState.businessTaxInfo.copy(event.field, event.value)
                previousState.copy(
                    businessTaxInfo = updatedBusinessTaxInfo
                ) to null
            }

            is TaxPaymentViewEvent.UpdateOnBehalfTaxInfo -> {
                val updatedOnBehalfTaxInfo =
                    previousState.onBehalfTaxInfo.copy(event.field, event.value)
                previousState.copy(
                    onBehalfTaxInfo = updatedOnBehalfTaxInfo
                ) to null
            }

            is TaxPaymentViewEvent.SelectTaxEntry -> {
                if (!event.taxEntry.isChecked && countChecked(previousState) == 5) {
                    return previousState to null
                }
                if (previousState.modeDelegate) {
                    val updatedOnBehalfEntries =
                        previousState.onBehalfTaxManualEntries.changeItemCheck(event.taxEntry.data)
                    previousState.copy(
                        onBehalfTaxManualEntries = updatedOnBehalfEntries
                    ) to null
                } else {
                    val searchResultOnFetchedList =
                        previousState.fetchedTaxId.find { it.data.uniqueId == event.taxEntry.data.uniqueId }
                    if (searchResultOnFetchedList != null) {
                        previousState.copy(
                            fetchedTaxId = previousState.fetchedTaxId.changeItemCheck(event.taxEntry.data)
                        ) to null
                    } else {
                        val updatedBusinessEntries =
                            previousState.businessTaxManualEntries.changeItemCheck(event.taxEntry.data)
                        previousState.copy(
                            businessTaxManualEntries = updatedBusinessEntries
                        ) to null
                    }
                }
            }

            is TaxPaymentViewEvent.AddInquiryItems -> {
                val result = mutableListOf<ModelCheckAble<BaseTransaction>>()
                for (item in event.inquiryItems) {
                    if (previousState.businessTaxManualEntries.any {
                            isPaymentDuplicated(it.data, item)
                        } || previousState.fetchedTaxId.any {
                            isPaymentDuplicated(it.data, item)
                        }
                    ) {
                        continue
                    }
                    result += ModelCheckAble(item)
                }
                Timber.d("Added ${result.size} record(s)")

                val updatedBusinessEntries = previousState.businessTaxManualEntries + result
                previousState.copy(
                    businessTaxManualEntries = updatedBusinessEntries
                ) to null
            }

            is TaxPaymentViewEvent.AddManualItems -> {
                val result = mutableListOf<ModelCheckAble<BaseTransaction>>()
                if (!previousState.modeDelegate) {
                    for (item in event.manualItems) {
                        if (previousState.businessTaxManualEntries.any {
                                isPaymentDuplicated(it.data, item)
                            } || previousState.fetchedTaxId.any {
                                isPaymentDuplicated(it.data, item)
                            }
                        ) {
                            continue
                        }
                        result += ModelCheckAble(item)
                    }
                    val sideEffectResult = if (result.size == event.manualItems.size) {
                        TaxPaymentSideEffect.ToastAddInquiryItemsSuccessfully
                    } else {
                        TaxPaymentSideEffect.ToastAddInquiryItemsDuplicated
                    }

                    val updatedBusinessEntries = previousState.businessTaxManualEntries + result
                    previousState.copy(
                        businessTaxManualEntries = updatedBusinessEntries
                    ) to sideEffectResult
                } else {
                    for (item in event.manualItems) {
                        if (previousState.onBehalfTaxManualEntries.any {
                                isPaymentDuplicated(it.data, item)
                            }
                        ) {
                            continue
                        }
                        result += ModelCheckAble(item)
                    }
                    val sideEffectResult = if (result.size == event.manualItems.size) {
                        TaxPaymentSideEffect.ToastAddInquiryItemsSuccessfully
                    } else {
                        TaxPaymentSideEffect.ToastAddInquiryItemsDuplicated
                    }

                    val updatedOnBehalfEntries = previousState.onBehalfTaxManualEntries + result
                    previousState.copy(
                        onBehalfTaxManualEntries = updatedOnBehalfEntries
                    ) to sideEffectResult
                }
            }

            is TaxPaymentViewEvent.InitScreenFailed -> {
                previousState.copy(
                    initializeState = InitializeState.COMPLETED,
                ) to TaxPaymentSideEffect.InitScreenFailed(
                    event.errorCode,
                    event.errorMessage,
                    event.failedStep,
                )
            }

            is TaxPaymentViewEvent.GetTaxPayerInfoSuccess -> {
                val apiTaxPayerInfo = TaxPayerInfo(
                    event.taxPayerInfo.taxCode,
                    event.taxPayerInfo.payerName,
                    event.taxPayerInfo.payerAddress
                )

                previousState.copy(
                    defaultTaxPayerInfo = apiTaxPayerInfo
                ) to TaxPaymentSideEffect.InquiryTransaction(event.taxPayerInfo.taxCode)
            }

            is TaxPaymentViewEvent.InquiryTransactionSuccessful -> {
                previousState.copy(
                    initializeState = InitializeState.COMPLETED,
                    fetchedTaxId = event.transactions.map {
                        ModelCheckAble(it)
                    },
                    businessTaxInfo = previousState.defaultTaxPayerInfo,
                ) to null
            }

            is TaxPaymentViewEvent.RetryInit -> {
                previousState to TaxPaymentSideEffect.RetryInit(event.initStep)
            }
        }
    }

    private fun isPaymentDuplicated(payment1: BaseTransaction, payment2: BaseTransaction): Boolean {
        fun compareNullableField(field1: String?, field2: String?): Boolean {
            if (field1 == null || field2 == null) return true
            return field1 == field2
        }
        return payment1.declarationNo == payment2.declarationNo &&
                payment1.declarationDate == payment2.declarationDate &&
                payment1.ecCode == payment2.ecCode &&
                payment1.chapterCode == payment2.chapterCode &&
                payment1.amount == payment2.amount &&
                payment1.ccy == payment2.ccy &&
                payment1.taxTypeCode == payment2.taxTypeCode &&
                payment1.eiTypeCode == payment2.eiTypeCode &&
                payment1.ccCode == payment2.ccCode &&
                payment1.transDesc == payment2.transDesc &&
                compareNullableField(payment1.treasuryCode, payment2.treasuryCode) &&
                compareNullableField(payment1.revAccCode, payment2.revAccCode) &&
                compareNullableField(payment1.revAuthCode, payment2.revAuthCode) &&
                compareNullableField(payment1.admAreaCode, payment2.admAreaCode)
    }

    private fun countChecked(viewState: TaxPaymentViewState): Int {
        return if (viewState.modeDelegate) {
            viewState.onBehalfTaxManualEntries.count { it.isChecked }
        } else {
            (viewState.businessTaxManualEntries + viewState.fetchedTaxId).count { it.isChecked }
        }
    }

    private fun List<ModelCheckAble<BaseTransaction>>.changeItemCheck(item: BaseTransaction): List<ModelCheckAble<BaseTransaction>> {
        return this.map {
            if (it.data.uniqueId == item.uniqueId) {
                it.copy(isChecked = !it.isChecked)
            } else {
                it
            }
        }
    }

    private fun TaxPayerInfo.copy(field: TaxPayerInfoField, value: String): TaxPayerInfo {
        return when (field) {
            TaxPayerInfoField.TAX_ID -> copy(taxId = value)
            TaxPayerInfoField.NAME -> copy(name = value)
            TaxPayerInfoField.ADDRESS -> copy(address = value)
        }
    }

}