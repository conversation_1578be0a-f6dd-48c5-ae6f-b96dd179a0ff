package vn.com.bidv.feature.government.service.model

import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants
import vn.com.bidv.sdkbase.utils.exts.formatDateToString
import java.util.Date
import java.util.UUID

data class AddPaymentDTO(
    override val declarationNo: String,
    val date: Date,
    val chapterCodeSelected: String,
    val economicCodeSelected: String,
    val currencyTypeSelected: String,
    val taxTypeSelected: String,
    val customsCurrencySelected: String,
    val tradeTypeSelected: String,
    override val amount: String,
    val transactionDescription: String,
    val ecName: String?,
    val eiTypeName: String,
): BaseTransaction() {
    override val declarationDate: String
        get() = date.formatDateToString(SdkBaseConstants.DateTimeConstants.FORMAT_YYYY_MM_DD)
    override val ccy = "VND"
    override val chapterCode: String
        get() = chapterCodeSelected
    override val ecCode: String
        get() = economicCodeSelected
    override val transDesc: String
        get() = transactionDescription
    override val taxTypeCode: String
        get() = taxTypeSelected
    override val ccCode: String
        get() = customsCurrencySelected
    override val eiTypeCode: String
        get() = tradeTypeSelected

    override fun getHeaderString() = declarationNo

    override fun getValueTitle() = "$eiTypeCode - $eiTypeName"

    override fun getValueTaxDescription() = ecName ?: "--"

    override fun getValueAmount() = amount

    override fun getValueCcy() = ccy

    override fun getValueDate(): Pair<String, String> {
        return SdkBaseConstants.DateTimeConstants.FORMAT_YYYY_MM_DD to declarationDate
    }

    override val uniqueId = UUID.randomUUID().toString()
    override val treasuryCode: String? = null
    override val revAccCode: String? = null
    override val revAuthCode: String? = null
    override val admAreaCode: String? = null
}
