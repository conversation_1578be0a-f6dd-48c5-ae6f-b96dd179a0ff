package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.paymentdetail

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.government.service.domain.model.InquiryCustomsDutyDMO

class PaymentDetailReducer :
    Reducer<PaymentDetailReducer.PaymentDetailViewState, PaymentDetailReducer.PaymentDetailViewEvent, PaymentDetailReducer.PaymentDetailViewEffect> {

    @Immutable
    data class PaymentDetailViewState(
        val inquiryData: InquiryCustomsDutyDMO? = null,
        val isInitialized: Boolean = false
    ) : ViewState

    @Immutable
    sealed class PaymentDetailViewEvent : ViewEvent {
        data class InitializeData(val inquiryData: InquiryCustomsDutyDMO) : PaymentDetailViewEvent()
    }

    @Immutable
    sealed class PaymentDetailViewEffect : SideEffect

    override fun reduce(
        previousState: PaymentDetailViewState,
        event: PaymentDetailViewEvent,
    ): Pair<PaymentDetailViewState, PaymentDetailViewEffect?> {
        return when (event) {
            is PaymentDetailViewEvent.InitializeData -> {
                previousState.copy(
                    inquiryData = event.inquiryData,
                    isInitialized = true
                ) to null
            }
        }
    }
} 