package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.detail.pendingtransaction

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.government.service.model.TransactionDetailDMO

class PendingTransactionDetailReducer :
    Reducer<PendingTransactionDetailReducer.PendingTransactionDetailViewState,
            PendingTransactionDetailReducer.PendingTransactionDetailViewEvent,
            PendingTransactionDetailReducer.PendingTransactionDetailViewEffect> {

    @Immutable
    data class PendingTransactionDetailViewState(
        val transactionId: String = "",
        val transactionData: TransactionDetailDMO? = null
    ) : ViewState

    @Immutable
    sealed class PendingTransactionDetailViewEvent : ViewEvent {
        data class LoadTransactionDetail(val transactionId: String) : PendingTransactionDetailViewEvent()
        data class LoadTransactionDetailSuccess(val data: TransactionDetailDMO?) : PendingTransactionDetailViewEvent()
        data class LoadTransactionDetailError(val errorMessage: String) : PendingTransactionDetailViewEvent()
        data object PushTransaction : PendingTransactionDetailViewEvent()
        data object PrintTransaction : PendingTransactionDetailViewEvent()
        data object EditTransaction : PendingTransactionDetailViewEvent()
        data object ViewTransactionHistory : PendingTransactionDetailViewEvent()
    }

    @Immutable
    sealed class PendingTransactionDetailViewEffect : SideEffect {
        data class FetchTransactionDetail(val transactionId: String) : PendingTransactionDetailViewEffect()
        data class PushTransaction(val transactionId: String, val approve: Boolean) : PendingTransactionDetailViewEffect()
        data class PrintTransaction(val transactionId: String) : PendingTransactionDetailViewEffect()
        data class EditTransaction(val transactionId: String) : PendingTransactionDetailViewEffect(), UIEffect
        data class ViewTransactionHistory(val transactionId: String) : PendingTransactionDetailViewEffect(), UIEffect
        data class ShowSuccessMessage(val message: String) : PendingTransactionDetailViewEffect(), UIEffect
        data class ShowErrorMessage(val message: String) : PendingTransactionDetailViewEffect(), UIEffect
        data object NavigateBack : PendingTransactionDetailViewEffect(), UIEffect
    }

    override fun reduce(
        previousState: PendingTransactionDetailViewState,
        event: PendingTransactionDetailViewEvent
    ): Pair<PendingTransactionDetailViewState, PendingTransactionDetailViewEffect?> {
        return when (event) {
            is PendingTransactionDetailViewEvent.LoadTransactionDetail -> {
                previousState.copy(
                    transactionId = event.transactionId
                ) to PendingTransactionDetailViewEffect.FetchTransactionDetail(event.transactionId)
            }
            
            is PendingTransactionDetailViewEvent.LoadTransactionDetailSuccess -> {
                previousState.copy(
                    transactionData = event.data
                ) to null
            }

            is PendingTransactionDetailViewEvent.LoadTransactionDetailError -> TODO()
            
            is PendingTransactionDetailViewEvent.PushTransaction -> {
                previousState to PendingTransactionDetailViewEffect.PushTransaction(
                    previousState.transactionId,
                    true
                )
            }
            
            is PendingTransactionDetailViewEvent.PrintTransaction -> {
                previousState to PendingTransactionDetailViewEffect.PrintTransaction(previousState.transactionId)
            }
            
            is PendingTransactionDetailViewEvent.EditTransaction -> {
                previousState to PendingTransactionDetailViewEffect.EditTransaction(previousState.transactionId)
            }
            
            is PendingTransactionDetailViewEvent.ViewTransactionHistory -> {
                previousState to PendingTransactionDetailViewEffect.ViewTransactionHistory(
                    previousState.transactionId
                )
            }
        }
    }
}
