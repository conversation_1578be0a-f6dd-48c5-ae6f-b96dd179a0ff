package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.main.tabcontent.pendingtransaction

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.designsystem.ui.listwithloadmorev2.ListAutoLoadMoreReducer
import vn.com.bidv.designsystem.ui.listwithloadmorev2.ListAutoLoadMoreViewModel
import vn.com.bidv.feature.government.service.constants.Constants
import vn.com.bidv.feature.government.service.domain.usecase.GovernmentServiceUseCase
import vn.com.bidv.feature.government.service.domain.model.TxnPendingListDMO
import vn.com.bidv.feature.government.service.model.ListCustomsDutiesRuleFilter
import vn.com.bidv.sdkbase.domain.DomainResult
import javax.inject.Inject

@HiltViewModel
class LoadListPendingCustomsDutiesViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    reducer: ListAutoLoadMoreReducer<TxnPendingListDMO, ListCustomsDutiesRuleFilter>,
    private val governmentServiceUseCase: GovernmentServiceUseCase

) : ListAutoLoadMoreViewModel<TxnPendingListDMO, ListCustomsDutiesRuleFilter>(
    reducer = reducer,
    itemPerPage = Constants.ITEM_PER_PAGE
) {
    override fun fetchData(
        pageIndex: Int,
        pageSize: Int,
        rule: ListCustomsDutiesRuleFilter?,
        onLoadSuccess: (data: List<TxnPendingListDMO>, total: Int?) -> Unit,
        onLoadFail: (String?) -> Unit
    ): Job {
        return viewModelScope.launch(dispatcher) {
            when (val result = governmentServiceUseCase.getListPendingTransaction(
                rule = rule,
                pageIndex = pageIndex,
                pageSize = pageSize
            )) {
                is DomainResult.Success -> {
                    result.data?.let {
                        onLoadSuccess(
                            it.items ?: listOf(),
                            it.total?.toInt()
                        )
                    }
                }

                is DomainResult.Error -> {
                    onLoadFail(result.errorMessage)
                }
            }
        }
    }
}