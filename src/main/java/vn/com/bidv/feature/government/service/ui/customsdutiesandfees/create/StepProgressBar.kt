package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme

@Composable
fun StepProgressBar(
    uiState: CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewState
) {
    val color = LocalColorScheme.current

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(IBSpacing.spacing2xs)
            .background(Color.Transparent)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(IBSpacing.spacing2xs),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            repeat(3) { index ->
                val step = index + 1

                Box(
                    modifier = Modifier
                        .weight(1f)
                        .height(IBSpacing.spacing2xs)
                        .background(
                            if (uiState.currentStep >= step)
                                color.contentBrand_01Primary
                            else
                                color.bgNon_opaqueHovered
                        )
                )

                // Chỉ thêm Spacer nếu chưa phải phần tử cuối
                if (step < 3) {
                    Spacer(modifier = Modifier.width(IBSpacing.spacing2xs))
                }
            }
        }
    }
}
