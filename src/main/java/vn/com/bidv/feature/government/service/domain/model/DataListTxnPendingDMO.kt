package vn.com.bidv.feature.government.service.domain.model

import com.google.gson.annotations.SerializedName

data class DataListTxnPendingDMO(
    /* List data */
    @SerializedName("items")
    val items: List<TxnPendingListDMO>? = null,

    /* Total size */
    @SerializedName("total")
    val total: Long? = null
)

data class TxnPendingListDMO(

    @SerializedName("txnId")
    val txnId: String? = null,

    @SerializedName("debitAccNo")
    val debitAccNo: String? = null,

    @SerializedName("taxCode")
    val taxCode: String? = null,

    @SerializedName("declarationNo")
    val declarationNo: String? = null,

    @SerializedName("amount")
    val amount: String? = null,

    @SerializedName("ccy")
    val ccy: String? = null,

    @SerializedName("batchNo")
    val batchNo: String? = null,

    @SerializedName("status")
    val status: String? = null,

    @SerializedName("createdDate")
    val createdDate: String? = null,

    /* Mã kho bạc */
    @SerializedName("treasuryCode")
    val treasuryCode: String? = null,

    /* Tên kho bạc */
    @SerializedName("treasuryName")
    val treasuryName: String? = null,

    /* Mã địa bàn hành chính */
    @SerializedName("admAreaCode")
    val admAreaCode: String? = null,

    /* Tên địa bàn hành chính */
    @SerializedName("admAreaName")
    val admAreaName: String? = null,

    /* Mã tài khoản thu */
    @SerializedName("revAccCode")
    val revAccCode: String? = null,

    /* Tên tài khoản thu */
    @SerializedName("revAccName")
    val revAccName: String? = null,

    /* Mã cơ quan thu */
    @SerializedName("revAuthCode")
    val revAuthCode: String? = null,

    /* Tên cơ quan thu */
    @SerializedName("revAuthName")
    val revAuthName: String? = null,

    /* Ngân hàng thụ hưởng */
    @SerializedName("bbCode")
    val bbCode: String? = null,

    @SerializedName("statusName")
    val statusName: String? = null

)
