package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class CustomsFeeInfoMainViewModel @Inject constructor(): ViewModelIBankBase<CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewState, CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent, CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEffect>(
    initialState = CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewState(currentStep = 1),
    reducer = CustomsFeeInfoMainReducer()
)