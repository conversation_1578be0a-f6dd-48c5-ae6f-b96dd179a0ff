package vn.com.bidv.feature.government.service.domain.usecase

import vn.com.bidv.feature.common.data.MasterDataRepository
import vn.com.bidv.feature.common.data.masterdata.model.InqCustomerCriteriaDto
import vn.com.bidv.feature.government.service.domain.model.TaxPayerInfoDMO
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class GetCustomerInfoUseCase @Inject constructor(
    private val masterDataRepository: MasterDataRepository
) {
    suspend fun getCustomerInfo(cifNo: String): DomainResult<TaxPayerInfoDMO> {
        val result = masterDataRepository.getCustomerInfo(
            InqCustomerCriteriaDto(
                cifNo = cifNo
            )
        )
        return result.convert {
            TaxPayerInfoDMO(
                taxCode = taxCode ?: "",
                payerName = name ?: "",
                payerAddress = address ?: "",
            )
        }
    }
}