package vn.com.bidv.feature.government.service.domain.usecase

import vn.com.bidv.feature.government.service.data.GovernmentServiceRepository
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnDeleteReq
import vn.com.bidv.feature.government.service.model.TxnDeleteResultDMO
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class DeleteTransactionsUseCase @Inject constructor(
    private val governmentServiceRepository: GovernmentServiceRepository
) {
    suspend operator fun invoke(txnIds: List<String>): DomainResult<TxnDeleteResultDMO> {
        val result = governmentServiceRepository.deleteTransaction(
            TxnDeleteReq(txnIds = txnIds)
        )
        return result.convert {
            TxnDeleteResultDMO(code, message)
        }
    }
}