package vn.com.bidv.feature.government.service.navigation

import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.compose.dialog
import androidx.navigation.navArgument
import androidx.navigation.navigation
import androidx.navigation.toRoute
import kotlinx.serialization.Serializable
import vn.com.bidv.feature.government.service.domain.model.InquiryCustomsDutyDMO
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.CustomsFeeInfoMainScreen
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.inquirypayment.InquiryTransactionScreen
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.paymentdetail.PaymentDetailScreen
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.main.ListCustomsDutiesMainScreen
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.detail.pendingtransaction.PendingTransactionDetailScreen
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.AddPaymentScreen
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.delete.DeleteTransactionFlowPopup
import vn.com.bidv.feature.government.service.model.DeleteTransactionFlowPopupArguments
import vn.com.bidv.sdkbase.navigation.FeatureGraphBuilder
import vn.com.bidv.sdkbase.navigation.IBankMainRouting
import vn.com.bidv.sdkbase.utils.CustomSafeArgs
import javax.inject.Inject
import kotlin.reflect.typeOf

sealed class GovernmentServiceRoute(val route: String) {
    data object GovernmentServiceMainRoute : GovernmentServiceRoute(
        route = "government_service_main_route",
    )

    data object PendingTransactionDetailRoute : GovernmentServiceRoute(
        route = "pending_transaction_detail_route/{transactionId}",
    ) {
        fun createRoute(transactionId: String): String {
            return "pending_transaction_detail_route/$transactionId"
        }
    }

    data object PendingTransactionEditRoute : GovernmentServiceRoute(
        route = "edit_pending_transaction/{transactionId}"
    ) {
        fun createRoute(transactionId: String): String {
            return "edit_pending_transaction/$transactionId"
        }
    }

    @Serializable
    data class InquiryTransactionScreen(
        val taxNumber: String,
    )

    data object CustomsFeeInfoMainRoute: GovernmentServiceRoute(
        route = "customs_fee_info_main_route"
    )

    data object AddPaymentRouter: GovernmentServiceRoute(
        route = "add_payment_router"
    )

    @Serializable
    data class TaxPaymentDetailRoute(
        val args: InquiryCustomsDutyDMO,
    )


    @Serializable
    data class DeleteTransactionFlowPopupRoute(
        val args: DeleteTransactionFlowPopupArguments,
    )
}

class GovernmentServiceNavigation @Inject constructor() : FeatureGraphBuilder {
    override fun buildGraph(
        navGraphBuilder: NavGraphBuilder,
        navController: NavHostController,
        registeredRoutes: (args: List<String>) -> Unit
    ) {
        navGraphBuilder.navigation(
            startDestination = GovernmentServiceRoute.GovernmentServiceMainRoute.route,
            route = IBankMainRouting.GovernmentServiceRoute.GovernmentServiceMainRoute.route
        ) {
            composable(
                route = GovernmentServiceRoute.GovernmentServiceMainRoute.route
            ) {
                ListCustomsDutiesMainScreen(navController)
            }

            composable(
                route = GovernmentServiceRoute.PendingTransactionDetailRoute.route,
                arguments = listOf(
                    navArgument("transactionId") { type = NavType.StringType }
                )
            ) { backStackEntry ->
                val transactionId = backStackEntry.arguments?.getString("transactionId") ?: ""
                PendingTransactionDetailScreen(
                    transactionId = transactionId,
                    navController = navController
                )
            }

            composable<GovernmentServiceRoute.InquiryTransactionScreen> {
                val route = it.toRoute<GovernmentServiceRoute.InquiryTransactionScreen>()
                InquiryTransactionScreen(
                    navHostController = navController,
                    taxNumber = route.taxNumber,
                )
            }

            composable(
                route = GovernmentServiceRoute.CustomsFeeInfoMainRoute.route
            ) {
                CustomsFeeInfoMainScreen(navController)
            }

            composable(
                route = GovernmentServiceRoute.AddPaymentRouter.route
            ) {
                AddPaymentScreen(navController)
            }

            composable<GovernmentServiceRoute.TaxPaymentDetailRoute>(
                typeMap = mapOf(typeOf<InquiryCustomsDutyDMO>() to CustomSafeArgs.SerializableNavType(
                    InquiryCustomsDutyDMO.serializer()
                ))
            ) {
                    backStackEntry ->
                val args = backStackEntry.toRoute<GovernmentServiceRoute.TaxPaymentDetailRoute>()
                PaymentDetailScreen(
                    inquiryData = args.args,
                    navController = navController
                )
            }


            dialog<GovernmentServiceRoute.DeleteTransactionFlowPopupRoute>(
                typeMap = mapOf(
                    typeOf<DeleteTransactionFlowPopupArguments>() to CustomSafeArgs.SerializableNavType(
                        DeleteTransactionFlowPopupArguments.serializer()
                    )
                )
            ) {
                val route = it.toRoute<GovernmentServiceRoute.DeleteTransactionFlowPopupRoute>().args
                DeleteTransactionFlowPopup(
                    navController,
                    route.txnIds,
                )
            }
        }

        registeredRoutes(
            listOf(
                IBankMainRouting.GovernmentServiceRoute.GovernmentServiceMainRoute.route
            )
        )
    }
}
