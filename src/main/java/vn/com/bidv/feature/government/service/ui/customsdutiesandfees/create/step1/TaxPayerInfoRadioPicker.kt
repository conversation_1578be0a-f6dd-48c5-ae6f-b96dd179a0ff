package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step1

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import vn.com.bidv.feature.government.service.model.SubmissionType
import vn.com.bidv.feature.government.service.ui.common.CustomRadioOption
import vn.com.bidv.localization.R

@Composable
fun TaxPayerInfoRadioPicker(
    modeDelegate: Boolean,
    modifier: Modifier = Modifier,
    onCheckedChange: (SubmissionType) -> Unit
) {
    val selectedOption = if (modeDelegate) SubmissionType.ON_BEHALF else SubmissionType.FOR_ENTERPRISE

    Row(
        modifier = modifier
            .selectableGroup(),
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        CustomRadioOption(
            text = stringResource(R.string.nop_cho_dn),
            selected = selectedOption == SubmissionType.FOR_ENTERPRISE,
            iconResId = vn.com.bidv.designsystem.R.drawable.nop_thue_tron_outline,
            modifier = Modifier.weight(1f),
            onClick = {
                onCheckedChange(SubmissionType.FOR_ENTERPRISE)
            }
        )

        CustomRadioOption(
            text = stringResource(R.string.nop_thay),
            selected = selectedOption == SubmissionType.ON_BEHALF,
            iconResId = vn.com.bidv.designsystem.R.drawable.nop_thay_thue_outline,
            modifier = Modifier.weight(1f),
            onClick = {
                onCheckedChange(SubmissionType.ON_BEHALF)
            }
        )
    }
}