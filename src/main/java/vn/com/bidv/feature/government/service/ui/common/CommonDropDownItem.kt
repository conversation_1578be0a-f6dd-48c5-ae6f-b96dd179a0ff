package vn.com.bidv.feature.government.service.ui.common

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import vn.com.bidv.designsystem.component.IBankContextMenu
import vn.com.bidv.designsystem.component.IBankEmptyState
import vn.com.bidv.designsystem.component.dataentry.IBFrameState
import vn.com.bidv.designsystem.component.dataentry.IBankInputDropdownBaseV2
import vn.com.bidv.designsystem.component.dataentry.IBankInputDropdownTypeData
import vn.com.bidv.designsystem.component.feedback.bottomsheet.IBankSearchDialog
import vn.com.bidv.designsystem.component.feedback.bottomsheet.SearchDialogState
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.localization.R
import vn.com.bidv.feature.government.service.model.FieldStatus

@Composable
fun <T> CommonDropDown(
    labelText: String,
    required: Boolean = true,
    selectedItem: T?,
    displayTextSelector: (T?) -> String,
    fieldError: FieldStatus?,
    onClickEnd: () -> Unit,
    onClickClear: () -> Unit
) {
    val messageError = if (fieldError == FieldStatus.INVALID) {
        stringResource(R.string.truong_s_bat_buoc_nhap, labelText)
    } else ""

    val state = if (fieldError == FieldStatus.INVALID) {
        IBFrameState.ERROR(LocalColorScheme.current)
    } else {
        IBFrameState.DEFAULT(LocalColorScheme.current)
    }

    IBankInputDropdownBaseV2(
        labelText = labelText,
        required = required,
        typeData = IBankInputDropdownTypeData.Select(text = displayTextSelector(selectedItem)),
        iconEnd = vn.com.bidv.designsystem.R.drawable.arrow_bottom_outline,
        hintTextStart = messageError,
        state = state,
        onClickEnd = onClickEnd,
        onClickClear = onClickClear
    )
}

@Composable
fun <T> CommonSearchDialog(
    title: String,
    itemSelected: T?,
    listData: List<T>?,
    showSearchBox: Boolean = true,
    compareKey: (T, T?) -> Boolean = { a, b -> a == b },
    searchFilter: (T, String) -> Boolean,
    onDismiss: (T?) -> Unit,
    itemTitleSelector: (T) -> String,
    contentState: SearchDialogState = SearchDialogState.CONTENT,
) {
    if (listData != null) {
        IBankSearchDialog(
            title = title,
            itemSelected = itemSelected,
            compareKey = { it },
            showSearchBox = showSearchBox,
            listData = listData,
            searchFilter = searchFilter,
            state = contentState,
            onRequestDismiss = { onDismiss(it ?: itemSelected) },
            listSearchFilterText = listOf(),
            errorView = {
                IBankEmptyState(
                    modifier = Modifier.fillMaxSize(),
                    supportingText = stringResource(R.string.khong_tai_duoc_du_lieu),
                    textButton = stringResource(R.string.thu_lai)
                )
            }
        ) { _, searchItem ->
            val isSelected = compareKey(searchItem.data, itemSelected)
            IBankContextMenu(
                isSelected = isSelected,
                title = itemTitleSelector(searchItem.data)
            )
        }
    }
}

