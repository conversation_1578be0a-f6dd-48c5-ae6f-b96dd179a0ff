package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.inquirypayment

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.dataentry.IBFrameState
import vn.com.bidv.designsystem.component.dataentry.IBankInputFieldBase
import vn.com.bidv.designsystem.component.datepicker.IBankInputDatePicker
import vn.com.bidv.designsystem.component.datepicker.model.DatePickerConfig
import vn.com.bidv.designsystem.component.navigation.button.IBankNormalButton
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonType
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.IBankTheme
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.designsystem.ui.listwithloadmorev2.ListAutoLoadMoreReducer
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.ItemCardCommon
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.ListTransactionBaseReducer
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.ListTransactionBaseScreen
import vn.com.bidv.feature.government.service.ui.common.IBankYearPickerDialog
import vn.com.bidv.feature.government.service.constants.Constants
import vn.com.bidv.feature.government.service.ui.common.ItemCardBody
import vn.com.bidv.feature.government.service.ui.common.ItemCardHeader
import vn.com.bidv.localization.R
import java.util.Calendar

/**
 * vấn tin khoản nộp
 */
@Composable
fun InquiryTransactionScreen(
    taxNumber: String,
    navHostController: NavHostController
) {
    val viewModel: InquiryTransactionViewModel = hiltViewModel()
    val loadListViewModel: LoadListInquiryTransactionViewModel = hiltViewModel()
    val lazyListState: LazyListState = rememberLazyListState()
    val focusManager = LocalFocusManager.current
    val keyboardController = LocalSoftwareKeyboardController.current

    BaseScreen(
        navController = navHostController,
        viewModel = viewModel,
        topAppBarConfig = TopAppBarConfig(
            showHomeIcon = true,
            titleTopAppBar = stringResource(R.string.thue_phi_hai_quan)
        ),
        handleSideEffect = { },
    ) { viewState, onEvent ->

        LaunchedEffect(true) {
            onEvent(
                ListTransactionBaseReducer.ListTransactionBaseViewEvent.UpdateRuleFilter(
                    InquiryTransactionRuleFilter(
                        taxNumber = taxNumber
                    )
                )
            )
        }
        var isDeclarationNumberFocused by remember { mutableStateOf(false) }
        val selectedItemsCount = viewState.listDataState.count { it.isChecked }

        var shouldShowResult by remember { mutableStateOf(false) }

        Box(modifier = Modifier.imePadding().fillMaxSize()) {
            Column {
                InquiryFormContent(
                    ruleFilter = viewState.ruleFilters ?: InquiryTransactionRuleFilter(),
                    onUpdateFilter = { formField, newFilter ->
                        onEvent(
                            ListTransactionBaseReducer.ListTransactionBaseViewEvent.UpdateRuleFilter(
                                newFilter
                            )
                        )
                        if (formField == InquiryFormField.DeclarationYear) {
                            loadListViewModel.updateFilter(newFilter)
                        }
                    },
                    onDeclarationNumberFocusChanged = { isFocused ->
                        isDeclarationNumberFocused = isFocused
                    }
                )

                if (shouldShowResult) {
                    ListTransactionBaseScreen(
                        viewModel = viewModel,
                        navController = navHostController,
                        lazyListState = lazyListState,
                        loadListViewModel = loadListViewModel,
                        shouldShowSearchArea = false,
                        shouldShowCreateButton = false,
                        ruleFilterDefault = InquiryTransactionRuleFilter(),
                        reloadKey = Constants.INQUIRY_TRANSACTION_RELOAD_KEY,
                        listAction = emptyList(),
                        handleAction = { _, _ -> },
                        textEmptySearch = stringResource(R.string.khong_tim_thay_thong_tin_khoan_nop_cua_ban)
                    ) { item, onEventListView ->
                        ItemCardCommon(
                            transactionsDMO = item,
                            uiState = viewState,
                            onEventListView = onEventListView,
                            handleAction = { },
                            contentHeader = { model ->
                                ItemCardHeader(model.data)
                            },
                            contentBody = { model ->
                                ItemCardBody(model.data)
                            },
                            onEvent = {},
                            navController = navHostController,
                        )
                    }
                }
            }

            BottomButtonArea(
                showInquiryButton = isDeclarationNumberFocused,
                showSubmitButton = !isDeclarationNumberFocused && selectedItemsCount > 0,
                isInquiryEnabled = !viewState.ruleFilters?.taxDeclarationNo.isNullOrEmpty(),
                onInquiry = {
                    focusManager.clearFocus()
                    keyboardController?.hide()
                    shouldShowResult = true
                    loadListViewModel.updateFilter(viewState.ruleFilters!!)
                },
                onSubmitSelection = {
                    viewModel.returnResult {
                        navHostController.popBackStack()
                    }
                },
                modifier = Modifier.align(Alignment.BottomCenter)
            )
        }
    }
}

fun LoadListInquiryTransactionViewModel.updateFilter(filter: InquiryTransactionRuleFilter) {
    sendEvent(
        ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent.UpdateRuleFilters(
            filter
        )
    )
}

enum class InquiryFormField {
    DeclarationNumber,
    DeclarationYear,
}

@Composable
private fun InquiryFormContent(
    ruleFilter: InquiryTransactionRuleFilter,
    onUpdateFilter: (InquiryFormField, InquiryTransactionRuleFilter) -> Unit,
    onDeclarationNumberFocusChanged: (Boolean) -> Unit
) {
    val colorScheme = LocalColorScheme.current
    var showDatePickerDialog by remember { mutableStateOf(false) }

    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp),
        modifier = Modifier
            .fillMaxWidth()
            .padding(IBSpacing.spacingM)
    ) {
        IBankInputFieldBase(
            required = true,
            text = ruleFilter.taxDeclarationNo,
            placeholderText = stringResource(R.string.so_to_khai_hai_quan),
            onFocusChange = {
                onDeclarationNumberFocusChanged(it)
            },
            onClickClear = {
                onUpdateFilter(InquiryFormField.DeclarationNumber, ruleFilter.copy(taxDeclarationNo = ""))
            }
        ) {
            onUpdateFilter(InquiryFormField.DeclarationNumber, ruleFilter.copy(taxDeclarationNo = it.text))
        }

        IBankInputDatePicker(
            state = if (ruleFilter.taxDeclarationNo.isEmpty())
                IBFrameState.DISABLE(colorScheme)
            else
                IBFrameState.DEFAULT(colorScheme),
            labelText = stringResource(R.string.nam_dang_ky),
            text = ruleFilter.taxDeclarationYear?.toString() ?: "",
            onClickClear = {
                onUpdateFilter(InquiryFormField.DeclarationYear, ruleFilter.copy(taxDeclarationYear = null))
            },
            onClickEnd = {
                if (ruleFilter.taxDeclarationNo.isNotEmpty()) {
                    showDatePickerDialog = true
                }
            }
        )
    }

    if (showDatePickerDialog) {
        IBankYearPickerDialog(
            modifier = Modifier,
            title = stringResource(R.string.chon_nam_dang_ky),
            config = DatePickerConfig.build {
                minDate = Calendar.getInstance().apply { add(Calendar.YEAR, -5) }.time
                maxDate = Calendar.getInstance().time
            },
            selectedYear = ruleFilter.taxDeclarationYear ?: Calendar.getInstance()[Calendar.YEAR],
            positiveButtonText = stringResource(R.string.van_tin),
            onDismissRequest = {
                showDatePickerDialog = false
            },
            onClickSubmit = { year ->
                onUpdateFilter(InquiryFormField.DeclarationYear, ruleFilter.copy(taxDeclarationYear = year))
            }
        )
    }
}



@Preview
@Composable
private fun PrevInquiryFormContent() {
    IBankTheme {
        Surface {
            InquiryFormContent(
                ruleFilter = InquiryTransactionRuleFilter(
                    taxDeclarationNo = "***********",
                    taxDeclarationYear = 2025
                ),
                onUpdateFilter = { _, _ -> },
                onDeclarationNumberFocusChanged = { }
            )
        }
    }
}

@Composable
private fun BottomButtonArea(
    onInquiry: () -> Unit,
    onSubmitSelection: () -> Unit,
    showInquiryButton: Boolean,
    showSubmitButton: Boolean,
    isInquiryEnabled: Boolean,
    modifier: Modifier = Modifier
) {
    val colorScheme = LocalColorScheme.current

    if (showInquiryButton || showSubmitButton) {
        Row(
            modifier = modifier
                .fillMaxWidth()
                .background(colorScheme.bgMainTertiary)
                .padding(IBSpacing.spacingM)
        ) {
            when {
                showInquiryButton -> {
                    IBankNormalButton(
                        modifier = Modifier.fillMaxWidth(),
                        type = NormalButtonType.PRIMARY(colorScheme),
                        text = stringResource(R.string.van_tin),
                        isEnable = isInquiryEnabled,
                        onClick = onInquiry
                    )
                }
                showSubmitButton -> {
                    IBankNormalButton(
                        modifier = Modifier.fillMaxWidth(),
                        type = NormalButtonType.PRIMARY(colorScheme),
                        text = stringResource(R.string.them_khoan_nop),
                        onClick = onSubmitSelection
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun PrevBottomButtonArea() {
    IBankTheme {
        Column {
            Surface {
                BottomButtonArea(
                    onInquiry = { },
                    onSubmitSelection = { },
                    showInquiryButton = true,
                    showSubmitButton = false,
                    isInquiryEnabled = true,
                )
            }
            Surface {
                BottomButtonArea(
                    onInquiry = { },
                    onSubmitSelection = { },
                    showInquiryButton = false,
                    showSubmitButton = true,
                    isInquiryEnabled = true,
                )
            }
            Surface {
                BottomButtonArea(
                    onInquiry = { },
                    onSubmitSelection = { },
                    showInquiryButton = true,
                    showSubmitButton = false,
                    isInquiryEnabled = false,
                )
            }
        }
    }
}
