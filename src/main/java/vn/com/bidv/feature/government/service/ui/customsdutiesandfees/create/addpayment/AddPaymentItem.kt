package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment

sealed class AddPaymentItem {
    sealed class DropDownItem: AddPaymentItem(){
        data object ChapterCode : DropDownItem()
        data object EconomicCode : DropDownItem()
        data object CurrencyType : DropDownItem()
        data object TaxType : DropDownItem()
        data object CustomsCurrency : DropDownItem()
        data object TradeType : DropDownItem()
    }

    data object Date : AddPaymentItem()

    data object InputMoney : AddPaymentItem()

    data object DeclarationNumber : AddPaymentItem()
    data object TransactionDescription : AddPaymentItem()
}