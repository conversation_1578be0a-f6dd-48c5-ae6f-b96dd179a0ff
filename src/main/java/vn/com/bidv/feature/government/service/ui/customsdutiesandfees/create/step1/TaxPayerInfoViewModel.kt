package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step1

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.designsystem.component.feedback.snackbar.IBankSnackBarInfo
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.feature.government.service.domain.usecase.GovernmentServiceUseCase
import vn.com.bidv.feature.government.service.domain.usecase.GetCustomerInfoUseCase
import vn.com.bidv.feature.government.service.domain.usecase.InquiryTransactionUseCase
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.inquirypayment.InquiryTransactionViewModel
import vn.com.bidv.localization.R
import vn.com.bidv.network.NetworkStatusCode
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class TaxPayerInfoViewModel @Inject constructor(
    private val governmentServiceUseCase: GovernmentServiceUseCase,
    private val inquiryTransactionUseCase: InquiryTransactionUseCase,
    private val getCustomerInfoUseCase: GetCustomerInfoUseCase,
    private val userInfoUseCase: UserInfoUseCase,
) :
    ViewModelIBankBase<
            TaxPayerInfoReducer.TaxPaymentViewState,
            TaxPayerInfoReducer.TaxPaymentViewEvent,
            TaxPayerInfoReducer.TaxPaymentSideEffect
            >(
        initialState = TaxPayerInfoReducer.TaxPaymentViewState(),
        reducer = TaxPayerInfoReducer()
    ) {
    override fun handleEffect(
        sideEffect: TaxPayerInfoReducer.TaxPaymentSideEffect,
        onResult: (TaxPayerInfoReducer.TaxPaymentViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is TaxPayerInfoReducer.TaxPaymentSideEffect.InitScreen -> {
                handleGetCustomerInfo(onResult)
            }
            is TaxPayerInfoReducer.TaxPaymentSideEffect.ToastAddInquiryItemsSuccessfully -> {
                snackBar(resourceProvider.getString(R.string.luu_thanh_cong))
            }
            is TaxPayerInfoReducer.TaxPaymentSideEffect.ToastAddInquiryItemsDuplicated -> {
                snackBar(resourceProvider.getString(R.string.khoan_nop_da_ton_tai))
            }
            is TaxPayerInfoReducer.TaxPaymentSideEffect.InitScreenFailed -> {
                /*noop*/
            }
            is TaxPayerInfoReducer.TaxPaymentSideEffect.InquiryTransaction -> {
                handleInquiryTransaction(sideEffect.taxCode, onResult)
            }
            is TaxPayerInfoReducer.TaxPaymentSideEffect.RetryInit -> {
                when (sideEffect.initStep) {
                    is TaxPayerInfoReducer.InitStep.FetchUserInfo -> {
                        handleGetCustomerInfo(onResult)
                    }

                    is TaxPayerInfoReducer.InitStep.FetchDefaultListInquiry -> {
                        handleInquiryTransaction(sideEffect.initStep.taxCode, onResult)
                    }
                }
            }
        }
    }

    private fun snackBar(text: String) {
        showSnackBar(
            IBankSnackBarInfo(
                message = text,
                primaryButtonText = resourceProvider.getString(R.string.dong)
            )
        )
    }

    private fun handleGetCustomerInfo(onResult: (TaxPayerInfoReducer.TaxPaymentViewEvent) -> Unit) {
        val cifNo = userInfoUseCase.getUserInfoFromStorage().getSafeData()?.user?.cifNo ?: ""

        callDomain(
            onSuccess = { customerInfoResult ->
                val taxPayerInfo = customerInfoResult.data
                if (taxPayerInfo == null) {
                    onResult(
                        TaxPayerInfoReducer.TaxPaymentViewEvent.InitScreenFailed(
                            failedStep = TaxPayerInfoReducer.InitStep.FetchUserInfo,
                            null,
                            resourceProvider.getString(R.string.co_loi_xay_ra_trong_qua_trinh_xu_ly_vui_long_thu_lai)
                        )
                    )
                    return@callDomain
                }

                onResult(
                    TaxPayerInfoReducer.TaxPaymentViewEvent.GetTaxPayerInfoSuccess(
                        taxPayerInfo = taxPayerInfo
                    )
                )
            },
            onFail = { customerInfoError ->
                onResult(
                    TaxPayerInfoReducer.TaxPaymentViewEvent.InitScreenFailed(
                        failedStep = TaxPayerInfoReducer.InitStep.FetchUserInfo,
                        customerInfoError?.errorCode,
                        customerInfoError?.errorMessage
                    )
                )
            }
        ) {
            getCustomerInfoUseCase.getCustomerInfo(cifNo)
        }
    }

    private fun handleInquiryTransaction(
        taxCode: String,
        onResult: (TaxPayerInfoReducer.TaxPaymentViewEvent) -> Unit
    ) {
        callDomain(
            isListenAllError = true,
            onSuccess = { inquiryResult ->
                val transactions = inquiryResult.data?.items ?: emptyList()
                onResult(
                    TaxPayerInfoReducer.TaxPaymentViewEvent.InquiryTransactionSuccessful(
                        transactions = transactions
                    )
                )
            },
            onFail = { inquiryError ->
                when(inquiryError?.errorCode) {
                    NetworkStatusCode.CONNECT_TIME_OUT -> onResult(
                        TaxPayerInfoReducer.TaxPaymentViewEvent.InitScreenFailed(
                            failedStep = TaxPayerInfoReducer.InitStep.FetchDefaultListInquiry(taxCode),
                            inquiryError.errorCode,
                            inquiryError.errorMessage
                        )
                    )

                    else -> {
                        onResult(
                        TaxPayerInfoReducer.TaxPaymentViewEvent.InitScreenFailed(
                            failedStep = TaxPayerInfoReducer.InitStep.FetchDefaultListInquiry(taxCode),
                            inquiryError?.errorCode,
                            inquiryError?.errorMessage
                        )
                    )}
                }
            }
        ) {
            inquiryTransactionUseCase.inquiryTransaction(
                taxId = taxCode,
                declarationNo = null,
                year = null,
            )
        }
    }
}
