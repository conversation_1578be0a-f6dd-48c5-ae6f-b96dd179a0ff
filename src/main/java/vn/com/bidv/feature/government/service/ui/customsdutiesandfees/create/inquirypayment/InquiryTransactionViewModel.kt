package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.inquirypayment

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.ListTransactionBaseReducer
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.ListTransactionBaseViewModel
import vn.com.bidv.feature.government.service.domain.usecase.ReturnInquiryResultUseCase
import vn.com.bidv.feature.government.service.domain.model.InquiryCustomsDutyDMO
import javax.inject.Inject

@HiltViewModel
class InquiryTransactionViewModel @Inject constructor(
    private val returnResultUseCase: ReturnInquiryResultUseCase,
) : ListTransactionBaseViewModel<InquiryCustomsDutyDMO, InquiryTransactionRuleFilter>(
    initialState = ListTransactionBaseReducer.ListTransactionBaseViewState(),
    reducer = ListTransactionBaseReducer()
) {
    fun returnResult(onComplete: () -> Unit) {
        val selectedResult = this.uiState.value.listDataState.filter { it.isChecked }.map { it.data }
        viewModelScope.launch {
            returnResultUseCase.invoke(selectedResult)
            onComplete()
        }
    }
}