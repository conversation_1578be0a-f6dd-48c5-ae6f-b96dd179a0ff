package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.inquirypayment

import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import vn.com.bidv.designsystem.ui.listwithloadmorev2.ListAutoLoadMoreReducer
import vn.com.bidv.feature.government.service.domain.model.InquiryCustomsDutyDMO
import vn.com.bidv.feature.government.service.domain.usecase.InquiryTransactionUseCase
import vn.com.bidv.sdkbase.ui.ViewModelIBankListBase
import javax.inject.Inject

@HiltViewModel
class LoadListInquiryTransactionViewModel @Inject constructor(
    reducer: ListAutoLoadMoreReducer<InquiryCustomsDutyDMO, InquiryTransactionRuleFilter>,
    private val inquiryTransactionUseCase: InquiryTransactionUseCase,
) : ViewModelIBankListBase<InquiryCustomsDutyDMO, InquiryTransactionRuleFilter>(
    reducer = reducer,
    itemPerPage = 20
) {
    override fun fetchData(
        pageIndex: Int,
        pageSize: Int,
        rule: InquiryTransactionRuleFilter?,
        onLoadSuccess: (data: List<InquiryCustomsDutyDMO>, total: Int?) -> Unit,
        onLoadFail: (String?) -> Unit
    ): Job {
        return callDomain(
            onSuccess = { result ->
                result.data?.let {
                    onLoadSuccess(
                        it.items,
                        it.items.size
                    )
                } ?: onLoadSuccess(emptyList(), 0)
            },
            onFail = { result ->
                onLoadFail(
                    result?.errorMessage
                        ?: resourceProvider.getString(vn.com.bidv.localization.R.string.co_loi_xay_ra_khi_ap_dung_ma_uu_dai_quy_khach_vui_long_thu_lai)
                )
            }
        ) {
            inquiryTransactionUseCase.inquiryTransaction(
                taxId = rule?.taxNumber ?: "",
                declarationNo = rule?.taxDeclarationNo,
                year = rule?.taxDeclarationYear
            )
        }
    }
}
