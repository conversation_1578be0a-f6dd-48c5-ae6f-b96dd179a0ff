package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.main

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState

class ListCustomsDutiesMainReducer :
    Reducer<
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewState,
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent,
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEffect
            > {

    @Immutable
    data class CustomsDutiesAndFeesMainViewState(
        val tabLayoutIndex: Int = 0
    ) : ViewState

    @Immutable
    sealed class CustomsDutiesAndFeesMainViewEvent : ViewEvent {
        data class SelectedTab(val indexTab: Int) : CustomsDutiesAndFeesMainViewEvent()
    }

    @Immutable
    sealed class CustomsDutiesAndFeesMainViewEffect : SideEffect

    override fun reduce(
        previousState: CustomsDutiesAndFeesMainViewState,
        event: CustomsDutiesAndFeesMainViewEvent
    ): Pair<CustomsDutiesAndFeesMainViewState, CustomsDutiesAndFeesMainViewEffect?> {
        return when (event) {
            is CustomsDutiesAndFeesMainViewEvent.SelectedTab -> {
                previousState.copy(
                    tabLayoutIndex = event.indexTab
                ) to null
            }
        }
    }
}