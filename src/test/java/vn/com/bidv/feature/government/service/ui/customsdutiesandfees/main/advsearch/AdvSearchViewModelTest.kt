package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.main.advsearch

import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.impl.annotations.MockK
import io.mockk.mockkClass
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNotNull
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import vn.com.bidv.feature.government.service.domain.usecase.GovernmentServiceUseCase
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.main.tabcontent.pendingtransaction.advsearch.AdvSearchReducer
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.main.tabcontent.pendingtransaction.advsearch.AdvSearchViewModel
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.TransactionStatusBase

@ExperimentalCoroutinesApi
class AdvSearchViewModelTest {

    @MockK(relaxed = true)
    private lateinit var governmentServiceUseCase: GovernmentServiceUseCase

    private lateinit var viewModel: AdvSearchViewModel

    private val mockCurrencyList = listOf("VND", "USD", "EUR")
    private val mockStatusList = listOf(
        TransactionStatusBase.INIT,
        TransactionStatusBase.REJECTED,
        TransactionStatusBase.APPROVED
    )

    @Before
    fun setUp() {
        MockKAnnotations.init(this, relaxed = true)
        Dispatchers.setMain(Dispatchers.Unconfined)

        // Create a mock ViewModel that bypasses the dependency injection
        viewModel = mockkClass(AdvSearchViewModel::class, relaxed = true)
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }

    @Test
    fun `test AdvSearchReducer initial state`() {
        val initialState = AdvSearchReducer.AdvSearchState()
        
        assertEquals(false, initialState.invalidAmount)
        assertEquals(null, initialState.listCurrency)
        assertEquals(null, initialState.listCurrencySelected)
        assertEquals(null, initialState.listStatus)
        assertEquals(null, initialState.listStatusSelected)
        assertEquals(null, initialState.maxAmount)
        assertEquals(null, initialState.minAmount)
        assertEquals(null, initialState.debit)
        assertEquals(null, initialState.tax)
        assertEquals(null, initialState.declaration)
        assertEquals(null, initialState.batch)
        assertEquals(null, initialState.startDate)
        assertEquals(null, initialState.endDate)
        assertEquals(null, initialState.modalConfirmConfig)
    }

    @Test
    fun `test AdvSearchReducer handles state correctly`() {
        val reducer = AdvSearchReducer()
        val initialState = AdvSearchReducer.AdvSearchState()

        // Test GetCurrencyListSuccess event
        val currencyEvent = AdvSearchReducer.AdvSearchEvent.GetCurrencyListSuccess(mockCurrencyList)
        val (newState, effect) = reducer.reduce(initialState, currencyEvent)
        
        assertEquals(mockCurrencyList, newState.listCurrency)
        assertEquals(mockCurrencyList, newState.listCurrencySelected)
        assertEquals(null, effect)
    }

    @Test
    fun `test AdvSearchReducer handles currency list with existing selection`() {
        val reducer = AdvSearchReducer()
        val existingSelection = listOf("USD")
        val initialState = AdvSearchReducer.AdvSearchState(listCurrencySelected = existingSelection)

        val currencyEvent = AdvSearchReducer.AdvSearchEvent.GetCurrencyListSuccess(mockCurrencyList)
        val (newState, effect) = reducer.reduce(initialState, currencyEvent)
        
        assertEquals(mockCurrencyList, newState.listCurrency)
        assertEquals(existingSelection, newState.listCurrencySelected)
        assertEquals(null, effect)
    }

    @Test
    fun `test AdvSearchReducer handles status list success`() {
        val reducer = AdvSearchReducer()
        val initialState = AdvSearchReducer.AdvSearchState()

        val statusEvent = AdvSearchReducer.AdvSearchEvent.GetStatusListSuccess(mockStatusList)
        val (newState, effect) = reducer.reduce(initialState, statusEvent)
        
        assertEquals(mockStatusList, newState.listStatus)
        assertEquals(mockStatusList, newState.listStatusSelected)
        assertEquals(null, effect)
    }

    @Test
    fun `test AdvSearchReducer handles status list with existing selection`() {
        val reducer = AdvSearchReducer()
        val existingSelection = listOf(TransactionStatusBase.INIT)
        val initialState = AdvSearchReducer.AdvSearchState(listStatusSelected = existingSelection)

        val statusEvent = AdvSearchReducer.AdvSearchEvent.GetStatusListSuccess(mockStatusList)
        val (newState, effect) = reducer.reduce(initialState, statusEvent)
        
        assertEquals(mockStatusList, newState.listStatus)
        assertEquals(existingSelection, newState.listStatusSelected)
        assertEquals(null, effect)
    }

    @Test
    fun `test AdvSearchReducer handles ShowCurrencyBottomSheet event`() {
        val reducer = AdvSearchReducer()
        val initialState = AdvSearchReducer.AdvSearchState()

        val showCurrencyEvent = AdvSearchReducer.AdvSearchEvent.ShowCurrencyBottomSheet
        val (newState, effect) = reducer.reduce(initialState, showCurrencyEvent)
        
        assertEquals(initialState, newState)
        assertEquals(AdvSearchReducer.AdvSearchEffect.FetCurrencyList, effect)
    }

    @Test
    fun `test AdvSearchReducer handles ShowStatusBottomSheet event`() {
        val reducer = AdvSearchReducer()
        val initialState = AdvSearchReducer.AdvSearchState()

        val showStatusEvent = AdvSearchReducer.AdvSearchEvent.ShowStatusBottomSheet
        val (newState, effect) = reducer.reduce(initialState, showStatusEvent)
        
        assertEquals(initialState, newState)
        assertEquals(AdvSearchReducer.AdvSearchEffect.FetStatusList, effect)
    }

    @Test
    fun `test AdvSearchReducer handles ValueTextChanged event`() {
        val reducer = AdvSearchReducer()
        val initialState = AdvSearchReducer.AdvSearchState()

        val valueChangedEvent = AdvSearchReducer.AdvSearchEvent.ValueTextChanged(
            maxAmount = "10000",
            minAmount = "1000",
            debit = "123456",
            tax = "789012",
            declaration = "DEC001",
            batch = "BAT001"
        )
        val (newState, effect) = reducer.reduce(initialState, valueChangedEvent)
        
        assertEquals("10000", newState.maxAmount)
        assertEquals("1000", newState.minAmount)
        assertEquals("123456", newState.debit)
        assertEquals("789012", newState.tax)
        assertEquals("DEC001", newState.declaration)
        assertEquals("BAT001", newState.batch)
        assertEquals(null, effect)
    }

    @Test
    fun `test AdvSearchReducer handles partial ValueTextChanged event`() {
        val reducer = AdvSearchReducer()
        val initialState = AdvSearchReducer.AdvSearchState(
            maxAmount = "5000",
            debit = "existing"
        )

        val valueChangedEvent = AdvSearchReducer.AdvSearchEvent.ValueTextChanged(
            minAmount = "1000",
            tax = "789012"
        )
        val (newState, effect) = reducer.reduce(initialState, valueChangedEvent)
        
        assertEquals("5000", newState.maxAmount) // Should remain unchanged
        assertEquals("1000", newState.minAmount)
        assertEquals("existing", newState.debit) // Should remain unchanged
        assertEquals("789012", newState.tax)
        assertEquals(null, effect)
    }

    @Test
    fun `test AdvSearchReducer handles ValidateAmount event`() {
        val reducer = AdvSearchReducer()
        val initialState = AdvSearchReducer.AdvSearchState(invalidAmount = false)

        val validateEvent = AdvSearchReducer.AdvSearchEvent.ValidateAmount(true)
        val (newState, effect) = reducer.reduce(initialState, validateEvent)
        
        assertEquals(true, newState.invalidAmount)
        assertEquals(null, effect)
    }

    @Test
    fun `test AdvSearchReducer handles ValidateAmount event with same value`() {
        val reducer = AdvSearchReducer()
        val initialState = AdvSearchReducer.AdvSearchState(invalidAmount = true)

        val validateEvent = AdvSearchReducer.AdvSearchEvent.ValidateAmount(true)
        val (newState, effect) = reducer.reduce(initialState, validateEvent)
        
        assertEquals(initialState, newState)
        assertEquals(null, effect)
    }

    @Test
    fun `test AdvSearchReducer handles ClearSearchFilterContent event`() {
        val reducer = AdvSearchReducer()
        val initialState = AdvSearchReducer.AdvSearchState(
            maxAmount = "5000",
            minAmount = "1000",
            debit = "123456",
            invalidAmount = true
        )

        val clearEvent = AdvSearchReducer.AdvSearchEvent.ClearSearchFilterContent
        val (newState, effect) = reducer.reduce(initialState, clearEvent)
        
        assertEquals(AdvSearchReducer.AdvSearchState(), newState)
        assertEquals(null, effect)
    }

    @Test
    fun `test AdvSearchReducer handles SetCurrencyList event`() {
        val reducer = AdvSearchReducer()
        val initialState = AdvSearchReducer.AdvSearchState()
        val selectedCurrencies = listOf("USD", "EUR")

        val setCurrencyEvent = AdvSearchReducer.AdvSearchEvent.SetCurrencyList(selectedCurrencies)
        val (newState, effect) = reducer.reduce(initialState, setCurrencyEvent)
        
        assertEquals(selectedCurrencies, newState.listCurrencySelected)
        assertEquals(null, effect)
    }

    @Test
    fun `test AdvSearchReducer handles SetStatusList event`() {
        val reducer = AdvSearchReducer()
        val initialState = AdvSearchReducer.AdvSearchState()
        val selectedStatuses = listOf(TransactionStatusBase.INIT, TransactionStatusBase.REJECTED)

        val setStatusEvent = AdvSearchReducer.AdvSearchEvent.SetStatusList(selectedStatuses)
        val (newState, effect) = reducer.reduce(initialState, setStatusEvent)
        
        assertEquals(selectedStatuses, newState.listStatusSelected)
        assertEquals(null, effect)
    }

    @Test
    fun `test GovernmentServiceUseCase getListCurrency returns default VND`() = runTest {
        coEvery { governmentServiceUseCase.getListCurrency() } returns DomainResult.Success(listOf("VND"))

        val result = governmentServiceUseCase.getListCurrency()
        
        assertTrue(result is DomainResult.Success)
        assertEquals(listOf("VND"), result.getSafeData())
    }

    @Test
    fun `test GovernmentServiceUseCase getListStatus returns expected statuses`() = runTest {
        val expectedStatuses = listOf(TransactionStatusBase.INIT, TransactionStatusBase.REJECTED)
        coEvery { governmentServiceUseCase.getListStatus() } returns DomainResult.Success(expectedStatuses)

        val result = governmentServiceUseCase.getListStatus()
        
        assertTrue(result is DomainResult.Success)
        assertEquals(expectedStatuses, result.getSafeData())
    }

    @Test
    fun `test AdvSearchEffect sealed class structure`() {
        val allEffects = listOf(
            AdvSearchReducer.AdvSearchEffect.FetCurrencyList,
            AdvSearchReducer.AdvSearchEffect.FetStatusList
        )

        allEffects.forEach { effect ->
            assertTrue("Effect should be instance of AdvSearchEffect", effect is AdvSearchReducer.AdvSearchEffect)
        }
    }

    @Test
    fun `test error domain results`() = runTest {
        coEvery { governmentServiceUseCase.getListCurrency() } returns DomainResult.Error(
            errorCode = "500",
            errorMessage = "Server error"
        )

        val result = governmentServiceUseCase.getListCurrency()
        
        assertTrue(result is DomainResult.Error)
        val errorResult = result as DomainResult.Error
        assertEquals("500", errorResult.errorCode)
        assertEquals("Server error", errorResult.errorMessage)
    }

    @Test
    fun `test ViewModel class can be instantiated`() {
        val realViewModel = AdvSearchViewModel(governmentServiceUseCase)
        assertNotNull("ViewModel should be successfully created", realViewModel)
    }

    @Test
    fun `test all AdvSearchEvent types exist`() {
        val events = listOf(
            AdvSearchReducer.AdvSearchEvent.ValueTextChanged(),
            AdvSearchReducer.AdvSearchEvent.ClearSearchFilterContent,
            AdvSearchReducer.AdvSearchEvent.ShowCurrencyBottomSheet,
            AdvSearchReducer.AdvSearchEvent.ShowStatusBottomSheet,
            AdvSearchReducer.AdvSearchEvent.GetCurrencyListSuccess(null),
            AdvSearchReducer.AdvSearchEvent.GetStatusListSuccess(null),
            AdvSearchReducer.AdvSearchEvent.SetCurrencyList(null),
            AdvSearchReducer.AdvSearchEvent.SetStatusList(null),
            AdvSearchReducer.AdvSearchEvent.ValidateAmount(false),
            AdvSearchReducer.AdvSearchEvent.ClearDate,
            AdvSearchReducer.AdvSearchEvent.SelectDate(null, null),
            AdvSearchReducer.AdvSearchEvent.ShowError(null)
        )

        events.forEach { event ->
            assertTrue("Event should be instance of AdvSearchEvent", event is AdvSearchReducer.AdvSearchEvent)
        }
    }
} 