package vn.com.bidv.feature.government.service.domain

import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNotNull
import org.junit.Before
import org.junit.Test
import vn.com.bidv.feature.government.service.data.GovernmentServiceRepository
import vn.com.bidv.feature.government.service.data.governmentservice.model.DataListTxnPendingListRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnPendingListRes
import vn.com.bidv.feature.government.service.domain.usecase.GovernmentServiceUseCase
import vn.com.bidv.feature.government.service.model.ListCustomsDutiesRuleFilter
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.sdkbase.data.LocalRepository
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.TransactionStatusBase
import java.util.Date

class GovernmentServiceUseCaseTest {

    private val mockGovernmentServiceRepository: GovernmentServiceRepository = mockk()
    private val mockLocalRepository: LocalRepository = mockk()
    private lateinit var useCase: GovernmentServiceUseCase

    @Before
    fun setUp() {
        useCase = GovernmentServiceUseCase(
            mockGovernmentServiceRepository,
            localRepository = mockLocalRepository
        )
    }

    @Test
    fun `constructor should create instance with provided dependencies`() {
        // Assert
        assertNotNull(useCase)
    }

    @Test
    fun `getListPendingTransaction should return success result when repository succeeds`() = runTest {
        // Arrange
        val pageIndex = 1
        val pageSize = 20
        val rule = ListCustomsDutiesRuleFilter(
            search = "test search",
            minAmount = "1000",
            maxAmount = "10000",
            debit = "1234567890",
            tax = "0123456789",
            declaration = "DECL001",
            batch = "BATCH001",
            startDate = Date(1704067200000), // 2024-01-01
            endDate = Date(1706745599000),   // 2024-01-31
            listStatusSelected = listOf("PENDING", "APPROVED")
        )

        val expectedResponse = DataListTxnPendingListRes(
            items = listOf(
                TxnPendingListRes(
                    txnId = "TX123",
                    amount = "1000000",
                    ccy = "VND"
                )
            ),
            total = 1L
        )

        coEvery { 
            mockGovernmentServiceRepository.getListPendingTransaction(any()) 
        } returns NetworkResult.Success(expectedResponse)

        // Act
        val result = useCase.getListPendingTransaction(rule, pageIndex, pageSize)

        // Assert
        assert(result is DomainResult.Success)
        coVerify { mockGovernmentServiceRepository.getListPendingTransaction(any()) }
    }

    @Test
    fun `getListPendingTransaction should handle null rule filter`() = runTest {
        // Arrange
        val pageIndex = 1
        val pageSize = 10
        val rule: ListCustomsDutiesRuleFilter? = null

        val expectedResponse = DataListTxnPendingListRes(
            items = emptyList(),
            total = 0L
        )

        coEvery { 
            mockGovernmentServiceRepository.getListPendingTransaction(any()) 
        } returns NetworkResult.Success(expectedResponse)

        // Act
        val result = useCase.getListPendingTransaction(rule, pageIndex, pageSize)

        // Assert
        assert(result is DomainResult.Success)
        coVerify { 
            mockGovernmentServiceRepository.getListPendingTransaction(
                match { request ->
                    request.page?.pageNum == pageIndex &&
                    request.page!!.pageSize == pageSize &&
                    request.search == null &&
                    request.startDate == null &&
                    request.endDate == null &&
                    request.minAmount == null &&
                    request.maxAmount == null &&
                    request.ccy == "VND" &&
                    request.statuses == null &&
                    request.debitAccNo == null &&
                    request.taxCode == null &&
                    request.declarationNo == null &&
                    request.batchNo == null
                }
            )
        }
    }

    @Test
    fun `getListPendingTransaction should handle empty rule filter`() = runTest {
        // Arrange
        val pageIndex = 2
        val pageSize = 15
        val rule = ListCustomsDutiesRuleFilter()

        val expectedResponse = DataListTxnPendingListRes(
            items = emptyList(),
            total = 0L
        )

        coEvery { 
            mockGovernmentServiceRepository.getListPendingTransaction(any()) 
        } returns NetworkResult.Success(expectedResponse)

        // Act
        val result = useCase.getListPendingTransaction(rule, pageIndex, pageSize)

        // Assert
        assert(result is DomainResult.Success)
        coVerify { 
            mockGovernmentServiceRepository.getListPendingTransaction(
                match { request ->
                    request.page?.pageNum == pageIndex &&
                    request.page!!.pageSize == pageSize &&
                    request.ccy == "VND"
                }
            )
        }
    }

    @Test
    fun `getListPendingTransaction should return error when repository fails`() = runTest {
        // Arrange
        val pageIndex = 1
        val pageSize = 20
        val rule = ListCustomsDutiesRuleFilter(search = "test")

        coEvery { 
            mockGovernmentServiceRepository.getListPendingTransaction(any()) 
        } returns NetworkResult.Error(
            errorCode = "E001",
            errorMessage = "Network error"
        )

        // Act
        val result = useCase.getListPendingTransaction(rule, pageIndex, pageSize)

        // Assert
        assert(result is DomainResult.Error)
        val errorResult = result as DomainResult.Error
        assertEquals("E001", errorResult.errorCode)
        assertEquals("Network error", errorResult.errorMessage)
    }

    @Test
    fun `getListPendingTransaction should handle partial rule filter data`() = runTest {
        // Arrange
        val pageIndex = 1
        val pageSize = 20
        val rule = ListCustomsDutiesRuleFilter(
            search = "partial test",
            minAmount = "5000",
            startDate = Date(1704067200000) // Only some fields filled
        )

        val expectedResponse = DataListTxnPendingListRes(
            items = emptyList(),
            total = 0L
        )

        coEvery { 
            mockGovernmentServiceRepository.getListPendingTransaction(any()) 
        } returns NetworkResult.Success(expectedResponse)

        // Act
        val result = useCase.getListPendingTransaction(rule, pageIndex, pageSize)

        // Assert
        assert(result is DomainResult.Success)
        coVerify { 
            mockGovernmentServiceRepository.getListPendingTransaction(
                match { request ->
                    request.search == "partial test" &&
                    request.minAmount == "5000" &&
                    request.startDate == "2024-01-01" &&
                    request.maxAmount == null &&
                    request.endDate == null &&
                    request.debitAccNo == null
                }
            )
        }
    }

    @Test
    fun `getListCurrency should return VND currency list`() {
        // Act
        val result = useCase.getListCurrency()

        // Assert
        assert(result is DomainResult.Success)
        val successResult = result as DomainResult.Success
        assertEquals(listOf("VND"), successResult.data)
    }

    @Test
    fun `getListStatus should return correct transaction statuses`() {
        // Act
        val result = useCase.getListStatus()

        // Assert
        assert(result is DomainResult.Success)
        val successResult = result as DomainResult.Success
        assertEquals(2, successResult.data?.size)
        assertEquals(TransactionStatusBase.INIT, successResult.data?.get(0))
        assertEquals(TransactionStatusBase.REJECTED, successResult.data?.get(1))
    }

    @Test
    fun `getListStatus should always return same statuses`() {
        // Act
        val result1 = useCase.getListStatus()
        val result2 = useCase.getListStatus()

        // Assert
        assert(result1 is DomainResult.Success)
        assert(result2 is DomainResult.Success)
        assertEquals(
            (result1 as DomainResult.Success).data,
            (result2 as DomainResult.Success).data
        )
    }

    @Test
    fun `getListPendingTransaction should handle zero page size`() = runTest {
        // Arrange
        val pageIndex = 1
        val pageSize = 0
        val rule: ListCustomsDutiesRuleFilter? = null

        val expectedResponse = DataListTxnPendingListRes(
            items = emptyList(),
            total = 0L
        )

        coEvery { 
            mockGovernmentServiceRepository.getListPendingTransaction(any()) 
        } returns NetworkResult.Success(expectedResponse)

        // Act
        val result = useCase.getListPendingTransaction(rule, pageIndex, pageSize)

        // Assert
        assert(result is DomainResult.Success)
        coVerify { 
            mockGovernmentServiceRepository.getListPendingTransaction(
                match { request -> 
                    request.page?.pageNum == pageIndex &&
                    request.page?.pageSize == pageSize
                }
            )
        }
    }

    @Test
    fun `getListPendingTransaction should handle negative page index`() = runTest {
        // Arrange
        val pageIndex = -1
        val pageSize = 20
        val rule: ListCustomsDutiesRuleFilter? = null

        val expectedResponse = DataListTxnPendingListRes(
            items = emptyList(),
            total = 0L
        )

        coEvery { 
            mockGovernmentServiceRepository.getListPendingTransaction(any()) 
        } returns NetworkResult.Success(expectedResponse)

        // Act
        val result = useCase.getListPendingTransaction(rule, pageIndex, pageSize)

        // Assert
        assert(result is DomainResult.Success)
        coVerify { 
            mockGovernmentServiceRepository.getListPendingTransaction(
                match { request -> request.page?.pageNum == pageIndex }
            )
        }
    }
}
