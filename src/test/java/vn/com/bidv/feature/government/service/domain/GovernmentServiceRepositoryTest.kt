package vn.com.bidv.feature.government.service.domain

import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNotNull
import org.junit.Before
import org.junit.Test
import vn.com.bidv.feature.government.service.data.GovernmentServiceRepository
import vn.com.bidv.feature.government.service.data.governmentservice.apis.GovernmentServiceApi
import vn.com.bidv.feature.government.service.data.governmentservice.model.DataListTxnPendingListRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.Page
import vn.com.bidv.feature.government.service.data.governmentservice.model.ResultString
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnDeleteReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnDetailReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnDetailRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnPendingListReq
import vn.com.bidv.network.NetworkResult
import java.io.IOException

class GovernmentServiceRepositoryTest {

    private val mockGovernmentServiceApi: GovernmentServiceApi = mockk()
    private lateinit var repository: GovernmentServiceRepository

    @Before
    fun setUp() {
        repository = GovernmentServiceRepository(mockGovernmentServiceApi)
    }

    @Test
    fun `constructor should create instance with provided dependencies`() {
        // Assert
        assertNotNull(repository)
    }

}
