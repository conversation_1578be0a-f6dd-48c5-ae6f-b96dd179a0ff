package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.main

import org.junit.Assert.assertEquals
import org.junit.Assert.assertNotNull
import org.junit.Assert.assertNull
import org.junit.Before
import org.junit.Test
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState

class ListCustomsDutiesMainReducerTest {

    private lateinit var reducer: ListCustomsDutiesMainReducer
    private lateinit var initialState: ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewState

    @Before
    fun setUp() {
        reducer = ListCustomsDutiesMainReducer()
        initialState = ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewState()
    }

    @Test
    fun `reducer should implement Reducer interface`() {
        // Assert
        assert(reducer is Reducer<*, *, *>)
    }

    @Test
    fun `CustomsDutiesAndFeesMainViewState should implement ViewState interface`() {
        // Assert
        assert(initialState is ViewState)
    }

    @Test
    fun `CustomsDutiesAndFeesMainViewState should have default tabLayoutIndex of 0`() {
        // Act
        val state = ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewState()

        // Assert
        assertEquals(0, state.tabLayoutIndex)
    }

    @Test
    fun `CustomsDutiesAndFeesMainViewState should accept custom tabLayoutIndex`() {
        // Act
        val state =
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewState(tabLayoutIndex = 2)

        // Assert
        assertEquals(2, state.tabLayoutIndex)
    }

    @Test
    fun `CustomsDutiesAndFeesMainViewEvent should implement ViewEvent interface`() {
        // Arrange
        val event = ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(1)

        // Assert
        assert(event is ViewEvent)
    }

    @Test
    fun `CustomsDutiesAndFeesMainViewEffect should implement SideEffect interface`() {
        // Note: Since CustomsDutiesAndFeesMainViewEffect is a sealed class with no implementations,
        // we can only test that it extends SideEffect
        // This test verifies the class structure
        assert(ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEffect::class.java.interfaces.contains(SideEffect::class.java))
    }

    @Test
    fun `SelectedTab event should hold indexTab value correctly`() {
        // Arrange
        val indexTab = 3

        // Act
        val event =
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(indexTab)

        // Assert
        assertEquals(indexTab, event.indexTab)
    }

    @Test
    fun `reduce should handle SelectedTab event with index 0`() {
        // Arrange
        val event = ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(0)

        // Act
        val (newState, effect) = reducer.reduce(initialState, event)

        // Assert
        assertEquals(0, newState.tabLayoutIndex)
        assertNull(effect)
        assertNotNull(newState)
    }

    @Test
    fun `reduce should handle SelectedTab event with index 1`() {
        // Arrange
        val event = ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(1)

        // Act
        val (newState, effect) = reducer.reduce(initialState, event)

        // Assert
        assertEquals(1, newState.tabLayoutIndex)
        assertNull(effect)
    }

    @Test
    fun `reduce should handle SelectedTab event with index 2`() {
        // Arrange
        val event = ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(2)

        // Act
        val (newState, effect) = reducer.reduce(initialState, event)

        // Assert
        assertEquals(2, newState.tabLayoutIndex)
        assertNull(effect)
    }

    @Test
    fun `reduce should handle SelectedTab event with negative index`() {
        // Arrange
        val event = ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(-1)

        // Act
        val (newState, effect) = reducer.reduce(initialState, event)

        // Assert
        assertEquals(-1, newState.tabLayoutIndex)
        assertNull(effect)
    }

    @Test
    fun `reduce should handle SelectedTab event with large index`() {
        // Arrange
        val event = ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(999)

        // Act
        val (newState, effect) = reducer.reduce(initialState, event)

        // Assert
        assertEquals(999, newState.tabLayoutIndex)
        assertNull(effect)
    }

    @Test
    fun `reduce should update state from non-default initial state`() {
        // Arrange
        val customInitialState =
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewState(tabLayoutIndex = 5)
        val event = ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(3)

        // Act
        val (newState, effect) = reducer.reduce(customInitialState, event)

        // Assert
        assertEquals(3, newState.tabLayoutIndex)
        assertNull(effect)
    }

    @Test
    fun `reduce should always return null effect for SelectedTab event`() {
        // Arrange
        val events = listOf(
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(0),
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(1),
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(2),
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(10)
        )

        // Act & Assert
        events.forEach { event ->
            val (_, effect) = reducer.reduce(initialState, event)
            assertNull("Effect should be null for event: $event", effect)
        }
    }

    @Test
    fun `reduce should maintain state immutability`() {
        // Arrange
        val originalState =
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewState(tabLayoutIndex = 1)
        val event = ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(2)

        // Act
        val (newState, _) = reducer.reduce(originalState, event)

        // Assert
        assertEquals(1, originalState.tabLayoutIndex) // Original state unchanged
        assertEquals(2, newState.tabLayoutIndex) // New state has updated value
        assert(originalState !== newState) // Different instances
    }

    @Test
    fun `reduce should handle multiple sequential tab selections`() {
        // Arrange
        var currentState = initialState
        val tabSelections = listOf(1, 3, 0, 2, 4)

        // Act & Assert
        tabSelections.forEach { tabIndex ->
            val event =
                ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(tabIndex)
            val (newState, effect) = reducer.reduce(currentState, event)
            
            assertEquals(tabIndex, newState.tabLayoutIndex)
            assertNull(effect)
            currentState = newState
        }
        
        // Final state should have the last selected tab
        assertEquals(4, currentState.tabLayoutIndex)
    }

    @Test
    fun `reduce should handle same tab selection multiple times`() {
        // Arrange
        val event = ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(1)

        // Act
        val (firstState, firstEffect) = reducer.reduce(initialState, event)
        val (secondState, secondEffect) = reducer.reduce(firstState, event)

        // Assert
        assertEquals(1, firstState.tabLayoutIndex)
        assertEquals(1, secondState.tabLayoutIndex)
        assertNull(firstEffect)
        assertNull(secondEffect)
    }

    @Test
    fun `data class should support equality`() {
        // Arrange
        val state1 =
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewState(tabLayoutIndex = 2)
        val state2 =
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewState(tabLayoutIndex = 2)
        val state3 =
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewState(tabLayoutIndex = 3)

        // Act & Assert
        assertEquals(state1, state2)
        assert(state1 != state3)
    }

    @Test
    fun `data class should support copy functionality`() {
        // Arrange
        val originalState =
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewState(tabLayoutIndex = 1)

        // Act
        val copiedState = originalState.copy(tabLayoutIndex = 5)

        // Assert
        assertEquals(1, originalState.tabLayoutIndex)
        assertEquals(5, copiedState.tabLayoutIndex)
        assert(originalState !== copiedState)
    }

    @Test
    fun `SelectedTab event should support equality`() {
        // Arrange
        val event1 = ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(2)
        val event2 = ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(2)
        val event3 = ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(3)

        // Act & Assert
        assertEquals(event1, event2)
        assert(event1 != event3)
    }

    @Test
    fun `reducer should be stateless and reusable`() {
        // Arrange
        val anotherReducer = ListCustomsDutiesMainReducer()
        val event = ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(1)

        // Act
        val (result1, effect1) = reducer.reduce(initialState, event)
        val (result2, effect2) = anotherReducer.reduce(initialState, event)

        // Assert
        assertEquals(result1, result2)
        assertEquals(effect1, effect2)
    }
}
