package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.main

import io.mockk.mockk
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNotNull
import org.junit.Before
import org.junit.Test
import vn.com.bidv.sdkbase.data.LocalRepository
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import vn.com.bidv.sdkbase.utils.ResourceProvider
import java.lang.reflect.Field

class ListCustomsDutiesMainViewModelTest {

    private val mockLocalRepository: LocalRepository = mockk(relaxed = true)
    private val mockResourceProvider: ResourceProvider = mockk(relaxed = true)
    private lateinit var viewModel: ListCustomsDutiesMainViewModel

    @Before
    fun setUp() {
        viewModel = ListCustomsDutiesMainViewModel()
        
        // Initialize lateinit properties using reflection to avoid UninitializedPropertyAccessException
        setPrivateField(viewModel, "localRepositoryImp", mockLocalRepository)
        setPrivateField(viewModel, "iOdispatcherImp", Dispatchers.Unconfined)
        setPrivateField(viewModel, "resourceProviderImp", mockResourceProvider)
    }

    private fun setPrivateField(target: Any, fieldName: String, value: Any) {
        val field: Field = target.javaClass.superclass.getDeclaredField(fieldName)
        field.isAccessible = true
        field.set(target, value)
    }

    @Test
    fun `constructor should create instance successfully`() {
        // Assert
        assertNotNull(viewModel)
    }

    @Test
    fun `viewModel should extend ViewModelIBankBase`() {
        // Assert
        assert(viewModel is ViewModelIBankBase<*, *, *>)
    }

    @Test
    fun `viewModel should have correct initial state with tabLayoutIndex 1`() = runTest {
        // Act
        val initialState = viewModel.uiState.first()

        // Assert
        assertEquals(1, initialState.tabLayoutIndex)
    }

    @Test
    fun `viewModel should use ListCustomsDutiesMainReducer`() {
        // This test verifies the reducer type through the state management behavior
        // We can test this by sending an event and verifying the state change
        
        // Act
        viewModel.sendEvent(
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(2)
        )

        // The fact that this doesn't throw an exception confirms the correct reducer is used
        assert(true)
    }

    @Test
    fun `sendEvent should update state correctly for SelectedTab with index 0`() = runTest {
        // Act
        viewModel.sendEvent(
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(0)
        )

        // Allow state to update
        kotlinx.coroutines.delay(100)
        val currentState = viewModel.uiState.first()

        // Assert
        assertEquals(0, currentState.tabLayoutIndex)
    }

    @Test
    fun `sendEvent should update state correctly for SelectedTab with index 2`() = runTest {
        // Act
        viewModel.sendEvent(
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(2)
        )

        // Allow state to update
        kotlinx.coroutines.delay(100)
        val currentState = viewModel.uiState.first()

        // Assert
        assertEquals(2, currentState.tabLayoutIndex)
    }

    @Test
    fun `sendEvent should update state correctly for SelectedTab with index 3`() = runTest {
        // Act
        viewModel.sendEvent(
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(3)
        )

        // Allow state to update
        kotlinx.coroutines.delay(100)
        val currentState = viewModel.uiState.first()

        // Assert
        assertEquals(3, currentState.tabLayoutIndex)
    }

    @Test
    fun `sendEvent should handle negative tab index`() = runTest {
        // Act
        viewModel.sendEvent(
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(-1)
        )

        // Allow state to update
        kotlinx.coroutines.delay(100)
        val currentState = viewModel.uiState.first()

        // Assert
        assertEquals(-1, currentState.tabLayoutIndex)
    }

    @Test
    fun `sendEvent should handle large tab index`() = runTest {
        // Act
        viewModel.sendEvent(
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(999)
        )

        // Allow state to update
        kotlinx.coroutines.delay(100)
        val currentState = viewModel.uiState.first()

        // Assert
        assertEquals(999, currentState.tabLayoutIndex)
    }

    @Test
    fun `sendEvent should handle multiple sequential tab selections`() = runTest {
        // Arrange
        val tabSelections = listOf(0, 2, 1, 3)

        // Act & Assert
        tabSelections.forEach { tabIndex ->
            viewModel.sendEvent(
                ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(tabIndex)
            )
            
            // Allow state to update
            kotlinx.coroutines.delay(50)
            val currentState = viewModel.uiState.first()
            assertEquals(tabIndex, currentState.tabLayoutIndex)
        }
    }

    @Test
    fun `sendEvent should handle same tab selection multiple times`() = runTest {
        // Act
        viewModel.sendEvent(
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(2)
        )
        kotlinx.coroutines.delay(50)
        
        viewModel.sendEvent(
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(2)
        )
        kotlinx.coroutines.delay(50)
        
        val currentState = viewModel.uiState.first()

        // Assert
        assertEquals(2, currentState.tabLayoutIndex)
    }

    @Test
    fun `handleEffect should not throw exception for empty effect handling`() {
        // Since ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEffect is a sealed class
        // with no implementations, and the ViewModel doesn't override handleEffect,
        // we test that the default behavior doesn't cause issues
        
        // This test verifies that the ViewModel can be instantiated and used without
        // custom effect handling, which is the case for this simple ViewModel
        
        // Act & Assert - Should not throw exception
        viewModel.sendEvent(
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(1)
        )
        
        assert(true)
    }

    @Test
    fun `viewModel should maintain state consistency across multiple operations`() = runTest {
        // Arrange
        val operations = listOf(
            0 to 0,
            1 to 1,
            2 to 2,
            0 to 0,
            3 to 3
        )

        // Act & Assert
        operations.forEach { (inputIndex, expectedIndex) ->
            viewModel.sendEvent(
                ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(
                    inputIndex
                )
            )
            
            kotlinx.coroutines.delay(50)
            val currentState = viewModel.uiState.first()
            assertEquals(
                "Tab index should be $expectedIndex after selecting tab $inputIndex",
                expectedIndex,
                currentState.tabLayoutIndex
            )
        }
    }

    @Test
    fun `viewModel should have proper type parameters`() {
        // This test verifies the generic type parameters are correctly set
        // by checking that the ViewModel can handle the expected types
        
        // Arrange
        val event = ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(1)
        
        // Act & Assert - Should compile and run without type errors
        viewModel.sendEvent(event)
        assert(true)
    }

    @Test
    fun `viewModel should have no-argument constructor for Hilt injection`() {
        // This test verifies that the ViewModel can be instantiated with no arguments
        // which is required for Hilt dependency injection
        
        // Act & Assert - Should not throw exception
        val newViewModel = ListCustomsDutiesMainViewModel()
        assertNotNull(newViewModel)
    }

    @Test
    fun `viewModel should initialize with correct reducer and initial state`() = runTest {
        // This test verifies that the ViewModel is properly configured with
        // the correct reducer and initial state
        
        // Act
        val initialState = viewModel.uiState.first()
        
        // Assert
        assertEquals(1, initialState.tabLayoutIndex)
        assertNotNull(initialState)
        
        // Verify reducer works by sending an event
        viewModel.sendEvent(
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(5)
        )
        
        kotlinx.coroutines.delay(100)
        val updatedState = viewModel.uiState.first()
        assertEquals(5, updatedState.tabLayoutIndex)
    }

    @Test
    fun `viewModel should handle rapid tab selections correctly`() = runTest {
        // Test rapid successive tab selections to ensure state consistency
        
        // Act
        repeat(10) { index ->
            viewModel.sendEvent(
                ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(index)
            )
        }
        
        // Allow all events to process
        kotlinx.coroutines.delay(200)
        val finalState = viewModel.uiState.first()
        
        // Assert - Final state should be the last sent event (index 9)
        assertEquals(9, finalState.tabLayoutIndex)
    }

    @Test
    fun `viewModel should handle boundary values correctly`() = runTest {
        // Test with boundary values
        val boundaryValues = listOf(
            Int.MIN_VALUE,
            -1,
            0,
            1,
            Int.MAX_VALUE
        )
        
        boundaryValues.forEach { value ->
            // Act
            viewModel.sendEvent(
                ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(value)
            )
            
            kotlinx.coroutines.delay(50)
            val currentState = viewModel.uiState.first()
            
            // Assert
            assertEquals(value, currentState.tabLayoutIndex)
        }
    }
}
