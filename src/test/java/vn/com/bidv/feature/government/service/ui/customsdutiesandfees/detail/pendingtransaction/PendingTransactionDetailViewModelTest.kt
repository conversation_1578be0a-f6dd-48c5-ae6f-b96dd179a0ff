package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.detail.pendingtransaction

import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNotNull
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import vn.com.bidv.feature.government.service.domain.usecase.GetPendingTransactionDetailUseCase
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.ResourceProvider

@ExperimentalCoroutinesApi
class PendingTransactionDetailViewModelTest {

    @MockK(relaxed = true)
    private lateinit var getPendingTransactionDetailUseCase: GetPendingTransactionDetailUseCase

    @MockK(relaxed = true)
    private lateinit var resourceProvider: ResourceProvider

    private lateinit var viewModel: PendingTransactionDetailViewModel

    @Before
    fun setUp() {
        MockKAnnotations.init(this, relaxed = true)
        Dispatchers.setMain(Dispatchers.Unconfined)
        
        every { resourceProvider.getString(any()) } returns "Test string"
        every { resourceProvider.getString(R.string.co_loi_xay_ra_quy_khach_vui_long_thu_lai) } returns "An error occurred, please try again"

        viewModel = PendingTransactionDetailViewModel(
            dispatcher = Dispatchers.Unconfined,
            getPendingTransactionDetailUseCase = getPendingTransactionDetailUseCase,
            resourceProvider = resourceProvider
        )
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }

    @Test
    fun `test constructor injection`() {
        assertNotNull(viewModel)
        assertTrue(viewModel.resourceProvider === resourceProvider)
    }

    @Test
    fun `test handleEffect with PushTransaction effect`() = runTest {
        val transactionId = "TXN123"
        val effect = PendingTransactionDetailReducer.PendingTransactionDetailViewEffect.PushTransaction(transactionId, true)
        var eventCalled = false

        viewModel.handleEffect(effect) { 
            eventCalled = true
        }

        // PushTransaction doesn't trigger any event in the current implementation
        assertEquals(false, eventCalled)
    }

    @Test
    fun `test handleEffect with PushTransaction effect handles exception`() = runTest {
        val transactionId = ""
        val effect = PendingTransactionDetailReducer.PendingTransactionDetailViewEffect.PushTransaction(transactionId, true)
        var eventCalled = false

        viewModel.handleEffect(effect) { 
            eventCalled = true
        }

        // PushTransaction doesn't trigger any event in the current implementation
        assertEquals(false, eventCalled)
    }

    @Test
    fun `test pushTransaction with approve false`() = runTest {
        val transactionId = "TXN123"
        val effect = PendingTransactionDetailReducer.PendingTransactionDetailViewEffect.PushTransaction(transactionId, false)
        var eventCalled = false

        viewModel.handleEffect(effect) { 
            eventCalled = true
        }

        assertEquals(false, eventCalled)
    }

    @Test
    fun `test pushTransaction with different transaction ids`() = runTest {
        val transactionIds = listOf("TXN123", "TXN456", "")

        transactionIds.forEach { transactionId ->
            val effect = PendingTransactionDetailReducer.PendingTransactionDetailViewEffect.PushTransaction(transactionId, true)
            var eventCalled = false

            viewModel.handleEffect(effect) { 
                eventCalled = true
            }
            
            assertEquals(false, eventCalled)
        }
    }

    @Test
    fun `test handleEffect with ShowSuccessMessage effect - no-op`() {
        val effect = PendingTransactionDetailReducer.PendingTransactionDetailViewEffect.ShowSuccessMessage("Success")
        var eventCalled = false

        viewModel.handleEffect(effect) { 
            eventCalled = true
        }

        assertEquals(false, eventCalled)
    }

    @Test
    fun `test handleEffect with ShowErrorMessage effect - no-op`() {
        val effect = PendingTransactionDetailReducer.PendingTransactionDetailViewEffect.ShowErrorMessage("Error")
        var eventCalled = false

        viewModel.handleEffect(effect) { 
            eventCalled = true
        }

        assertEquals(false, eventCalled)
    }

    @Test
    fun `test handleEffect with NavigateBack effect - no-op`() {
        val effect = PendingTransactionDetailReducer.PendingTransactionDetailViewEffect.NavigateBack
        var eventCalled = false

        viewModel.handleEffect(effect) { 
            eventCalled = true
        }

        assertEquals(false, eventCalled)
    }

    @Test
    fun `test handleEffect with EditTransaction effect - no-op`() {
        val effect = PendingTransactionDetailReducer.PendingTransactionDetailViewEffect.EditTransaction("TXN123")
        var eventCalled = false

        viewModel.handleEffect(effect) { 
            eventCalled = true
        }

        assertEquals(false, eventCalled)
    }

    @Test
    fun `test handleEffect with ViewTransactionHistory effect - no-op`() {
        val effect = PendingTransactionDetailReducer.PendingTransactionDetailViewEffect.ViewTransactionHistory("TXN123")
        var eventCalled = false

        viewModel.handleEffect(effect) { 
            eventCalled = true
        }

        assertEquals(false, eventCalled)
    }

    @Test
    fun `test all UI effect types are handled as no-op`() {
        val uiEffects = listOf(
            PendingTransactionDetailReducer.PendingTransactionDetailViewEffect.ShowSuccessMessage("Success"),
            PendingTransactionDetailReducer.PendingTransactionDetailViewEffect.ShowErrorMessage("Error"),
            PendingTransactionDetailReducer.PendingTransactionDetailViewEffect.NavigateBack,
            PendingTransactionDetailReducer.PendingTransactionDetailViewEffect.EditTransaction("TXN123"),
            PendingTransactionDetailReducer.PendingTransactionDetailViewEffect.ViewTransactionHistory("TXN123")
        )

        uiEffects.forEach { effect ->
            var eventCalled = false
            viewModel.handleEffect(effect) { 
                eventCalled = true
            }
            assertEquals("Effect $effect should be no-op", false, eventCalled)
        }
    }

    @Test
    fun `test coroutine dispatcher is used correctly`() = runTest {
        val transactionId = "TXN123"
        val effect = PendingTransactionDetailReducer.PendingTransactionDetailViewEffect.PushTransaction(transactionId, true)

        viewModel.handleEffect(effect) { }
        
        assertTrue("Test completed successfully", true)
    }

    @Test
    fun `test ViewModel creation with different dispatchers`() {
        val viewModelWithUnconfined = PendingTransactionDetailViewModel(
            dispatcher = Dispatchers.Unconfined,
            getPendingTransactionDetailUseCase = getPendingTransactionDetailUseCase,
            resourceProvider = resourceProvider
        )

        assertTrue("ViewModel should be created successfully", viewModelWithUnconfined.resourceProvider === resourceProvider)
    }

    @Test
    fun `test resource provider string fetching`() {
        val testStringRes = R.string.co_loi_xay_ra_quy_khach_vui_long_thu_lai
        val expectedString = "An error occurred, please try again"
        
        val actualString = viewModel.resourceProvider.getString(testStringRes)
        
        assertEquals(expectedString, actualString)
    }

    @Test
    fun `test resource provider is accessible`() {
        val errorMessage = viewModel.resourceProvider.getString(R.string.co_loi_xay_ra_quy_khach_vui_long_thu_lai)
        assertEquals("An error occurred, please try again", errorMessage)
    }

    @Test
    fun `test handleEffect method existence and callable`() {
        val effect = PendingTransactionDetailReducer.PendingTransactionDetailViewEffect.ShowSuccessMessage("Test")
        var callbackInvoked = false
        
        viewModel.handleEffect(effect) { 
            callbackInvoked = true
        }
        
        // For UI effects, callback should not be invoked
        assertEquals(false, callbackInvoked)
    }
} 