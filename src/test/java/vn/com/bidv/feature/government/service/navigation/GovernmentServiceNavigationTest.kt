package vn.com.bidv.feature.government.service.navigation

import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import io.mockk.mockk
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNotNull
import org.junit.Before
import org.junit.Test

class GovernmentServiceNavigationTest {

    private lateinit var navigation: GovernmentServiceNavigation
    private val mockNavGraphBuilder: NavGraphBuilder = mockk(relaxed = true)
    private val mockNavController: NavHostController = mockk(relaxed = true)

    @Before
    fun setUp() {
        navigation = GovernmentServiceNavigation()
    }

    @Test
    fun `constructor should create instance successfully`() {
        // Assert
        assertNotNull(navigation)
    }

    @Test
    fun `GovernmentServiceMainRoute should have correct route`() {
        // Act & Assert
        assertEquals("government_service_main_route", GovernmentServiceRoute.GovernmentServiceMainRoute.route)
    }

    @Test
    fun `PendingTransactionDetailRoute should have correct route`() {
        // Act & Assert
        assertEquals(
            "pending_transaction_detail_route/{transactionId}",
            GovernmentServiceRoute.PendingTransactionDetailRoute.route
        )
    }

    @Test
    fun `PendingTransactionDetailRoute createRoute should format correctly`() {
        // Arrange
        val transactionId = "TX123456789"

        // Act
        val result = GovernmentServiceRoute.PendingTransactionDetailRoute.createRoute(transactionId)

        // Assert
        assertEquals("pending_transaction_detail_route/TX123456789", result)
    }

    @Test
    fun `PendingTransactionDetailRoute createRoute should handle empty string`() {
        // Arrange
        val transactionId = ""

        // Act
        val result = GovernmentServiceRoute.PendingTransactionDetailRoute.createRoute(transactionId)

        // Assert
        assertEquals("pending_transaction_detail_route/", result)
    }

    @Test
    fun `PendingTransactionDetailRoute createRoute should handle special characters`() {
        // Arrange
        val transactionId = "TX-123_456@789"

        // Act
        val result = GovernmentServiceRoute.PendingTransactionDetailRoute.createRoute(transactionId)

        // Assert
        assertEquals("pending_transaction_detail_route/TX-123_456@789", result)
    }

    @Test
    fun `PendingTransactionEditRoute should have correct route`() {
        // Act & Assert
        assertEquals(
            "edit_pending_transaction/{transactionId}",
            GovernmentServiceRoute.PendingTransactionEditRoute.route
        )
    }

    @Test
    fun `PendingTransactionEditRoute createRoute should format correctly`() {
        // Arrange
        val transactionId = "TX987654321"

        // Act
        val result = GovernmentServiceRoute.PendingTransactionEditRoute.createRoute(transactionId)

        // Assert
        assertEquals("edit_pending_transaction/TX987654321", result)
    }

    @Test
    fun `PendingTransactionEditRoute createRoute should handle empty string`() {
        // Arrange
        val transactionId = ""

        // Act
        val result = GovernmentServiceRoute.PendingTransactionEditRoute.createRoute(transactionId)

        // Assert
        assertEquals("edit_pending_transaction/", result)
    }

    @Test
    fun `all route objects should have non-empty route strings`() {
        // Act & Assert
        assert(GovernmentServiceRoute.GovernmentServiceMainRoute.route.isNotEmpty())
        assert(GovernmentServiceRoute.PendingTransactionDetailRoute.route.isNotEmpty())
        assert(GovernmentServiceRoute.PendingTransactionEditRoute.route.isNotEmpty())
    }

    @Test
    fun `route strings should not contain spaces`() {
        // Act & Assert
        assert(!GovernmentServiceRoute.GovernmentServiceMainRoute.route.contains(" "))
        assert(!GovernmentServiceRoute.PendingTransactionDetailRoute.route.contains(" "))
        assert(!GovernmentServiceRoute.PendingTransactionEditRoute.route.contains(" "))
    }

    @Test
    fun `parameterized routes should contain parameter placeholders`() {
        // Act & Assert
        assert(GovernmentServiceRoute.PendingTransactionDetailRoute.route.contains("{transactionId}"))
        assert(GovernmentServiceRoute.PendingTransactionEditRoute.route.contains("{transactionId}"))
    }

    @Test
    fun `createRoute methods should replace parameter placeholders`() {
        // Arrange
        val testId = "TEST123"

        // Act
        val detailRoute = GovernmentServiceRoute.PendingTransactionDetailRoute.createRoute(testId)
        val editRoute = GovernmentServiceRoute.PendingTransactionEditRoute.createRoute(testId)

        // Assert
        assert(!detailRoute.contains("{transactionId}"))
        assert(!editRoute.contains("{transactionId}"))
        assert(detailRoute.contains(testId))
        assert(editRoute.contains(testId))
    }
}
