# Publish Plugin Local Repo
Publish `plugin` to local repository for using in project.
- **Plugin**: is common gradle config in your build gradle file. It can be used in many modules.
- **Local Repository**: is folder in your project that contains library and plugin. Default is `repository` folder in your project
- **Source**: `build-logic` folder contains all source code of plugin.
##  Publishing
Your `plugin` auto publish to Local Repository (`repository` folder in your project).
#### **Step 1**: Apply plugin in `build.gradle.kts` of build-logic module

```groovy
plugins {
  /*...*/
  `java-gradle-plugin`
  `maven-publish`
}
```

#### **Step 2**: Declare your `plugins` in `build.gradle.kts` of build-logic module

```groovy 
gradlePlugin {
  plugins {
    register("pluginOne"){
      id = "plugin.one.id"
      implementationClass = "com.example.PluginOne"
      version = "1.0.0"
    }
    register("pluginTwo") {
      id = "plugin.two.id"
      implementationClass = "com.example.PluginTwo"
      version = "1.0.0"
    }
  }
}
``` 

#### Step 3: Config `publishing` in `build.gradle.kts` of build-logic module
```groovy
publishing {
    publications {
        /*pluginMaven is required*/
        create<MavenPublication>("pluginMaven") {
            groupId = "group.id.of.plugin"
            artifactId = "artifact.id.of.plugin"
            version = "1.0.0"
        }
    }

    repositories {
        maven {
            name = "Local"
            url = uri("path_to_local_repo")
        }
    }
}
```

#### Step 4: Publish to local repo
- Publish all plugins to local repository
    ```shell
    ./gradlew publishAllPublicationToLocalRepository
    ```
- Publish specific plugin to local repository
  ```shell
    # publish plugin
    ./gradlew publish<NameOfPlugin>PluginMarketMavenPublicationToLocalRepository
    #publish source code of plugin
    ./gredlew publishPluginMavenPublicationToLocalRepository
  ```

