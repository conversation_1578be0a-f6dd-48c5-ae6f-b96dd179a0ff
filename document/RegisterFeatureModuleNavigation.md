# Register a new feature module to common navigation graph
Each module should not be depended to another one on navigation.

## Register routes to main app navigation
Any public routes will be registered in `MainAppNavigations` class in `navigation` package of `sdkbase` module.
Define each public route by a `data object` insided a `seal class` represent for module in `IBankMainRouting` of `MainAppNavigations` as below:

```kotlin
    sealed class IBankMainRouting(val route: String, val deepLinkPattern: String) {
        sealed class ModuleRouteName {
            data object ModuleSubRoute1Name : IBankMainRouting(
                route = "name1",
                deepLinkPattern = "link1"
            )
            data object ModuleSubRoute2Name : IBankMainRouting(
                route = "name2",
                deepLinkPattern = "link2"
            )
            ...
        }
    }
```

## Add registered routes to list of public routes
All public routes will be return to a list for usage of main navigation graph. Add new public routes to function `listPublicRoutes()` in `MainAppNavigations`.

```kotlin
    fun listPublicRoutes(): List<String> {
        return listOf(
            IBankMainRouting.ModuleRouteName.ModuleSubRoute1Name.route,
            IBankMainRouting.ModuleRouteName.ModuleSubRoute2Name.route,
            ...
        )
    }   
```

## Setup navigation of each module
Each feature module will create a `navigation` package, with a navigation class to define navigating logic of it own
Override the `buildGraph` function of interface `FeatureGraphBuilder` in `navigation` package of `sdkbase` module 

```kotlin
    class ModuleNameNavigation @Inject constructor() : FeatureGraphBuilder {

    override fun buildGraph(
        navGraphBuilder: NavGraphBuilder,
        navController: NavHostController,
        registeredRoutes: (List<String>) -> Unit
    ) {
        navGraphBuilder.navigation(
            startDestination = [routeName],
            route = IBankMainRouting.ModuleRouteName.ModuleSubRoute1Name.route
        ) {
            composable(
                route = [routeName],
            ) {
                [ModuleScreen](navController)
            }
            ...
        }

        registeredRoutes(
            listOf(
                IBankMainRouting.ModuleRouteName.ModuleSubRoute1Name.route,
                IBankMainRouting.ModuleRouteName.ModuleSubRoute2Name.route,
                ...
            )
        )

    }
}
```

## Inject navigation class of each module to main navigation graph
Each feature module will inject its own navigation class in `di` package

```kotlin
    @Singleton
    @Provides
    @IntoSet
    fun provideModuleNameFeatureGraphBuilder(): FeatureGraphBuilder {
        return ModuleNameNavigation()
    }   
```

## Set `featureGraphBuilder` in `MainActivity` collect all module's public routes to main app navigation graph

```kotlin
    @Inject
    lateinit var featureGraphBuilder: Set<@JvmSuppressWildcards FeatureGraphBuilder>   
```

