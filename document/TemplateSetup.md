# Template setup

This project includes custom file templates for generating MVI components such as `MVITemplate`
To ensure that Android Studio uses the project-specific templates instead of the global (default)
templates, follow the steps below.

- Go to `File > Setting` navigate to `Editor > File and Code Templates`, switch Scheme
  from `Default`  to `Project`
  **Using the Template**
- Right-click on the folder and select **New** from the context menu > `MVITemplate`