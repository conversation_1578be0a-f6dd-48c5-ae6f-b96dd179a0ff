import com.android.build.gradle.BaseExtension
import vn.com.bidv.convention.utils.isAndroidModule
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@Suppress("DSL_SCOPE_VIOLATION") // TODO: Remove once KTIJ-19369 is fixed
plugins {
    alias(libs.plugins.local.android.application)
    alias(libs.plugins.local.android.application.compose)
    alias(libs.plugins.local.android.application.flavor)
    alias(libs.plugins.local.android.application.firebase)
    alias(libs.plugins.local.android.openapi.generate)
    //plugin hilt must at bottom
    alias(libs.plugins.local.android.hilt)
    alias(libs.plugins.local.networkConfig)
    alias(libs.plugins.local.report.jacoco)
//    alias(libs.plugins.local.ibank.protect)
}

networkConfig {
    packageName = "vn.com.bidv.ibank"
    className = "IBankNetworkConfig"
}

fun getVersionName(): String {
    return "2.0.0"
}

fun getVersionCode(): Int {
    return (System.currentTimeMillis() / 10001).toInt()
}

android {
    namespace = "vn.com.bidv.ibank"

    defaultConfig {
        applicationId = "vn.com.bidv.ibank"
        versionCode = getVersionCode()
        versionName = getVersionName()

        testInstrumentationRunner = "android.support.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary = true
        }
    }

    buildTypes {
        val release by getting {
            isMinifyEnabled = false
            setProguardFiles(getCustomProguardFiles("..\\proguard"))
        }
    }

    packaging {
        resources {
            excludes.add("/META-INF/{AL2.0,LGPL2.1}")
            excludes.add("/META-INF/DEPENDENCIES")
            excludes.add("/META-INF/versions/9/OSGI-INF/MANIFEST.MF")
        }
    }

    buildFeatures {
        buildConfig = true
        resValues = true
    }

}

dependencies {
    // Common dependencies
    implementation(libs.androidx.core.splashscreen)

    implementation(libs.androidx.compose.material3.windowSizeClass)
    implementation(libs.androidx.activity.compose)
    implementation(libs.androidx.compose.runtime.tracing)
    implementation(libs.androidx.compose.material3)
    implementation(libs.coil.kt)
    implementation(libs.coil.kt.compose)
    implementation(libs.androidx.hilt.navigation.compose)
    implementation(libs.androidx.lifecycle.runtimeCompose)
    implementation(libs.androidx.lifecycle.viewModelCompose)
    implementation(libs.kotlinx.coroutines.android)


//    implementation(libs.core.secure)
    implementation(project(":core:public:sdkbase"))

    implementation(libs.core.log)
    implementation(libs.core.network)
    implementation(libs.core.ibank.network)
//    implementation(project(":core:private:ibank-network"))
    implementation(libs.core.secure)

    implementation(project(":core:public:designsystem"))
    implementation(project(":core:public:localization"))
    implementationAllModule(rootFile = "feature")
    // TODO: check to remove firebase pom
    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.cloud.messaging)
}

fun DependencyHandler.implementationAllModule(rootFile: String, vararg excludes: String) {
    val dir = rootProject.file(rootFile)
    if (!dir.isDirectory) {
        throw RuntimeException("File can be directory")
    }
    dir.listFiles()?.forEach { file ->
        if (file.isAndroidModule()) {
            val featureName = file.name
            val moduleName = ":${dir.name}:$featureName"
            if (!excludes.contains(featureName)) {
                implementation(project(moduleName))
            }
        }
    }
}

fun BaseExtension.getCustomProguardFiles(path: String, vararg excludes: String): List<File> {
    val proguardDir = file(path)
    if (!proguardDir.isDirectory) {
        throw RuntimeException("File can be directory")
    }
    val defaultProguardFile = getDefaultProguardFile("proguard-android-optimize.txt")
    val list = mutableListOf<File>()
    list.add(defaultProguardFile)
    proguardDir.listFiles()?.forEach {
        if (it.isFile && it.extension == "pro" && !excludes.contains(it.name)) {
            list.add(it)
        }
    }
    return list
}

