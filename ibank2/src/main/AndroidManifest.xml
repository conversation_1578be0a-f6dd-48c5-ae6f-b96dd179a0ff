<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <!--
    Firebase automatically adds the AD_ID permission, even though we don't use it. If you use this
    permission you must declare how you're using it to Google Play, otherwise the app will be
    rejected when publishing it. To avoid this we remove the permission entirely.
    -->
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" tools:node="remove"/>

    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <application
        android:name=".IBankApplication"
        android:enableOnBackInvokedCallback="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.iBank.Splash">
        <profileable android:shell="true" tools:targetApi="q" />

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:configChanges="fontScale|orientation|screenLayout|screenSize|keyboardHidden|layoutDirection|locale"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan"
            tools:ignore="DiscouragedApi,LockedOrientationActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <service
            android:name=".IBankFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/logo_bidv_small" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/notification_color" />

        <!-- Disable Firebase analytics by default. This setting is overwritten for the 'prod' flavor -->
        <meta-data android:name="firebase_analytics_collection_deactivated" android:value="true" />
        <!-- Disable collection of AD_ID for all build variants -->
        <meta-data android:name="google_analytics_adid_collection_enabled" android:value="false" />
    </application>
</manifest>