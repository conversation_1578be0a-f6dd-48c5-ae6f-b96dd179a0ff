package vn.com.bidv.ibank

import android.content.Context
import android.content.res.Configuration
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.MotionEvent
import androidx.activity.SystemBarStyle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.compose.material3.windowsizeclass.ExperimentalMaterial3WindowSizeClassApi
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.res.stringResource
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.NavDestination
import androidx.navigation.NavHostController
import androidx.navigation.compose.rememberNavController
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import vn.com.bidv.common.utils.CollectSideEffect
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.theme.IBankTheme
import vn.com.bidv.ibank.ui.IBankApp
import vn.com.bidv.log.BLogUtil
import vn.com.bidv.sdkbase.navigation.FeatureGraphBuilder
import vn.com.bidv.sdkbase.navigation.LOGIN_SCREEN
import vn.com.bidv.sdkbase.utils.LocaleManager
import vn.com.bidv.sdkbase.utils.NotificationUtils
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants
import javax.inject.Inject
import vn.com.bidv.localization.R as RLocalization

@AndroidEntryPoint
class MainActivity : IBankBaseActivity() {

    @Inject
    lateinit var featureGraphBuilder: Set<@JvmSuppressWildcards FeatureGraphBuilder>
    private val viewModel: MainActivityViewModel by viewModels()
    private var currentDestination: NavDestination? = null

    @OptIn(ExperimentalMaterial3WindowSizeClassApi::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        BLogUtil.d("onCreate Main activity")
        setupSplashScreen()
        // Not restore last state after app is kill by system
        super.onCreate(null)

        // Turn off the decor fitting system windows, which allows us to handle insets,
        // including IME animations, and go edge-to-edge
        // This also sets up the initial system bar style based on the platform theme
        enableEdgeToEdge()

        setContent {
            val darkTheme = shouldUseDarkTheme()

            // Update the edge to edge configuration to match the theme
            // This is the same parameters as the default enableEdgeToEdge call, but we manually
            // resolve whether or not to show dark theme using uiState, since it can be different
            // than the configuration's dark theme value based on the user preference.
            DisposableEffect(darkTheme) {
                enableEdgeToEdge(
                    statusBarStyle = SystemBarStyle.auto(
                        Color.TRANSPARENT,
                        Color.TRANSPARENT,
                    ) { darkTheme },
                    navigationBarStyle = SystemBarStyle.auto(
                        lightScrim,
                        darkScrim,
                    ) { darkTheme },
                )
                onDispose {}
            }
            val iBankNavController: NavHostController = rememberNavController()

            iBankNavController.addOnDestinationChangedListener { _, destination, _ ->
                currentDestination = destination
                if (isLoginScreen(destination)) {
                    viewModel.stopJobCheckScreenTimeout()
                } else {
                    viewModel.startJobCheckScreenTimeout()
                }
            }

            IBankTheme(isPremium = false) {
                IBankApp(
                    navController = iBankNavController,
                    viewModel = viewModel,
                    featureGraphBuilder = featureGraphBuilder
                )
            }
            AppTimeoutPopup(viewModel)
        }
        // notification comes when app is killed
        pushNotification(intent, viewModel)
    }

    override fun attachBaseContext(base: Context) {
        val configuration = Configuration(base.resources.configuration)
        configuration.fontScale = 1.0f
        val context = base.createConfigurationContext(configuration)
        super.attachBaseContext(context)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        // notification coming when app in inactive/background, data included in intent extra
        pushNotification(intent, viewModel)
    }

    private fun pushNotification(intent: Intent, viewModel: MainActivityViewModel) {
        intent.extras?.getString(SdkBaseConstants.NotificationConstants.MORE_INFO)
            ?.let { moreInfo ->
                val notificationData = NotificationUtils.getNotificationData(moreInfo)
                notificationData?.let {
                    viewModel.pushNotification(it)
                }
            }
    }

    private fun setupSplashScreen() {
        val splashScreen = installSplashScreen()
        lifecycleScope.launch {
            lifecycle.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.isInitDataLoading.collect { count ->
                    // Handle the observed value
                    BLogUtil.d("initDataCount: $count")
                }
            }
        }
        splashScreen.setKeepOnScreenCondition {
            viewModel.isInitDataLoading.value
        }

    }

    override fun dispatchTouchEvent(event: MotionEvent): Boolean {
        if (event.action == MotionEvent.ACTION_DOWN) {
            val currentTime = System.currentTimeMillis()
            viewModel.updateLastTimeOnTouch(currentTime)
        }
        return super.dispatchTouchEvent(event)
    }

    override fun onStart() {
        super.onStart()
        if (!isLoginScreen(currentDestination)) {
            viewModel.startJobCheckScreenTimeout()
        }
    }

    override fun onResume() {
        BLogUtil.d("OnResume")
        super.onResume()
        LocaleManager.checkNeedResetLocate(this)
    }

    @Composable
    private fun AppTimeoutPopup(viewModel: MainActivityViewModel) {
        var isAppTimeout by remember { mutableStateOf(false) }
        CollectSideEffect(viewModel.isAppTimeout) {
            isAppTimeout = it
        }
        if (isAppTimeout) {
            viewModel.hidePopupError()
            IBankModalConfirm(
                modalConfirmType = ModalConfirmType.Info,
                title = stringResource(RLocalization.string.thong_bao),
                supportingText = stringResource(RLocalization.string.he_thong_da_tu_dong_dang_xuat_sau_s_phut_khong_co_thao_tac_vui_long_dang_nhap_lai, "${viewModel.getAppTimeout() / (60 * 1000)}"),
                listDialogButtonInfo = listOf(
                    DialogButtonInfo(
                        label = stringResource(RLocalization.string.dong),
                        onClick = {
                            viewModel.sendEvent(MainActivityReducer.MainActivityViewEvent.OnLogout(false))
                        }
                    )
                ),
                isShowIconClose = false,
                onDismissRequest = {
                    viewModel.setAppTimeout(false)
                }
            )
        }

    }

    private fun isLoginScreen(destination: NavDestination?): Boolean {
        val currentRoute = destination?.route
        return currentRoute == LOGIN_SCREEN
    }

    override fun onPause() {
        super.onPause()
        BLogUtil.d("On pause")
    }

    override fun onStop() {
        viewModel.stopJobCheckScreenTimeout()
        super.onStop()
        BLogUtil.d("OnStop")
    }

    override fun onDestroy() {
        BLogUtil.d("OnDestroy")
        super.onDestroy()
    }

}

//@Composable
private fun shouldUseDarkTheme(): Boolean = false

/**
 * The default light scrim, as defined by androidx and the platform:
 * https://cs.android.com/androidx/platform/frameworks/support/+/androidx-main:activity/activity/src/main/java/androidx/activity/EdgeToEdge.kt;l=35-38;drc=27e7d52e8604a080133e8b842db10c89b4482598
 */
private val lightScrim = android.graphics.Color.argb(0xe6, 0xFF, 0xFF, 0xFF)

/**
 * The default dark scrim, as defined by androidx and the platform:
 * https://cs.android.com/androidx/platform/frameworks/support/+/androidx-main:activity/activity/src/main/java/androidx/activity/EdgeToEdge.kt;l=40-44;drc=27e7d52e8604a080133e8b842db10c89b4482598
 */
private val darkScrim = android.graphics.Color.argb(0x80, 0x1b, 0x1b, 0x1b)