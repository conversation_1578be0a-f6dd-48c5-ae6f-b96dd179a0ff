package vn.com.bidv.ibank

import androidx.compose.ui.unit.IntOffset
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CompletableJob
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.common.sharePreference.Storage
import vn.com.bidv.designsystem.component.feedback.snackbar.IBankSnackBarInfo
import vn.com.bidv.feature.common.constants.SmartOTPError
import vn.com.bidv.feature.common.di.AuthProvider
import vn.com.bidv.feature.common.domain.notificationcommon.CommonNotyMarkAsReadUseCase
import vn.com.bidv.feature.common.domain.notificationcommon.TabNotiType
import vn.com.bidv.feature.common.domain.smartotp.SmartOTPKey
import vn.com.bidv.feature.common.domain.smartotp.SmartOTPUseCase
import vn.com.bidv.ibank.domain.CurrentVersionResponseDMO
import vn.com.bidv.ibank.domain.SyncTimeUseCase
import vn.com.bidv.ibank.domain.UpdateVersionUseCase
import vn.com.bidv.log.BLogUtil
import vn.com.bidv.network.NetworkConfig
import vn.com.bidv.sdkbase.data.LocalRepository
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.data.NotificationData
import vn.com.bidv.sdkbase.data.NotificationModel
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class MainActivityViewModel @Inject constructor(
    private val networkConfig: NetworkConfig,
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    override val localRepository: LocalRepository,
    private val updateVersionUseCase: UpdateVersionUseCase,
    private val syncTimeUseCase: SyncTimeUseCase,
    private val smartOTPUseCase: SmartOTPUseCase,
    private val commonNotyMarkAsReadUseCase: CommonNotyMarkAsReadUseCase,
    private val authProvider: AuthProvider
) : ViewModelIBankBase<MainActivityReducer.MainActivityViewState, MainActivityReducer.MainActivityViewEvent, MainActivityReducer.MainActivityViewEffect>(
    initialState = MainActivityReducer.MainActivityViewState(),
    reducer = MainActivityReducer()
) {

    private val reducer = MainActivityReducer()
    private val _events = MutableStateFlow<MainActivityReducer.MainActivityViewEvent?>(null)
    private val _uiState = MutableStateFlow(MainActivityReducer.MainActivityViewState())
    private val _effects = MutableStateFlow<MainActivityReducer.MainActivityViewEffect?>(null)
    val effects: StateFlow<MainActivityReducer.MainActivityViewEffect?> = _effects

    var isInitDataLoading: StateFlow<Boolean>
    val isLoading: StateFlow<Boolean> = localRepository.loadingCount.map { it > 0 }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5_000),
        initialValue = false
    )
    private var _currentVersionResponseDMO = MutableStateFlow<CurrentVersionResponseDMO?>(null)
    val currentVersionResponseDMO: StateFlow<CurrentVersionResponseDMO?> =
        _currentVersionResponseDMO

    private var job: CompletableJob? = null
    private var scope: CoroutineScope? = null
    private var lastTimeOnTouch: Long = 0
    private var _isAppTimeout = MutableStateFlow(false)
    val isAppTimeout: StateFlow<Boolean> = _isAppTimeout

    val errorMessage: Flow<String> = localRepository.errorMessage
    val sessionExpiredMessage: Flow<String> = localRepository.sessionExpiredMessage
    val snackBarMessage: Flow<IBankSnackBarInfo> = localRepository.snackBarInfo
    val notificationModel: Flow<NotificationModel> = localRepository.notificationModel
    val isNeedToSyncTime: Flow<Boolean> = localRepository.isNeedToSyncTime

    init {
        // current only need wait load user info from storage
        val numberInitDataCount = 1
        localRepository.setInitDataCount(numberInitDataCount)

        isInitDataLoading = localRepository.initDataCount.map {
            it > 0
        }.stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5_000),
            initialValue = true
        )
        lastTimeOnTouch = System.currentTimeMillis()

        viewModelScope.launch {
            _events.collect { event ->
                event?.let { it ->
                    val (newState, effect) = reducer.reduce(_uiState.value, it)
                    _uiState.value = newState
                    effect?.let {
                        _effects.value = effect
                        handleEffect(it)
                    }
                }
            }
        }
    }

    private fun handleEffect(effect: MainActivityReducer.MainActivityViewEffect) {
        when (effect) {
            is MainActivityReducer.MainActivityViewEffect.Logout -> {
                logout(effect.isExistApp, localRepository.isLoginSuccess())
            }

            is MainActivityReducer.MainActivityViewEffect.LogoutSuccess -> {

            }
        }
    }

    override fun sendEvent(event: MainActivityReducer.MainActivityViewEvent) {
        _events.value = event
    }

    fun getSnackBarOffset(): IntOffset {
        return localRepository.snackBarOffset.value
    }

    fun checkVersion() {
        if (!localRepository.isNeedToCheckAppVersion) return
        viewModelScope.launch(dispatcher) {
            val result = updateVersionUseCase.updateVersion()
            if (result is DomainResult.Success) {
                localRepository.updateNeedToCheckAppVersion(false)
                _currentVersionResponseDMO.value = result.getSafeData()
            } else if (result is DomainResult.Error) {
                BLogUtil.e("checkVersion error: ${result.errorCode} - ${result.errorMessage}")
            }
        }
    }

    fun resetCheckVersion() {
        _currentVersionResponseDMO.value = null
    }

    fun isLoginSuccess() = localRepository.isLoginSuccess()

    fun pushNotification(notificationData: NotificationData) {
        viewModelScope.launch {
            val isLogin = localRepository.isLoginSuccess()
            localRepository.pushNotification(
                model = NotificationModel(
                    isLogin = isLogin,
                    notificationData = notificationData
                )
            )
        }
    }

    fun cleanNotificationData() {
        viewModelScope.launch {
            localRepository.pushNotification(null)
        }
    }

    fun markNotificationAsRead(notifyId: Long?, displayTab: String?, tabNotiType: TabNotiType) {
        viewModelScope.launch {
            commonNotyMarkAsReadUseCase.markNotyItemRead(
                notifyId = notifyId,
                tabNotiType = tabNotiType,
                displayTab = displayTab
            )
        }
    }

    fun getAppTimeout(): Long {
        return networkConfig.appTimeoutMiniSecond
    }

    fun setAppTimeout(isAppTimeout: Boolean) {
        _isAppTimeout.value = isAppTimeout
    }

    fun startJobCheckScreenTimeout() {
        if (job?.isActive == true) {
            return
        }
        job = SupervisorJob()
        job?.let { job ->
            scope = CoroutineScope(Dispatchers.Default + job)
            scope?.launch {
                while (true) {
                    delay(1000)
                    checkAppTimeout(System.currentTimeMillis())
                }
            }
        }
    }

    fun updateLastTimeOnTouch(currentTime: Long) {
        lastTimeOnTouch = currentTime
    }

    fun syncTimeSmartOtp() {
        viewModelScope.launch(dispatcher) {
            val smartOtp = smartOTPUseCase.getListUserSmartOtpDMO().firstOrNull()
            if (smartOtp != null) {
                val result =
                    syncTimeUseCase.syncTime(smartOtp.userId.toLongOrNull() ?: 0, smartOtp.smToken)
                if (result is DomainResult.Success) {
                    BLogUtil.d("syncTimeSmartOtp success $result")
                    Storage.put(
                        SmartOTPKey.SYNC_TIME,
                        ((result.data?.time ?: 0) - System.currentTimeMillis()).toString()
                    )
                } else if (result is DomainResult.Error) {
                    BLogUtil.e("syncTimeSmartOtp error: ${result.errorCode} - ${result.errorMessage}")
                    when (result.errorCode) {
                        SmartOTPError.SMART_OTP_02.name, SmartOTPError.SMART_OTP_09.name -> {
                            smartOTPUseCase.deleteUserSmartOtp(smartOtp.userId)
                        }
                    }
                }
            }
            localRepository.updateNeedToSyncTime(false)
        }
    }

    private fun checkAppTimeout(currentTime: Long) {
        if (currentTime - lastTimeOnTouch > getAppTimeout()) {
            setAppTimeout(true)
            lastTimeOnTouch = currentTime
            stopJobCheckScreenTimeout()
        }
    }

    fun stopJobCheckScreenTimeout() {
        if (job?.isActive == true && job?.isCompleted == false) {
            job?.cancel()
        }
    }

    override fun onCleared() {
        stopJobCheckScreenTimeout()
        super.onCleared()
    }

    private fun logout(isExistApp: Boolean, loginSuccess: Boolean) {
        if (!loginSuccess) {
            sendEvent(MainActivityReducer.MainActivityViewEvent.OnLogoutSuccess(isExistApp))
            return
        }
        callDomain(
            isListenAllError = true,
            isListenSessionExpired = true,
            onFail = {
                sendEvent(MainActivityReducer.MainActivityViewEvent.OnLogoutSuccess(isExistApp))
            },
            onSuccess = {
                sendEvent(MainActivityReducer.MainActivityViewEvent.OnLogoutSuccess(isExistApp))
            },
        ) {
            authProvider.logout()
        }
    }

    fun clearEffect() {
        _effects.value = null
    }

    fun hidePopupError() {
        viewModelScope.launch {
            localRepository.showPopupError("")
        }
    }
}

