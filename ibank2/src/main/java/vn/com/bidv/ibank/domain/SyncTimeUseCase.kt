package vn.com.bidv.ibank.domain

import vn.com.bidv.ibank.data.SyncTimeRepository
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class SyncTimeUseCase @Inject constructor(
    private val repository: SyncTimeRepository
) {
    suspend fun syncTime(userId: Long, smToken: String): DomainResult<SmartOtpTimeDMO> {
        val result = repository.syncTime(userId, smToken)
        val domain = result.convert(SmartOtpTimeDMO::class.java)
        return domain
    }
}