safeApply(from = "ibank-guard/repositories.gradle.kts")
safeApply(from = "build-protect/catalog.gradle.kts")

safeIncludeBuild("build-logic") {
    dependencySubstitution {
        all {
            if (requested is ProjectComponentSelector && requested.displayName.contains("includedBuildModule")) {
                throw GradleException("Modules from the included build are not allowed in this project: ${requested.displayName}")
            }
        }
    }
}
safeIncludeBuild("build-protect")

pluginManagement {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
        maven {
            url = uri("repository")
        }
    }
}
dependencyResolutionManagement {
    repositoriesMode = RepositoriesMode.FAIL_ON_PROJECT_REPOS
    repositories {
        google()
        mavenCentral()
        maven {
            url = uri("$rootDir\\repository")
        }
    }
}

rootProject.name = "iBank_2"

//enableFeaturePreview("TYPESAFE_PROJECT_ACCESSORS")

include(":ibank2")

val corePublicDir = file("core/public")
corePublicDir.includeAll(prefixModulePath = ":core")

val corePrivateDir = file("core/private")
corePrivateDir.includeAll(prefixModulePath = ":core")

val featureDir = file("feature")
featureDir.includeAll()

featureDir.listFiles { file -> file.isDirectory }?.forEach { feature ->
    val featureName = feature.name
    val libsDir = file("${feature.path}/libs")
    if (libsDir.exists()) {
        libsDir.includeAll(prefixModulePath = ":feature:$featureName")
    }
}

fun File.includeAll(prefixModulePath: String = "", vararg excludes: String) {
    if (!isDirectory) {
        throw RuntimeException("File can be directory")
    }
    listFiles()?.forEach { file ->
        if (file.isAndroidModule()) {
            val featureName = file.name
            val moduleName = "${prefixModulePath}:${name}:$featureName"
            if (!excludes.contains(featureName)) {
                include(moduleName)
            }
        }
    }
}

fun File.isAndroidModule(): Boolean {
    return this.isDirectory && (File(this, "build.gradle").exists() || File(
        this, "build.gradle.kts"
    ).exists()) && File(this, "src").exists()
}

fun safeApply(from: String) {
    try {
        apply(from = from)
    } catch (e: Exception) {
        logger.error("apply $from error")
    }
}

fun safeIncludeBuild(path: String, configuration: Action<ConfigurableIncludedBuild>? = null) {
    val buildDir = file(path)
    if (buildDir.exists() && File(buildDir, "settings.gradle.kts").exists()) {
        if (configuration != null) {
            includeBuild(path, configuration)
        } else {
            includeBuild(path)
        }
    } else {
        logger.error("include build $path not found")
    }
}