openapi: 3.0.1
info:
  title: OpenAPI definition
  version: v0
servers:
- url: http://*************
  description: Generated server url
paths:
  /account/widget/overall/1.0:
    post:
      tags:
      - homepage_overview_widget
      summary: get account balance
      description: get account balance
      operationId: getOverallInfo
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OverallDto'
      parameters: null
  /account/widget/hist/1.0:
    post:
      tags:
      - homepage_overview_widget
      summary: get non-financial payment account history
      description: get non-financial payment account history
      operationId: getAccountHist
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NetworkResponse'
      parameters: null
  /account/widget/detail/1.0:
    post:
      tags:
      - homepage_overview_widget
      summary: Inquiry default account detail
      description: Inquiry default account detail
      operationId: getAccountDetail
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FinAcctDetailDto'
      parameters: null
  /account/widget/balance-by-curr/1.0:
    post:
      tags:
      - homepage_overview_widget
      summary: get account balance
      description: get account balance
      operationId: getAccountBalanceByCurr
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AcctBalanceByCurrDto'
      parameters: null
  /account/statement/periodic/file/list/1.0:
    post:
      tags:
      - homepage_overview_widget
      summary: Get List Periodic Statement File
      description: Get List Periodic Statement File
      operationId: getPeriodicStatementFileList
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PeriodicStatementFileListCriteriaDto'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataListPeriodicStatementFileDto'
      parameters: null
  /account/statement/one-time/list/1.0:
    post:
      tags:
      - homepage_overview_widget
      summary: Get List Onetime Statement Request
      description: Get List Onetime Statement Request
      operationId: getOnetimeStatementRequestList
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OnetimeStatementListCriteriaDto'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataListOnetimeStatementDto'
      parameters: null
  /account/statement/one-time/file/get-url/1.0:
    post:
      tags:
      - homepage_overview_widget
      summary: Get Onetime Statement File URL
      description: Get Onetime Statement File URL
      operationId: getOnetimeStatementRequestFileLink
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OnetimeStatementFileLinkCriteriaDto'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnetimeStatementFileLinkDto'
      parameters: null
  /account/statement/one-time/create/1.0:
    post:
      tags:
      - homepage_overview_widget
      summary: Create New Onetime Statement Request
      description: Create New Onetime Statement Request
      operationId: createOnetimeStatementRequest
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OnetimeStatementCreateCriteriaDto'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnetimeStatementCreateDto'
      parameters: null
  /account/statement/account/list/1.0:
    post:
      tags:
      - homepage_overview_widget
      summary: Query Statement Account List
      description: Query Statement Account List
      operationId: getStatementAccountList
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StatementAccountListCriteriaDto'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataListStatementAccountListDto'
      parameters: null
  /account/payment-account/trans-his/list/1.0:
    post:
      tags:
      - homepage_overview_widget
      summary: Get Payment Account History
      description: Get Payment Account History
      operationId: accountPaymentHisList
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentAccountHistoryCriteriaDto'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataListPaymentAccountHistoryDto'
      parameters: null
  /account/payment-account/list/1.0:
    post:
      tags:
      - homepage_overview_widget
      summary: Query Payment Account List
      description: Query Payment Account List
      operationId: paymentAccountListInquiry
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentAccountListCriteriaDto'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataListPaymentAccountListDto'
      parameters: null
  /account/payment-account/export/1.0:
    post:
      tags:
      - homepage_overview_widget
      summary: Export Payment Account List
      description: Export Payment Account List
      operationId: exportPaymentAccount
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentAccountListExportCriteriaDto'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FileInfoDto'
      parameters: null
  /account/payment-account/detail/1.0:
    post:
      tags:
      - homepage_overview_widget
      summary: Get Payment Account Detail
      description: Get Payment Account Detail
      operationId: paymentAccountDetailInquiry
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentAccountDetailCriteriaDto'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentAccountDetailDto'
      parameters: null
  /account/loan-account/trans-his/list/1.0:
    post:
      tags:
      - homepage_overview_widget
      summary: Get Loan account history
      description: Get Loan account history
      operationId: loanAcctHistoryListInquiry
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoanAcctHistoryCriteriaDto'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataListLoanAcctHistoryDto'
      parameters: null
  /account/loan-account/list/1.0:
    post:
      tags:
      - homepage_overview_widget
      summary: Get Loan account list
      description: Get Loan account list
      operationId: loanAccountListInquiry
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoanAccountCriteriaDto'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataListLoanAccountDto'
      parameters: null
  /account/loan-account/export/1.0:
    post:
      tags:
      - homepage_overview_widget
      summary: Export loan account list
      description: Export loan account list
      operationId: loanAccountListExport
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoanAccountExportCriteriaDto'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FileInfoDto'
      parameters: null
  /account/loan-account/detail/export/1.0:
    post:
      tags:
      - homepage_overview_widget
      summary: Export loan account detail
      description: Export loan account detail
      operationId: loanAccountDetailExport
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoanAccountDetailCriteriaDto'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FileInfoDto'
      parameters: null
  /account/loan-account/detail/1.0:
    post:
      tags:
      - homepage_overview_widget
      summary: Inquiry loan account detail
      description: Inquiry loan account detail
      operationId: loanAccountDetailInquiry
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoanAccountDetailCriteriaDto'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoanAccountDetailDto'
      parameters: null
  /account/guarantee-account/list/1.0:
    post:
      tags:
      - homepage_overview_widget
      summary: Get guarantee account list
      description: Get guarantee account list
      operationId: guaranteeAccountListInquiry
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GuaranteeAccountCriteriaDto'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataListGuaranteeAccountDto'
      parameters: null
  /account/guarantee-account/export/1.0:
    post:
      tags:
      - homepage_overview_widget
      summary: Export guarantee account list
      description: Export guarantee account list
      operationId: guaranteeAccountListExport
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GuaranteeAccountExportCriteriaDto'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FileInfoDto'
      parameters: null
  /account/deposit-account/trans-his/list/1.0:
    post:
      tags:
      - homepage_overview_widget
      summary: Get Term Deposit Account Transactions History
      description: Get Term Deposit Account Transactions History
      operationId: termDepositAccountHistoryInquiry
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DepositAccountTxnHistoryCriteriaDto'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataListDepositAccountTxnHistoryDto'
      parameters: null
  /account/deposit-account/list/1.0:
    post:
      tags:
      - homepage_overview_widget
      summary: Term Deposit Account List
      description: Term Deposit Account List
      operationId: termDepositAccountListInquiry
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DepositAccountInquiryCriteriaDto'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataListDepositAccountInquiryDto'
      parameters: null
  /account/deposit-account/export/1.0:
    post:
      tags:
      - homepage_overview_widget
      summary: Export Term Deposit Account List
      description: Export Term Deposit Account List
      operationId: exportTermDepositAccount
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DepositAccountListExportCriteriaDto'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FileInfoDto'
      parameters: null
  /account/deposit-account/detail/1.0:
    post:
      tags:
      - homepage_overview_widget
      summary: Get Term Deposit Account Detail
      description: Get Term Deposit Account Detail
      operationId: termDepositAccountDetailInquiry
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DepositAccountDetailInquiryCriteriaDto'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DepositAccountDetailDto'
      parameters: null
components:
  schemas:
    OverallDto:
      type: object
      properties:
        totalDepositBal:
          type: number
        paymentBal:
          type: number
        termDepositBal:
          type: number
        totalLoanBal:
          type: number
        shortTermBal:
          type: number
        midLongTermBal:
          type: number
        overdraftBal:
          type: number
        latestUpdated:
          type: string
      description: Data
    ResponseError:
      type: object
      properties:
        errorCode:
          type: string
        errorDesc:
          type: string
        refVal:
          type: object
      description: Addition Error List
    ResultOverallDto:
      type: object
      properties:
        status:
          type: integer
          description: Result status
          format: int32
        code:
          type: string
          description: Result code detail
        message:
          type: string
          description: Result code description
        errors:
          type: array
          description: Addition Error List
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
        data:
          $ref: '#/components/schemas/OverallDto'
    ResultListTransaction:
      type: object
      properties:
        status:
          type: integer
          description: Result status
          format: int32
        code:
          type: string
          description: Result code detail
        message:
          type: string
          description: Result code description
        errors:
          type: array
          description: Addition Error List
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
        data:
          type: array
          description: Data
          items:
            $ref: '#/components/schemas/Transaction'
    Transaction:
      type: object
      properties:
        acctNo:
          type: string
        tranAmount:
          type: string
        debitAmount:
          type: string
        creditAmount:
          type: string
        creditOrDebit:
          type: string
        dat6:
          type: string
        tranDate:
          type: string
        tranTime:
          type: string
        remark:
          type: string
        postingDate:
          type: string
        postingOrder:
          type: string
        fileIndicator:
          type: string
        nextRunbal:
          type: string
        moreRecordIndicator:
          type: string
        refNo:
          type: string
        trantime:
          type: string
          writeOnly: true
      description: Data
    FinAcctDetailDto:
      type: object
      properties:
        accountNo:
          type: string
        accountName:
          type: string
        availableBalance:
          type: number
        currCode:
          type: string
      description: Data
    ResultFinAcctDetailDto:
      type: object
      properties:
        status:
          type: integer
          description: Result status
          format: int32
        code:
          type: string
          description: Result code detail
        message:
          type: string
          description: Result code description
        errors:
          type: array
          description: Addition Error List
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
        data:
          $ref: '#/components/schemas/FinAcctDetailDto'
    AcctBalanceByCurrDto:
      type: object
      properties:
        balanceByCurr:
          type: object
          additionalProperties:
            type: number
      description: Data
    ResultAcctBalanceByCurrDto:
      type: object
      properties:
        status:
          type: integer
          description: Result status
          format: int32
        code:
          type: string
          description: Result code detail
        message:
          type: string
          description: Result code description
        errors:
          type: array
          description: Addition Error List
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
        data:
          $ref: '#/components/schemas/AcctBalanceByCurrDto'
    Filter:
      type: object
      properties:
        operator:
          type: string
          description: ct/ eq/ neq/ gt/ gte/ lt/ lte
        field:
          type: string
        value:
          type: string
    Order:
      type: object
      properties:
        field:
          type: string
        direction:
          type: string
    Page:
      type: object
      properties:
        pageSize:
          minimum: 1
          type: integer
          description: Row number/ page, min = 1
          format: int32
        pageNum:
          minimum: 1
          type: integer
          description: Page index (start from 1), min = 1
          format: int32
        getTotal:
          type: boolean
          description: Get total record size flag
    PeriodicStatementFileListCriteriaDto:
      type: object
      properties:
        orders:
          type: array
          items:
            $ref: '#/components/schemas/Order'
        filters:
          type: array
          items:
            $ref: '#/components/schemas/Filter'
        page:
          $ref: '#/components/schemas/Page'
        keyword:
          type: string
        stmtFromDate:
          type: string
        stmtToDate:
          type: string
    DataListPeriodicStatementFileDto:
      type: object
      properties:
        items:
          type: array
          description: List data
          items:
            $ref: '#/components/schemas/PeriodicStatementFileDto'
        total:
          type: integer
          description: Total size
          format: int64
      description: Data
    PeriodicStatementFileDto:
      type: object
      properties:
        id:
          type: string
        stmtAcc:
          type: string
        stmtFromDate:
          type: string
        stmtToDate:
          type: string
        updatedDate:
          type: string
        fileUrl:
          type: string
      description: List data
    ResultListPeriodicStatementFileDto:
      type: object
      properties:
        status:
          type: integer
          description: Result status
          format: int32
        code:
          type: string
          description: Result code detail
        message:
          type: string
          description: Result code description
        errors:
          type: array
          description: Addition Error List
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
        data:
          $ref: '#/components/schemas/DataListPeriodicStatementFileDto'
    OnetimeStatementListCriteriaDto:
      type: object
      properties:
        orders:
          type: array
          items:
            $ref: '#/components/schemas/Order'
        filters:
          type: array
          items:
            $ref: '#/components/schemas/Filter'
        page:
          $ref: '#/components/schemas/Page'
        keyword:
          maxLength: 50
          minLength: 0
          type: string
    DataListOnetimeStatementDto:
      type: object
      properties:
        items:
          type: array
          description: List data
          items:
            $ref: '#/components/schemas/OnetimeStatementDto'
        total:
          type: integer
          description: Total size
          format: int64
      description: Data
    OnetimeStatementDto:
      type: object
      properties:
        id:
          type: string
        status:
          type: string
        cif:
          type: string
        accName:
          type: string
        accNo:
          type: string
        accType:
          type: string
        createdDate:
          type: string
        updatedDate:
          type: string
        stmtFromDate:
          type: string
        stmtToDate:
          type: string
      description: List data
    ResultListOnetimeStatementDto:
      type: object
      properties:
        status:
          type: integer
          description: Result status
          format: int32
        code:
          type: string
          description: Result code detail
        message:
          type: string
          description: Result code description
        errors:
          type: array
          description: Addition Error List
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
        data:
          $ref: '#/components/schemas/DataListOnetimeStatementDto'
    OnetimeStatementFileLinkCriteriaDto:
      type: object
      properties:
        id:
          type: string
    OnetimeStatementFileLinkDto:
      type: object
      properties:
        fileUrl:
          type: string
      description: Data
    ResultOnetimeStatementFileLinkDto:
      type: object
      properties:
        status:
          type: integer
          description: Result status
          format: int32
        code:
          type: string
          description: Result code detail
        message:
          type: string
          description: Result code description
        errors:
          type: array
          description: Addition Error List
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
        data:
          $ref: '#/components/schemas/OnetimeStatementFileLinkDto'
    OnetimeStatementCreateCriteriaDto:
      required:
      - accNo
      - accType
      - fileType
      - stmtFromDate
      - stmtToDate
      type: object
      properties:
        accNo:
          type: string
        accType:
          pattern: DDA|CD|LN
          type: string
        stmtFromDate:
          type: string
        stmtToDate:
          type: string
        fileType:
          pattern: MT940|PDF|Excel
          type: string
    OnetimeStatementCreateDto:
      type: object
      properties:
        id:
          type: string
      description: Data
    ResultOnetimeStatementCreateDto:
      type: object
      properties:
        status:
          type: integer
          description: Result status
          format: int32
        code:
          type: string
          description: Result code detail
        message:
          type: string
          description: Result code description
        errors:
          type: array
          description: Addition Error List
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
        data:
          $ref: '#/components/schemas/OnetimeStatementCreateDto'
    StatementAccountListCriteriaDto:
      required:
      - accType
      type: object
      properties:
        accType:
          pattern: DDA|CD|LN
          type: string
        keyword:
          maxLength: 50
          minLength: 0
          type: string
    DataListStatementAccountListDto:
      type: object
      properties:
        items:
          type: array
          description: List data
          items:
            $ref: '#/components/schemas/StatementAccountListDto'
        total:
          type: integer
          description: Total size
          format: int64
      description: Data
    ResultListStatementAccountListDto:
      type: object
      properties:
        status:
          type: integer
          description: Result status
          format: int32
        code:
          type: string
          description: Result code detail
        message:
          type: string
          description: Result code description
        errors:
          type: array
          description: Addition Error List
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
        data:
          $ref: '#/components/schemas/DataListStatementAccountListDto'
    StatementAccountListDto:
      type: object
      properties:
        accNo:
          type: string
        accName:
          type: string
      description: List data
    PaymentAccountHistoryCriteriaDto:
      required:
      - accountNo
      type: object
      properties:
        accountNo:
          pattern: ^[0-9]*$
          type: string
        startDate:
          type: string
        endDate:
          type: string
        moreRecordIndicator:
          pattern: ^[YN]$
          type: string
        postingDate:
          type: string
        postingOrder:
          type: string
        nextRUNBAL:
          type: string
        noOfRecords:
          maximum: **********
          minimum: 1
          type: integer
          format: int32
    DataListPaymentAccountHistoryDto:
      type: object
      properties:
        items:
          type: array
          description: List data
          items:
            $ref: '#/components/schemas/PaymentAccountHistoryDto'
        total:
          type: integer
          description: Total size
          format: int64
      description: Data
    PaymentAccountHistoryDto:
      type: object
      properties:
        coreRefNo:
          type: string
        transTime:
          type: string
        debitAmount:
          type: number
        creditAmount:
          type: number
        remark:
          type: string
        nextRUNBAL:
          type: string
        postingDate:
          type: string
        postingOrder:
          type: string
        moreRecordIndicator:
          type: string
      description: List data
    ResultListPaymentAccountHistoryDto:
      type: object
      properties:
        status:
          type: integer
          description: Result status
          format: int32
        code:
          type: string
          description: Result code detail
        message:
          type: string
          description: Result code description
        errors:
          type: array
          description: Addition Error List
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
        data:
          $ref: '#/components/schemas/DataListPaymentAccountHistoryDto'
    PaymentAccountListCriteriaDto:
      type: object
      properties:
        keyword:
          maxLength: 100
          minLength: 0
          type: string
    DataListPaymentAccountListDto:
      type: object
      properties:
        items:
          type: array
          description: List data
          items:
            $ref: '#/components/schemas/PaymentAccountListDto'
        total:
          type: integer
          description: Total size
          format: int64
      description: Data
    PaymentAccountListDto:
      type: object
      properties:
        accountNo:
          type: string
        accountName:
          type: string
        status:
          type: string
        currCode:
          type: string
        balance:
          type: number
        availableBalance:
          type: number
        openedDate:
          type: string
      description: List data
    ResultListPaymentAccountListDto:
      type: object
      properties:
        status:
          type: integer
          description: Result status
          format: int32
        code:
          type: string
          description: Result code detail
        message:
          type: string
          description: Result code description
        errors:
          type: array
          description: Addition Error List
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
        data:
          $ref: '#/components/schemas/DataListPaymentAccountListDto'
    PaymentAccountListExportCriteriaDto:
      type: object
      properties:
        keyword:
          maxLength: 100
          minLength: 0
          type: string
        currCode:
          pattern: ^[a-zA-Z]*$
          type: string
    FileInfoDto:
      type: object
      properties:
        filename:
          type: string
        fileUrl:
          type: string
      description: Data
    ResultFileInfoDto:
      type: object
      properties:
        status:
          type: integer
          description: Result status
          format: int32
        code:
          type: string
          description: Result code detail
        message:
          type: string
          description: Result code description
        errors:
          type: array
          description: Addition Error List
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
        data:
          $ref: '#/components/schemas/FileInfoDto'
    PaymentAccountDetailCriteriaDto:
      required:
      - accountNo
      type: object
      properties:
        accountNo:
          pattern: ^[0-9]*$
          type: string
    PaymentAccountDetailDto:
      type: object
      properties:
        accountNo:
          type: string
        accountName:
          type: string
        accountRelation:
          type: string
        accountRestriction:
          type: string
        status:
          type: string
        currCode:
          type: string
        branchName:
          type: string
        overdraftAcctNo:
          type: string
        availableBalance:
          type: number
        balance:
          type: number
        overdraftLimit:
          type: number
        holdBalance:
          type: number
      description: Data
    ResultPaymentAccountDetailDto:
      type: object
      properties:
        status:
          type: integer
          description: Result status
          format: int32
        code:
          type: string
          description: Result code detail
        message:
          type: string
          description: Result code description
        errors:
          type: array
          description: Addition Error List
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
        data:
          $ref: '#/components/schemas/PaymentAccountDetailDto'
    LoanAcctHistoryCriteriaDto:
      required:
      - accountNo
      type: object
      properties:
        accountNo:
          pattern: ^[0-9]*$
          type: string
        postingOrder:
          type: string
        noOfRecords:
          maximum: **********
          minimum: 1
          type: integer
          format: int32
    DataListLoanAcctHistoryDto:
      type: object
      properties:
        items:
          type: array
          description: List data
          items:
            $ref: '#/components/schemas/LoanAcctHistoryDto'
        total:
          type: integer
          description: Total size
          format: int64
      description: Data
    LoanAcctHistoryDto:
      type: object
      properties:
        effDate:
          type: string
        increaseOriginalAmount:
          type: number
        reducedOriginalAmount:
          type: number
        reducedInterestAmount:
          type: number
        penaltyFee:
          type: number
        endBalance:
          type: number
        remark:
          type: string
        postingDate:
          type: string
        moreRecordIndicator:
          type: string
      description: List data
    ResultListLoanAcctHistoryDto:
      type: object
      properties:
        status:
          type: integer
          description: Result status
          format: int32
        code:
          type: string
          description: Result code detail
        message:
          type: string
          description: Result code description
        errors:
          type: array
          description: Addition Error List
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
        data:
          $ref: '#/components/schemas/DataListLoanAcctHistoryDto'
    LoanAccountCriteriaDto:
      type: object
      properties:
        keyword:
          maxLength: 100
          minLength: 0
          type: string
    DataListLoanAccountDto:
      type: object
      properties:
        items:
          type: array
          description: List data
          items:
            $ref: '#/components/schemas/LoanAccountDto'
        total:
          type: integer
          description: Total size
          format: int64
      description: Data
    LoanAccountDto:
      type: object
      properties:
        accountNo:
          type: string
        accountName:
          type: string
        maturityDate:
          type: string
        interestRate:
          type: number
        currentBalance:
          type: number
        ccy:
          type: string
      description: List data
    ResultListLoanAccountDto:
      type: object
      properties:
        status:
          type: integer
          description: Result status
          format: int32
        code:
          type: string
          description: Result code detail
        message:
          type: string
          description: Result code description
        errors:
          type: array
          description: Addition Error List
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
        data:
          $ref: '#/components/schemas/DataListLoanAccountDto'
    LoanAccountExportCriteriaDto:
      type: object
      properties:
        keyword:
          maxLength: 100
          minLength: 0
          type: string
        ccy:
          pattern: ^[a-zA-Z]*$
          type: string
    LoanAccountDetailCriteriaDto:
      required:
      - accountNo
      type: object
      properties:
        accountNo:
          pattern: ^[0-9]*$
          type: string
    LoanAccountDetailDto:
      type: object
      properties:
        accountNo:
          type: string
        accountName:
          type: string
        ccy:
          type: string
        currentPrincipalBalance:
          type: number
        firstReleaseDate:
          type: string
        lastReleaseDate:
          type: string
        interestRate:
          type: number
        term:
          type: string
        nextPymtDueDate:
          type: string
        nextIntPymtDueDate:
          type: string
        originalAmount:
          type: number
        lateInterestCharge:
          type: number
        latePrincipalCharge:
          type: number
        accruedInterest:
          type: number
        maturityDate:
          type: string
        loanType:
          type: string
      description: Data
    ResultLoanAccountDetailDto:
      type: object
      properties:
        status:
          type: integer
          description: Result status
          format: int32
        code:
          type: string
          description: Result code detail
        message:
          type: string
          description: Result code description
        errors:
          type: array
          description: Addition Error List
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
        data:
          $ref: '#/components/schemas/LoanAccountDetailDto'
    GuaranteeAccountCriteriaDto:
      type: object
      properties:
        keyword:
          maxLength: 100
          minLength: 0
          type: string
    DataListGuaranteeAccountDto:
      type: object
      properties:
        items:
          type: array
          description: List data
          items:
            $ref: '#/components/schemas/GuaranteeAccountDto'
        total:
          type: integer
          description: Total size
          format: int64
      description: Data
    GuaranteeAccountDto:
      type: object
      properties:
        refNo:
          type: string
        beneficiaryParty:
          type: string
        dueDate:
          type: string
        originalAmount:
          type: number
        currentAmount:
          type: number
        ccy:
          type: string
        releaseDate:
          type: string
        guaranteedParty:
          type: string
        guaranteeType:
          type: string
        guaranteeBranch:
          type: string
        guaranteeStatus:
          type: string
      description: List data
    ResultListGuaranteeAccountDto:
      type: object
      properties:
        status:
          type: integer
          description: Result status
          format: int32
        code:
          type: string
          description: Result code detail
        message:
          type: string
          description: Result code description
        errors:
          type: array
          description: Addition Error List
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
        data:
          $ref: '#/components/schemas/DataListGuaranteeAccountDto'
    GuaranteeAccountExportCriteriaDto:
      type: object
      properties:
        keyword:
          maxLength: 100
          minLength: 0
          type: string
        ccy:
          type: string
    DepositAccountTxnHistoryCriteriaDto:
      required:
      - accountNo
      type: object
      properties:
        accountNo:
          pattern: ^[0-9]*$
          type: string
        moreRecordIndicator:
          pattern: ^[YN]$
          type: string
        postingDate:
          type: string
        postingOrder:
          type: string
        nextRUNBAL:
          type: string
        noOfRecords:
          maximum: **********
          minimum: 1
          type: integer
          format: int32
    DataListDepositAccountTxnHistoryDto:
      type: object
      properties:
        items:
          type: array
          description: List data
          items:
            $ref: '#/components/schemas/DepositAccountTxnHistoryDto'
        total:
          type: integer
          description: Total size
          format: int64
      description: Data
    DepositAccountTxnHistoryDto:
      type: object
      properties:
        effDate:
          type: string
        nextRUNBAL:
          type: number
        earlyRepaymentPenaltyInterest:
          type: number
        reducedOriginalAmount:
          type: number
        reducedInterestAmount:
          type: number
        increaseOriginalAmount:
          type: number
        increaseInterestAmount:
          type: number
        remark:
          type: string
        transDesc:
          type: string
        postingDate:
          type: string
        postingOrder:
          type: string
        moreRecordIndicator:
          type: string
      description: List data
    ResultListDepositAccountTxnHistoryDto:
      type: object
      properties:
        status:
          type: integer
          description: Result status
          format: int32
        code:
          type: string
          description: Result code detail
        message:
          type: string
          description: Result code description
        errors:
          type: array
          description: Addition Error List
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
        data:
          $ref: '#/components/schemas/DataListDepositAccountTxnHistoryDto'
    DepositAccountInquiryCriteriaDto:
      type: object
      properties:
        keyword:
          maxLength: 100
          minLength: 0
          type: string
    DataListDepositAccountInquiryDto:
      type: object
      properties:
        items:
          type: array
          description: List data
          items:
            $ref: '#/components/schemas/DepositAccountInquiryDto'
        total:
          type: integer
          description: Total size
          format: int64
      description: Data
    DepositAccountInquiryDto:
      type: object
      properties:
        accountNo:
          type: string
        accountName:
          type: string
        currCode:
          type: string
        status:
          type: string
        dueDate:
          type: string
        interestRate:
          type: number
        currentBalance:
          type: number
      description: List data
    ResultListDepositAccountInquiryDto:
      type: object
      properties:
        status:
          type: integer
          description: Result status
          format: int32
        code:
          type: string
          description: Result code detail
        message:
          type: string
          description: Result code description
        errors:
          type: array
          description: Addition Error List
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
        data:
          $ref: '#/components/schemas/DataListDepositAccountInquiryDto'
    DepositAccountListExportCriteriaDto:
      type: object
      properties:
        keyword:
          maxLength: 100
          minLength: 0
          type: string
        currCode:
          pattern: ^[a-zA-Z]*$
          type: string
    DepositAccountDetailInquiryCriteriaDto:
      required:
      - accountNo
      type: object
      properties:
        accountNo:
          pattern: ^[0-9]*$
          type: string
    DepositAccountDetailDto:
      type: object
      properties:
        accountNo:
          type: string
        accountName:
          type: string
        maturityDate:
          type: string
        timeDepositTerm:
          type: string
        timeDepositTermCode:
          type: string
        ledgerBalance:
          type: number
        interestRate:
          type: number
        dateEntered:
          type: string
        maturityDisposition:
          type: string
        maturityTrfAccountNo:
          type: string
        accountRelation:
          type: string
        interestTerm:
          type: string
        originalAmount:
          type: number
        holdAmount:
          type: number
        withdrawableInterest:
          type: number
      description: Data
    ResultDepositAccountDetailDto:
      type: object
      properties:
        status:
          type: integer
          description: Result status
          format: int32
        code:
          type: string
          description: Result code detail
        message:
          type: string
          description: Result code description
        errors:
          type: array
          description: Addition Error List
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
        data:
          $ref: '#/components/schemas/DepositAccountDetailDto'
tags:
- homepage_overview_widget
formated: 1
