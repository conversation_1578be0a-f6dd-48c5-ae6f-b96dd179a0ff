openapi: 3.0.1
info:
  title: OpenAPI definition
  version: v0
servers:
- url: http://**************
  description: Generated server url
paths:
  /dashboard/widget/txn-count/list/1.0:
    post:
      tags:
      - homepage_account_balance
      summary: API count txn widget
      operationId: getTxnCount
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataListTxnCountResponseDto'
      parameters: null
  /dashboard/widget/txn-count-checker/list/1.0:
    post:
      tags:
      - homepage_account_balance
      summary: API count txn checker
      operationId: getTxnCountPendingApprovalChecker
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataListTxnCountResponseDto'
      parameters: null
  /dashboard/widget/non-fin-account/detail/1.0:
    post:
      tags:
      - homepage_account_balance
      summary: get account balance detail
      description: get account balance detail
      operationId: getAccountBalance
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AccountDetailCriteriaDto'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountBalanceDto'
      parameters: null
  /dashboard/widget/non-fin-account/default/1.0:
    post:
      tags:
      - homepage_account_balance
      summary: get default non-financial account
      description: get default non-financial account
      operationId: getNonFinAccountDefault
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AccountDefaultCriteriaDto'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountDto'
      parameters: null
  /dashboard/widget/dda-account-balance/by-currency/1.0:
    post:
      tags:
      - homepage_account_balance
      summary: get total account balance by currency
      description: get total account balance by currency
      operationId: getListAccountBalanceByCurr
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BalanceCurrenyDto'
      parameters: null
  /dashboard/widget/account-balance/overall/1.0:
    post:
      tags:
      - homepage_account_balance
      summary: get overall account balance
      description: get overall account balance
      operationId: getOverallAcctBalance
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OverallAcctBalanceCriteriaDto'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataListOverallBalanceDto'
      parameters: null
components:
  schemas:
    DataListTxnCountResponseDto:
      type: object
      properties:
        items:
          type: array
          description: List data
          items:
            $ref: '#/components/schemas/TxnCountResponseDto'
        total:
          type: integer
          description: Total size
          format: int64
      description: Data
    ResponseError:
      type: object
      properties:
        errorCode:
          type: string
        errorDesc:
          type: string
        refVal:
          type: object
      description: Addition Error List
    ResultListTxnCountResponseDto:
      type: object
      properties:
        status:
          type: integer
          description: Result status
          format: int32
        code:
          type: string
          description: Result code detail
        message:
          type: string
          description: Result code description
        errors:
          type: array
          description: Addition Error List
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
        data:
          $ref: '#/components/schemas/DataListTxnCountResponseDto'
    TxnCountDto:
      type: object
      properties:
        txnCode:
          type: string
        menuCode:
          type: string
        count:
          type: integer
          format: int32
        status:
          type: string
        priority:
          type: integer
          format: int32
    TxnCountResponseDto:
      type: object
      properties:
        txnCountDtos:
          type: array
          items:
            $ref: '#/components/schemas/TxnCountDto'
        code:
          type: string
        total:
          type: integer
          format: int32
      description: List data
    AccountDetailCriteriaDto:
      type: object
      properties:
        accountNo:
          type: string
    AccountBalanceDto:
      type: object
      properties:
        accountNo:
          type: string
        balance:
          type: number
        currCode:
          type: string
      description: Data
    ResultAccountBalanceDto:
      type: object
      properties:
        status:
          type: integer
          description: Result status
          format: int32
        code:
          type: string
          description: Result code detail
        message:
          type: string
          description: Result code description
        errors:
          type: array
          description: Addition Error List
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
        data:
          $ref: '#/components/schemas/AccountBalanceDto'
    AccountDefaultCriteriaDto:
      type: object
      properties:
        viewTransHist:
          type: string
    AccountDto:
      type: object
      properties:
        accountNo:
          type: string
        accountName:
          type: string
        ccy:
          type: string
        productCode:
          type: string
        purposeCode:
          type: string
        accRelation:
          type: string
        accType:
          type: string
        transactions:
          type: array
          items:
            $ref: '#/components/schemas/TransactionDto'
        default:
          type: boolean
      description: Data
    ResultAccountDto:
      type: object
      properties:
        status:
          type: integer
          description: Result status
          format: int32
        code:
          type: string
          description: Result code detail
        message:
          type: string
          description: Result code description
        errors:
          type: array
          description: Addition Error List
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
        data:
          $ref: '#/components/schemas/AccountDto'
    TransactionDto:
      type: object
      properties:
        acctNo:
          type: string
        ccy:
          type: string
        tranDate:
          type: string
        remark:
          type: string
        tranTime:
          type: string
        refNo:
          type: string
        tranAmount:
          type: string
        creditOrDebit:
          type: string
        availableBalance:
          type: string
    BalanceCurrDetailDto:
      type: object
      properties:
        currCode:
          type: string
        totalBalance:
          type: number
        totalBalanceConvert:
          type: number
        per:
          type: number
          format: float
    BalanceCurrenyDto:
      type: object
      properties:
        balCurrList:
          type: array
          items:
            $ref: '#/components/schemas/BalanceCurrDetailDto'
        updatedDate:
          type: string
      description: Data
    ResultBalanceCurrenyDto:
      type: object
      properties:
        status:
          type: integer
          description: Result status
          format: int32
        code:
          type: string
          description: Result code detail
        message:
          type: string
          description: Result code description
        errors:
          type: array
          description: Addition Error List
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
        data:
          $ref: '#/components/schemas/BalanceCurrenyDto'
    OverallAcctBalanceCriteriaDto:
      required:
      - type
      type: object
      properties:
        type:
          type: string
    BalanceAccTypeDto:
      type: object
      properties:
        accType:
          type: string
        totalBalance:
          type: number
    DataListOverallBalanceDto:
      type: object
      properties:
        items:
          type: array
          description: List data
          items:
            $ref: '#/components/schemas/OverallBalanceDto'
        total:
          type: integer
          description: Total size
          format: int64
      description: Data
    OverallBalanceDto:
      type: object
      properties:
        type:
          type: string
        totalBalance:
          type: number
        balAccTypeList:
          type: array
          items:
            $ref: '#/components/schemas/BalanceAccTypeDto'
        updatedDate:
          type: string
      description: List data
    ResultListOverallBalanceDto:
      type: object
      properties:
        status:
          type: integer
          description: Result status
          format: int32
        code:
          type: string
          description: Result code detail
        message:
          type: string
          description: Result code description
        errors:
          type: array
          description: Addition Error List
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
        data:
          $ref: '#/components/schemas/DataListOverallBalanceDto'
tags:
- homepage_account_balance
formated: 1
