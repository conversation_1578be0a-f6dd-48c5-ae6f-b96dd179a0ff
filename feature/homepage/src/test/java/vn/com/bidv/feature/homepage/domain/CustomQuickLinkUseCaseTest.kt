package vn.com.bidv.feature.homepage.domain
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.Assert.assertEquals
import org.junit.Test
import vn.com.bidv.feature.common.data.utilities.model.ResultString
import vn.com.bidv.feature.homepage.data.HomepageRepository
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.sdkbase.data.LocalRepository
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.ResourceProvider

class CustomQuickLinkUseCaseTest {

    private val mockResourceProvider: ResourceProvider = mockk()
    private val mockHomepageRepository: HomepageRepository = mockk()
    private val mockLocalRepository: LocalRepository = mockk(relaxed = true)
    private val useCase = CustomQuickLinkUseCase(mockResourceProvider, mockHomepageRepository, mockLocalRepository)

    @Test
    fun `updateUserQuickLink should return success result`() = runBlocking {
        // Arrange
        val quickLinkList = listOf("link1", "link2")
        val expectedMessage = "Update successful"

        coEvery { mockHomepageRepository.updateUserQuickLink(quickLinkList) } returns NetworkResult.Success(
            ResultString(message = expectedMessage)
        )

        // Act
        val result = useCase.updateUserQuickLink(quickLinkList)

        // Assert
        assert(result is DomainResult.Success)
        assertEquals(expectedMessage, (result as DomainResult.Success).data)
    }

    @Test
    fun `updateUserQuickLink should return failure result when repository fails`() = runBlocking {
        // Arrange
        val quickLinkList = listOf("link1", "link2")
        val errorMessage = "Failed to update quick link info"
        val errorCode = "E002"

        coEvery { mockHomepageRepository.updateUserQuickLink(quickLinkList) } returns NetworkResult.Error(
            errorCode = errorCode,
            errorMessage = errorMessage
        )

        // Act
        val result = useCase.updateUserQuickLink(quickLinkList)

        // Assert
        assert(result is DomainResult.Error)
        assertEquals(errorCode, (result as DomainResult.Error).errorCode)
        assertEquals(errorMessage, result.errorMessage)
    }
}
