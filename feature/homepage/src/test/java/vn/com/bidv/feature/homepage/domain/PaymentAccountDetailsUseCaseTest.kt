package vn.com.bidv.feature.homepage.domain

import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.Assert.assertEquals
import org.junit.Test
import vn.com.bidv.feature.common.data.MasterDataRepository
import vn.com.bidv.feature.common.data.UtilitiesRepository
import vn.com.bidv.feature.common.data.masterdata.model.AuthorizedAccountListCriteriaDto
import vn.com.bidv.feature.common.data.masterdata.model.DataListAccountDto
import vn.com.bidv.feature.common.data.utilities.model.ResultString
import vn.com.bidv.feature.common.domain.data.AccGroupType
import vn.com.bidv.feature.homepage.data.HomepageRepository
import vn.com.bidv.feature.homepage.data.homepage_account_balance.model.AccountBalanceDto
import vn.com.bidv.feature.homepage.data.homepage_account_balance.model.AccountDto
import vn.com.bidv.feature.homepage.domain.model.AccountBalanceDMO
import vn.com.bidv.feature.homepage.domain.model.DataListAccountDMO
import vn.com.bidv.feature.homepage.domain.model.FinAcctDetailDMO
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.sdkbase.domain.DomainResult

class PaymentAccountDetailsUseCaseTest {

    private var homepageRepository: HomepageRepository = mockk()

    private var masterRepository: MasterDataRepository = mockk()

    private var utilitiesRepository: UtilitiesRepository = mockk()

    private var useCase = PaymentAccountDetailsUseCase(homepageRepository, masterRepository, utilitiesRepository)


    @Test
    fun `getAccountDetail success`() = runBlocking {
        val mockNetworkResult = AccountDto()
        val expectedResult = FinAcctDetailDMO()

        coEvery { homepageRepository.getAccountDetail() } returns NetworkResult.Success(mockNetworkResult)

        val result = useCase.getAccountDetail()

        assert(result is DomainResult.Success)
        assertEquals(expectedResult, (result as DomainResult.Success).data)
    }

    @Test
    fun `getAccountDetail failure`() = runBlocking {
        val errorCode = "E002"
        val errorMessage = "Failed to retrieve overview info"

        coEvery { homepageRepository.getAccountDetail() } returns NetworkResult.Error(
            errorCode = errorCode,
            errorMessage = errorMessage
        )

        val result = useCase.getAccountDetail()

        assert(result is DomainResult.Error)
        assertEquals(errorCode, (result as DomainResult.Error).errorCode)
        assertEquals(errorMessage, result.errorMessage)
    }

    @Test
    fun `getAccountBalance should return success result with AccountBalanceDMO`() = runBlocking {
        val mockNetworkResult = AccountBalanceDto()
        val expectedResult = AccountBalanceDMO()

        coEvery { homepageRepository.getAccountBalance("12345") } returns NetworkResult.Success(mockNetworkResult)

        val result = useCase.getAccountBalance("12345")

        assert(result is DomainResult.Success)
        assertEquals(expectedResult, (result as DomainResult.Success).data)
    }

    @Test
    fun `getAccountBalance failure`() = runBlocking {
        val errorCode = "E002"
        val errorMessage = "Failed to retrieve overview info"

        coEvery { homepageRepository.getAccountBalance("12345") } returns NetworkResult.Error(
            errorCode = errorCode,
            errorMessage = errorMessage
        )

        val result = useCase.getAccountBalance("12345")

        assert(result is DomainResult.Error)
        assertEquals(errorCode, (result as DomainResult.Error).errorCode)
        assertEquals(errorMessage, result.errorMessage)
    }

    @Test
    fun `getAuthorizedAccountList should return success result with DataListAccountDMO`() = runBlocking {
        val mockNetworkResult = DataListAccountDto()
        val expectedResult = DataListAccountDMO()

        coEvery { masterRepository.getAuthorizedAccountList(any()) } returns NetworkResult.Success(mockNetworkResult)

        val result = useCase.getAuthorizedAccountList()

        assert(result is DomainResult.Success)
        assertEquals(expectedResult, (result as DomainResult.Success).data)
    }

    @Test
    fun `getAuthorizedAccountList failure`() = runBlocking {
        val errorCode = "E002"
        val errorMessage = "Failed to retrieve overview info"

        coEvery { masterRepository.getAuthorizedAccountList(
            AuthorizedAccountListCriteriaDto(
                accType = AccGroupType.DDA.code,
                grpType = AccGroupType.ACC_INQ.code
            )
        ) } returns NetworkResult.Error(
            errorCode = errorCode,
            errorMessage = errorMessage
        )

        val result = useCase.getAuthorizedAccountList()

        assert(result is DomainResult.Error)
        assertEquals(errorCode, (result as DomainResult.Error).errorCode)
        assertEquals(errorMessage, result.errorMessage)
    }

    @Test
    fun `setDefaultAccount success`() = runBlocking {

        coEvery { utilitiesRepository.setDefaultAccount(
            accType = AccGroupType.DDA.code,
            accNo = "12345",
            grpType = AccGroupType.ACC_INQ.code,
            enable = true
        ) } returns NetworkResult.Success(ResultString())

        val result = useCase.setDefaultAccount("12345")

        assert(result is DomainResult.Success)
    }

    @Test
    fun `setDefaultAccount failure`() = runBlocking {
        val errorCode = "E002"
        val errorMessage = "Failed to retrieve overview info"

        coEvery { utilitiesRepository.setDefaultAccount( accType = AccGroupType.DDA.code,
            accNo = "12345",
            grpType = AccGroupType.ACC_INQ.code,
            enable = true) } returns NetworkResult.Error(
            errorCode = errorCode,
            errorMessage = errorMessage
        )

        val result = useCase.setDefaultAccount("12345")

        assert(result is DomainResult.Error)
        assertEquals(errorCode, (result as DomainResult.Error).errorCode)
        assertEquals(errorMessage, result.errorMessage)
    }
}