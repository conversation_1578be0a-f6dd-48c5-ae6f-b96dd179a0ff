package vn.com.bidv.feature.homepage.ui

import org.junit.Test
import org.junit.Assert.*
import vn.com.bidv.feature.common.domain.data.PermissionResDMO
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.ModelCheckAble
import vn.com.bidv.feature.homepage.ui.customquicklink.CustomQuickLinkReducer

class CustomQuickLinkReducerTest {

    @Test
    fun `test OnInitDataService event`() {
        val event = CustomQuickLinkReducer.CustomQuickLinkViewEvent.OnInitDataService
        assertNotNull(event)
    }

    @Test
    fun `test UpdateSearchText event`() {
        val searchText = "test"
        val event = CustomQuickLinkReducer.CustomQuickLinkViewEvent.UpdateSearchText(searchText)
        assertEquals(searchText, event.searchText)
    }

    @Test
    fun `test SetSearchView event`() {
        val isSearchView = true
        val event = CustomQuickLinkReducer.CustomQuickLinkViewEvent.SetSearchView(isSearchView)
        assertTrue(event.isSearchView)
    }

    @Test
    fun `test GetServicesFromStorageSuccess event`() {
        val listChildrenService: List<ModelCheckAble<PermissionResDMO>>? = null
        val listQuickLink: List<PermissionResDMO> = emptyList()
        val event = CustomQuickLinkReducer.CustomQuickLinkViewEvent.GetServicesFromStorageSuccess(listChildrenService, listQuickLink)
        assertNull(event.listChildrenService)
        assertEquals(listQuickLink, event.listQuickLink)
    }

    @Test
    fun `test GetSearchListChildrenService event`() {
        val listSearchChildrenService: List<ModelCheckAble<PermissionResDMO>>? = null
        val event = CustomQuickLinkReducer.CustomQuickLinkViewEvent.GetSearchListChildrenService(listSearchChildrenService)
        assertNull(event.listSearchChildrenService)
    }

    @Test
    fun `test SelectItem event`() {
        val item = ModelCheckAble(PermissionResDMO(), true)
        val event = CustomQuickLinkReducer.CustomQuickLinkViewEvent.SelectItem(item)
        assertEquals(item, event.item)
    }

    @Test
    fun `test RearrangeItemsQuickLink event`() {
        val fromIndex = 0
        val toIndex = 1
        val event = CustomQuickLinkReducer.CustomQuickLinkViewEvent.RearrangeItemsQuickLink(fromIndex, toIndex)
        assertEquals(fromIndex, event.fromIndex)
        assertEquals(toIndex, event.toIndex)
    }

    @Test
    fun `test HandleSelectItemResult event`() {
        val listQuickLink: List<PermissionResDMO> = emptyList()
        val listChildrenService: List<ModelCheckAble<PermissionResDMO>>? = null
        val listSearchChildrenService: List<ModelCheckAble<PermissionResDMO>>? = null
        val event = CustomQuickLinkReducer.CustomQuickLinkViewEvent.HandleSelectItemResult(listQuickLink, listChildrenService, listSearchChildrenService)
        assertEquals(listQuickLink, event.listQuickLink)
        assertNull(event.listChildrenService)
        assertNull(event.listSearchChildrenService)
    }

    @Test
    fun `test UpdateUserQuickLink event`() {
        val listQuickLink: List<PermissionResDMO> = emptyList()
        val event = CustomQuickLinkReducer.CustomQuickLinkViewEvent.UpdateUserQuickLink(listQuickLink)
        assertEquals(listQuickLink, event.listQuickLink)
    }

    @Test
    fun `test UpdateUserQuickLinkSuccessEvent event`() {
        val event = CustomQuickLinkReducer.CustomQuickLinkViewEvent.UpdateUserQuickLinkSuccessEvent
        assertNotNull(event)
    }
}