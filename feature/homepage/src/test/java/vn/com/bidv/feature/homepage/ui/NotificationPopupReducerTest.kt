package vn.com.bidv.feature.homepage.ui

import org.junit.Assert.assertEquals
import org.junit.Test
import vn.com.bidv.feature.homepage.domain.notificationpopupdmo.ModelListPopupNotifyStatementResponseDMO
import vn.com.bidv.feature.homepage.ui.notificationpopupscreen.NotificationPopupReducer
import vn.com.bidv.feature.homepage.ui.notificationpopupscreen.NotificationPopupReducer.NotificationPopupViewEffect
import vn.com.bidv.feature.homepage.ui.notificationpopupscreen.NotificationPopupReducer.NotificationPopupViewEvent
import vn.com.bidv.feature.homepage.ui.notificationpopupscreen.NotificationPopupReducer.NotificationPopupViewState
import kotlin.test.assertNull
import kotlin.test.assertTrue

class NotificationPopupReducerTest {
    private val reducer = NotificationPopupReducer()

    @Test
    fun `reduce should return GetNotificationPopup effect get popup from storage when OnGetNotificationPopup event occurs`() {
        val initialState = NotificationPopupViewState.Result()
        val (newState, effect) =
            reducer.reduce(
                initialState,
                NotificationPopupViewEvent.OnGetNotificationPopup(true),
            )
        assertEquals(initialState, newState)
        assertTrue(effect is NotificationPopupViewEffect.GetNotificationPopup)
    }

    @Test
    fun `reduce should return GetNotificationPopup effect get popup from API when OnGetNotificationPopup event occurs`() {
        val initialState = NotificationPopupViewState.Result()
        val (newState, effect) =
            reducer.reduce(
                initialState,
                NotificationPopupViewEvent.OnGetNotificationPopup(),
            )
        assertEquals(initialState, newState)
        assertTrue(effect is NotificationPopupViewEffect.GetNotificationPopup)
    }

    @Test
    fun `reduce should update ui state when GetNotificationPopupSuccess event occurs`() {
        val initialState = NotificationPopupViewState.Result()
        val (newState, effect) =
            reducer.reduce(
                initialState,
                NotificationPopupViewEvent.GetNotificationPopupSuccess(
                    listNotificationPopupResponseDMO = ModelListPopupNotifyStatementResponseDMO(
                        items = null,
                        total = 0L,
                        listNotificationPopup = null
                    )
                ),
            )
        val uiState = newState.listModelNotificationPopupUI
        assertNull(uiState.listNotificationPopup)
        assertTrue(uiState.isPopupLoaded)
        assertEquals(0L, uiState.totalPopup)
        assertNull(effect)
    }

    @Test
    fun `reduce should update isPopupLoaded state true when GetNotificationPopupFail event occurs`() {
        val initialState = NotificationPopupViewState.Result()
        val (newState, effect) =
            reducer.reduce(
                initialState,
                NotificationPopupViewEvent.GetNotificationPopupFail,
            )
        val uiState = newState.listModelNotificationPopupUI
        assertTrue(uiState.isPopupLoaded)
        assertNull(effect)
    }

    @Test
    fun `reduce should update nextPopup index when OnNextPopup event occurs`() {
        val initialState = NotificationPopupViewState.Result()
        val (newState, effect) =
            reducer.reduce(
                initialState,
                NotificationPopupViewEvent.OnNextPopup,
            )
        val uiState = newState.listModelNotificationPopupUI
        assertEquals(1, uiState.indexPopup)
        assertTrue(effect is NotificationPopupViewEffect.RemovePopupInCacheById)
    }

    @Test
    fun `reduce should update nextPopup index and return UpdateStatusDisplayPopup effect when OnButtonDisplayClicked event occurs`() {
        val initialState = NotificationPopupViewState.Result()
        val (newState, effect) =
            reducer.reduce(
                initialState,
                NotificationPopupViewEvent.OnButtonDisplayClicked,
            )
        val uiState = newState.listModelNotificationPopupUI
        assertEquals(1, uiState.indexPopup)
        assertTrue(effect is NotificationPopupViewEffect.UpdateStatusDisplayPopup)
    }

    @Test
    fun `reduce should return UpdateStatusFeedbackPopup effect when OnButtonFeedbackClicked event occurs`() {
        val initialState = NotificationPopupViewState.Result()
        val (newState, effect) =
            reducer.reduce(
                initialState,
                NotificationPopupViewEvent.OnButtonFeedbackClicked,
            )
        assertEquals(initialState, newState)
        assertTrue(effect is NotificationPopupViewEffect.UpdateStatusFeedbackPopup)
    }

    @Test
    fun `reduce should return UpdateStatusDisplayPopup effect when OnButtonLinkClicked event occurs`() {
        val initialState = NotificationPopupViewState.Result()
        val (newState, effect) =
            reducer.reduce(
                initialState,
                NotificationPopupViewEvent.OnButtonLinkClicked,
            )
        val uiState = newState.listModelNotificationPopupUI
        assertEquals(initialState, newState)
        assertTrue(effect is NotificationPopupViewEffect.UpdateStatusDisplayPopup)
        assertEquals(0, uiState.indexPopup)
    }

    @Test
    fun `reduce should update index next popup state effect when UpdateStatusFeedbackPopupSuccess event occurs`() {
        val initialState = NotificationPopupViewState.Result()
        val (newState, effect) =
            reducer.reduce(
                initialState,
                NotificationPopupViewEvent.UpdateStatusFeedbackPopupSuccess,
            )
        val uiState = newState.listModelNotificationPopupUI
        assertEquals(1, uiState.indexPopup)
        assertNull(effect)
    }

    @Test
    fun `reduce should return UpdateStatusFeedbackPopupStatusFail effect when UpdateStatusFeedbackPopupFail event occurs`() {
        val initialState = NotificationPopupViewState.Result()
        val (newState, effect) =
            reducer.reduce(
                initialState,
                NotificationPopupViewEvent.UpdateStatusFeedbackPopupFail(
                    errorMessage = "CONNECT_TIME_OUT"
                ),
            )
        assertEquals(initialState, newState)
        assertTrue(effect is NotificationPopupViewEffect.UpdateStatusFeedbackPopupStatusFail)
        assertEquals("CONNECT_TIME_OUT", effect.errorMessage)
    }
}