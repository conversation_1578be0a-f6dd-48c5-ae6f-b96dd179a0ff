package vn.com.bidv.feature.homepage.domain

import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.ModelCheckAble
import vn.com.bidv.feature.common.data.utilities.model.ResultString
import vn.com.bidv.feature.homepage.constants.WidgetType
import vn.com.bidv.feature.homepage.data.HomepageRepository
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.sdkbase.data.LocalRepository
import vn.com.bidv.sdkbase.data.ShareDataDTO
import vn.com.bidv.sdkbase.domain.DomainResult

class CustomWidgetUseCaseTest {

    private lateinit var useCase: CustomWidgetUseCase
    private val mockHomepageRepository: HomepageRepository = mockk()
    private val mockLocalRepository: LocalRepository = mockk(relaxed = true)

    @Before
    fun setUp() {
        useCase = CustomWidgetUseCase(mockHomepageRepository, mockLocalRepository)
    }

    @Test
    fun `getListAllListWidget should return list with correct check states`() {
        // Arrange
        val selectedWidgets = listOf(WidgetType.Widget_TransManage, WidgetType.Widget_OverView)

        // Act
        val result = useCase.getListAllListWidget(selectedWidgets)

        // Assert
        assert(result.isNotEmpty())
        result.forEach {
            assert(it.isChecked == selectedWidgets.contains(it.data))
        }
    }

    @Test
    fun `getNumberMinPickup should return correct minimum number`() {
        // Arrange
        val widgets = listOf(WidgetType.Widget_OverView, WidgetType.Widget_TransManage)

        // Act
        val result = useCase.getNumberMinPickup(widgets)

        // Assert
        assertEquals(1, result)
    }

    @Test
    fun `selectListWidget should toggle check state`() {
        // Arrange
        val listWidgets = listOf(
            ModelCheckAble(WidgetType.Widget_OverView, false),
            ModelCheckAble(WidgetType.Widget_TransManage, true)
        )
        val targetWidget = listWidgets[0]

        // Act
        val result = useCase.selectListWidget(listWidgets, targetWidget)

        // Assert
        assert(result.find { it.data == targetWidget.data }?.isChecked == true)
    }

    @Test
    fun `applyListWidget should return correct updated list`() {
        // Arrange
        val listWidgets = listOf(
            ModelCheckAble(WidgetType.Widget_TransManage, true),
            ModelCheckAble(WidgetType.Widget_ExchangeRate, false)
        )
        val initialWidgets = listOf(WidgetType.Widget_NotiDueAmount)

        // Act
        val result = useCase.applyListWidget(listWidgets, initialWidgets)

        // Assert
        assert(result.contains(WidgetType.Widget_NotiDueAmount))
    }

    @Test
    fun `updateWidgetInfoByUser should return success`() = runBlocking {
        // Arrange
        val widgets = listOf(WidgetType.Widget_NotiDueAmount)
        coEvery { mockHomepageRepository.updateWidgetInfoByUser(any()) } returns NetworkResult.Success(ResultString(message = "Success"))

        // Act
        val result = useCase.updateWidgetInfoByUser(widgets)

        // Assert
        assert(result is DomainResult.Success)
        assertEquals("Success", (result as DomainResult.Success).data)
    }

    @Test
    fun `needReloadDataHome should call shareDataTo`() = runBlocking {
        // Act
        useCase.needReloadDataHome()

        // Assert
        coVerify { mockLocalRepository.shareDataTo(any(), any<ShareDataDTO>()) }
    }
}
