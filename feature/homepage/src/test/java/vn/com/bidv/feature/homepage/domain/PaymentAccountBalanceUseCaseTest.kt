package vn.com.bidv.feature.homepage.domain

import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.Test
import vn.com.bidv.feature.homepage.data.HomepageRepository
import vn.com.bidv.feature.homepage.data.homepage_account_balance.model.BalanceCurrDetailDto
import vn.com.bidv.feature.homepage.data.homepage_account_balance.model.BalanceCurrenyDto
import vn.com.bidv.feature.homepage.domain.model.BalanceCurrDetailDMO
import vn.com.bidv.feature.homepage.domain.model.BalanceCurrencyDMO
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.sdkbase.domain.DomainResult
import java.math.BigDecimal
import kotlin.test.assertEquals

class PaymentAccountBalanceUseCaseTest {
    private val mockRepository: HomepageRepository = mockk()
    private val useCase = PaymentAccountBalanceUseCase(mockRepository)
    private val currencyListDMO =
        BalanceCurrencyDMO(
            balCurrList =
                listOf(
                    BalanceCurrDetailDMO(
                        currCode = "USD",
                        totalBalance = BigDecimal(***********),
                    ),
                    BalanceCurrDetailDMO(
                        currCode = "VND",
                        totalBalance = BigDecimal(***********),
                    ),
                    BalanceCurrDetailDMO(
                        currCode = "JPY",
                        totalBalance = BigDecimal(***********),
                    ),
                ),
            updatedDate = "22/08/2000",
        )

    private val currencyListDTO =
        BalanceCurrenyDto(
            balCurrList =
                listOf(
                    BalanceCurrDetailDto(
                        currCode = "USD",
                        totalBalance = BigDecimal(***********),
                    ),
                    BalanceCurrDetailDto(
                        currCode = "VND",
                        totalBalance = BigDecimal(***********),
                    ),
                    BalanceCurrDetailDto(
                        currCode = "JPY",
                        totalBalance = BigDecimal(***********),
                    ),
                ),
            updatedDate = "22/08/2000",
        )

    @Test
    fun `getPaymentAccountBalance should return success result with PaymentAccountBalanceDMO`() =
        runTest {
            coEvery { mockRepository.getListAccountBalanceByCurr() } returns
                NetworkResult.Success(
                    currencyListDTO,
                )
            val result = useCase.getAccountBalanceInfo()
            assert(result is DomainResult.Success)
            assertEquals(currencyListDMO, (result as DomainResult.Success).data)
        }
}
