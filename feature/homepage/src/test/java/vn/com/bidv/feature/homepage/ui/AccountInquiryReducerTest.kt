package vn.com.bidv.feature.homepage.ui

import vn.com.bidv.feature.homepage.ui.accountinquiry.AccountInquiryReducer
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import vn.com.bidv.common.patterns.mvi.BaseState
import vn.com.bidv.feature.homepage.domain.model.InquiryAccountType

class AccountInquiryReducerTest {

//    private lateinit var reducer: AccountInquiryReducer
//
//    @Before
//    fun setUp() {
//        reducer = AccountInquiryReducer()
//    }
//
//    @Test
//    fun `test FetchUserData event`() {
//        val initialState = AccountInquiryReducer.AccountInquiryViewState.CommonState(BaseState.InitScreen)
//        val event = AccountInquiryReducer.AccountInquiryViewEvent.FetchUserData
//
//        val (newState, effect) = reducer.reduce(initialState, event)
//
//        assertEquals(initialState, newState)
//        assertEquals(AccountInquiryReducer.AccountInquiryViewEffect.FetchUserInfo, effect)
//    }
//
//    @Test
//    fun `test FetchUserDataSuccess event`() {
//        val initialState = AccountInquiryReducer.AccountInquiryViewState.CommonState(BaseState.InitScreen)
//        val inquiryList = listOf(InquiryAccountType.PAYMENT, InquiryAccountType.LOAN)
//        val event = AccountInquiryReducer.AccountInquiryViewEvent.FetchUserDataSuccess(inquiryList)
//
//        val (newState, effect) = reducer.reduce(initialState, event)
//
//        assertEquals(AccountInquiryReducer.AccountInquiryViewState.ShowContent(inquiryList), newState)
//        assertEquals(null, effect)
//    }
//
//    @Test
//    fun `test FetchUserDataFailure event`() {
//        val initialState = AccountInquiryReducer.AccountInquiryViewState.CommonState(BaseState.InitScreen)
//        val errorMessage = "Error occurred"
//        val event = AccountInquiryReducer.AccountInquiryViewEvent.FetchUserDataFailure(errorMessage)
//
//        val (newState, effect) = reducer.reduce(initialState, event)
//
//        assertEquals(AccountInquiryReducer.AccountInquiryViewState.CommonState(BaseState.Error(errorMessage)), newState)
//        assertEquals(AccountInquiryReducer.AccountInquiryViewEffect.ShowError(errorMessage), effect)
//    }
//
//    @Test
//    fun `test FetchUserData event in Error state`() {
//        val initialState = AccountInquiryReducer.AccountInquiryViewState.CommonState(BaseState.Error("Error occurred"))
//        val event = AccountInquiryReducer.AccountInquiryViewEvent.FetchUserData
//
//        val (newState, effect) = reducer.reduce(initialState, event)
//
//        assertEquals(initialState, newState)
//        assertEquals(AccountInquiryReducer.AccountInquiryViewEffect.FetchUserInfo, effect)
//    }
}