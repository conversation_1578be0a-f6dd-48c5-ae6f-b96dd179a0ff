import io.mockk.mockk
import org.junit.Assert.assertEquals
import org.junit.Test
import vn.com.bidv.feature.homepage.domain.WidgetState
import vn.com.bidv.feature.homepage.domain.model.BalanceCurrencyDMO
import vn.com.bidv.feature.homepage.ui.paymentaccountbalance.PaymentAccountBalanceReducer
import vn.com.bidv.feature.homepage.ui.paymentaccountbalance.PaymentAccountBalanceReducer.PaymentAccountBalanceViewEffect
import vn.com.bidv.feature.homepage.ui.paymentaccountbalance.PaymentAccountBalanceReducer.PaymentAccountBalanceViewEvent
import vn.com.bidv.feature.homepage.ui.paymentaccountbalance.PaymentAccountBalanceReducer.PaymentAccountBalanceViewState

class PaymentAccountBalanceReducerTest {
    private val reducer = PaymentAccountBalanceReducer()

    @Test
    fun `should toggle show balance on first time`() {
        val initialState =
            PaymentAccountBalanceViewState(
                shouldHideBalance = false,
                dataFetchedAtLeastOnce = false,
            )
        val (newState, effect) =
            reducer.reduce(
                initialState,
                PaymentAccountBalanceViewEvent.OnToggleShowBalanceButton,
            )
        assertEquals(true, newState.shouldHideBalance)
        assertEquals(WidgetState.LOADING, newState.state)
        assertEquals(effect, PaymentAccountBalanceViewEffect.FetchAccountBalanceList)
    }

    @Test
    fun `should update state on GetAccountBalanceListSuccess`() {
        val initialState = PaymentAccountBalanceViewState()
        val mockBalance = BalanceCurrencyDMO(listOf(mockk(), mockk()), "2024-02-26")
        val (newState, effect) =
            reducer.reduce(
                initialState,
                PaymentAccountBalanceViewEvent.GetAccountBalanceListSuccess(mockBalance),
            )
        assertEquals(WidgetState.CONTENT, newState.state)
        assertEquals(false, newState.shouldHideBalance)
        assertEquals(mockBalance.balCurrList, newState.accountBalanceList)
        assertEquals(null, effect)
    }

    @Test
    fun `should handle empty balance list`() {
        val initialState = PaymentAccountBalanceViewState()
        val mockBalance = BalanceCurrencyDMO(emptyList(), "2024-02-26")
        val (newState, effect) =
            reducer.reduce(
                initialState,
                PaymentAccountBalanceViewEvent.GetAccountBalanceListSuccess(mockBalance),
            )
        assertEquals(WidgetState.NOCONTENT, newState.state)
        assertEquals(null, effect)
    }

    @Test
    fun `should set error state on GetAccountBalanceListFailed`() {
        val initialState = PaymentAccountBalanceViewState(state = WidgetState.LOADING)
        val (newState, effect) =
            reducer.reduce(
                initialState,
                PaymentAccountBalanceViewEvent.GetAccountBalanceListFailed(errorCode = "403", errorMessage = "error"),
            )
        assertEquals(WidgetState.NO_ACCOUNT_CIF, newState.state)
        assertEquals(null, effect)
    }
}
