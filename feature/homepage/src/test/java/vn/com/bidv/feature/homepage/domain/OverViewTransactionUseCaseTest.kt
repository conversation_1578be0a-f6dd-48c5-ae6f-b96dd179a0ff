package vn.com.bidv.feature.homepage.domain

import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.Assert.assertEquals
import org.junit.Test
import vn.com.bidv.feature.homepage.data.HomepageRepository
import vn.com.bidv.feature.homepage.data.NotificationPopupRepository
import vn.com.bidv.feature.homepage.data.homepage_account_balance.model.DataListOverallBalanceDto
import vn.com.bidv.feature.homepage.data.homepage_overview_widget.model.OverallDto
import vn.com.bidv.feature.homepage.domain.model.DataListOverallBalanceDMO
import vn.com.bidv.feature.homepage.domain.model.OverallDMO
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.sdkbase.domain.DomainResult


class OverViewTransactionUseCaseTest {
    private val mockRepository: HomepageRepository = mockk()
    private val useCase = OverViewTransactionUseCase(mockRepository)

    @Test
    fun `getOverallInfo should return success result with OverallDMO`() = runBlocking {
        val mockNetworkResult = DataListOverallBalanceDto(
            items = emptyList(),
            total = 0
        )
        val expectedResult = DataListOverallBalanceDMO(
            items = emptyList(),
            total = 0
        )

        coEvery { mockRepository.getOverallInfo("DEP") } returns NetworkResult.Success(mockNetworkResult)

        val result = useCase.getOverallInfo("DEP")

        assert(result is DomainResult.Success)
        assertEquals(expectedResult, (result as DomainResult.Success).data)
    }

    @Test
    fun `getOverallInfo should return failure result when repository fails`() = runBlocking {
        val errorCode = "E002"
        val errorMessage = "Failed to retrieve overview info"

        coEvery { mockRepository.getOverallInfo("DEP") } returns NetworkResult.Error(
            errorCode = errorCode,
            errorMessage = errorMessage
        )

        val result = useCase.getOverallInfo("DEP")

        assert(result is DomainResult.Error)
        assertEquals(errorCode, (result as DomainResult.Error).errorCode)
        assertEquals(errorMessage, result.errorMessage)
    }
}