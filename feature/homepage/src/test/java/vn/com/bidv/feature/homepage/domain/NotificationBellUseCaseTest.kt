package vn.com.bidv.feature.homepage.domain

import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.Test
import vn.com.bidv.feature.common.data.notify.NotificationUnReadRepository
import vn.com.bidv.feature.common.data.notify.NotifyCommonRepository
import vn.com.bidv.feature.common.data.utilitiesnotify.model.NotifyStatementCountResponse
import vn.com.bidv.feature.common.domain.notificationcommon.CountUnreadNotificationsUseCase
import vn.com.bidv.feature.common.domain.notificationcommon.ModelNotifyStatementCountDMO
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.network.NetworkStatusCode.CONNECT_TIME_OUT
import vn.com.bidv.sdkbase.domain.DomainResult
import kotlin.test.assertEquals
import kotlin.test.assertIs

class NotificationBellUseCaseTest {
    private val repository: NotifyCommonRepository = mockk()
    private val unreadRepository: NotificationUnReadRepository = mockk()
    private val useCase = CountUnreadNotificationsUseCase(repository, unreadRepository)

    private val mockDataResult = NotifyStatementCountResponse(
        totalNotifyUnread = 100L,
        totalNotifyUnreadBal = 10L,
        totalNotifyUnreadSys = 20L,
        totalNotifyUnreadPromo = 30L,
        totalNotifyUnreadTrans = 40L
    )

    @Test
    fun `invoke should return success result with ModelNotifyStatementCountDMO`() =
        runTest {
            coEvery { repository.countUnread() } returns
                    NetworkResult.Success(
                        mockDataResult
                    )
            every { unreadRepository.updateNotyUnreadCountState(any()) } returns Unit
            val result = useCase.invoke()
            assert(result is DomainResult.Success)
            assertIs<ModelNotifyStatementCountDMO>(result.getSafeData())
        }

    @Test
    fun `countUnread should return error result with repository fails`() =
        runTest {
            coEvery { repository.countUnread() } returns
                    NetworkResult.Error(
                        errorCode = CONNECT_TIME_OUT,
                        errorMessage = "CONNECT_TIME_OUT"
                    )
            val result = useCase.invoke()
            assert(result is DomainResult.Error)
            assertEquals(CONNECT_TIME_OUT, (result as DomainResult.Error).errorCode)
            assertEquals("CONNECT_TIME_OUT", result.errorMessage)
        }
}