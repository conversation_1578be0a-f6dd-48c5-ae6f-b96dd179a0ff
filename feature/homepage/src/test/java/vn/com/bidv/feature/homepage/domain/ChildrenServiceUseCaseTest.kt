package vn.com.bidv.feature.homepage.domain

import org.junit.Assert.*
import org.junit.Test
import vn.com.bidv.feature.common.domain.data.PermissionResDMO
import vn.com.bidv.sdkbase.utils.ResourceProvider

import io.mockk.mockk
import org.junit.Assert.assertEquals

class ChildrenServiceUseCaseTest {

    private var resourceProvider: ResourceProvider = mockk()

    private var childrenServiceUseCase = ChildrenServiceUseCase(resourceProvider)

    @Test
    fun testGetChildrenService() {
        val child1 = PermissionResDMO(children = null)
        val child2 = PermissionResDMO(children = null)
        val parent = PermissionResDMO(children = listOf(child1, child2))

        parent.children?.let {
            childrenServiceUseCase.getChildrenService(it)
            assertEquals(2, it.size)
            assertTrue(it.contains(child1))
            assertTrue(it.contains(child2))
        }
    }

    @Test
    fun testGetSearchListChildrenService_withEmptyKeyword() {
        val parent = PermissionResDMO(children = null)

        val result = childrenServiceUseCase.getSearchListChildrenService(listOf(parent), "")

        assertTrue(result.isEmpty())
    }
}