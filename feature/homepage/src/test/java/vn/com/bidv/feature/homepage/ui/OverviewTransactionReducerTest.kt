import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.Assert.*
import vn.com.bidv.feature.homepage.domain.WidgetState
import vn.com.bidv.feature.homepage.domain.model.DataListOverallBalanceDMO
import vn.com.bidv.feature.homepage.ui.overviewtransaction.OverviewTransactionReducer
import vn.com.bidv.feature.homepage.constants.HomePageConstants

class OverviewTransactionReducerTest {

    private val reducer = OverviewTransactionReducer()

    @Test
    fun `test RefreshData event`() {
        val initialState = OverviewTransactionReducer.OverviewTransactionViewState()
        val (newState, effect) = reducer.reduce(initialState, OverviewTransactionReducer.OverviewTransactionViewEvent.RefreshData)
        assertEquals(WidgetState.LOADING, newState.stateDEP)
        assertTrue(newState.isShowDataDeposit)
        assertEquals(OverviewTransactionReducer.OverviewTransactionViewEffect.GetOverallInfo(HomePageConstants.DEP), effect)
    }

    @Test
    fun `test GetOverallInfoSuccess event with null data`() =
        runTest {
            val initialState = OverviewTransactionReducer.OverviewTransactionViewState()
        val event = OverviewTransactionReducer.OverviewTransactionViewEvent.GetOverallInfoSuccess(null, HomePageConstants.DEP)
        val (newState, effect) = reducer.reduce(initialState, event)
        assertEquals(WidgetState.NOCONTENT, newState.stateDEP)
        assertNull(newState.overallDEPDMO)
        assertNull(effect)
    }

    @Test
    fun `test GetOverallInfoSuccess event with valid data`() = runTest {
        val initialState = OverviewTransactionReducer.OverviewTransactionViewState()
        val overallDMO = DataListOverallBalanceDMO()
        val event = OverviewTransactionReducer.OverviewTransactionViewEvent.GetOverallInfoSuccess(overallDMO, HomePageConstants.DEP)
        val (newState, effect) = reducer.reduce(initialState, event)
        assertEquals(WidgetState.NOCONTENT, newState.stateDEP)
        assertEquals(overallDMO, newState.overallDEPDMO)
        assertNull(effect)
    }

    @Test
    fun `test SetShowDataDeposit event`() {
        val initialState = OverviewTransactionReducer.OverviewTransactionViewState()
        val event = OverviewTransactionReducer.OverviewTransactionViewEvent.SetShowDataDeposit(true)
        val (newState, effect) = reducer.reduce(initialState, event)
        assertEquals(WidgetState.LOADING, newState.stateDEP)
        assertTrue(newState.isShowDataDeposit)
        assertEquals(OverviewTransactionReducer.OverviewTransactionViewEffect.GetOverallInfo(HomePageConstants.DEP), effect)
    }

    @Test
    fun `test SetShowDataLoan event`() {
        val initialState = OverviewTransactionReducer.OverviewTransactionViewState()
        val event = OverviewTransactionReducer.OverviewTransactionViewEvent.SetShowDataLoan(true)
        val (newState, effect) = reducer.reduce(initialState, event)
        assertEquals(WidgetState.LOADING, newState.stateLN)
        assertTrue(newState.isShowDataLoan)
        assertEquals(OverviewTransactionReducer.OverviewTransactionViewEffect.GetOverallInfo(HomePageConstants.LN), effect)
    }

    @Test
    fun `test GetOverallInfoFail event`() {
        val initialState = OverviewTransactionReducer.OverviewTransactionViewState()
        val event = OverviewTransactionReducer.OverviewTransactionViewEvent.GetOverallInfoFail(HomePageConstants.DEP, messageError = null)
        val (newState, effect) = reducer.reduce(initialState, event)
        assertEquals(WidgetState.ERROR, newState.stateDEP)
        assertNull(effect)
    }
}