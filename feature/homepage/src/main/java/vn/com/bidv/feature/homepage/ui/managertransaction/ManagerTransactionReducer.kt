package vn.com.bidv.feature.homepage.ui.managertransaction

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.common.domain.data.TxnCountResponseDMO
import vn.com.bidv.feature.homepage.domain.WidgetState

class ManagerTransactionReducer :
    Reducer<ManagerTransactionReducer.ManagerTransactionViewState, ManagerTransactionReducer.ManagerTransactionViewEvent, ManagerTransactionReducer.ManagerTransactionViewEffect> {

    @Immutable
    data class ManagerTransactionViewState(
        val isInitData: Boolean = false,
        val listTxnCount: List<TxnCountResponseDMO> = emptyList(),
        val state: WidgetState = WidgetState.LOADING,
        val messageError: String? = null,
        val isReload: Boolean? = false
    ) : ViewState

    @Immutable
    sealed class ManagerTransactionViewEvent : ViewEvent {
        data object OnInitData : ManagerTransactionViewEvent()
        data object RefreshData : ManagerTransactionViewEvent()
        data class GetTxnIdManagerTransactionSuccess(val listTxnCount: List<TxnCountResponseDMO>) :
            ManagerTransactionViewEvent()
        data class GetTxnIdManagerTransactionFail(val message: String?) :
            ManagerTransactionViewEvent()
        data class UpdateStatusReloadScreen(val status: Boolean) : ManagerTransactionViewEvent()
    }

    @Immutable
    sealed class ManagerTransactionViewEffect : SideEffect {
        data object GetTxnIdManagerTransaction : ManagerTransactionViewEffect()
    }

    override fun reduce(
        previousState: ManagerTransactionViewState,
        event: ManagerTransactionViewEvent,
    ): Pair<ManagerTransactionViewState, ManagerTransactionViewEffect?> {
        return when (event) {
            is ManagerTransactionViewEvent.OnInitData -> {
                previousState.copy(
                    isInitData = true
                ) to ManagerTransactionViewEffect.GetTxnIdManagerTransaction
            }

            is ManagerTransactionViewEvent.GetTxnIdManagerTransactionSuccess -> {
                previousState.copy(
                    listTxnCount = event.listTxnCount,
                    state = if (event.listTxnCount.isEmpty()) WidgetState.NOCONTENT else WidgetState.CONTENT
                ) to null
            }

            is ManagerTransactionViewEvent.GetTxnIdManagerTransactionFail -> {
                previousState.copy(
                    state = WidgetState.ERROR,
                    messageError = event.message
                ) to null
            }

            ManagerTransactionViewEvent.RefreshData -> {
                previousState.copy(
                    state = WidgetState.LOADING
                ) to ManagerTransactionViewEffect.GetTxnIdManagerTransaction
            }

            is ManagerTransactionViewEvent.UpdateStatusReloadScreen -> {
                previousState.copy(
                    isReload = event.status
                ) to null
            }
        }

    }
}
