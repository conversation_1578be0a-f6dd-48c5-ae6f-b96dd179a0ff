package vn.com.bidv.feature.homepage.data

import okhttp3.MultipartBody
import vn.com.bidv.feature.common.data.utilities.apis.UtilitiesApi
import vn.com.bidv.feature.common.data.utilities.model.DataListExchangeRateResponse
import vn.com.bidv.feature.common.data.utilities.model.ResultString
import vn.com.bidv.feature.common.data.utilities.model.UploadAvatarRes
import vn.com.bidv.feature.common.data.utilities.model.UserLangUpdateRequest
import vn.com.bidv.feature.common.data.utilities.model.UserQuickLinkRequest
import vn.com.bidv.feature.common.data.utilities.model.WidgetInfoUpdateRequest
import vn.com.bidv.feature.homepage.data.homepage_account_balance.apis.HomepageAccountBalanceApi
import vn.com.bidv.feature.homepage.data.homepage_account_balance.model.AccountBalanceDto
import vn.com.bidv.feature.homepage.data.homepage_account_balance.model.AccountDefaultCriteriaDto
import vn.com.bidv.feature.homepage.data.homepage_account_balance.model.AccountDetailCriteriaDto
import vn.com.bidv.feature.homepage.data.homepage_account_balance.model.AccountDto
import vn.com.bidv.feature.homepage.data.homepage_account_balance.model.BalanceCurrenyDto
import vn.com.bidv.feature.homepage.data.homepage_account_balance.model.DataListOverallBalanceDto
import vn.com.bidv.feature.homepage.data.homepage_account_balance.model.DataListTxnCountResponseDto
import vn.com.bidv.feature.homepage.data.homepage_account_balance.model.OverallAcctBalanceCriteriaDto
import vn.com.bidv.network.NetworkResult
import javax.inject.Inject
import vn.com.bidv.network.domain.BaseRepository

class HomepageRepository @Inject constructor(
    private val commonUtilityService: UtilitiesApi,
    private val homepageAccountBalanceApi: HomepageAccountBalanceApi,
) : BaseRepository() {
    suspend fun updateUserQuickLink(listQuickLinks: List<String>): NetworkResult<ResultString> =
        launch {
            commonUtilityService.updateUserQuickLink(
                UserQuickLinkRequest(
                    listQuickLink = listQuickLinks
                )
            )
        }

    suspend fun getOverallInfo(type: String): NetworkResult<DataListOverallBalanceDto> =
        launch {
            homepageAccountBalanceApi.getOverallAcctBalance(OverallAcctBalanceCriteriaDto(
                type = type
            ))
        }

    suspend fun updateLanguage(language: String) = launch {
        commonUtilityService.updateUserLanguage(UserLangUpdateRequest(language))
    }

    suspend fun getAccountDetail(): NetworkResult<AccountDto> =
        launch {
            homepageAccountBalanceApi.getNonFinAccountDefault(AccountDefaultCriteriaDto())
        }

    suspend fun getAccountBalance(accountNo: String?): NetworkResult<AccountBalanceDto> =
        launch {
            homepageAccountBalanceApi.getAccountBalance(AccountDetailCriteriaDto(accountNo = accountNo))
        }

    suspend fun updateWidgetInfoByUser(listWidget: List<String>): NetworkResult<ResultString> =
        launch {
            commonUtilityService.updateWidgetInfoByUser(
                WidgetInfoUpdateRequest(
                    listWidget = listWidget
                )
            )
        }

    suspend fun getListAccountBalanceByCurr(): NetworkResult<BalanceCurrenyDto> =
        launch {
            homepageAccountBalanceApi.getListAccountBalanceByCurr()
        }

    suspend fun getExchangeRateList(): NetworkResult<DataListExchangeRateResponse> =
        launch {
            commonUtilityService.listExchangeRates()
        }

    suspend fun uploadAvatar(file: MultipartBody.Part): NetworkResult<UploadAvatarRes> =
        launch {
            commonUtilityService.uploadAvatar(file)
        }

    suspend fun getTxnCount(): NetworkResult<DataListTxnCountResponseDto> =
        launch {
            homepageAccountBalanceApi.getTxnCount()
        }
}
