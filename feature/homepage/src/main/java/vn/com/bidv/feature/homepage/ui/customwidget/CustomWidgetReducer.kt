package vn.com.bidv.feature.homepage.ui.customwidget

import androidx.compose.runtime.Immutable
import vn.com.bidv.feature.homepage.ui.customquicklink.draggable.rearrangeItems
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.ModelCheckAble
import vn.com.bidv.feature.homepage.constants.WidgetType

class CustomWidgetReducer :
    Reducer<CustomWidgetReducer.CustomWidgetViewState, CustomWidgetReducer.CustomWidgetViewEvent, CustomWidgetReducer.CustomWidgetViewEffect> {

    @Immutable
    data class CustomWidgetViewState(
        val isInitSuccess: Boolean = false,
        val listWidget: List<WidgetType> = emptyList(),
        val listAllWidget: List<ModelCheckAble<WidgetType>> = emptyList(),
        val isEnableApplyBtn: Boolean = false,
        val numberMinPickup: Int = 1,
    ) : ViewState

    @Immutable
    sealed class CustomWidgetViewEvent : ViewEvent {
        data object OnInitDataWidget : CustomWidgetViewEvent()
        data class GetWidgetFormStorageSuccess(val listWidget: List<WidgetType>, val listAllWidget: List<ModelCheckAble<WidgetType>>, val numberMinPickup: Int) :
            CustomWidgetViewEvent()
        data class RearrangeItemsWidget(val fromIndex: Int, val toIndex: Int) : CustomWidgetViewEvent()
        data class SelectItem(val item: ModelCheckAble<WidgetType>) : CustomWidgetViewEvent()
        data class HandleSelectedWidgetResult(val listAllWidget: List<ModelCheckAble<WidgetType>>) : CustomWidgetViewEvent()
        data object ApplySetListWidget : CustomWidgetViewEvent()
        data object DismissApplySetListWidget : CustomWidgetViewEvent()
        data class ApplySetListWidgetSuccess(val listWidget: List<WidgetType>) : CustomWidgetViewEvent()
        data object UpdateWidgetInfoByUser : CustomWidgetViewEvent()
        data object UpdateWidgetInfoByUserSuccess : CustomWidgetViewEvent()
        data class GetListAllWidgetWhenDismissSuccess(val listAllWidget: List<ModelCheckAble<WidgetType>>) : CustomWidgetViewEvent()
    }

    @Immutable
    sealed class CustomWidgetViewEffect : SideEffect {
        data object GetWidgetFromStorage : CustomWidgetViewEffect()
        data class GetListAllWidgetWhenDismiss(val listWidget: List<WidgetType>) : CustomWidgetViewEffect()
        data class HandleSelectedWidget(val listAllWidget: List<ModelCheckAble<WidgetType>>, val item: ModelCheckAble<WidgetType>) : CustomWidgetViewEffect()
        data class ApplySetListWidgetSideEffect(val listAllWidget: List<ModelCheckAble<WidgetType>>, val listWidget: List<WidgetType>) : CustomWidgetViewEffect()
        data class UpdateWidgetInfoByUserSideEffect(val listWidget: List<WidgetType>) : CustomWidgetViewEffect()
        data object UpdateWidgetInfoByUserSuccessSideEffect : CustomWidgetViewEffect(), UIEffect
    }

    override fun reduce(
        previousState: CustomWidgetViewState,
        event: CustomWidgetViewEvent,
    ): Pair<CustomWidgetViewState, CustomWidgetViewEffect?> {
        return when (event) {
            is CustomWidgetViewEvent.OnInitDataWidget -> {
                return previousState.copy(
                    isInitSuccess = true
                ) to CustomWidgetViewEffect.GetWidgetFromStorage
            }

            is CustomWidgetViewEvent.GetWidgetFormStorageSuccess -> {
                return previousState.copy(
                    listWidget = event.listWidget,
                    listAllWidget = event.listAllWidget,
                    isEnableApplyBtn = event.listAllWidget.count{ it.isChecked } >= event.numberMinPickup,
                    numberMinPickup = event.numberMinPickup
                ) to null
            }

            is CustomWidgetViewEvent.RearrangeItemsWidget -> {
                return previousState.copy(
                    listWidget = previousState.listWidget.toMutableList().rearrangeItems(event.fromIndex, event.toIndex)
                ) to null
            }

            is CustomWidgetViewEvent.SelectItem -> {
                return previousState to CustomWidgetViewEffect.HandleSelectedWidget(
                    listAllWidget = previousState.listAllWidget,
                    item = event.item
                )
            }

            is CustomWidgetViewEvent.HandleSelectedWidgetResult -> {
                return previousState.copy(
                    listAllWidget = event.listAllWidget,
                    isEnableApplyBtn = event.listAllWidget.count{ it.isChecked } >= previousState.numberMinPickup
                ) to null
            }

            is CustomWidgetViewEvent.ApplySetListWidget -> {
                return previousState to CustomWidgetViewEffect.ApplySetListWidgetSideEffect(
                    listAllWidget = previousState.listAllWidget,
                    listWidget = previousState.listWidget
                )
            }

            is CustomWidgetViewEvent.DismissApplySetListWidget -> {
                return previousState to CustomWidgetViewEffect.GetListAllWidgetWhenDismiss(previousState.listWidget)
            }

            is CustomWidgetViewEvent.ApplySetListWidgetSuccess -> {
                return previousState.copy(
                    listWidget = event.listWidget
                ) to null
            }

            is CustomWidgetViewEvent.UpdateWidgetInfoByUser -> {
                return previousState to CustomWidgetViewEffect.UpdateWidgetInfoByUserSideEffect(
                    listWidget = previousState.listWidget
                )
            }

            is CustomWidgetViewEvent.UpdateWidgetInfoByUserSuccess -> {
                return previousState to CustomWidgetViewEffect.UpdateWidgetInfoByUserSuccessSideEffect
            }

            is CustomWidgetViewEvent.GetListAllWidgetWhenDismissSuccess -> {
                return previousState.copy(
                    listAllWidget = event.listAllWidget,
                    isEnableApplyBtn = event.listAllWidget.count{ it.isChecked } >= previousState.numberMinPickup
                ) to null
            }
        }

    }
}
