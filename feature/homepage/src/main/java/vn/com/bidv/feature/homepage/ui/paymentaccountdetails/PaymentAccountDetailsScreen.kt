package vn.com.bidv.feature.homepage.ui.paymentaccountdetails

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.ClipboardManager
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import kotlinx.coroutines.launch
import vn.com.bidv.common.ui.BaseMVIScreen
import vn.com.bidv.designsystem.component.IBankEmptyState
import vn.com.bidv.designsystem.component.feedback.bottomsheet.IBankSearchDialog
import vn.com.bidv.designsystem.component.feedback.snackbar.IBankSnackBarInfo
import vn.com.bidv.feature.homepage.ui.paymentaccountdetails.PaymentAccountDetailsReducer.PaymentAccountDetailsViewEvent
import vn.com.bidv.feature.homepage.ui.paymentaccountdetails.PaymentAccountDetailsReducer.PaymentAccountDetailsViewState
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.feature.common.domain.data.AccountDMO
import vn.com.bidv.feature.homepage.constants.HomePageConstants
import vn.com.bidv.feature.homepage.ui.widgetcommon.WidgetCommonScreen
import vn.com.bidv.feature.homepage.utils.removeSpecialCharacters
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.VNCharacterUtil
import vn.com.bidv.sdkbase.utils.formatMoney

@Composable
fun PaymentAccountDetailsScreen() {
    val paymentAccountDetailsViewModel: PaymentAccountDetailsViewModel = hiltViewModel()
    BaseMVIScreen(
        viewModel = paymentAccountDetailsViewModel,
        renderContent = { uiState, onEvent ->
            if (!uiState.isInitData) {
                onEvent(PaymentAccountDetailsViewEvent.OnFetchData)
            }

            WidgetCommonScreen(
                state = uiState.state,
                onClickRetry = {
                    onEvent(PaymentAccountDetailsViewEvent.OnFetchData)
                },
                messageNoData = stringResource(R.string.chi_tiet_tai_khoan_khong_co_du_lieu),
                message = uiState.messageError ?: stringResource(R.string.khong_tai_duoc_du_lieu),
                messageNoAccountCif = stringResource(R.string.chi_tiet_tai_khoan_khong_co_phan_quyen_dich_vu),
                modifier = Modifier
                    .wrapContentHeight()
                    .padding(IBSpacing.spacingM)
                    .clip(
                        RoundedCornerShape(
                            IBSpacing.spacingS
                        )
                    )
                    .fillMaxWidth(),
            ) {
                PaymentAccountDetailsContent(
                    uiState = uiState,
                    onEvent = onEvent,
                    viewModel = paymentAccountDetailsViewModel
                )
            }
        },
        handleSideEffect = {
            //Do Nothing
        }
    )
}

@Composable
private fun PaymentAccountDetailsContent(
    uiState: PaymentAccountDetailsViewState,
    onEvent: (PaymentAccountDetailsViewEvent) -> Unit,
    viewModel: PaymentAccountDetailsViewModel
) {

    val clipboardManager: ClipboardManager = LocalClipboardManager.current
    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current
    val scope = rememberCoroutineScope()
    val snackBarInfo = IBankSnackBarInfo(message = stringResource(R.string.sao_chep_thanh_cong),
        primaryButtonText = stringResource(R.string.dong),
        primaryButtonAction = {}
    )
    Box(
        modifier = Modifier
            .wrapContentHeight()
            .background(IBGradient.cardQuarternary)
            .fillMaxWidth()
    ) {
        Image(
            painter = painterResource(id = vn.com.bidv.designsystem.R.drawable.bidv_flower_card_deposit),
            contentDescription = null,
            contentScale = ContentScale.Crop,
            modifier = Modifier
                .align(Alignment.TopEnd)
                .wrapContentHeight()
        )

        Column(
            modifier = Modifier
                .wrapContentHeight()
                .fillMaxWidth()
                .padding(IBSpacing.spacingM),
            verticalArrangement = Arrangement.spacedBy(IBSpacing.spacing2xs)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = uiState.finAccDetail?.accountNo.orEmpty(),
                    color = colorSchema.contentOn_specialPrimary,
                    style = typography.titleTitle_m,
                    textAlign = TextAlign.Start
                )

                Spacer(Modifier.width(IBSpacing.spacingXs))

                Icon(
                    modifier = Modifier
                        .size(IBSpacing.spacingL)
                        .clickable {
                            uiState.finAccDetail?.accountNo?.let {
                                clipboardManager.setText(AnnotatedString(it))
                                scope.launch {
                                    viewModel.showSnackBar(
                                       snackBarInfo
                                    )
                                }
                            }
                        },
                    imageVector = ImageVector.vectorResource(id = vn.com.bidv.designsystem.R.drawable.copy_outline),
                    contentDescription = null,
                    tint = colorSchema.contentOn_specialPrimary,
                )

                Spacer(Modifier.weight(1f))

                Icon(
                    modifier = Modifier
                        .size(IBSpacing.spacingL)
                        .clickable {
                            onEvent(PaymentAccountDetailsViewEvent.SetShowBottomSheet(true))
                        },
                    imageVector = ImageVector.vectorResource(id = vn.com.bidv.designsystem.R.drawable.circle_arrow_down_outline),
                    contentDescription = null,
                    tint = colorSchema.contentOn_specialPrimary,
                )

            }

            Text(
                text = uiState.finAccDetail?.accountName.orEmpty(),
                color = colorSchema.contentOn_specialSecondary,
                style = typography.bodyBody_m,
                textAlign = TextAlign.Start,
            )

            Spacer(modifier = Modifier.height(IBSpacing.spacingM))

            Text(
                text = stringResource(R.string.so_du_kha_dung),
                color = colorSchema.contentOn_specialSecondary,
                style = typography.bodyBody_m,
                textAlign = TextAlign.Start,
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(IBSpacing.spacing2xs)
            ) {
                Text(
                    text = if (uiState.isShowData) uiState.accountBalance?.let {
                        (it.balance ?: 0).toString().formatMoney(it.currCode, isShowCurrCode = false)
                    }
                        ?: HomePageConstants.NOT_SHOW_DATA_MONEY else HomePageConstants.NOT_SHOW_DATA_MONEY,
                    color = colorSchema.contentOn_specialPrimary,
                    style = typography.headlineHeadline_s,
                    textAlign = TextAlign.Start,
                )

                Text(
                    text = uiState.finAccDetail?.ccy ?: stringResource(R.string.vnd),
                    color = colorSchema.contentOn_specialSecondary,
                    style = typography.titleTitle_s,
                    textAlign = TextAlign.Start,
                )

                Spacer(modifier = Modifier.width(IBSpacing.spacingXs))

                Icon(
                    modifier = Modifier
                        .size(IBSpacing.spacingL)
                        .clickable {
                            onEvent(PaymentAccountDetailsViewEvent.SetShowData(!uiState.isShowData))
                        },
                    imageVector = ImageVector.vectorResource(id = if (uiState.isShowData) vn.com.bidv.designsystem.R.drawable.eyes_closed_outline else vn.com.bidv.designsystem.R.drawable.eyes_open_outline),
                    contentDescription = null,
                    tint = colorSchema.contentOn_specialPrimary,
                )
            }
        }
    }

    if (uiState.isShowBottomSheetAccount) {
        IBankSearchDialog(
            title = stringResource(R.string.chon_tai_khoan_hien_thi),
            itemSelected = uiState.currentAccount,
            compareKey = { it.accountNo },
            showSearchBox = true,
            listData = uiState.listAccount,
            searchFilter = { item, key ->
                VNCharacterUtil.removeAccent(item.searchKeyForPaymentAccountDetail())
                    .contains(VNCharacterUtil.removeAccent(key).removeSpecialCharacters().trim(), ignoreCase = true)
            },
            state = uiState.bottomSheetState,
            onRequestDismiss = {
                onEvent(PaymentAccountDetailsViewEvent.SetShowBottomSheet(false, it))
            },
            listSearchFilterText = listOf(),
            errorView = {
                IBankEmptyState(
                    modifier = Modifier.fillMaxSize(),
                    supportingText = stringResource(R.string.khong_tai_duoc_du_lieu),
                    textButton = stringResource(R.string.thu_lai)
                )
            }
            ) { _, searchItem ->
            AccountItemBottomView(searchItem.isSelected, searchItem.data)
        }
    }
}

@Composable
fun AccountItemBottomView(
    isSelected: Boolean = false, account: AccountDMO
) {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current
    Row(
        Modifier
            .fillMaxWidth()
            .background(
                color = if (isSelected) colorScheme.bgBrand_01Tertiary else Color.Transparent,
                shape = RoundedCornerShape(IBSpacing.spacingXs)
            )
            .padding(IBSpacing.spacingM), verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f), verticalArrangement = Arrangement.Center
        ) {
            Text(
                color = colorScheme.contentMainPrimary,
                text = "${account.accountNo.orEmpty()} | ${account.currCode}",
                style = typography.bodyBody_l,
                modifier = Modifier.wrapContentWidth()
            )

            Text(
                color = colorScheme.contentMainTertiary,
                text = account.accountName ?: "",
                style = typography.bodyBody_m,
                modifier = Modifier.wrapContentWidth()
            )
        }
        Box(modifier = Modifier.size(32.dp)) {
            if (isSelected) {
                Icon(
                    modifier = Modifier.size(20.dp),
                    painter = painterResource(id = vn.com.bidv.designsystem.R.drawable.check),
                    contentDescription = ""
                )
            }
        }

    }
}

