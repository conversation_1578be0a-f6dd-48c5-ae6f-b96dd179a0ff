package vn.com.bidv.feature.homepage.ui.contact

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState

class ContactReducer :
    Reducer<ContactReducer.ContactViewState, ContactReducer.ContactViewEvent, ContactReducer.ContactViewEffect> {

    data object ContactViewState : ViewState

    @Immutable
    sealed class ContactViewEvent : ViewEvent {
        data object OnError : ContactViewEvent()
    }

    @Immutable
    sealed class ContactViewEffect : SideEffect {
        data object OnError : ContactViewEffect()
    }

    override fun reduce(
        previousState: ContactViewState,
        event: ContactViewEvent,
    ): Pair<ContactViewState, ContactViewEffect?> {
        return when (event) {
            is ContactViewEvent.OnError -> {
                previousState to ContactViewEffect.OnError
            }
        }
    }
}
