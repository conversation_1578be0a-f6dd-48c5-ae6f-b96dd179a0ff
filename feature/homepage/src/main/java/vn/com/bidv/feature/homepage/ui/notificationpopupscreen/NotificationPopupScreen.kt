package vn.com.bidv.feature.homepage.ui.notificationpopupscreen

import OpenBrowser
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.common.extenstion.isNotNullOrEmpty
import vn.com.bidv.common.utils.CollectSideEffect
import vn.com.bidv.common.utils.unpack
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.component.feedback.modalconfirm.WebViewSettings
import vn.com.bidv.feature.homepage.constants.NotificationPopupConstants.BLANK
import vn.com.bidv.feature.homepage.domain.notificationpopupdmo.Button
import vn.com.bidv.feature.homepage.ui.notificationpopupscreen.NotificationPopupReducer.NotificationPopupViewEffect
import vn.com.bidv.feature.homepage.ui.notificationpopupscreen.NotificationPopupReducer.NotificationPopupViewEvent
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.navigation.notifyroute.NotificationBuilderUtils

@Composable
fun NotificationPopupScreen(navController: NavHostController) {
    val notificationPopupViewModel: NotificationPopupViewModel = hiltViewModel()

    NotificationPopupContent(notificationPopupViewModel, navController)
}

@Composable
private fun NotificationPopupContent(
    notificationPopupViewModel: NotificationPopupViewModel,
    navController: NavHostController
) {
    val (uiState, onEvent, _) = notificationPopupViewModel.unpack()
    val listModelNotificationPopupUI = uiState.listModelNotificationPopupUI
    var urlWebView by remember { mutableStateOf(BLANK) }
//    TODO: update the loading when opening the popup later
    var isShowLoadingContent by remember { mutableStateOf(false) }
    var isShowErrorPopup by remember { mutableStateOf(false) }
    var messageErrorPopup by remember { mutableStateOf(BLANK) }
    var isClosePopup by remember { mutableStateOf(false) }

    val webViewSettings = WebViewSettings(
        webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(
                view: WebView?,
                request: WebResourceRequest?
            ): Boolean {
                request?.url?.let { uri ->
                    urlWebView = uri.toString()
                }
                return true
            }

            override fun onPageStarted(
                view: WebView?,
                url: String?,
                favicon: android.graphics.Bitmap?
            ) {
                isShowLoadingContent = true
            }

            override fun onPageFinished(view: WebView?, url: String?) {
                isShowLoadingContent = false
            }
        }
    )

    if (!notificationPopupViewModel.isPopupLoadedFirst()) {
        onEvent(NotificationPopupViewEvent.OnGetNotificationPopup())
    } else if (!listModelNotificationPopupUI.isPopupLoaded) {
        onEvent(
            NotificationPopupViewEvent.OnGetNotificationPopup(isFromStorage = true)
        )
    }

    // open browser when click link in content modal
    if (urlWebView.isNotBlank()) {
        OpenBrowser(urlWebView)
        urlWebView = BLANK
    }

    if (isShowErrorPopup) {
        IBankModalConfirm(
            modalConfirmType = ModalConfirmType.Error,
            title = stringResource(R.string.loi),
            supportingText = messageErrorPopup,
            listDialogButtonInfo = listOf(
                DialogButtonInfo(
                    label = stringResource(R.string.close),
                )
            ),
            onDismissRequest = {
                isShowErrorPopup = false
                onEvent(NotificationPopupViewEvent.OnNextPopup)
            }
        )
    }

    CollectSideEffect(notificationPopupViewModel.sideEffect) { sideEffect ->
        when (sideEffect) {
            is NotificationPopupViewEffect.UpdateStatusFeedbackPopupStatusFail -> {
                messageErrorPopup = sideEffect.errorMessage.orEmpty()
                isShowErrorPopup = true
            }

            else -> {
                // do nothing
            }
        }
    }

    listModelNotificationPopupUI.listNotificationPopup?.takeIf {
        it.isNotEmpty() && listModelNotificationPopupUI.hasNext()
    }?.let {
        if (isClosePopup) return@let
        val notificationPopupUI = it[listModelNotificationPopupUI.indexPopup]
        val isCollectFeedback = notificationPopupUI.collectFeedback
        val redirectId = notificationPopupUI.additionalInfo?.popup?.redirect?.redirectId.orEmpty()
        val isRedirect = redirectId.isNotNullOrEmpty()
        val listButtonLink = notificationPopupUI.additionalInfo?.popup?.button
        val title = notificationPopupUI.title.orEmpty()
        val htmlContent = getHtmlContent(notificationPopupUI.content)

        when {
            !isCollectFeedback && !isRedirect -> {
                // 1. show popup no redirect, no collect feedback
                IBankModalConfirm(
                    title = title,
                    supportingText = htmlContent,
                    isHtmlContent = true,
                    webViewSettings = webViewSettings,
                    listDialogButtonInfo = getListButtonNoRedirectAndNoCollectFeedback(
                        listButtonLink = listButtonLink,
                        onButtonLinkClick = { url ->
                            urlWebView = url
                        }
                    ),
                    onDismissRequest = {
                        onEvent(NotificationPopupViewEvent.OnButtonDisplayClicked)
                    }
                )
            }

            isCollectFeedback && !isRedirect -> {
                // 2. show popup no redirect, has collect feed back
                IBankModalConfirm(
                    title = title,
                    supportingText = htmlContent,
                    isHtmlContent = true,
                    webViewSettings = webViewSettings,
                    listDialogButtonInfo = getListButtonNoRedirectAndHasCollectFeedback(
                        listButtonLink = listButtonLink,
                        onButtonAcceptClick = {
                            onEvent(NotificationPopupViewEvent.OnButtonFeedbackClicked)
                        },
                        onButtonLinkClick = { url ->
                            urlWebView = url
                        }
                    ),
                    onDismissRequest = {
                        onEvent(NotificationPopupViewEvent.OnButtonDisplayClicked)
                    }
                )
            }

            isCollectFeedback && isRedirect -> {
                // 3. show popup has redirect, has collect feedback
                IBankModalConfirm(
                    title = title,
                    supportingText = htmlContent,
                    isHtmlContent = true,
                    webViewSettings = webViewSettings,
                    listDialogButtonInfo = getListButtonHasRedirectAndHasCollectFeedback(
                        listButtonLink = listButtonLink,
                        onButtonAcceptClick = {
                            onEvent(NotificationPopupViewEvent.OnButtonFeedbackClicked)
                        },
                        onButtonDisplayClick = {
                            isClosePopup = true
                            onEvent(NotificationPopupViewEvent.OnButtonDisplayClicked)
                            NotificationBuilderUtils.routeToDestinationByNotyInfo(
                                navController = navController,
                                navigateId = redirectId
                            )
                        },
                        onButtonLinkClick = { url ->
                            urlWebView = url
                        }
                    ),
                    onDismissRequest = {
                        onEvent(NotificationPopupViewEvent.OnButtonDisplayClicked)
                    }
                )
            }

            else -> {
                // 4. show popup has redirect, no collect feedback
                IBankModalConfirm(
                    title = title,
                    supportingText = htmlContent,
                    isHtmlContent = true,
                    webViewSettings = webViewSettings,
                    listDialogButtonInfo = getListButtonHasRedirectAndNoCollectFeedback(
                        listButtonLink = listButtonLink,
                        onButtonDisplayClick = {
                            isClosePopup = true
                            onEvent(NotificationPopupViewEvent.OnButtonDisplayClicked)
                            NotificationBuilderUtils.routeToDestinationByNotyInfo(
                                navController = navController,
                                navigateId = redirectId
                            )
                        },
                        onButtonLinkClick = { url ->
                            onEvent(NotificationPopupViewEvent.OnButtonLinkClicked)
                            urlWebView = url
                        }
                    ),
                    onDismissRequest = {
                        onEvent(NotificationPopupViewEvent.OnButtonDisplayClicked)
                    }
                )
            }
        }

    }
}

@Composable
private fun getListButtonNoRedirectAndNoCollectFeedback(
    listButtonLink: List<Button>?,
    onButtonLinkClick: (url: String) -> Unit,
): List<DialogButtonInfo> {
    val listButtonInfo = mutableListOf<DialogButtonInfo>()

    if (listButtonLink.isNullOrEmpty()) {
        listButtonInfo.add(
            DialogButtonInfo(
                label = stringResource(R.string.dong),
            )
        )
    } else {
        listButtonLink.forEach {
            listButtonInfo.add(
                DialogButtonInfo(
                    label = it.buttonName.orEmpty(),
                    onClick = {
                        onButtonLinkClick(it.buttonValue.orEmpty())
                    },
                )
            )
        }
    }
    return listButtonInfo
}

@Composable
private fun getListButtonNoRedirectAndHasCollectFeedback(
    listButtonLink: List<Button>?,
    onButtonAcceptClick: () -> Unit,
    onButtonLinkClick: (url: String) -> Unit,
): List<DialogButtonInfo> {
    val listButtonInfo = mutableListOf<DialogButtonInfo>()
    listButtonInfo.add(
        DialogButtonInfo(
            label = stringResource(R.string.dong_y),
            isDismissRequest = false,
            onClick = {
                onButtonAcceptClick()
            }
        )
    )
    listButtonLink?.forEach {
        listButtonInfo.add(
            DialogButtonInfo(
                label = it.buttonName.orEmpty(),
                isDismissRequest = false,
                onClick = {
                    onButtonLinkClick(it.buttonValue.orEmpty())
                },
            )
        )
    }
    return listButtonInfo
}

@Composable
private fun getListButtonHasRedirectAndHasCollectFeedback(
    onButtonAcceptClick: () -> Unit,
    onButtonDisplayClick: () -> Unit,
    onButtonLinkClick: (url: String) -> Unit,
    listButtonLink: List<Button>?,
): List<DialogButtonInfo> {
    val listButtonInfo = mutableListOf<DialogButtonInfo>()
    listButtonInfo.add(
        DialogButtonInfo(
            label = stringResource(R.string.dong_y),
            isDismissRequest = false,
            onClick = {
                onButtonAcceptClick()
            }
        )
    )
    listButtonInfo.add(
        DialogButtonInfo(
            label = stringResource(R.string.xem_chi_tiet),
            isDismissRequest = false,
            onClick = {
                onButtonDisplayClick()
            }
        )
    )
    listButtonLink?.forEach {
        listButtonInfo.add(
            DialogButtonInfo(
                label = it.buttonName.orEmpty(),
                isDismissRequest = false,
                onClick = {
                    onButtonLinkClick(it.buttonValue.orEmpty())
                },
            )
        )
    }
    return listButtonInfo
}

@Composable
private fun getListButtonHasRedirectAndNoCollectFeedback(
    onButtonDisplayClick: () -> Unit,
    onButtonLinkClick: (url: String) -> Unit,
    listButtonLink: List<Button>?
): List<DialogButtonInfo> {
    val listButtonInfo = mutableListOf<DialogButtonInfo>()
    listButtonInfo.add(
        DialogButtonInfo(
            label = stringResource(R.string.xem_chi_tiet),
            isDismissRequest = false,
            onClick = {
                onButtonDisplayClick()
            }
        )
    )
    listButtonLink?.forEach {
        listButtonInfo.add(
            DialogButtonInfo(
                label = it.buttonName.orEmpty(),
                isDismissRequest = false,
                onClick = {
                    onButtonLinkClick(it.buttonValue.orEmpty())
                },
            )
        )
    }
    return listButtonInfo
}

private fun getHtmlContent(content: String?): String =
    """
        <html>
        <head>
            <style>
                p:empty {
                    display: block;
                    height: 1em;
                }
            </style>
        </head>
        <body>
            $content
        </body>
        </html>
    """