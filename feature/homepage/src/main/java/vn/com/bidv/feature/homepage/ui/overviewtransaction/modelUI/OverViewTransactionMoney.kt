package vn.com.bidv.feature.homepage.ui.overviewtransaction.modelUI

import androidx.compose.ui.graphics.Color
import vn.com.bidv.feature.homepage.domain.model.BalanceAccTypeDMO
import vn.com.bidv.localization.R
import java.math.BigDecimal

data class OverViewTransactionMoney(
    val money: BigDecimal,
    val typeMoney: OverViewMoneyType
)

enum class OverViewTransactionType {
    DEP,
    LOAN
}


fun List<BalanceAccTypeDMO>.toOverViewTransactionMoney(): List<OverViewTransactionMoney> {
    val mappedList = this.mapNotNull {
        if (it.totalBalance != BigDecimal.ZERO) {
            OverViewTransactionMoney(
                money = it.totalBalance ?: BigDecimal.ZERO,
                typeMoney = OverViewMoneyType.entries.find { overType -> overType.name == it.accType }
                    ?: OverViewMoneyType.NONE
            )
        } else {
            null
        }
    }.toMutableList()

    while (mappedList.size < 3) {
        mappedList.add(
            OverViewTransactionMoney(
                money = BigDecimal.ZERO,
                typeMoney = OverViewMoneyType.NONE
            )
        )
    }

    return mappedList
}

fun ListDefault(type: OverViewTransactionType) : List<OverViewTransactionMoney> {
    val listDefault = when (type) {
        OverViewTransactionType.DEP -> {
            return listOf(
                OverViewTransactionMoney(
                    money =  BigDecimal.ZERO,
                    typeMoney = OverViewMoneyType.DDA
                ),
                OverViewTransactionMoney(
                    money = BigDecimal.ZERO,
                    typeMoney = OverViewMoneyType.CD
                ),
                OverViewTransactionMoney(
                    money = BigDecimal.ZERO,
                    typeMoney = OverViewMoneyType.NONE
                )
            )
        }
        OverViewTransactionType.LOAN -> {
            listOf(
                OverViewTransactionMoney(
                    money = BigDecimal.ZERO,
                    typeMoney = OverViewMoneyType.SHORT_TERM
                ),
                OverViewTransactionMoney(
                    money = BigDecimal.ZERO,
                    typeMoney = OverViewMoneyType.MID_LONG_TERM
                ),
                OverViewTransactionMoney(
                    money = BigDecimal.ZERO,
                    typeMoney = OverViewMoneyType.OVER_DRAFT
                )
            )
        }
    }
    return listDefault
}




//TODO can gen lai ma mau
enum class OverViewMoneyType(val title: Int, val color: Color) {
    DDA(title = R.string.thanh_toan, color = Color(0xFFFFEEC8)),
    CD(title = R.string.co_ky_han, color = Color(0xFFFFCD5F)),
    SHORT_TERM(title = R.string.ngan_han, color = Color(0xFF9ADAD8)),
    MID_LONG_TERM(title = R.string.trung_dai_han, color = Color(0xFF37ADAA)),
    OVER_DRAFT(title = R.string.thau_chi, color =  Color(0xFF006B68)),
    NONE(title = R.string.khong_co_du_lieu, color = Color.Transparent)
}
