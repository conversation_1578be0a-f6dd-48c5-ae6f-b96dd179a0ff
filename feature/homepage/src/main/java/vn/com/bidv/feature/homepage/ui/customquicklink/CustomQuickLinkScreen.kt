package vn.com.bidv.feature.homepage.ui.customquicklink

import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.feature.homepage.ui.customquicklink.draggable.DraggableItem
import vn.com.bidv.feature.homepage.ui.customquicklink.draggable.rememberDraggableLazyGridState
import vn.com.bidv.feature.homepage.ui.customquicklink.draggable.draggable
import vn.com.bidv.designsystem.component.dataentry.IBankCheckBox
import vn.com.bidv.designsystem.component.dataentry.IBankInputSearchBase
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankActionBar
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankTopAppBar
import vn.com.bidv.feature.homepage.ui.customquicklink.CustomQuickLinkReducer.CustomQuickLinkViewEvent
import vn.com.bidv.feature.homepage.ui.customquicklink.CustomQuickLinkReducer.CustomQuickLinkViewState
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.ModelCheckAble
import vn.com.bidv.feature.common.domain.data.PermissionResDMO
import vn.com.bidv.feature.homepage.constants.HomePageConstants
import vn.com.bidv.feature.homepage.ui.service.modelUI.PermissionCode
import vn.com.bidv.feature.homepage.ui.service.modelUI.toPresenterModel
import vn.com.bidv.localization.R

@Composable
fun CustomQuickLinkScreen(navController: NavHostController) {
    val customQuickLinkViewModel: CustomQuickLinkViewModel = hiltViewModel()
    BaseScreen(
        navController = navController,
        viewModel = customQuickLinkViewModel,
        renderContent = { uiState, onEvent ->
            if (!uiState.isInitSuccess) {
                onEvent(CustomQuickLinkViewEvent.OnInitDataService)
            }
            CustomQuickLinkContent(
                uiState = uiState,
                onEvent = onEvent,
                navController = navController
            )
        },
        handleSideEffect = { sideEffect ->
            when (sideEffect) {
                is CustomQuickLinkReducer.CustomQuickLinkViewEffect.UpdateUserQuickLinkSuccessEffect -> {
                    navController.popBackStack()
                }
                else -> {
                    //Do nothing
                }
            }
        },
        topAppBarConfig = TopAppBarConfig(isShowTopAppBar = false),
        backgroundColor = LocalColorScheme.current.bgMainTertiary
    )
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun CustomQuickLinkContent(
    uiState: CustomQuickLinkViewState,
    onEvent: (CustomQuickLinkViewEvent) -> Unit,
    navController: NavHostController,
) {
    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current

    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current


    LaunchedEffect(uiState.isSearchView) {
        if (!uiState.isSearchView) {
            focusManager.clearFocus()
        } else {
            focusRequester.requestFocus()
        }
    }
    Box(modifier = Modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .windowInsetsPadding(WindowInsets.systemBars),
            verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingXs)
        )
        {

            if (!uiState.isSearchView) {
                IBankTopAppBar(
                    navController,
                    TopAppBarType.Title,
                    TopAppBarConfig(titleTopAppBar = stringResource(R.string.tuy_chinh_tinh_nang_yeu_thich))
                )

                Box(modifier = Modifier.padding(horizontal = IBSpacing.spacingM)) {
                    BoxWidgetMove(uiState, onEvent)
                }
            }

            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = IBSpacing.spacingM),
                verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingM)
            ) {
                item {
                    if (!uiState.isSearchView) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = IBSpacing.spacingXs)
                        ) {
                            Text(
                                text = if (uiState.listChildrenService.any { it.data.priorityLevel in HomePageConstants.validPriorityNormalQuickLink }) stringResource(R.string.tinh_nang_yeu_thich) else stringResource(R.string.tinh_nang_noi_bat),
                                color = colorSchema.contentMainPrimary,
                                style = typography.titleTitle_m,
                                textAlign = TextAlign.Start
                            )

                            Spacer(modifier = Modifier.height(IBSpacing.spacing2xs))

                            Text(
                                text = stringResource(R.string.vui_long_chon_tu_1_den_5_tinh_nang),
                                color = colorSchema.contentMainTertiary,
                                style = typography.bodyBody_m,
                                textAlign = TextAlign.Start
                            )
                        }
                    }
                }

                if (uiState.listChildrenService.any { it.data.priorityLevel in HomePageConstants.validPriorityNormalQuickLink }) {
                    stickyHeader {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(colorSchema.bgMainTertiary)
                        ) {

                            val inputPadding by animateDpAsState(
                                targetValue = if (uiState.isSearchView) IBSpacing.spacing4xl else IBSpacing.spacingNone,
                                animationSpec = tween(durationMillis = 300)
                            )

                            if (uiState.isSearchView) {
                                IconButton(
                                    modifier = Modifier
                                        .size(IBSpacing.spacing3xl, IBSpacing.spacing3xl)
                                        .align(Alignment.CenterStart),
                                    onClick = { onEvent(CustomQuickLinkViewEvent.SetSearchView(false)) }) {
                                    Icon(
                                        modifier = Modifier.defaultMinSize(
                                            IBSpacing.spacingL,
                                            IBSpacing.spacingL
                                        ),
                                        painter = painterResource(vn.com.bidv.designsystem.R.drawable.arrow_left_outline),
                                        contentDescription = null,
                                        tint = colorSchema.contentMainPrimary
                                    )
                                }
                            }

                            IBankInputSearchBase(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(start = inputPadding, bottom = IBSpacing.spacingXs)
                                    .focusRequester(focusRequester),
                                placeHolderText = stringResource(R.string.tim_kiem),
                                textValue = uiState.textSearch,
                                onTextChange = {
                                    onEvent(CustomQuickLinkViewEvent.UpdateSearchText(it))
                                },
                                onRequestChange = {
                                },
                                maxLength = HomePageConstants.MAX_LENGTH_SEARCH_QUICK_LINK,
                                onFocusChange = { isFocus ->
                                    if (isFocus) {
                                        onEvent(CustomQuickLinkViewEvent.SetSearchView(true))
                                    } else {
                                        onEvent(CustomQuickLinkViewEvent.SetSearchView(false))
                                    }
                                },
                            )
                        }
                    }
                }

                item {
                    ListServiceCheckBox(uiState, onEvent)
                }

                item {
                    Spacer(modifier = Modifier.height(80.dp))
                }

            }
        }

        if (!uiState.isSearchView) {
            Column(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .background(colorSchema.bgMainTertiary)
            ) {

                HorizontalDivider(
                    color = LocalColorScheme.current.borderSolidPrimary,
                    modifier = Modifier
                        .height(IBBorderDivider.borderDividerXs)
                        .fillMaxWidth()
                )

                IBankActionBar(
                    isVertical = false,
                    buttonNegative = DialogButtonInfo(
                        label = stringResource(R.string.huy),
                        onClick = {
                            navController.popBackStack()
                        }
                    ),
                    buttonPositive = DialogButtonInfo(
                        label = stringResource(R.string.ap_dung),
                        onClick = {
                            if (uiState.listQuickLink.isNotEmpty()) {
                                onEvent(CustomQuickLinkViewEvent.UpdateUserQuickLink(listQuickLink = uiState.listQuickLink))
                            }
                        },
                        isEnable = uiState.listQuickLink.isNotEmpty()
                    ),
                )
            }
        }

    }
}

@Composable
private fun BoxWidgetMove(
    uiState: CustomQuickLinkViewState,
    onEvent: (CustomQuickLinkViewEvent) -> Unit,
) {

    val lazyState = rememberDraggableLazyGridState(onSwap = { from, to ->
        onEvent(CustomQuickLinkViewEvent.RearrangeItemsQuickLink(
            fromIndex = from.index,
            toIndex = to.index
        ))
    }, isItemLocked = { itemToSwap ->
        itemToSwap.key is Int
    })

    val colorSchema = LocalColorScheme.current
    Box(
        modifier = Modifier
            .shadow(
                elevation = 6.dp, // Độ lớn của shadow
                shape = RoundedCornerShape(IBSpacing.spacingXs) // Hình dạng bo góc
            )
            .background(
                color = colorSchema.bgMainTertiary,
                shape = RoundedCornerShape(IBSpacing.spacingXs)
            )
            .padding(8.dp)
            .then(
                if (uiState.isSearchView) {
                    Modifier.height(0.dp)
                } else {
                    Modifier.wrapContentSize()
                }
            )
    ) {
        LazyVerticalGrid(
            columns = GridCells.Fixed(3),
            modifier = Modifier
                .fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(4.dp),
            horizontalArrangement = Arrangement.spacedBy(4.dp),
        ) {
            items(6) { index ->
                if (index < 5) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(
                                shape = RoundedCornerShape(IBSpacing.spacingXs),
                                color = colorSchema.bgMainTertiary
                            )
                            .drawBehind {
                                drawRoundRect(
                                    color = colorSchema.borderMainPrimary, style = Stroke(
                                        width = 2f,
                                        pathEffect = PathEffect.dashPathEffect(
                                            floatArrayOf(10f, 10f), 0f
                                        )
                                    )
                                )
                            }
                            .padding(8.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(104.dp)
                                .background(color = colorSchema.bgMainTertiary)
                        ) {
                            Icon(
                                modifier = Modifier
                                    .size(16.dp)
                                    .align(Alignment.Center),
                                painter = painterResource(id = vn.com.bidv.designsystem.R.drawable.plus),
                                contentDescription = "",
                                tint = Color.Unspecified
                            )
                        }

                    }
                } else {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(
                                shape = RoundedCornerShape(IBSpacing.spacingXs),
                                color = colorSchema.bgMainTertiary
                            )
                            .padding(8.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        MenuItemWidgetService(
                            permissionResDMO = PermissionResDMO(
                                code = PermissionCode.CUSTOM_WIDGET.code,
                                type = PermissionCode.CUSTOM_WIDGET.code
                            ),
                            showDelete = false,
                            modifier = Modifier.alpha(0.5f),
                            onEvent = onEvent
                        )
                    }
                }
            }
        }

        LazyVerticalGrid(
            columns = GridCells.Fixed(3),
            state = lazyState.gridState,
            modifier = Modifier
                .fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(4.dp),
            horizontalArrangement = Arrangement.spacedBy(4.dp),
        ) {
            items(items = uiState.listQuickLink, key = { it.code.orEmpty() }) { item ->
                DraggableItem(
                    key = item.code,
                    state = lazyState,
                    content = { isDragging, _ ->
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .background(
                                    shape = RoundedCornerShape(IBSpacing.spacingXs),
                                    color = colorSchema.bgMainTertiary
                                )
                                .draggable(
                                    draggableState = lazyState, key = item.code.orEmpty()
                                )
                                .padding(8.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            MenuItemWidgetService(
                                permissionResDMO = item,
                                showDelete = !isDragging,
                                onEvent = onEvent
                            )
                        }
                    }
                )
            }

            items(items = List(5 - uiState.listQuickLink.size) { it }, key = { it }) { item ->
                DraggableItem(
                    key = "",
                    state = lazyState,
                    content = { _, _ ->
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .background(
                                    shape = RoundedCornerShape(IBSpacing.spacingXs),
                                    color = colorSchema.bgMainTertiary
                                )
                                .draggable(
                                    draggableState = lazyState, key = item
                                )
                                .drawBehind {
                                    drawRoundRect(
                                        color = colorSchema.borderMainPrimary, style = Stroke(
                                            width = 2f,
                                            pathEffect = PathEffect.dashPathEffect(
                                                floatArrayOf(
                                                    10f,
                                                    10f
                                                ), 0f
                                            )
                                        )
                                    )
                                }
                                .padding(8.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(104.dp)
                                    .background(color = colorSchema.bgMainTertiary)
                                    .clickable {
                                        onEvent(CustomQuickLinkViewEvent.SetSearchView(true))
                                    }
                            ) {
                                Icon(
                                    modifier = Modifier
                                        .size(16.dp)
                                        .align(Alignment.Center),
                                    painter = painterResource(id = vn.com.bidv.designsystem.R.drawable.plus),
                                    contentDescription = "",
                                    tint = Color.Unspecified
                                )
                            }
                        }
                    })
            }
        }
    }
}

@Composable
fun MenuItemWidgetService(
    permissionResDMO: PermissionResDMO,
    modifier: Modifier = Modifier,
    colorBoxItem: Color = LocalColorScheme.current.bgMainPrimary,
    showDelete: Boolean = true,
    onEvent: (CustomQuickLinkViewEvent) -> Unit,
) {
    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current
    Column(
        modifier = modifier
            .fillMaxWidth()
            .height(104.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingXs)
    ) {

        Box(
            modifier = Modifier.wrapContentSize()
        ) {
            Box(
                modifier = Modifier
                    .size(IBSpacing.spacing4xl)
                    .clip(CircleShape)
                    .background(colorBoxItem),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    modifier = Modifier.size(IBSpacing.spacingL),
                    imageVector = ImageVector.vectorResource(id = permissionResDMO.toPresenterModel().icon),
                    contentDescription = null,
                    tint = Color.Unspecified,
                )
            }


            if (showDelete) {
                Box(
                    modifier = Modifier
                        .size(16.dp)
                        .align(Alignment.TopEnd)
                        .offset(x = 4.dp, y = (-4).dp)
                        .clip(CircleShape)
                        .background(colorSchema.bgNegativePrimary)
                        .clickable {
                            onEvent.invoke(
                                CustomQuickLinkViewEvent.SelectItem(
                                    ModelCheckAble(permissionResDMO, true)
                                )
                            )
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = ImageVector.vectorResource(id = vn.com.bidv.designsystem.R.drawable.minus),
                        contentDescription = null,
                        tint = colorSchema.contentOn_specialPrimary,
                        modifier = Modifier.size(12.dp)
                    )
                }
            }
        }


        Text(
            text = stringResource(permissionResDMO.toPresenterModel().title),
            color = colorSchema.contentMainSecondary,
            style = typography.bodyBody_s,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun ListServiceCheckBox(
    uiState: CustomQuickLinkViewState,
    onEvent: (CustomQuickLinkViewEvent) -> Unit
) {
    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingS)
    ) {
        val listKeyQuickLink =
            uiState.listChildrenService.filter { it.data.priorityLevel in HomePageConstants.validPriorityKeyQuickLink }
        val listNormalQuickLink =
            uiState.listChildrenService.filter { it.data.priorityLevel in HomePageConstants.validPriorityNormalQuickLink }
        if (!uiState.isSearchView) {
            if (listNormalQuickLink.isNotEmpty() && listKeyQuickLink.isNotEmpty()) {
                Text(
                    text = stringResource(R.string.tinh_nang_noi_bat),
                    color = colorSchema.contentMainPrimary,
                    style = typography.titleTitle_m,
                    textAlign = TextAlign.Start
                )
            }


            listKeyQuickLink.forEach { modelCheckAble ->
                ItemServiceCheckBox(modelCheckAble, uiState, onEvent)
            }
        }

        if (listNormalQuickLink.isNotEmpty()) {
            Text(
                text = stringResource(R.string.danh_sach_tinh_nang),
                color = colorSchema.contentMainPrimary,
                style = typography.titleTitle_m,
                textAlign = TextAlign.Start
            )
        }

        if (uiState.isSearchView) {
            uiState.listSearchChildrenService.forEach { modelCheckAble ->
                ItemServiceCheckBox(modelCheckAble, uiState, onEvent)
            }
        } else {
            listNormalQuickLink.forEach { modelCheckAble ->
                ItemServiceCheckBox(modelCheckAble, uiState, onEvent)
            }
        }

    }
}

@Composable
private fun ItemServiceCheckBox(
    data: ModelCheckAble<PermissionResDMO>,
    uiState: CustomQuickLinkViewState,
    onEvent: (CustomQuickLinkViewEvent) -> Unit
) {

    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = IBSpacing.spacingS)
            .clickable {
                onEvent.invoke(
                    CustomQuickLinkViewEvent.SelectItem(
                        data
                    )
                )
            },
        horizontalArrangement = Arrangement.spacedBy(IBSpacing.spacingS),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            modifier = Modifier.size(IBSpacing.spacing2xl),
            imageVector = ImageVector.vectorResource(id = data.data.toPresenterModel().icon),
            contentDescription = null,
            tint = Color.Unspecified,
        )

        Text(
            text = stringResource(data.data.toPresenterModel().title),
            color = colorSchema.contentMainPrimary,
            style = typography.bodyBody_l,
            textAlign = TextAlign.Start
        )

        Spacer(modifier = Modifier.weight(1f))

        IBankCheckBox(
            indeterminate = false,
            enable = (uiState.listQuickLink.size != 5 && !data.isChecked) || data.isChecked,
            checked = data.isChecked,
            onCheckedChange = {
                onEvent.invoke(
                    CustomQuickLinkViewEvent.SelectItem(
                        data
                    )
                )
            }
        )
    }
}



