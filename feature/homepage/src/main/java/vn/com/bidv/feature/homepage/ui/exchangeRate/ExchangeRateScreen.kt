package vn.com.bidv.feature.homepage.ui.exchangeRate

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import vn.com.bidv.common.ui.BaseMVIScreen
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.feature.homepage.domain.model.ExchangeRateDMO
import vn.com.bidv.feature.homepage.ui.exchangeRate.ExchangeRateReducer.ExchangeRateViewEvent
import vn.com.bidv.feature.homepage.ui.exchangeRate.ExchangeRateReducer.ExchangeRateViewState
import vn.com.bidv.feature.homepage.ui.exchangeRate.component.CurrencyTableView
import vn.com.bidv.feature.homepage.ui.widgetcommon.WidgetCommonScreen
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.navigation.IBankMainRouting

@Composable
fun ExchangeRateScreen(navController: NavController) {
    val viewModel: ExchangeRateViewModel = hiltViewModel()

    BaseMVIScreen(
        viewModel = viewModel,
        renderContent = { uiState, onEvent ->
            LaunchedEffect(Unit) {
                viewModel.sendEvent(ExchangeRateViewEvent.GetExchangeRateList)
            }
            WidgetCommonScreen(
                state = uiState.widgetState,
                onClickRetry = { onEvent(ExchangeRateViewEvent.OnRetryGetExchangeRateList) },
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(IBSpacing.spacingM)
                        .clip(RoundedCornerShape(IBSpacing.spacingS))
                        .wrapContentSize()
                        .background(Color.White),
                message = uiState.errorMessage ?: stringResource(R.string.khong_tai_duoc_du_lieu),
                messageNoAccountCif =
                    stringResource(
                        R.string.khong_co_phan_quyen_dich_vu,
                    ),
                headerView = { ExchangeRateHeaderView() },
                contentView = {
                    ExchangeRateContent(
                        uiState = uiState,
                        onEvent = onEvent,
                    )
                },
            )
        },
        handleSideEffect = {
            when (it) {
                is ExchangeRateReducer.ExchangeRateViewEffect.NavigateToExchangeRateRoute -> {
                    navController.navigate(IBankMainRouting.UtilitiesRoute.ExchangeRateRoute.route)
                }

                else -> {}
            }
        },
    )
}

@Composable
private fun ExchangeRateContent(
    uiState: ExchangeRateViewState,
    onEvent: (ExchangeRateViewEvent) -> Unit = {},
) {
    Column(
        Modifier
            .background(Color.White),
    ) {
        CurrencyTableView(exchangeRateList = uiState.exchangeRateList)
        SeeMoreButton(
            onClick = {
                onEvent(ExchangeRateViewEvent.OnClickSeeMore)
            },
            modifier = Modifier.padding(horizontal = IBSpacing.spacingM),
        )
        Spacer(modifier = Modifier.height(IBSpacing.spacingXs))
        HorizontalDivider(
            color = LocalColorScheme.current.borderMainSecondary,
            thickness = IBBorderDivider.borderDividerS,
        )
        ExchangeRateUpdatedTime(
            updatedTime = uiState.updatedDate,
            modifier = Modifier.padding(horizontal = IBSpacing.spacingM),
        )
    }
}

@Composable
private fun ExchangeRateHeaderView(modifier: Modifier = Modifier) {
    Text(
        text = stringResource(R.string.ty_gia),
        style = LocalTypography.current.titleTitle_m,
        modifier =
            Modifier
                .padding(top = IBSpacing.spacingS)
                .padding(horizontal = IBSpacing.spacingM)
                .then(modifier),
    )
}

@Composable
private fun ExchangeRateUpdatedTime(
    modifier: Modifier = Modifier,
    updatedTime: String,
) {
    Text(
        text = stringResource(R.string.cap_nhat_s, updatedTime),
        style = LocalTypography.current.bodyBody_m,
        color = LocalColorScheme.current.contentMainTertiary,
        modifier =
            Modifier
                .padding(vertical = IBSpacing.spacingM)
                .then(modifier),
    )
}

@Composable
private fun SeeMoreButton(
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
    buttonHeight: Dp = 44.dp,
) {
    Button(
        onClick = { onClick() },
        colors =
            ButtonDefaults.buttonColors(
                containerColor = LocalColorScheme.current.bgMainPrimary,
                contentColor = Color.Black,
            ),
        shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL),
        modifier =
            Modifier
                .fillMaxWidth()
                .height(buttonHeight)
                .then(modifier),
    ) {
        Text(
            text = stringResource(R.string.xem_them),
            style = LocalTypography.current.labelLabel_xl,
        )
    }
}

@Preview
@Composable
fun TableScreenPreview() {
    val uiState =
        ExchangeRateViewState(
            exchangeRateList =
                listOf(
                    ExchangeRateDMO(
                        jfxCod = "USD",
                        bidBuy = "3,000.00123",
                        bidSel = "0.891",
                    ),
                    ExchangeRateDMO(
                        jfxCod = "EUR",
                        bidBuy = "15,000.0000",
                        bidSel = "5,100.427",
                    ),
                    ExchangeRateDMO(
                        jfxCod = "JPY",
                        bidBuy = "200.1",
                        bidSel = "210.00",
                    ),
                    ExchangeRateDMO(
                        jfxCod = "USD",
                        bidBuy = "3,000.00123",
                        bidSel = "0.891",
                    ),
                    ExchangeRateDMO(
                        jfxCod = "USD",
                        bidBuy = "3,000.00123",
                        bidSel = "0.891",
                    ),
                    ExchangeRateDMO(
                        jfxCod = "USD",
                        bidBuy = "3,000.00123",
                        bidSel = "0.891",
                    ),
                ),
        )
    ExchangeRateContent(uiState = uiState) { }
}
