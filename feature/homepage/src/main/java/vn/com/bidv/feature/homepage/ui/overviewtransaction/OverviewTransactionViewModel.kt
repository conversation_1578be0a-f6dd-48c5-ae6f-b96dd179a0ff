package vn.com.bidv.feature.homepage.ui.overviewtransaction

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.feature.homepage.constants.HomePageConstants.ACCESS_FORBIDDEN_CODE
import vn.com.bidv.feature.homepage.domain.OverViewTransactionUseCase
import vn.com.bidv.feature.homepage.ui.overviewtransaction.OverviewTransactionReducer.OverviewTransactionViewEffect
import vn.com.bidv.feature.homepage.ui.overviewtransaction.OverviewTransactionReducer.OverviewTransactionViewEvent
import vn.com.bidv.feature.homepage.ui.overviewtransaction.OverviewTransactionReducer.OverviewTransactionViewState
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class OverviewTransactionViewModel @Inject constructor(
    private val overViewTransactionUseCase: OverViewTransactionUseCase
) : ViewModelIBankBase<OverviewTransactionViewState, OverviewTransactionViewEvent, OverviewTransactionViewEffect>(
    initialState = OverviewTransactionViewState(),
    reducer = OverviewTransactionReducer()
) {
    override fun handleEffect(
        sideEffect: OverviewTransactionViewEffect,
        onResult: (OverviewTransactionViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is OverviewTransactionViewEffect.GetOverallInfo -> {
                callDomain(
                    isListenAllError = true,
                    showLoadingIndicator = false,
                    onSuccess = {
                        onResult(
                            OverviewTransactionViewEvent.GetOverallInfoSuccess(overallDMO = it.data, type = sideEffect.type)
                        )
                    },
                    onFail = {
                        if (it?.isHttpError(ACCESS_FORBIDDEN_CODE) == true) {
                            onResult(
                                OverviewTransactionViewEvent.GetOverallInfoFailCif(type = sideEffect.type)
                            )
                        } else {
                            onResult(
                                OverviewTransactionViewEvent.GetOverallInfoFail(type = sideEffect.type, messageError = it?.errorMessage)
                            )
                        }
                    }
                ) {
                    overViewTransactionUseCase.getOverallInfo(type = sideEffect.type)
                }
            }
        }
    }
}
