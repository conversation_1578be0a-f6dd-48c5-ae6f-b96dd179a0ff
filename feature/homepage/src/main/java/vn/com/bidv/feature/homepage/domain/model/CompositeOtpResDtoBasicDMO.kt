package vn.com.bidv.feature.homepage.domain.model

import com.google.gson.annotations.SerializedName

data class CompositeOtpResDtoBasicDMO(
    /* Time for resending OTP */
    @SerializedName("resendOtpTime")
    val resendOtpTime: String? = null,

    /* Active OTP count */
    @SerializedName("otpActiveCount")
    val otpActiveCount: String? = null,

    /* Masked phone or email */
    @SerializedName("maskedPhoneOrEmail")
    val maskedPhoneOrEmail: String? = null,

    /* Content */
    @SerializedName("content")
    val content: String? = null,

    @SerializedName("transId")
    val transId: String? = null

)
