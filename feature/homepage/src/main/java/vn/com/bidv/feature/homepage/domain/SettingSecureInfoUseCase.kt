package vn.com.bidv.feature.homepage.domain

import android.content.Context
import com.google.gson.Gson
import com.google.gson.JsonParser
import dagger.hilt.android.qualifiers.ApplicationContext
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.feature.homepage.data.SettingSecureInfoRepository
import vn.com.bidv.feature.homepage.domain.model.SecuritySettingResDMO
import vn.com.bidv.feature.homepage.domain.model.SettingSecureInfoDMO
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import vn.com.bidv.secure.utils.BiometricUtils
import javax.inject.Inject
import vn.com.bidv.log.BLogUtil

class SettingSecureInfoUseCase @Inject constructor(
    @ApplicationContext private val context: Context,
    private val settingSecureInfoRepository: SettingSecureInfoRepository,
    private val userInfoUseCase: UserInfoUseCase,
)  {
    suspend fun getSettingSecureInfo(): DomainResult<SettingSecureInfoDMO> {
        val isBiometricEnable = BiometricUtils.isBiometricEnable(context)
        val isDeviceSupported = BiometricUtils.isDeviceSupported(context)

        val result = settingSecureInfoRepository.getSettingSecurity()
        val securitySettingResDMO = result.convert(SecuritySettingResDMO::class.java)

        val isRoleAdmin = userInfoUseCase.isAdminRole()
        val settingSecureInfoDMO = SettingSecureInfoDMO(
            isBiometricEnable = isBiometricEnable,
            isShowConfigBiometric = isDeviceSupported,
            isRoleAdmin = isRoleAdmin,
        )

        if (result is NetworkResult.Error) {
            val jsonString = Gson().toJson(settingSecureInfoDMO)
            val jsonElement = try {
                JsonParser.parseString(jsonString)
            } catch (e: Exception) {
                BLogUtil.e("Error parsing JSON: ${e.message}")
                null
            }
            return DomainResult.Error(
                data = jsonElement,
                errorMessage = result.errorMessage,
                errorCode = result.errorCode
            )
        }

        securitySettingResDMO.getSafeData()?.let {
            return DomainResult.Success(
                settingSecureInfoDMO.copy(
                    securitySettingResDMO = it
                )
            )
        }
        return DomainResult.Success(settingSecureInfoDMO)
    }
}