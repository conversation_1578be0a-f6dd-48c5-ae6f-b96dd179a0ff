package vn.com.bidv.feature.homepage.ui.contact

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.designsystem.component.feedback.snackbar.IBankSnackBarInfo
import vn.com.bidv.feature.homepage.ui.contact.ContactReducer.ContactViewEffect
import vn.com.bidv.feature.homepage.ui.contact.ContactReducer.ContactViewEvent
import vn.com.bidv.feature.homepage.ui.contact.ContactReducer.ContactViewState
import vn.com.bidv.localization.R.string
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class ContactViewModel @Inject constructor(
) : ViewModelIBankBase<ContactViewState, ContactViewEvent, ContactViewEffect>(
    initialState = ContactViewState,
    reducer = ContactReducer()
) {
    override fun handleEffect(sideEffect: ContactViewEffect, onResult: (ContactViewEvent) -> Unit) {
        if (sideEffect is ContactViewEffect.OnError) {
            showSnackBar(
                IBankSnackBarInfo(
                    message = resourceProvider.getString(string.co_loi_xay_ra_trong_qua_trinh_xu_ly_vui_long_thu_lai),
                    primaryButtonText = resourceProvider.getString(string.dong),
                )
            )
        }
    }
}
