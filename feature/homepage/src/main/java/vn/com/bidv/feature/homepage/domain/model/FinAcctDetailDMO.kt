package vn.com.bidv.feature.homepage.domain.model

import com.google.gson.annotations.SerializedName

data class FinAcctDetailDMO (

    @SerializedName("accountNo")
    val accountNo: String? = null,

    @SerializedName("accountName")
    val accountName: String? = null,

    @SerializedName("ccy")
    val ccy: String? = null,

    @SerializedName("productCode")
    val productCode: String? = null,

    @SerializedName("purposeCode")
    val purposeCode: String? = null,

    @SerializedName("accRelation")
    val accRelation: String? = null,

    @SerializedName("accType")
    val accType: String? = null,

    @SerializedName("default")
    val default: Boolean? = null
)