package vn.com.bidv.feature.homepage.domain

import vn.com.bidv.feature.homepage.data.HomepageRepository
import vn.com.bidv.feature.homepage.domain.model.BalanceCurrencyDMO
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class PaymentAccountBalanceUseCase
    @Inject
    constructor(
        private val homepageRepository: HomepageRepository,
    ) {
        suspend fun getAccountBalanceInfo(): DomainResult<BalanceCurrencyDMO> {
            val domainData = homepageRepository.getListAccountBalanceByCurr()
            val convertedData = domainData.convert(BalanceCurrencyDMO::class.java)
            return convertedData
        }
    }
