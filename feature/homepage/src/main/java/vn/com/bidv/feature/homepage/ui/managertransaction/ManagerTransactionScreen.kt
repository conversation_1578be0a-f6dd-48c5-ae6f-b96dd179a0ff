package vn.com.bidv.feature.homepage.ui.managertransaction

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.common.extenstion.isNotNullOrEmpty
import vn.com.bidv.common.ui.BaseMVIScreen
import vn.com.bidv.designsystem.component.datadisplay.badge.IBankBadgeLabel
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelColor
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelSize
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelType
import vn.com.bidv.designsystem.component.feedback.bottomsheet.IBankBottomSheet
import vn.com.bidv.feature.homepage.ui.managertransaction.ManagerTransactionReducer.ManagerTransactionViewEvent
import vn.com.bidv.feature.homepage.ui.managertransaction.ManagerTransactionReducer.ManagerTransactionViewState
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.feature.common.domain.data.PermissionResDMO
import vn.com.bidv.feature.common.domain.data.TxnCountDMO
import vn.com.bidv.feature.homepage.ui.exchangeRate.ExchangeRateReducer.ExchangeRateViewEvent
import vn.com.bidv.feature.homepage.ui.service.modelUI.toPresenterModel
import vn.com.bidv.feature.homepage.ui.widgetcommon.WidgetCommonScreen
import vn.com.bidv.localization.R

@Composable
fun ManagerTransactionScreen(navController: NavHostController) {
    val managerTransactionViewModel: ManagerTransactionViewModel = hiltViewModel()
    BaseMVIScreen(
        viewModel = managerTransactionViewModel,
        renderContent = { uiState, onEvent ->
            if (!uiState.isInitData) {
                onEvent(ManagerTransactionViewEvent.OnInitData)
            }

            ManagerTransactionContent(
                uiState = uiState,
                onEvent = onEvent,
                navController = navController
            )
        },
        handleSideEffect = {

        }
    )
}

@Composable
private fun ManagerTransactionContent(
    uiState: ManagerTransactionViewState,
    onEvent: (ManagerTransactionViewEvent) -> Unit,
    navController: NavHostController
) {
    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current

    LaunchedEffect(Unit) {
        if (uiState.isReload == true) {
            onEvent(ManagerTransactionViewEvent.UpdateStatusReloadScreen(false))
            onEvent(ManagerTransactionViewEvent.OnInitData)
        }
    }
    WidgetCommonScreen(
        state = uiState.state,
        onClickRetry = {
            onEvent(ManagerTransactionViewEvent.RefreshData)
        },
        message = uiState.messageError ?: stringResource(R.string.khong_tai_duoc_du_lieu),
        modifier = Modifier
            .wrapContentHeight()
            .padding(IBSpacing.spacingM)
            .clip(
                RoundedCornerShape(
                    IBSpacing.spacingS
                )
            )
            .background(color = colorSchema.bgMainTertiary)
            .fillMaxWidth(),
        headerView = {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        horizontal = IBSpacing.spacingM
                    )
                    .padding(top = IBSpacing.spacingS),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(R.string.quan_ly_giao_dich),
                    color = colorSchema.contentMainPrimary,
                    style = typography.titleTitle_m,
                    textAlign = TextAlign.Start
                )

                Spacer(modifier = Modifier.weight(1f))

                Icon(
                    modifier = Modifier
                        .graphicsLayer(scaleX = -1f)
                        .size(IBSpacing.spacingL)
                        .clickable {
                            onEvent(ManagerTransactionViewEvent.RefreshData)
                        },
                    imageVector = ImageVector.vectorResource(id = vn.com.bidv.designsystem.R.drawable.refresh),
                    contentDescription = null,
                    tint = colorSchema.contentMainPrimary,
                )
            }
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = IBSpacing.spacingM
                )
                .padding(bottom = IBSpacing.spacingS),
            verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingXs)
        ) {
            uiState.listTxnCount.forEach {
                ItemManagerTransactionSheet(
                    title = stringResource(PermissionResDMO(code = it.code).toPresenterModel().title),
                    parentCode = it.code.orEmpty(),
                    number = it.total.toString(),
                    subItems = it.txnCountDtos ?: emptyList(),
                    navController,
                    onEvent
                )
            }
        }
    }

}

@Composable
fun ItemManagerTransactionSheet(
    title: String,
    parentCode: String,
    number: String,
    subItems: List<TxnCountDMO>,
    navigation: NavHostController,
    onEvent: (ManagerTransactionViewEvent) -> Unit,
) {
    var isShowBottomSheet by remember { mutableStateOf(false) }
    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current

    Column(
        modifier = Modifier
            .clip(RoundedCornerShape(IBSpacing.spacingXs))
            .background(color = colorSchema.bgMainPrimary)
            .fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable {
                    if (subItems.isNotEmpty()) {
                        isShowBottomSheet = true
                    }
                }
                .padding(horizontal = IBSpacing.spacingM, vertical = IBSpacing.spacingS),
            horizontalArrangement = Arrangement.spacedBy(IBSpacing.spacingS),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = title,
                color = colorSchema.contentMainPrimary,
                style = typography.bodyBody_m,
                textAlign = TextAlign.Start,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.weight(1f)
            )

            Spacer(Modifier.width(8.dp))

            Text(
                text = number,
                color = colorSchema.contentMainPrimary,
                style = typography.headlineHeadline_s,
                textAlign = TextAlign.Start
            )

            Icon(
                modifier = Modifier
                    .size(IBSpacing.spacingL),
                imageVector = ImageVector.vectorResource(id = vn.com.bidv.designsystem.R.drawable.arrow_right),
                contentDescription = null,
                tint = colorSchema.contentMainPrimary,
            )
        }


        if (isShowBottomSheet) {
            ManagerTransactionBottomSheet(
                onEvent = onEvent,
                subItems = subItems,
                parentCode = parentCode,
                navigation = navigation,
                title = title,
                onDismiss = {
                    isShowBottomSheet = false
                }
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ManagerTransactionBottomSheet(
    onEvent: (ManagerTransactionViewEvent) -> Unit,
    subItems: List<TxnCountDMO>,
    parentCode: String,
    navigation: NavHostController,
    title: String,
    onDismiss: (() -> Unit)? = {}
) {
    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current
    IBankBottomSheet(
        title = title,
        onDismiss = onDismiss,
        applyMinHeight = false
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
        ) {
            LazyColumn(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingXs)
            ) {
                items(subItems) { item ->
                    val itemPermission = PermissionResDMO(code = parentCode + item.menuCode).toPresenterModel()
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(
                                vertical = IBSpacing.spacingXs,
                                horizontal = IBSpacing.spacingM
                            )
                            .clickable {
                                if (itemPermission.routeID.isNotNullOrEmpty()) {
                                    onEvent(
                                        ManagerTransactionViewEvent.UpdateStatusReloadScreen(
                                            true
                                        )
                                    )
                                    navigation.navigate(itemPermission.routeID.orEmpty())
                                }
                            },
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(IBSpacing.spacingS)
                    ) {
                        Icon(
                            modifier = Modifier.size(IBSpacing.spacing2xl),
                            imageVector = ImageVector.vectorResource(id = itemPermission.icon),
                            contentDescription = null,
                            tint = colorSchema.contentMainPrimary
                        )

                        Text(
                            text = stringResource(itemPermission.title),
                            color = colorSchema.contentMainPrimary,
                            style = typography.bodyBody_m,
                            maxLines = 2,
                            modifier = Modifier.weight(1f)
                        )

                        Spacer(Modifier.width(8.dp))

                        IBankBadgeLabel(
                            modifier = Modifier,
                            badgeColor = LabelColor.LIGHT_GRAY,
                            badgeSize = LabelSize.SM,
                            badgeType = LabelType.PILL,
                            title = item.count.toString()
                        )
                    }
                }
            }
        }
    }
}
