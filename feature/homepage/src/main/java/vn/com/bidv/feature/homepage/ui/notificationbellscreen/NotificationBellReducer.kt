package vn.com.bidv.feature.homepage.ui.notificationbellscreen

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.homepage.ui.notificationbellscreen.modelui.ModelCountNotifyUnreadUI

class NotificationBellReducer :
    Reducer<NotificationBellReducer.NotificationBellViewState,
            NotificationBellReducer.NotificationBellViewEvent,
            NotificationBellReducer.NotificationBellViewEffect> {

    @Immutable
    data class NotificationBellViewState(
        val modelCountNotifyUnreadUI: ModelCountNotifyUnreadUI = ModelCountNotifyUnreadUI.getDefault()
    ) : ViewState

    @Immutable
    sealed interface NotificationBellViewEvent : ViewEvent {
        data object GetTotalNotifyUnread : NotificationBellViewEvent

        data class GetTotalNotifyUnreadSuccess(
            val total: Long?
        ) : NotificationBellViewEvent
    }

    @Immutable
    sealed interface NotificationBellViewEffect : SideEffect {
        data object GetTotalNotifyUnread : NotificationBellViewEffect
    }

    override fun reduce(
        previousState: NotificationBellViewState,
        event: NotificationBellViewEvent,
    ): Pair<NotificationBellViewState, NotificationBellViewEffect?> {
        val previousUiState = previousState.modelCountNotifyUnreadUI

        return when (event) {
            is NotificationBellViewEvent.GetTotalNotifyUnread -> {
                previousState to NotificationBellViewEffect.GetTotalNotifyUnread
            }

            is NotificationBellViewEvent.GetTotalNotifyUnreadSuccess -> {
                NotificationBellViewState(
                    modelCountNotifyUnreadUI = previousUiState.copy(
                        totalNotifyUnread = event.total
                    )
                ) to null
            }
        }
    }
}
