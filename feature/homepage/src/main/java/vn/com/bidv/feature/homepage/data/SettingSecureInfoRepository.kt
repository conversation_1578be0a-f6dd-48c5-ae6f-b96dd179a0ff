package vn.com.bidv.feature.homepage.data

import vn.com.bidv.feature.common.data.utilities.apis.UtilitiesApi
import vn.com.bidv.feature.common.data.utilities.model.SecuritySettingResDto
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.network.domain.BaseRepository
import javax.inject.Inject

class SettingSecureInfoRepository @Inject constructor(
    private val service: UtilitiesApi
) : BaseRepository() {

    suspend fun getSettingSecurity(): NetworkResult<SecuritySettingResDto> = launch {
        service.getSecuritySettings()
    }
}