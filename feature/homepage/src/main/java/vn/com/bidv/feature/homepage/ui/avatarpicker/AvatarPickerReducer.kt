package vn.com.bidv.feature.homepage.ui.avatarpicker

import android.net.Uri
import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState

class AvatarPickerReducer :
    Reducer<AvatarPickerReducer.AvatarPickerViewState, AvatarPickerReducer.AvatarPickerViewEvent, AvatarPickerReducer.AvatarPickerViewEffect> {

    @Immutable
    data class AvatarPickerViewState(
        val isInitSuccess: Boolean = false,
        val profileImageUrl: String = "",
    ) : ViewState

    @Immutable
    sealed class AvatarPickerViewEvent : ViewEvent {
        data object OnInitData : AvatarPickerViewEvent()
        data class OnInitDataSuccess(val profileImage: String?) : AvatarPickerViewEvent()
        data class OnChangeAvatar(val imageUri: Uri) : AvatarPickerViewEvent()
        data class OnChangeAvatarSuccess(val avatarUrl: String?) : AvatarPickerViewEvent()
    }

    @Immutable
    sealed class AvatarPickerViewEffect : SideEffect {
        data object OnInitData : AvatarPickerViewEffect()
        data class ChangeAvatar(val imageUri: Uri) : AvatarPickerViewEffect()
    }

    override fun reduce(
        previousState: AvatarPickerViewState,
        event: AvatarPickerViewEvent,
    ): Pair<AvatarPickerViewState, AvatarPickerViewEffect?> {
        return when (event) {
            is AvatarPickerViewEvent.OnInitData -> {
                previousState to AvatarPickerViewEffect.OnInitData
            }

            is AvatarPickerViewEvent.OnInitDataSuccess -> {
                previousState.copy(
                    isInitSuccess = true,
                    profileImageUrl = event.profileImage ?: ""
                ) to null
            }

            is AvatarPickerViewEvent.OnChangeAvatar -> {
                previousState to AvatarPickerViewEffect.ChangeAvatar(
                    imageUri = event.imageUri
                )
            }

            is AvatarPickerViewEvent.OnChangeAvatarSuccess -> {
                previousState.copy(
                    profileImageUrl = event.avatarUrl ?: ""
                ) to null
            }

        }
    }
}
