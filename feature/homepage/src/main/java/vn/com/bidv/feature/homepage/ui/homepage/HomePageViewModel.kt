package vn.com.bidv.feature.homepage.ui.homepage

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.feature.common.domain.notificationcommon.CommonNotyMarkAsReadUseCase
import vn.com.bidv.feature.common.domain.notificationcommon.TabNotiType
import vn.com.bidv.feature.homepage.ui.homepage.HomePageReducer.HomePageViewEffect
import vn.com.bidv.feature.homepage.ui.homepage.HomePageReducer.HomePageViewEvent
import vn.com.bidv.feature.homepage.ui.homepage.HomePageReducer.HomePageViewState
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class HomePageViewModel @Inject constructor(
    private val commonNotyMarkAsReadUseCase: CommonNotyMarkAsReadUseCase,
    private val userInfoUseCase: UserInfoUseCase
) : ViewModelIBankBase<HomePageViewState, HomePageViewEvent, HomePageViewEffect>(
    initialState = HomePageViewState(),
    reducer = HomePageReducer()
) {
    override fun handleEffect(
        sideEffect: HomePageViewEffect,
        onResult: (HomePageViewEvent) -> Unit
    ) {
        when (sideEffect) {
            HomePageViewEffect.CheckShowQrEffect -> {
                onResult(
                    HomePageViewEvent.GetIsShowQr(isShowQr = userInfoUseCase.isMakerRole())
                )
            }
        }
    }

    fun cleanNotificationData() {
        viewModelScope.launch {
            localRepository.pushNotification(null)
        }
    }

    fun markNotificationAsRead(notifyId: Long?, displayTab: String?, tabNotiType: TabNotiType) {
        viewModelScope.launch {
            commonNotyMarkAsReadUseCase.markNotyItemRead(
                notifyId = notifyId,
                displayTab = displayTab,
                tabNotiType = tabNotiType
            )
        }
    }
}
