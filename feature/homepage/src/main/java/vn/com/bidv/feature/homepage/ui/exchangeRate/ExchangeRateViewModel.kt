package vn.com.bidv.feature.homepage.ui.exchangeRate

import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.feature.homepage.domain.ExchangeRateUseCase
import vn.com.bidv.feature.homepage.domain.model.ExchangeRateDMO
import vn.com.bidv.feature.homepage.ui.exchangeRate.ExchangeRateReducer.ExchangeRateViewEffect
import vn.com.bidv.feature.homepage.ui.exchangeRate.ExchangeRateReducer.ExchangeRateViewEvent
import vn.com.bidv.feature.homepage.ui.exchangeRate.ExchangeRateReducer.ExchangeRateViewState
import vn.com.bidv.feature.homepage.utils.formatExchangeRate
import vn.com.bidv.feature.homepage.utils.formatZeroDecimalValue
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import vn.com.bidv.sdkbase.utils.formatMoney
import javax.inject.Inject

@HiltViewModel
class ExchangeRateViewModel
    @Inject
    constructor(
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        private val exchangeRateUseCase: ExchangeRateUseCase,
    ) : ViewModelIBankBase<ExchangeRateViewState, ExchangeRateViewEvent, ExchangeRateViewEffect>(
            initialState = ExchangeRateViewState(),
            reducer = ExchangeRateReducer(),
        ) {
        override fun handleEffect(
            sideEffect: ExchangeRateViewEffect,
            onResult: (ExchangeRateViewEvent) -> Unit,
        ) {
            when (sideEffect) {
                is ExchangeRateViewEffect.FetchExchangeRateList -> {
                    callDomain(
                        isListenAllError = true,
                        showLoadingIndicator = false,
                        onFail = {
                            it?.let {
                                onResult(
                                    ExchangeRateViewEvent.GetExchangeRateListFailed(
                                        errorMessage = it.errorMessage,
                                        errorCode = it.errorCode,
                                    ),
                                )
                            }
                        },
                        onSuccess = { accBalance ->
                            val exchangeRateData = accBalance.data ?: return@callDomain

                            if (exchangeRateData.items.isNullOrEmpty()) return@callDomain

                            val formattedItems =
                                exchangeRateData.items.map { item ->
                                    ExchangeRateDMO(
                                        jfxCod = item.jfxCod,
                                        cash = item.cash.orEmpty(),
                                        bidBuy = item.bidBuy.formatExchangeRate(currCode = null).formatZeroDecimalValue(),
                                        bidSel = item.bidSel.formatExchangeRate(currCode = null).formatZeroDecimalValue(),
                                    )
                                }

                            val result = exchangeRateData.copy(items = formattedItems)
                            onResult(ExchangeRateViewEvent.GetExchangeRateListSuccess(result))
                        },
                    ) {
                        exchangeRateUseCase.getExchangeRateList()
                    }
                }

                else -> {}
            }
        }
    }
