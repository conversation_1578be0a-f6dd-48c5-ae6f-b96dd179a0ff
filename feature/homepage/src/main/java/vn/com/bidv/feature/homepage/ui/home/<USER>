package vn.com.bidv.feature.homepage.ui.home

import android.app.Activity
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.core.view.WindowInsetsControllerCompat
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.common.extenstion.isNotNullOrEmpty
import vn.com.bidv.common.ui.BaseMVIScreen
import vn.com.bidv.common.utils.CollectSideEffect
import vn.com.bidv.feature.homepage.ui.home.HomeReducer.HomeViewEvent
import vn.com.bidv.feature.homepage.ui.home.HomeReducer.HomeViewState
import vn.com.bidv.designsystem.component.datadisplay.avatar.AvatarInfo
import vn.com.bidv.designsystem.component.datadisplay.avatar.IBankAvatar
import vn.com.bidv.designsystem.component.feedback.bottomsheet.IBankBottomSheet
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.component.navigation.button.IBankNormalButton
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonSize
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonType
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankTopAppBar
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.feature.common.domain.data.PermissionResDMO
import vn.com.bidv.feature.common.domain.data.UserResDMO
import vn.com.bidv.feature.homepage.R
import vn.com.bidv.feature.homepage.constants.HomePageConstants.RELOAD_AVATAR
import vn.com.bidv.feature.homepage.constants.HomePageConstants.RELOAD_DATA_HOME
import vn.com.bidv.feature.homepage.constants.HomePageConstants.RELOAD_QUICK_LINK
import vn.com.bidv.feature.homepage.constants.HomePageConstants.RELOAD_WIDGET
import vn.com.bidv.feature.homepage.constants.WidgetType
import vn.com.bidv.feature.homepage.navigation.HomePageNavigationHelper
import vn.com.bidv.feature.homepage.ui.advertisementbanner.AdvertisementBannerScreen
import vn.com.bidv.feature.homepage.ui.exchangeRate.ExchangeRateScreen
import vn.com.bidv.feature.homepage.ui.managertransaction.ManagerTransactionScreen
import vn.com.bidv.feature.homepage.ui.notificationbellscreen.NotificationBellScreen
import vn.com.bidv.feature.homepage.ui.overviewtransaction.OverviewTransactionScreen
import vn.com.bidv.feature.homepage.ui.paymentaccountbalance.PaymentAccountBalanceScreen
import vn.com.bidv.feature.homepage.ui.paymentaccountdetails.PaymentAccountDetailsScreen
import vn.com.bidv.feature.homepage.ui.service.MenuItemService
import vn.com.bidv.feature.homepage.ui.service.modelUI.PermissionCode
import vn.com.bidv.feature.homepage.ui.service.modelUI.toPresenterModel
import vn.com.bidv.sdkbase.navigation.NavigationHelper
import vn.com.bidv.sdkbase.ui.component.snackbar.SnackBarPosition
import vn.com.bidv.sdkbase.ui.component.snackbar.pinSnackBarPosition
import vn.com.bidv.designsystem.R as RDesignSystem
import vn.com.bidv.localization.R as RLocalization

@Composable
fun HomeScreen(navController: NavHostController) {
    val homeViewModel: HomeViewModel = hiltViewModel()
    BaseMVIScreen(
        viewModel = homeViewModel,
        renderContent = { uiState, onEvent ->
            CollectSideEffect(homeViewModel.subscribeShareData(RELOAD_DATA_HOME)) {
                if (it.data == RELOAD_QUICK_LINK) {
                    onEvent(HomeViewEvent.UpdateQuickLink)
                }

                if (it.data == RELOAD_WIDGET) {
                    onEvent(HomeViewEvent.UpdateWidget)
                }

                if (it.data == RELOAD_AVATAR) {
                    onEvent(HomeViewEvent.UpdateAvatar)
                }
            }

            if (!uiState.isInitSuccess) {
                onEvent(HomeViewEvent.OnInitWelcomeUser)
            }
            HomeContent(
                uiState = uiState,
                onEvent = onEvent,
                navController = navController
            )
        },
        handleSideEffect = {
            if (it is HomeReducer.HomeViewEffect.LogoutSuccess) {
                NavigationHelper.navigationToLogin(navController)
            }
        }
    )
}

@Composable
private fun HomeContent(
    uiState: HomeViewState,
    onEvent: (HomeViewEvent) -> Unit,
    navController: NavHostController
) {
    val colorSchema = LocalColorScheme.current
    val lazyListState = rememberLazyListState()
    val activity = LocalContext.current as? Activity

    val isTop by remember {
        derivedStateOf { lazyListState.firstVisibleItemIndex == 0 && lazyListState.firstVisibleItemScrollOffset == 0 }
    }

    activity?.window?.let {
        val controller = WindowInsetsControllerCompat(it, it.decorView)
        controller.isAppearanceLightStatusBars = !isTop
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(colorSchema.bgMainPrimary)
        , contentAlignment = Alignment.BottomEnd
    ) {
        LazyColumn(
            modifier = Modifier
                .fillMaxSize(),
            state = lazyListState,
        ) {
            item {
                AppBarHome(navController, uiState, onEvent)
            }

            item {
                WidgetView(navController, widgets = uiState.widgets, uiState = uiState)
            }

        }

        Box(
            modifier = Modifier
                .wrapContentHeight()
                .pinSnackBarPosition(
                    snackBarPosition = SnackBarPosition.TopOut, margin = IBSpacing.spacingXs
                )
        )

    }
}

@Composable
private fun AppBarHome(
    navController: NavHostController,
    uiState: HomeViewState,
    onEvent: (HomeViewEvent) -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight()
    ) {
        Image(
            painter = painterResource(id = R.drawable.background_header_home),
            contentDescription = "Full Screen Image",
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop,
        )

        IBankTopAppBar(
            navController = null,
            topAppBarType = TopAppBarType.Result,
            topAppBarConfig = TopAppBarConfig(
                isShowNavigationIcon = true,
                iconLeading = vn.com.bidv.designsystem.R.drawable.bidv_logo
            ) {
                TopAppBarConfigHome(navController, uiState, onEvent)
            }
        )
    }
}

@Composable
private fun TopAppBarConfigHome(
    navController: NavHostController,
    uiState: HomeViewState,
    onEvent: (HomeViewEvent) -> Unit
) {
    var isShowUserInfoBottomSheet by remember { mutableStateOf(false) }
    var isShowConfirmLogoutModal by remember { mutableStateOf(false) }

    Row(
        modifier = Modifier
            .wrapContentWidth()
            .padding(end = IBSpacing.spacingM),
        horizontalArrangement = Arrangement.spacedBy(IBSpacing.spacingXs),
        verticalAlignment = Alignment.CenterVertically
    )
    {
        NotificationBellScreen(navController)

        IBankAvatar(
            modifier = Modifier
                .size(32.dp)
                .clickable {
                    isShowUserInfoBottomSheet = true
                },
            placeholderIcon = vn.com.bidv.designsystem.R.drawable.anonymous_avatar,
            avatarInfo = AvatarInfo(
                uiState.userInfo.fullname,
                uiState.userInfo.profileImage
            )
        )


        if (isShowUserInfoBottomSheet) {
            ShowUserInfoBottomSheet(
                onDismiss = {
                    isShowUserInfoBottomSheet = false
                },
                onClickLogout = {
                    isShowConfirmLogoutModal = true
                },
                onClickUserInfo = {
                    HomePageNavigationHelper.navigateToUserInfoScreen(navController)
                },
                onClickAvatarSetting = {
                    HomePageNavigationHelper.navigateToAvatarPickerScreen(navController)
                },
                userInfo = uiState.userInfo
            )
        }

        if (isShowConfirmLogoutModal) {
            IBankModalConfirm(
                modalConfirmType = ModalConfirmType.Question,
                title = stringResource(RLocalization.string.xac_nhan_dang_xuat),
                supportingText = stringResource(RLocalization.string.quy_khach_muon_dang_xuat_tai_khoan),
                listDialogButtonInfo = listOf(
                    DialogButtonInfo(
                        label = stringResource(RLocalization.string.xac_nhan),
                        onClick = {
                            onEvent(HomeViewEvent.OnLogout)
                        }
                    ),
                    DialogButtonInfo(
                        label = stringResource(RLocalization.string.huy),
                        onClick = {
                            isShowConfirmLogoutModal = false
                        }
                    )

                ),
                isShowIconClose = false,
                onDismissRequest = {
                    isShowConfirmLogoutModal = false
                }
            )
        }
    }
}

@Composable
fun QuickLinkView(
    navController: NavHostController,
    quickLinks: List<PermissionResDMO>,
) {
    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current

    Box(
        modifier = Modifier
            .wrapContentHeight()
            .fillMaxWidth()
    ) {
        Box(
            modifier = Modifier
                .align(Alignment.Center)
                .padding(IBSpacing.spacingS)
                .shadow(
                    elevation = IBSpacing.spacing2xs,
                    shape = RoundedCornerShape(IBSpacing.spacingXs),
                    clip = false
                )
                .background(
                    color = colorSchema.bgMainTertiary,
                    shape = RoundedCornerShape(IBSpacing.spacingXs)
                )
                .padding(IBSpacing.spacingM)
        ) {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingS)
            ) {
                quickLinks.chunked(3).forEach { rowItems ->
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        rowItems.forEach { item ->
                            Box(
                                modifier = Modifier
                                    .weight(1f)
                            ) {
                                MenuItemService(
                                    permissionResDMO = item,
                                    navigation = navController,
                                    colorBoxItem = Color.Transparent,
                                    onClickItem = {
                                        if (PermissionCode.CUSTOM_WIDGET.code == item.code) {
                                            HomePageNavigationHelper.navigateToCustomQuickLink(
                                                navController
                                            )
                                        } else {
                                            if (item.toPresenterModel().routeID.isNotNullOrEmpty()) {
                                                navController.navigate(item.toPresenterModel().routeID.orEmpty())
                                            }
                                        }
                                    }
                                )
                            }
                        }

                        repeat(3 - rowItems.size) {
                            Spacer(modifier = Modifier.weight(1f))
                        }
                    }

                }
            }
        }
    }
}

@Composable
private fun WidgetView(
    navController: NavHostController,
    widgets: List<String>,
    uiState: HomeViewState
) {
    val colorSchema = LocalColorScheme.current
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .background(colorSchema.bgMainPrimary)
            .offset(y = -(106).dp)
    ) {
        if (uiState.quickLinks.size > 1) {
            QuickLinkView(navController, uiState.quickLinks)
        }

        widgets.forEach {
            when (it) {
                WidgetType.Widget_PaymetAcctDetail.name -> {
                    PaymentAccountDetailsScreen()
                }

                WidgetType.Widget_NotiDueAmount.name -> {

                }

                WidgetType.Widget_AvailblePmtBal.name -> {
                    PaymentAccountBalanceScreen(navController)
                }

                WidgetType.Widget_ExchangeRate.name -> {
                    ExchangeRateScreen(navController)
                }

                WidgetType.Widget_OverView.name -> {
                    OverviewTransactionScreen()
                }

                WidgetType.Widget_TransManage.name -> {
                    ManagerTransactionScreen(navController)
                }
            }
        }

        AdvertisementBannerScreen(navController)

        IBankNormalButton(
            modifier = Modifier
                .align(Alignment.CenterHorizontally)
                .padding(horizontal = IBSpacing.spacingM),
            size = NormalButtonSize.M(LocalTypography.current),
            type = NormalButtonType.NONOPAQUE(LocalColorScheme.current),
            leadingIcon = ImageVector.vectorResource(id = vn.com.bidv.designsystem.R.drawable.edit_outline),
            text = stringResource(vn.com.bidv.localization.R.string.tuy_chinh),
            onClick = {
                HomePageNavigationHelper.navigateToCustomWidget(
                    navController
                )
            },
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ShowUserInfoBottomSheet(
    onDismiss: () -> Unit,
    onClickLogout: () -> Unit,
    onClickUserInfo: () -> Unit,
    onClickAvatarSetting: () -> Unit,
    userInfo: UserResDMO,
) {
    IBankBottomSheet(
        onDismiss = onDismiss,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingNone, Alignment.Top),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                IBankAvatar(
                    modifier = Modifier
                        .size(60.dp),
                    AvatarInfo(
                        userInfo.fullname,
                        userInfo.profileImage
                    )
                )

                Spacer(modifier = Modifier.height(IBSpacing.spacingS))

                userInfo.fullname?.let {
                    Text(
                        text = it,
                        style = LocalTypography.current.headlineHeadline_s,
                        textAlign = TextAlign.Center,
                        modifier = Modifier
                            .padding(
                                horizontal = IBSpacing.spacingM,
                            ),
                    )
                }

                userInfo.cifName?.let {
                    Text(
                        text = it,
                        style = LocalTypography.current.bodyBody_l,
                        textAlign = TextAlign.Center,
                        modifier = Modifier
                            .padding(
                                horizontal = IBSpacing.spacingM,
                            ),
                    )
                }

                Spacer(modifier = Modifier.height(IBSpacing.spacingM))

            }
            RowUserInfo(
                title = stringResource(RLocalization.string.cai_dat_hinh_dai_dien),
                iconId = RDesignSystem.drawable.cai_dat_giao_dien_outline,
                onClick = {
                    onDismiss()
                    onClickAvatarSetting()
                }
            )
            RowUserInfo(
                title = stringResource(RLocalization.string.thong_tin_nguoi_dung),
                iconId = RDesignSystem.drawable.user_outline,
                onClick = {
                    onDismiss()
                    onClickUserInfo()
                }
            )
            RowUserInfo(
                title = stringResource(RLocalization.string.dang_xuat),
                iconId = RDesignSystem.drawable.sign_out_outline,
                isTypeLogout = true,
                onClick = {
                    onDismiss()
                    onClickLogout()
                }
            )
        }
    }
}

@Composable
private fun RowUserInfo(
    title: String,
    iconId: Int,
    onClick: () -> Unit,
    isTypeLogout: Boolean = false
) {
    val colorRow = if (isTypeLogout) {
        LocalColorScheme.current.contentNegativePrimary
    } else {
        LocalColorScheme.current.contentMainPrimary
    }

    Row(
        modifier = Modifier
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null,
                onClick = onClick
            )
            .background(color = LocalColorScheme.current.bgMainTertiary)
            .fillMaxWidth()
            .heightIn(min = 48.dp)
            .padding(
                horizontal = IBSpacing.spacingM,
                vertical = IBSpacing.spacingS
            ),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = ImageVector.vectorResource(iconId),
            contentDescription = null,
            tint = colorRow,
            modifier = Modifier.size(IBSpacing.spacing2xl)
        )

        Spacer(modifier = Modifier.width(IBSpacing.spacingS))

        Box(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = LocalTypography.current.bodyBody_l,
                color = colorRow,
            )
        }
    }
}




