package vn.com.bidv.feature.homepage.ui.home

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.common.domain.data.PermissionResDMO
import vn.com.bidv.feature.common.domain.data.UserResDMO

class HomeReducer :
    Reducer<HomeReducer.HomeViewState, HomeReducer.HomeViewEvent, HomeReducer.HomeViewEffect> {

    data class HomeViewState(
        val isInitSuccess: Boolean = false,
        val userInfo: UserResDMO = UserResDMO(),
        val quickLinks: List<PermissionResDMO> = emptyList(),
        val widgets: List<String> = emptyList()
    ) : ViewState

    @Immutable
    sealed class HomeViewEvent : ViewEvent {
        data object OnInitWelcomeUser : HomeViewEvent()
        data class GetWelcomeUserInfoFromStorageSuccess(
            val userInfo: UserResDMO,
            val quickLinks: List<PermissionResDMO>,
            val widgets: List<String>
        ) : HomeViewEvent()

        data object UpdateQuickLink : HomeViewEvent()
        data object UpdateWidget : HomeViewEvent()
        data class UpdateQuickLinkSuccess(val quickLinks: List<PermissionResDMO>) : HomeViewEvent()
        data class UpdateWidgetSuccess(val widgets: List<String>) : HomeViewEvent()
        data object OnUpdateLoginStatusSuccess : HomeViewEvent()
        data object OnUpdateUserLanguage : HomeViewEvent()
        data object OnLogout : HomeViewEvent()
        data object OnLogoutSuccess : HomeViewEvent()
        data object UpdateAvatar : HomeViewEvent()
        data class UpdateAvatarSuccess(val userInfo: UserResDMO) : HomeViewEvent()
    }

    @Immutable
    sealed class HomeViewEffect : SideEffect {
        data object GetWelcomeUserInfoFromStorage : HomeViewEffect()
        data object UpdateQuickLinkSideEffect : HomeViewEffect()
        data object UpdateWidgetSideEffect : HomeViewEffect()
        data object UpdateFCMIdSideEffect : HomeViewEffect()
        data object UpdateLoginStatusSuccess : HomeViewEffect()
        data object UpdateUserLanguage : HomeViewEffect()
        data object Logout : HomeViewEffect()
        data object LogoutSuccess : HomeViewEffect(), UIEffect
        data object UpdateAvatarSideEffect : HomeViewEffect()
    }

    override fun reduce(
        previousState: HomeViewState,
        event: HomeViewEvent,
    ): Pair<HomeViewState, HomeViewEffect?> {
        return when (event) {
            is HomeViewEvent.OnInitWelcomeUser -> {
                previousState.copy(
                    isInitSuccess = true
                ) to HomeViewEffect.GetWelcomeUserInfoFromStorage
            }

            is HomeViewEvent.GetWelcomeUserInfoFromStorageSuccess -> {
                previousState.copy(
                    userInfo = event.userInfo,
                    quickLinks = event.quickLinks,
                    widgets = event.widgets
                ) to HomeViewEffect.UpdateFCMIdSideEffect
            }

            is HomeViewEvent.UpdateQuickLink -> {
                previousState to HomeViewEffect.UpdateQuickLinkSideEffect
            }

            is HomeViewEvent.UpdateQuickLinkSuccess -> {
                previousState.copy(
                    quickLinks = event.quickLinks
                ) to null
            }

            is HomeViewEvent.OnUpdateLoginStatusSuccess -> {
                previousState to HomeViewEffect.UpdateLoginStatusSuccess
            }

            is HomeViewEvent.OnUpdateUserLanguage -> {
                previousState to HomeViewEffect.UpdateUserLanguage
            }

            is HomeViewEvent.UpdateWidget -> {
                previousState to HomeViewEffect.UpdateWidgetSideEffect
            }

            is HomeViewEvent.UpdateWidgetSuccess -> {
                previousState.copy(
                    widgets = event.widgets
                ) to null
            }

            is HomeViewEvent.OnLogout -> {
                previousState to HomeViewEffect.Logout
            }

            is HomeViewEvent.OnLogoutSuccess -> {
                previousState to HomeViewEffect.LogoutSuccess
            }

            is HomeViewEvent.UpdateAvatar -> {
                previousState to HomeViewEffect.UpdateAvatarSideEffect
            }

            is HomeViewEvent.UpdateAvatarSuccess -> {
                previousState.copy(
                    userInfo = event.userInfo,
                ) to null
            }

            else -> {
                previousState to null
            }
        }
    }
}
