package vn.com.bidv.feature.homepage.domain

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import vn.com.bidv.common.sharePreference.Storage
import vn.com.bidv.feature.homepage.common.enum.NotificationDataKeys
import vn.com.bidv.feature.homepage.constants.NotificationPopupConstants.COLLECT_FEEDBACK_YES
import vn.com.bidv.feature.homepage.constants.NotificationPopupConstants.STATUS_UPDATE_LINK
import vn.com.bidv.feature.homepage.data.NotificationPopupRepository
import vn.com.bidv.feature.homepage.domain.notificationpopupdmo.AdditionalInfo
import vn.com.bidv.feature.homepage.domain.notificationpopupdmo.ModelListPopupNotifyStatementResponseDMO
import vn.com.bidv.feature.homepage.domain.notificationpopupdmo.ModelNotificationPopupDMO
import vn.com.bidv.feature.homepage.domain.notificationpopupdmo.ModelPopupNotifyStatementResponseDMO
import vn.com.bidv.feature.homepage.domain.notificationpopupdmo.ModelPopupNotifyUserStatementRequestDMO
import vn.com.bidv.log.BLogUtil
import vn.com.bidv.sdkbase.data.LocalRepository
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class NotificationPopupUseCase @Inject constructor(
    private val notificationPopupRepository: NotificationPopupRepository,
    private val localRepository: LocalRepository
) {
    suspend fun getNotificationPopup(isFromStorage: Boolean): DomainResult<ModelListPopupNotifyStatementResponseDMO> {
        localRepository.setPopupLoadedStatus(isLoaded = true)
        if (isFromStorage) {
            val listPopup = getListPopupNotificationFromStorage()
            return DomainResult.Success(
                ModelListPopupNotifyStatementResponseDMO(
                    listNotificationPopup = listPopup,
                    total = listPopup?.size?.toLong(),
                )
            )
        }
        val result = notificationPopupRepository.getPopupUser()
        val listNotificationPopupResponseDMO =
            result.convert(ModelListPopupNotifyStatementResponseDMO::class.java)

        listNotificationPopupResponseDMO.getSafeData()?.let {
            val listPopup = convertNotificationPopupDmoToList(it.items)
            updateListPopupNotificationInStorage(listPopup = listPopup)
            return DomainResult.Success(
                it.apply {
                    listNotificationPopup = listPopup
                }
            )
        }
        return listNotificationPopupResponseDMO
    }

    suspend fun updateStatusNotificationPopup(
        id: Long?,
        type: String
    ): DomainResult<Unit> {
        id?.let {
            if (type != STATUS_UPDATE_LINK) {
                removePopupInCacheById(id = id)
            }
            val result = notificationPopupRepository.updateStatusPopupUser(
                ModelPopupNotifyUserStatementRequestDMO(
                    id = id,
                    type = type
                )
            )
            return result.convert {}
        }
        return DomainResult.Success(null)
    }

    fun removePopupInCacheById(id: Long?) {
        val listPopup = getListPopupNotificationFromStorage()?.filter { it.id != id }
        updateListPopupNotificationInStorage(listPopup = listPopup)
    }

    private fun getListPopupNotificationFromStorage(): List<ModelNotificationPopupDMO>? {
        return try {
            val typeListPopup = object : TypeToken<List<ModelNotificationPopupDMO>>() {}.type
            Gson().fromJson(
                Storage.get(NotificationDataKeys.NOTIFICATION_POPUP_LIST),
                typeListPopup
            )
        } catch (e: Exception) {
            BLogUtil.e("getListPopupNotificationFromStorage error ${e.message}")
            null
        }
    }

    private fun updateListPopupNotificationInStorage(
        listPopup: List<ModelNotificationPopupDMO>?
    ) {
        listPopup?.runCatching {
            val listPopupJson = Gson().toJson(listPopup)
            Storage.put(NotificationDataKeys.NOTIFICATION_POPUP_LIST, listPopupJson)
        }
    }

    private fun convertNotificationPopupDmoToList(
        listPopupNotifyResponseDMO: List<ModelPopupNotifyStatementResponseDMO>?
    ): List<ModelNotificationPopupDMO>? {
        val gson = Gson()
        return listPopupNotifyResponseDMO?.map { popup ->
            val additionalInfo = gson.fromJson(popup.additionalInfo, AdditionalInfo::class.java)
            val collectFeedback = popup.collectFeedback == COLLECT_FEEDBACK_YES
            ModelNotificationPopupDMO(
                id = popup.id,
                title = popup.title,
                content = popup.content,
                collectFeedback = collectFeedback,
                priorityLevel = popup.priorityLevel,
                additionalInfo = additionalInfo
            )
        }?.sortedWith(compareBy {
            it.priorityLevel
        })
    }
}