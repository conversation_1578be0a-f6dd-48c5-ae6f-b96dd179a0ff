package vn.com.bidv.feature.homepage.domain.notificationpopupdmo

data class ModelNotificationPopupDMO(
    val id: Long? = null,
    val title: String? = null,
    val content: String? = null,
    val collectFeedback: Boolean = false,
    val priorityLevel: Int? = null,
    val additionalInfo: AdditionalInfo? = null
)

data class AdditionalInfo(
    val popup: Popup?
)

data class Popup(
    val redirect: Redirect?,
    val button: List<Button>?
)

data class Redirect(
    val redirectId: String?,
    val params: Any?
)

data class Button(
    val buttonName: String?,
    val buttonValue: String?,
    val buttonOrder: Int?
)