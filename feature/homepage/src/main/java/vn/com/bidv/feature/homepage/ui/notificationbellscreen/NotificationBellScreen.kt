package vn.com.bidv.feature.homepage.ui.notificationbellscreen

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.navigation.NavHostController
import vn.com.bidv.common.utils.unpack
import vn.com.bidv.designsystem.component.datadisplay.badge.IBankNotiBadge
import vn.com.bidv.designsystem.component.datadisplay.badge.NotiColor
import vn.com.bidv.designsystem.component.datadisplay.badge.NotiSize
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.feature.homepage.navigation.HomePageNavigationHelper
import vn.com.bidv.feature.homepage.ui.homepage.HomePageReducer.HomePageViewEvent
import vn.com.bidv.feature.homepage.ui.homepage.HomePageReducer.HomePageViewState
import vn.com.bidv.feature.homepage.ui.homepage.HomePageViewModel
import vn.com.bidv.feature.homepage.ui.notificationbellscreen.NotificationBellReducer.NotificationBellViewEvent
import vn.com.bidv.feature.homepage.ui.notificationbellscreen.NotificationBellReducer.NotificationBellViewState
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants.NotificationConstants.FCM_TRIGGER_UPDATE_TOTAL_NOTIFICATION_UNREAD
import vn.com.bidv.sdkbase.utils.orDefault

@Composable
fun NotificationBellScreen(navController: NavHostController) {
    val notificationBellViewModel: NotificationBellViewModel = hiltViewModel()
    val homePageViewModel: HomePageViewModel = hiltViewModel()
    val (uiState, onEvent, _) = notificationBellViewModel.unpack()
    val (uiHomeState, onHomeEvent, _) = homePageViewModel.unpack()

    NotificationBellContent(uiState, uiHomeState, onEvent, onHomeEvent, navController)
}

@Composable
private fun NotificationBellContent(
    uiState: NotificationBellViewState,
    uiHomeState: HomePageViewState,
    onEvent: (NotificationBellViewEvent) -> Unit,
    onHomeEvent: (HomePageViewEvent) -> Unit,
    navController: NavHostController
) {
    val totalNotifyUnread = uiState.modelCountNotifyUnreadUI.totalNotifyUnread.orDefault()
    val shouldFetchTotalNotifyUnread = uiHomeState.shouldFetchTotalNotifyUnread

    if (shouldFetchTotalNotifyUnread) {
        onEvent(NotificationBellViewEvent.GetTotalNotifyUnread)
        onHomeEvent(HomePageViewEvent.MarkTotalNotifyAsShouldFetch(false))
    }

    DisposableEffect(Unit) {
        val receiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                onEvent(NotificationBellViewEvent.GetTotalNotifyUnread)
            }
        }

        val intentFilter = IntentFilter(FCM_TRIGGER_UPDATE_TOTAL_NOTIFICATION_UNREAD)
        LocalBroadcastManager.getInstance(navController.context)
            .registerReceiver(receiver, intentFilter)

        onDispose {
            LocalBroadcastManager.getInstance(navController.context).unregisterReceiver(receiver)
        }
    }

    NotificationBellButton(
        count = totalNotifyUnread,
        onClick = {
            HomePageNavigationHelper.navigateToNotificationScreen(navController)
        })
}

@Composable
fun NotificationBellButton(
    count: Long,
    onClick: () -> Unit
) {
    val displayCount = if (count > 99L) "99+" else count.toString()
    val colorSchema = LocalColorScheme.current

    Box {
        IconButton(
            onClick = onClick,
            modifier = Modifier.align(Alignment.Center)
        ) {
            Icon(
                modifier = Modifier.size(24.dp),
                painter = painterResource(vn.com.bidv.designsystem.R.drawable.notification),
                contentDescription = null,
                tint = colorSchema.contentOn_specialPrimary
            )
        }

        if (count > 0L) {
            Box(
                modifier = Modifier
                    .offset(x = (22).dp, y = (4).dp)
                    .clip(RoundedCornerShape(IBCornerRadius.cornerRadiusRound))
                    .background(colorSchema.bgMainTertiary),
                contentAlignment = Alignment.Center
            ) {
                IBankNotiBadge(
                    title = displayCount,
                    modifier = Modifier
                        .padding(2.dp),
                    badgeSize = NotiSize.SMALL,
                    badgeColor = NotiColor.RED
                )
            }
        }
    }
}

@Preview
@Composable
fun TestNotificationBellButton() {
    NotificationBellButton(
        count = 10,
        onClick = {}
    )
}
