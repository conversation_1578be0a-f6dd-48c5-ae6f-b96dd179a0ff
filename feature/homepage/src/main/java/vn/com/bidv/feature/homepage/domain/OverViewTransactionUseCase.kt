package vn.com.bidv.feature.homepage.domain

import vn.com.bidv.feature.homepage.data.HomepageRepository
import vn.com.bidv.feature.homepage.domain.model.DataListOverallBalanceDMO
import vn.com.bidv.feature.homepage.domain.model.OverallDMO
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class OverViewTransactionUseCase @Inject constructor(
    private val homepageRepository: HomepageRepository
) {
    suspend fun getOverallInfo(type: String): DomainResult<DataListOverallBalanceDMO> {
        val domain = homepageRepository.getOverallInfo(type)
        val result = domain.convert(DataListOverallBalanceDMO::class.java)
        return result
    }
}