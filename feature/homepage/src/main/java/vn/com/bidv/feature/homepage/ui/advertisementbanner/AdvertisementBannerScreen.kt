package vn.com.bidv.feature.homepage.ui.advertisementbanner

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.feature.homepage.navigation.HomepageRoute
import vn.com.bidv.feature.homepage.ui.advertisementbanner.model.AdvertisementBannerModelUI
import vn.com.bidv.feature.homepage.ui.advertisementbanner.model.AdvertisementBannerType
import vn.com.bidv.localization.R

@Composable
fun AdvertisementBannerScreen(navController: NavHostController) {
    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current
    val banners = AdvertisementBannerModelUI.entries.toList()

    Column(
        modifier = Modifier
            .padding(
                vertical = IBSpacing.spacingS
            )
            .padding(start = IBSpacing.spacingM)
            .fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingS)
    ) {
        Text(
            text = stringResource(R.string.moi_nhat_tu_bidv),
            color = colorSchema.contentMainPrimary,
            style = typography.titleTitle_m,
            textAlign = TextAlign.Start
        )

        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(IBSpacing.spacingM)
        ) {
            items(banners) {
                if (it.type == AdvertisementBannerType.AdvertisementBannerDetail) {
                    ItemBannerTypeDetail(
                        it,
                        navController
                    )
                } else {
                    ItemBannerTypeFull(
                        it,
                        navController
                    )
                }
            }
        }
    }

}

@Composable
private fun ItemBannerTypeDetail(
    item: AdvertisementBannerModelUI,
    navController: NavHostController
) {
    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current
    val sizeItem = LocalConfiguration.current.screenWidthDp.dp * 0.5f - IBSpacing.spacing2xl
    Box(
        modifier = Modifier
            .width(sizeItem)
            .height(sizeItem)
            .clip(
                RoundedCornerShape(
                    IBSpacing.spacingXs
                )
            )
            .border(
                width = IBSpacing.spacing3xs / 2,
                color = colorSchema.borderMainPrimary,
                shape = RoundedCornerShape(IBSpacing.spacingXs)
            )
            .background(color = colorSchema.bgMainTertiary)
            .clickable {
                navController.navigate(
                    HomepageRoute.WebViewBannerRoute(item)
                )
            }
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingXs)
        ) {

            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(sizeItem / 2)
                    .padding(top = IBSpacing.spacing2xs)
                    .padding(horizontal = IBSpacing.spacing2xs)
            )
            {
                Image(
                    painter = painterResource(id = item.urlResId),
                    contentDescription = "Full Screen Image",
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(IBSpacing.spacing2xs)),
                    contentScale = ContentScale.Crop,
                )
            }
            Text(
                text = item.title,
                color = colorSchema.contentMainPrimary,
                style = typography.bodyBody_s,
                textAlign = TextAlign.Start,
                modifier = Modifier.padding(horizontal = IBSpacing.spacingXs)
            )

            Row(
                modifier = Modifier.padding(horizontal = IBSpacing.spacing2xs),
                horizontalArrangement = Arrangement.spacedBy(IBSpacing.spacing2xs),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Spacer(modifier = Modifier.weight(1f))

                Text(
                    text = stringResource(R.string.chi_tiet),
                    color = colorSchema.contentBrand_01Primary,
                    style = typography.labelLabel_m,
                    modifier = Modifier.padding(horizontal = IBSpacing.spacing2xs)
                )

                Icon(
                    modifier = Modifier
                        .size(IBSpacing.spacingL),
                    imageVector = ImageVector.vectorResource(id = vn.com.bidv.designsystem.R.drawable.arrow2_right_outline),
                    contentDescription = null,
                    tint = colorSchema.contentBrand_01Primary,
                )
            }
        }
    }
}

@Composable
private fun ItemBannerTypeFull(
    item: AdvertisementBannerModelUI,
    navController: NavHostController
) {
    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current
    val sizeItem = LocalConfiguration.current.screenWidthDp.dp * 0.5f - IBSpacing.spacing2xl

    Box(
        modifier = Modifier
            .width(sizeItem)
            .height(sizeItem)
            .clip(
                RoundedCornerShape(
                    IBSpacing.spacingXs
                )
            )
            .border(
                width = IBSpacing.spacing3xs / 2,
                color = colorSchema.borderMainPrimary,
                shape = RoundedCornerShape(IBSpacing.spacingXs)
            )
            .background(color = colorSchema.bgMainTertiary)
            .clickable {
                navController.navigate(
                    HomepageRoute.WebViewBannerRoute(item)
                )
            }
    ) {
        Image(
            painter = painterResource(id = item.urlResId),
            contentDescription = "Full Screen Image",
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop,
        )

        Text(
            text = item.title,
            color = colorSchema.contentMainPrimary,
            style = typography.titleTitle_m,
            textAlign = TextAlign.Start,
            modifier = Modifier.padding(
                vertical = IBSpacing.spacingS,
                horizontal = IBSpacing.spacingXs
            )
        )
    }

}

