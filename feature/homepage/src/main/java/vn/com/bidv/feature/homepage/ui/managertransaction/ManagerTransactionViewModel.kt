package vn.com.bidv.feature.homepage.ui.managertransaction

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.feature.common.domain.GetPendingApprovalCountUseCase
import vn.com.bidv.feature.homepage.domain.ManagerTransactionUseCase
import vn.com.bidv.feature.homepage.ui.managertransaction.ManagerTransactionReducer.ManagerTransactionViewEffect
import vn.com.bidv.feature.homepage.ui.managertransaction.ManagerTransactionReducer.ManagerTransactionViewEvent
import vn.com.bidv.feature.homepage.ui.managertransaction.ManagerTransactionReducer.ManagerTransactionViewState
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class ManagerTransactionViewModel @Inject constructor(
    private val managerTransactionUseCase: ManagerTransactionUseCase
) : ViewModelIBankBase<ManagerTransactionViewState, ManagerTransactionViewEvent, ManagerTransactionViewEffect>(
    initialState = ManagerTransactionViewState(),
    reducer = ManagerTransactionReducer()
) {
    override fun handleEffect(
        sideEffect: ManagerTransactionViewEffect,
        onResult: (ManagerTransactionViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is ManagerTransactionViewEffect.GetTxnIdManagerTransaction -> {
                callDomain(
                    isListenAllError = true,
                    showLoadingIndicator = false,
                    onSuccess = {
                        onResult(
                            ManagerTransactionViewEvent.GetTxnIdManagerTransactionSuccess(it.data?.items?.filter { item -> item.total != 0 } ?: emptyList())
                        )
                    },
                    onFail = {
                        onResult(
                            ManagerTransactionViewEvent.GetTxnIdManagerTransactionFail(it?.errorMessage)
                        )
                    }
                ) {
                    managerTransactionUseCase.getTxnCount()
                }
            }

            else -> {

            }
        }
    }
}

