package vn.com.bidv.feature.homepage.domain

import vn.com.bidv.feature.common.constants.FunctionCode
import vn.com.bidv.feature.common.constants.MenuCode
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.feature.homepage.domain.model.InquiryAccountType
import vn.com.bidv.sdkbase.domain.DomainResult
import javax.inject.Inject

class GetInquiryPermissionUseCase @Inject constructor(private val userInfoUseCase: UserInfoUseCase) {
    operator fun invoke(): DomainResult<List<InquiryAccountType>> {
        val accountTypes = listOf(
            MenuCode.M_ACCOUNT_QUERY_DDA to InquiryAccountType.PAYMENT,
            MenuCode.M_ACCOUNT_QUERY_CD to InquiryAccountType.DEPOSIT,
            MenuCode.M_ACCOUNT_QUERY_LN to InquiryAccountType.LOAN,
            MenuCode.M_ACCOUNT_QUERY_BG to InquiryAccountType.BAO_LANH
        ).mapNotNull { (menuCode, accountType) ->
            accountType.takeIf {
                userInfoUseCase.getListFunctionCode(menuCode)
                    .contains(FunctionCode.INQUIRY)
            }
        }
        return DomainResult.Success(accountTypes)
    }
}