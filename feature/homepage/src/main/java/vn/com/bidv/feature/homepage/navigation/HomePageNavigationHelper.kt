package vn.com.bidv.feature.homepage.navigation

import androidx.navigation.NavHostController
import vn.com.bidv.sdkbase.navigation.IBankMainRouting
import vn.com.bidv.sdkbase.utils.exts.navigateWithArgument

object HomePageNavigationHelper {
    internal fun navigateToUserSettingScreen(navigation: NavHostController) {
        navigation.navigate(HomepageRoute.USER_SETTING_ROUTE)
    }

    internal fun navigateToSettingSecureInfoScreen(navigation: NavHostController) {
        navigation.navigate(HomepageRoute.SETTING_SECURE_INFO_ROUTE)
    }

    internal fun navigateToReActiveSmartOtpScreen(navigation: NavHostController) {
        navigation.navigateWithArgument(
            route = IBankMainRouting.AuthRoutes.ReActiveSmartOtpRoute.route,
            listData = listOf(
                Pair(IBankMainRouting.AuthRoutes.ARG_TRANS_AUTH, null)
            )
        )
    }

    internal fun navigateToManageQuestionsScreen(navigation: NavHostController) {
        navigation.navigate(IBankMainRouting.AuthRoutes.ManageQuestionsRoute.route)
    }

    internal fun navigateToManageApprovalRequestScreen(navigation: NavHostController) {
        navigation.navigate(IBankMainRouting.AuthRoutes.ManageApprovalRequestsRoute.route)
    }

    internal fun navigateToNotificationSettingScreen(navigation: NavHostController) {
        navigation.navigate(IBankMainRouting.NotifyRoute.NotifySettingMainRoute.route)
    }

    internal fun navigateToNotificationScreen(navigation: NavHostController) {
        navigation.navigate(IBankMainRouting.NotifyRoute.NotifyMainRoute.route)
    }

    internal fun navigateToPositiveChangePwScreen(navigation: NavHostController) {
        navigation.navigate(IBankMainRouting.AuthRoutes.PositiveChangePwRoute.route)
    }

    internal fun navigateToCustomQuickLink(navigation: NavHostController) {
        navigation.navigate(HomepageRoute.CUSTOM_QUICK_LINK_ROUTE)
    }

    internal fun navigateToCustomWidget(navigation: NavHostController) {
        navigation.navigate(HomepageRoute.CUSTOM_WIDGET_ROUTE)
    }

    internal fun navigateToChangePinScreen(navigation: NavHostController) {
        navigation.navigate(IBankMainRouting.AuthRoutes.ChangePinRoute.route)
    }

    fun navigateToTurnOnBiometric(navigation: NavHostController, turnOnBiometric: Boolean) {
        navigation.navigateWithArgument(
            route = IBankMainRouting.AuthRoutes.TurnOnBiometricRoute.route,
            listData = listOf(
                Pair(
                    IBankMainRouting.AuthRoutes.ARG_TRANS_AUTH,
                    turnOnBiometric.toString()
                )
            )
        )
    }

    fun navigateToSettingLanguageScreen(navigation: NavHostController) {
        navigation.navigate(IBankMainRouting.AuthRoutes.SettingLanguageRoute.route)
    }

    fun navigateToAvatarPickerScreen(navigation: NavHostController) {
        navigation.navigate(IBankMainRouting.HomeRoute.AvatarPickerRoute.route)
    }

    fun navigateToUserInfoScreen(navigation: NavHostController) {
        navigation.navigate(IBankMainRouting.AuthRoutes.UserInfoRoute.route)
    }
}
