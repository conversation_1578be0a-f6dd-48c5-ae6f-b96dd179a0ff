package vn.com.bidv.feature.homepage.ui.settingsecureinfo

import com.google.gson.Gson
import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.feature.homepage.domain.SettingSecureInfoUseCase
import vn.com.bidv.feature.homepage.domain.model.SettingSecureInfoDMO
import vn.com.bidv.feature.homepage.ui.settingsecureinfo.SettingSecureInfoReducer.SettingSecureInfoViewEffect
import vn.com.bidv.feature.homepage.ui.settingsecureinfo.SettingSecureInfoReducer.SettingSecureInfoViewEvent
import vn.com.bidv.feature.homepage.ui.settingsecureinfo.SettingSecureInfoReducer.SettingSecureInfoViewState
import vn.com.bidv.log.BLogUtil
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class SettingSecureInfoViewModel @Inject constructor(
    private val settingSecureInfoUseCase: SettingSecureInfoUseCase,
) : ViewModelIBankBase<SettingSecureInfoViewState, SettingSecureInfoViewEvent, SettingSecureInfoViewEffect>(
    initialState = SettingSecureInfoViewState(),
    reducer = SettingSecureInfoReducer()
) {
    override fun handleEffect(
        sideEffect: SettingSecureInfoViewEffect,
        onResult: (SettingSecureInfoViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is SettingSecureInfoViewEffect.GetSettingSecureInfo -> {
                callDomain(
                    isListenAllError = true,
                    showLoadingIndicator = sideEffect.isShowLoading,
                    onSuccess = {
                        onResult(
                            SettingSecureInfoViewEvent.OnGetSettingSecureInfoSuccess(
                                it.data ?: SettingSecureInfoDMO()
                            )
                        )
                    },
                    onFail = {
                        val settingSecureInfo = it?.data.let { data ->
                            runCatching { Gson().fromJson(data, SettingSecureInfoDMO::class.java) }
                                .onFailure { e -> BLogUtil.e("Error parsing UserSmartOtpDMO: ${e.message}") }
                                .getOrNull()
                        }
                        onResult(
                            SettingSecureInfoViewEvent.OnGetSettingSecureInfoFail(
                                data = settingSecureInfo ?: SettingSecureInfoDMO(),
                                errorMessage = it?.errorMessage ?: ""
                            )
                        )
                    }
                ) {
                    settingSecureInfoUseCase.getSettingSecureInfo()
                }
            }

            else -> {
                //nothing
            }
        }
    }
}
