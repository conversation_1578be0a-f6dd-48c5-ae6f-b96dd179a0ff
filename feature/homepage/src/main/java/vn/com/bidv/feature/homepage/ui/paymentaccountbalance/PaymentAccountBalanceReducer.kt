package vn.com.bidv.feature.homepage.ui.paymentaccountbalance

import androidx.compose.runtime.Immutable
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.homepage.common.enum.CurrencyCode
import vn.com.bidv.feature.homepage.constants.HomePageConstants
import vn.com.bidv.feature.homepage.domain.WidgetState
import vn.com.bidv.feature.homepage.domain.model.BalanceCurrDetailDMO
import vn.com.bidv.feature.homepage.domain.model.BalanceCurrencyDMO
import vn.com.bidv.sdkbase.utils.exts.toDateWithSeconds

class PaymentAccountBalanceReducer :
    Reducer<PaymentAccountBalanceReducer.PaymentAccountBalanceViewState, PaymentAccountBalanceReducer.PaymentAccountBalanceViewEvent, PaymentAccountBalanceReducer.PaymentAccountBalanceViewEffect> {
    @Immutable
    data class PaymentAccountBalanceViewState(
        val state: WidgetState = WidgetState.CONTENT,
        val shouldHideBalance: Boolean = false,
        val dataFetchedAtLeastOnce: Boolean = false,
        val updatedDate: String = "",
        val accountBalanceList: List<BalanceCurrDetailDMO> =
            List(4) {
                BalanceCurrDetailDMO(
                    CurrencyCode.IC_CURRENCY_DEFAULT_HIDDEN.currencyCode,
                )
            }, // initial list before fetching data first time
        var shouldShowBottomSheet: Boolean = false,
        val cardItemWidth: Dp = 194.dp,
        val cardItemHeight: Dp = 104.dp,
        val contentContainerHeight: Dp = 152.dp, // Subtract the height of the view container from the height of the AccountBalanceHeader
        val isInitialized: Boolean = false,
        val errorMessage: String = ""
    ) : ViewState

    @Immutable
    sealed interface PaymentAccountBalanceViewEvent : ViewEvent {
        data object OnToggleShowBalanceButton : PaymentAccountBalanceViewEvent

        data class GetAccountBalanceListSuccess(
            val accountBalanceInfo: BalanceCurrencyDMO,
        ) : PaymentAccountBalanceViewEvent

        data object GetAccountBalanceList : PaymentAccountBalanceViewEvent

        data object OnRefreshData : PaymentAccountBalanceViewEvent

        data object OnToggleShowBottomSheet : PaymentAccountBalanceViewEvent

        data class GetAccountBalanceListFailed(
            val errorCode: String,
            val errorMessage: String
        ) : PaymentAccountBalanceViewEvent

        data object OnClickSeeMore : PaymentAccountBalanceViewEvent

        data object FetchUserInfo : PaymentAccountBalanceViewEvent

        data class OnMaskedData(
            val shouldDataMasked: Boolean,
        ) : PaymentAccountBalanceViewEvent
    }

    @Immutable
    sealed interface PaymentAccountBalanceViewEffect : SideEffect {
        data object FetchAccountBalanceList : PaymentAccountBalanceViewEffect

        data object OnClickSeeMore : PaymentAccountBalanceViewEffect, UIEffect

        data object FetchUserInfo : PaymentAccountBalanceViewEffect
    }

    override fun reduce(
        previousState: PaymentAccountBalanceViewState,
        event: PaymentAccountBalanceViewEvent,
    ): Pair<PaymentAccountBalanceViewState, PaymentAccountBalanceViewEffect?> =
        when (event) {
            is PaymentAccountBalanceViewEvent.OnToggleShowBalanceButton -> {
                // Update the state:
                // - Set `hasDataFetchedOnce = true` to ensure data is fetched only once.
                // - Toggle `shouldHideBalance` to show/hide the account balance.
                if (!previousState.dataFetchedAtLeastOnce) {
                    previousState.copy(
                        shouldHideBalance = !previousState.shouldHideBalance,
                        state = WidgetState.LOADING,
                        dataFetchedAtLeastOnce = true,
                    ) to PaymentAccountBalanceViewEffect.FetchAccountBalanceList
                } else {
                    previousState.copy(
                        shouldHideBalance = !previousState.shouldHideBalance,
                    ) to null
                }
            }

            PaymentAccountBalanceViewEvent.GetAccountBalanceList ->
                previousState.copy(state = WidgetState.LOADING) to PaymentAccountBalanceViewEffect.FetchAccountBalanceList

            is PaymentAccountBalanceViewEvent.GetAccountBalanceListSuccess ->
                previousState.copy(
                    state = if (event.accountBalanceInfo.balCurrList.isNullOrEmpty()) WidgetState.NOCONTENT else WidgetState.CONTENT,
                    updatedDate =
                        event.accountBalanceInfo.updatedDate
                            .orEmpty()
                            .toDateWithSeconds(),
                    shouldHideBalance = false,
                    accountBalanceList = event.accountBalanceInfo.balCurrList.orEmpty(),
                ) to null

            PaymentAccountBalanceViewEvent.OnRefreshData ->
                previousState.copy(dataFetchedAtLeastOnce = true, state = WidgetState.LOADING) to
                    PaymentAccountBalanceViewEffect.FetchAccountBalanceList

            PaymentAccountBalanceViewEvent.OnToggleShowBottomSheet ->
                previousState.copy(
                    shouldShowBottomSheet = !previousState.shouldShowBottomSheet,
                ) to null

            PaymentAccountBalanceViewEvent.OnClickSeeMore ->
                previousState to PaymentAccountBalanceViewEffect.OnClickSeeMore

            PaymentAccountBalanceViewEvent.FetchUserInfo ->
                previousState.copy(isInitialized = true) to PaymentAccountBalanceViewEffect.FetchUserInfo

            is PaymentAccountBalanceViewEvent.OnMaskedData ->
                previousState.copy(
                    shouldHideBalance = event.shouldDataMasked,
                    dataFetchedAtLeastOnce = !event.shouldDataMasked,
                ) to
                    if (event.shouldDataMasked) {
                        null
                    } else {
                        PaymentAccountBalanceViewEffect.FetchAccountBalanceList
                    }

            is PaymentAccountBalanceViewEvent.GetAccountBalanceListFailed -> {
                val screenState =
                    if (event.errorCode.contains(HomePageConstants.ACCESS_FORBIDDEN_CODE)) {
                        WidgetState.NO_ACCOUNT_CIF
                    } else {
                        WidgetState.ERROR
                    }
                previousState.copy(
                    state = screenState,
                    errorMessage = event.errorMessage
                ) to null
            }
        }
}
