package vn.com.bidv.feature.homepage.domain

import android.content.Context
import android.net.Uri
import dagger.hilt.android.qualifiers.ApplicationContext
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import vn.com.bidv.feature.homepage.constants.HomePageConstants.RELOAD_AVATAR
import vn.com.bidv.feature.homepage.constants.HomePageConstants.RELOAD_DATA_HOME
import vn.com.bidv.feature.homepage.data.HomepageRepository
import vn.com.bidv.feature.homepage.domain.model.UploadAvatarDMO
import vn.com.bidv.sdkbase.data.LocalRepository
import vn.com.bidv.sdkbase.data.ShareDataDTO
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import java.io.File
import java.io.FileOutputStream
import javax.inject.Inject

class AvatarUseCase @Inject constructor(
    private val homepageRepository: HomepageRepository,
    private val localRepository: LocalRepository,
    @ApplicationContext private val context: Context
) {
    suspend fun updateAvatar(imageUri: Uri): DomainResult<UploadAvatarDMO> {
        val file = uriToFile(context, imageUri)
        val imagePart = createMultipartBodyPart(file)
        val domain = homepageRepository.uploadAvatar(imagePart)
        deleteFileInCache(file)
        val result = domain.convert(UploadAvatarDMO::class.java)
        return result
    }

    private fun uriToFile(context: Context, uri: Uri): File {
        val file =
            File(
                context.cacheDir,
                (System.currentTimeMillis() / 10001).toInt().toString() + ".jpeg"
            )
        context.contentResolver.openInputStream(uri)?.use { input ->
            FileOutputStream(file).use { output ->
                input.copyTo(output)
            }
        }
        return file
    }

    private fun createMultipartBodyPart(file: File): MultipartBody.Part {
        val requestFile = file.asRequestBody("image/jpeg".toMediaTypeOrNull())
        return MultipartBody.Part.createFormData("file", file.name, requestFile)
    }

    private fun deleteFileInCache(file: File) {
        try {
            if (file.exists()) {
                file.delete()
            }
        } catch (_: Exception) {
        }
    }

    suspend fun reloadAvatarInHomePage() {
        localRepository.shareDataTo(
            RELOAD_DATA_HOME, ShareDataDTO(
                RELOAD_DATA_HOME,
                RELOAD_AVATAR)
        )
    }
}