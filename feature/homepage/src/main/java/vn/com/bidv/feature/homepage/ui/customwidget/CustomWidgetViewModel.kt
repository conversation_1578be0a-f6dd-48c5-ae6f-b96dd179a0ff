package vn.com.bidv.feature.homepage.ui.customwidget

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.feature.homepage.constants.WidgetType
import vn.com.bidv.feature.homepage.domain.CustomWidgetUseCase
import vn.com.bidv.feature.homepage.ui.customwidget.CustomWidgetReducer.CustomWidgetViewEffect
import vn.com.bidv.feature.homepage.ui.customwidget.CustomWidgetReducer.CustomWidgetViewEvent
import vn.com.bidv.feature.homepage.ui.customwidget.CustomWidgetReducer.CustomWidgetViewState
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class CustomWidgetViewModel @Inject constructor(
    private val userInfoUseCase: UserInfoUseCase,
    private val customWidgetUseCase: CustomWidgetUseCase
) : ViewModelIBankBase<CustomWidgetViewState, CustomWidgetViewEvent, CustomWidgetViewEffect>(
    initialState = CustomWidgetViewState(),
    reducer = CustomWidgetReducer()
) {
    override fun handleEffect(
        sideEffect: CustomWidgetViewEffect,
        onResult: (CustomWidgetViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is CustomWidgetViewEffect.GetWidgetFromStorage -> {
                onResult(
                    CustomWidgetViewEvent.GetWidgetFormStorageSuccess(
                        listWidget = userInfoUseCase.getWidgets()
                            .mapNotNull { name -> WidgetType.entries.find { it.name == name } },
                        listAllWidget = customWidgetUseCase.getListAllListWidget(
                            userInfoUseCase.getWidgets()
                                .mapNotNull { name -> WidgetType.entries.find { it.name == name } }),
                        numberMinPickup = customWidgetUseCase.getNumberMinPickup(
                            userInfoUseCase.getWidgets()
                                .mapNotNull { name -> WidgetType.entries.find { it.name == name } }
                        )
                    )
                )
            }

            is CustomWidgetViewEffect.HandleSelectedWidget -> {
                onResult(
                    CustomWidgetViewEvent.HandleSelectedWidgetResult(
                        listAllWidget = customWidgetUseCase.selectListWidget(
                            listAllWidget = sideEffect.listAllWidget,
                            item = sideEffect.item
                        ),
                    )
                )
            }

            is CustomWidgetViewEffect.ApplySetListWidgetSideEffect -> {
                onResult(
                    CustomWidgetViewEvent.ApplySetListWidgetSuccess(
                        listWidget = customWidgetUseCase.applyListWidget(
                            listAllWidget = sideEffect.listAllWidget,
                            listWidget = sideEffect.listWidget
                        )
                    )
                )
            }

            is CustomWidgetViewEffect.UpdateWidgetInfoByUserSideEffect -> {
                callDomain(
                    isListenAllError = true,
                    onSuccess = {
                        userInfoUseCase.changeWidgetUserInfoFromStorage(sideEffect.listWidget.map { it.name })

                        onResult(
                            CustomWidgetViewEvent.UpdateWidgetInfoByUserSuccess
                        )

                        viewModelScope.launch(Dispatchers.Default) {
                            customWidgetUseCase.needReloadDataHome()
                        }
                    },
                    onFail = { result ->
                        showPopupError(
                            errorMessage = result?.errorMessage.orEmpty()
                        )
                    }
                ) {
                    customWidgetUseCase.updateWidgetInfoByUser(sideEffect.listWidget)
                }
            }

            is CustomWidgetViewEffect.GetListAllWidgetWhenDismiss -> {
                onResult(
                    CustomWidgetViewEvent.GetListAllWidgetWhenDismissSuccess(
                        listAllWidget = customWidgetUseCase.getListAllListWidget(
                            listWidget = sideEffect.listWidget
                        )
                    )
                )
            }

            is CustomWidgetViewEffect.UpdateWidgetInfoByUserSuccessSideEffect -> {
                //Do Nothing
            }
        }
    }
}
