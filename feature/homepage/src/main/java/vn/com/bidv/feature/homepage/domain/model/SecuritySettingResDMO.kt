package vn.com.bidv.feature.homepage.domain.model

import com.google.gson.annotations.SerializedName

data class SecuritySettingResDMO (
    @SerializedName("smartOtp")
    val smartOtpSetting: SmartOtpSettingDMO? = null
){

}

data class SmartOtpSettingDMO(

    @SerializedName("status")
    val status: String? = null,

    @SerializedName("statusName")
    val statusName: String? = null,

    @SerializedName("deviceModel")
    val deviceModel: String? = null,

    @SerializedName("deviceId")
    val deviceId: String? = null,

    @SerializedName("deviceIdActiveSmartOTP")
    val deviceIdActiveSmartOTP: Boolean? = null,

    @SerializedName("enableReRegister")
    val enableReRegister: Boolean? = null,

    @SerializedName("enableChangePin")
    val enableChangePin: Boolean? = null

) {

}
