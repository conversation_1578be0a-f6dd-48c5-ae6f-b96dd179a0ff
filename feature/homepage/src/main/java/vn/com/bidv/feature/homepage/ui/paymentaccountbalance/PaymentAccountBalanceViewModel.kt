package vn.com.bidv.feature.homepage.ui.paymentaccountbalance

import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.localization.R
import vn.com.bidv.feature.homepage.domain.PaymentAccountBalanceUseCase
import vn.com.bidv.feature.homepage.ui.paymentaccountbalance.PaymentAccountBalanceReducer.PaymentAccountBalanceViewEffect
import vn.com.bidv.feature.homepage.ui.paymentaccountbalance.PaymentAccountBalanceReducer.PaymentAccountBalanceViewEvent
import vn.com.bidv.feature.homepage.ui.paymentaccountbalance.PaymentAccountBalanceReducer.PaymentAccountBalanceViewState
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class PaymentAccountBalanceViewModel
    @Inject
    constructor(
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        private val paymentAccountBalanceUseCase: PaymentAccountBalanceUseCase,
        private val userInfoUseCase: UserInfoUseCase,
    ) : ViewModelIBankBase<PaymentAccountBalanceViewState, PaymentAccountBalanceViewEvent, PaymentAccountBalanceViewEffect>(
            initialState = PaymentAccountBalanceViewState(),
            reducer = PaymentAccountBalanceReducer(),
        ) {
        override fun handleEffect(
            sideEffect: PaymentAccountBalanceViewEffect,
            onResult: (PaymentAccountBalanceViewEvent) -> Unit,
        ) {
            when (sideEffect) {
                is PaymentAccountBalanceViewEffect.FetchAccountBalanceList -> {
                    callDomain(
                        isListenAllError = true,
                        showLoadingIndicator = false,
                        onSuccess = { accBalance ->
                            accBalance.getSafeData()?.let {
                                onResult(PaymentAccountBalanceViewEvent.GetAccountBalanceListSuccess(it))
                            }
                        },
                        onFail = {
                            it?.let {
                                onResult(
                                    PaymentAccountBalanceViewEvent.GetAccountBalanceListFailed(
                                        it.errorCode,
                                        it.errorMessage.orEmpty()
                                    ),
                                )
                            }
                        },
                    ) {
                        paymentAccountBalanceUseCase.getAccountBalanceInfo()
                    }
                }

                PaymentAccountBalanceViewEffect.FetchUserInfo -> {
                    val shouldShowDataFlagKey = "widget.dda.account.balance.view.data.flag"
                    callDomain(
                        isListenAllError = true,
                        showLoadingIndicator = false,
                        onSuccess = { userInfo ->
                            val shouldMaskDataFlag =
                                userInfo.getSafeData()?.params?.get(shouldShowDataFlagKey)
                            onResult(
                                PaymentAccountBalanceViewEvent.OnMaskedData(
                                    shouldMaskDataFlag == SHOW_DATA_FLAG.MASK_DATA.value,
                                ),
                            )
                        },
                        onFail = {
                            it?.let {
                                onResult(
                                    PaymentAccountBalanceViewEvent.GetAccountBalanceListFailed(
                                        it.errorCode,
                                        it.errorMessage.orEmpty()
                                    ),
                                )
                            }
                        },
                    ) {
                        userInfoUseCase.getUserInfoFromStorage()
                    }
                }

                else -> {}
            }
        }
    }

enum class SHOW_DATA_FLAG(
    val value: String,
) {
    MASK_DATA("N"),
}
