package vn.com.bidv.feature.login.ui.settinglanguage

import android.content.Context
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineDispatcher
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.feature.login.ui.settinglanguage.SettingLanguageReducer.SettingLanguageViewEffect
import vn.com.bidv.feature.login.ui.settinglanguage.SettingLanguageReducer.SettingLanguageViewEvent
import vn.com.bidv.feature.login.ui.settinglanguage.SettingLanguageReducer.SettingLanguageViewState
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import vn.com.bidv.sdkbase.utils.LocaleDef
import vn.com.bidv.sdkbase.utils.LocaleManager
import javax.inject.Inject

@HiltViewModel
class SettingLanguageViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    @ApplicationContext private val context: Context
) : ViewModelIBankBase<SettingLanguageViewState, SettingLanguageViewEvent, SettingLanguageViewEffect>(
    initialState = SettingLanguageViewState(),
    reducer = SettingLanguageReducer()
) {
    override fun handleEffect(
        sideEffect: SettingLanguageViewEffect,
        onResult: (SettingLanguageViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is SettingLanguageViewEffect.InitSettingLanguage -> {
                val languageSelected = LocaleManager.getCurrentLanguage(context).localeDef
                val listLanguage = LocaleManager.getListItemLanguage()
                listLanguage.forEach {
                    it.isSelected = languageSelected == it.localeDef
                }
                onResult(SettingLanguageViewEvent.OnInitSettingLanguageSuccess(listLanguage, isLoginSuccess = localRepository.isLoginSuccess()))
            }

            is SettingLanguageViewEffect.ChangeLanguage -> {
                LocaleManager.setNewLocale(sideEffect.context, sideEffect.locale)
                LocaleManager.setNewLocale(context, sideEffect.locale)
                onResult(SettingLanguageViewEvent.OnChangeLanguageSuccess)
            }

            is SettingLanguageViewEffect.ChangeLanguageSuccess -> {
                // do nothing
            }

        }
    }
}
