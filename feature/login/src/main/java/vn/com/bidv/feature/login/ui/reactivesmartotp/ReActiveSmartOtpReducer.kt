package vn.com.bidv.feature.login.ui.reactivesmartotp

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.login.domain.model.TransUpdateBasicOtpDMO

class ReActiveSmartOtpReducer :
    Reducer<ReActiveSmartOtpReducer.ReActiveSmartOtpViewState, ReActiveSmartOtpReducer.ReActiveSmartOtpViewEvent, ReActiveSmartOtpReducer.ReActiveSmartOtpViewEffect> {

    @Immutable
    data class ReActiveSmartOtpViewState(
        val isInitSuccess: Boolean = false,
    ) : ViewState

    @Immutable
    sealed class ReActiveSmartOtpViewEvent : ViewEvent {
        data object OnStartReActiveSmartOtp : ReActiveSmartOtpViewEvent()
        data class OnStartReActiveSmartOtpSuccess(val transUpdateBasicOtpDMO: TransUpdateBasicOtpDMO?) :
            ReActiveSmartOtpViewEvent()
    }

    @Immutable
    sealed class ReActiveSmartOtpViewEffect : SideEffect {
        data object StartReActiveSmartOtp : ReActiveSmartOtpViewEffect()
        data class StartReActiveSmartOtpSuccess(val transUpdateBasicOtpDMO: TransUpdateBasicOtpDMO?) :
            ReActiveSmartOtpViewEffect(), UIEffect
    }

    override fun reduce(
        previousState: ReActiveSmartOtpViewState,
        event: ReActiveSmartOtpViewEvent,
    ): Pair<ReActiveSmartOtpViewState, ReActiveSmartOtpViewEffect?> {
        return handleReActiveSmartOtp(previousState, event)

    }

    private fun handleReActiveSmartOtp(
        previousState: ReActiveSmartOtpViewState,
        event: ReActiveSmartOtpViewEvent
    ): Pair<ReActiveSmartOtpViewState, ReActiveSmartOtpViewEffect?> {
        return when (event) {
            is ReActiveSmartOtpViewEvent.OnStartReActiveSmartOtp -> {
                previousState to ReActiveSmartOtpViewEffect.StartReActiveSmartOtp
            }

            is ReActiveSmartOtpViewEvent.OnStartReActiveSmartOtpSuccess -> {
                previousState to ReActiveSmartOtpViewEffect.StartReActiveSmartOtpSuccess(event.transUpdateBasicOtpDMO)
            }
        }
    }
}
