package vn.com.bidv.feature.login.ui.smsOTP.verifyotpbiometric

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.ui.BaseDialogScreen
import vn.com.bidv.feature.login.constants.SmartOTPErrorCode
import vn.com.bidv.feature.login.domain.VerifyPwOtpErrorType
import vn.com.bidv.feature.login.domain.model.ModelCreateOtpResDMO
import vn.com.bidv.feature.login.navigation.AuthRouts
import vn.com.bidv.feature.login.navigation.NavigationHelper
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPContent
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPReducer
import vn.com.bidv.localization.R

@Composable
fun VerifyOtpBiometricModal(navController: NavHostController, model: ModelCreateOtpResDMO) {
    val viewModel: VerifyOtpBiometricModalViewModel = hiltViewModel()
    var isShowErrorPopup by remember { mutableStateOf(false) }
    var isShowErrorMessage by remember { mutableStateOf("") }
    var isNavigateToSettingSecureInfo by remember { mutableStateOf(false) }

    BaseDialogScreen(
        viewModel = viewModel,
        renderContent = { uiState, onEvent ->
            if (!uiState.initSuccess) {
                onEvent(
                    BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.InitData(
                        model
                    )
                )
            }
            BaseIBankModalOTPContent(
                uiState = uiState,
                onEvent = onEvent,
                viewModel = viewModel
            )
            if (isShowErrorPopup) {
                IBankModalConfirm(
                    modalConfirmType = ModalConfirmType.Error,
                    supportingText = isShowErrorMessage,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(R.string.close),
                        )
                    ),
                    onDismissRequest = {
                        isShowErrorPopup = false
                        if (isNavigateToSettingSecureInfo) {
                            navController.popBackStack(AuthRouts.turnOnBiometricRoute, true)
                        } else {
                            onEvent(BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.ClearOTP)
                        }
                    }
                )
            }
        },
        handleSideEffect = { sideEffect ->
            when (sideEffect) {
                is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTPError -> {
                    isShowErrorPopup = true
                    isShowErrorMessage = sideEffect.errorMessage ?: navController.context.getString(
                        R.string.co_loi_xay_ra_trong_qua_trinh_xu_ly_vui_long_thu_lai
                    )
                    isNavigateToSettingSecureInfo = sideEffect.errorCode == VerifyPwOtpErrorType.SMS_OTP_BLOCK
                }

                is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTPSuccess<Boolean> -> {
                    navController.popBackStack()
                }

                is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.OnDismiss -> {
                    navController.popBackStack(AuthRouts.turnOnBiometricRoute, true)
                }

                else -> {
                    // no thing
                }
            }
        }
    )

}