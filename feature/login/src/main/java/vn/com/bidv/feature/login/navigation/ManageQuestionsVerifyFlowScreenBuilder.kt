package vn.com.bidv.feature.login.navigation

import com.google.gson.Gson
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionFlowScreenBuilder
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionTypeConstant
import vn.com.bidv.feature.login.domain.ManageQuestionsVerifyUseCase
import vn.com.bidv.feature.login.domain.model.DataListSecurityQuestionAnswerDMO
import vn.com.bidv.feature.login.domain.model.ModelLoginDMO
import vn.com.bidv.feature.login.ui.setupsecurityinfo.requestConfirmModal.model.ModelRequestConfirm
import javax.inject.Inject

class ManageQuestionsVerifyFlowScreenBuilder @Inject constructor(
    useCase: ManageQuestionsVerifyUseCase,
) : VerifyTransactionFlowScreenBuilder(
    useCase = useCase,
    type = VerifyTransactionTypeConstant.ManageQuestions,
    isShowResultPopup = true,
    content = { navController, data, _ ->
        val dataListSecurityQuestionAnswerDMO = try {
            Gson().fromJson(
                data,
                DataListSecurityQuestionAnswerDMO::class.java
            )
        } catch (ex: Exception) {
            null
        }
        NavigationHelper.navigateToSetupSecurityQuestion(
            navController = navController,
            modelRequestConfirm = ModelRequestConfirm(
                listQuestion = dataListSecurityQuestionAnswerDMO?.items ?: emptyList(),
                modelLoginDMO = ModelLoginDMO(isSecurityQuestionSet = true)
            )
        )
    }
)