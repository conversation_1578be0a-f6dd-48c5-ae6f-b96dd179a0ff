package vn.com.bidv.feature.login.constants

object Constants {
    const val LOGIN_ACCEPTED = "ACCEPTED"
    const val FIRST_TIME_PW_CHANGE = "FIRST_TIME_PW_CHANGE"
    const val ADMIN_ROLE = "Admin"
    const val EXPIRED_PW = "EXPIRED_PW"
    const val WAITING_ACTIVATION = "WAITING_ACTIVATION"
    const val NEW_DEVICE = "NEW_DEVICE"
    const val USER_PROFILE = "USER_PROFILE"
    const val PW_CHANGE = "PW_CHANGE"
    const val FORGOT_PW = "FORGOT_PW"
    const val CHANGE_PIN_SUCCESS = "CHANGE_PIN_SUCCESS"
    const val USER_INFO_SUCCESS = "USER_INFO_SUCCESS"
    const val NEED_DELETE_ALL_SMART_OTP_SUCCESS = "NEED_DELETE_ALL_SMART_OTP_SUCCESS"

    object ViewID {
        const val USER_NAME_VIEW_ID = "USER_NAME_VIEW_ID"
        const val PASSWORD_VIEW_ID = "PASSWORD_VIEW_ID"
        const val OLD_PASSWORD_VIEW_ID = "OLD_PASSWORD_VIEW_ID"
        const val NEW_PASSWORD_VIEW_ID = "NEW_PASSWORD_VIEW_ID"
        const val CONFIRM_PASSWORD_VIEW_ID = "CONFIRM_PASSWORD_VIEW_ID"
        const val QUESTION_VIEW_ID = "QUESTION_VIEW_ID"
        const val ANSWER_VIEW_ID = "ANSWER_VIEW_ID"
        const val PHONE_NUMBER_VIEW_ID = "PHONE_NUMBER_VIEW_ID"
        const val EMAIL_VIEW_ID = "EMAIL_VIEW_ID"
        const val ID_NUMBER_VIEW_ID = "ID_NUMBER_VIEW_ID"
        const val EXPIRATION_DATE_VIEW_ID = "EXPIRATION_DATE_VIEW_ID"
        const val REGISTRATION_NUMBER_VIEW_ID = "REGISTRATION_NUMBER_VIEW_ID"
    }

    const val CONSECUTIVE_CHARACTERS = "012|123|234|345|456|567|678|789|890|abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz"
    const val NORMAL_CHARACTERS = ".*[a-z].*"
    const val UPPER_CHARACTERS = ".*[A-Z].*"
    const val NUMBER_CHARACTERS = ".*[0-9].*"
    const val SPECIAL_CHARACTERS = ".*[!@#\$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*"
    const val DEFAULT_TIME_OUT_RESEND_OTP = 30
    const val DEFAULT_TIME_OUT_EFFECT_OTP = 120
    const val EMAIL_REGEX = "^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"


    const val SAME_DEVICE = "Y"
    const val OTHER_DEVICE = "N"
    const val MAX_TIME_TXN = "120"


    object Biometric {
        const val BIOMETRIC_TOUCH_ID = "TOUCH_ID"
        const val SETTING_BIOMETRIC = "SETTING_BIOMETRIC"
    }

    object VerifyBiometricOtp {
        const val VERIFY_BIOMETRIC_SMS_OTP = "VERIFY_BIOMETRIC_SMS_OTP"
    }

    const val CLEAR_INPUT_PIN = "CLEAR_INPUT_PIN"

}