package vn.com.bidv.feature.login.navigation

import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionFlowScreenBuilder
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionTypeConstant
import vn.com.bidv.feature.login.domain.UserInfoVerifyUseCase
import vn.com.bidv.feature.login.ui.userinfoverifyresultscreen.UserInfoVerifyResultScreen
import javax.inject.Inject

class UserInfoVerifyFlowScreenBuilder @Inject constructor(
    useCase: UserInfoVerifyUseCase,
): VerifyTransactionFlowScreenBuilder(
    useCase = useCase,
    type = VerifyTransactionTypeConstant.UserInfo,
    content = { navController, data, _ ->
        UserInfoVerifyResultScreen(
            navController = navController,
            data = data
        )
    }
)