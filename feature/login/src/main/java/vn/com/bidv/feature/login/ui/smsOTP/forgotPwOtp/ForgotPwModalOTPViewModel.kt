package vn.com.bidv.feature.login.ui.smsOTP.forgotPwOtp

import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.feature.login.domain.FcmUseCase
import vn.com.bidv.feature.login.domain.LoginUseCase
import vn.com.bidv.feature.login.domain.model.ModelCreateOtpResDMO
import vn.com.bidv.feature.login.domain.model.ModelForgotPwOTPConfigDMO
import vn.com.bidv.feature.login.domain.model.ModelLoginDMO
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPReducer
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPViewModel
import javax.inject.Inject

@HiltViewModel
class ForgotPwModalOTPViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val loginUseCase: LoginUseCase,
    private val fcmUseCase: FcmUseCase
) : BaseIBankModalOTPViewModel<ModelForgotPwOTPConfigDMO, ModelCreateOtpResDMO, ModelLoginDMO>(
    initialState = BaseIBankModalOTPReducer.BaseIBankModalOTPViewState(),
    reducer = BaseIBankModalOTPReducer()
) {

    override fun handleEffect(
        sideEffect: BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect<ModelForgotPwOTPConfigDMO, ModelCreateOtpResDMO, ModelLoginDMO>,
        onResult: (BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent<ModelForgotPwOTPConfigDMO, ModelCreateOtpResDMO, ModelLoginDMO>) -> Unit
    ) {
        when (sideEffect) {
            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.RetrySendOTP -> {
                callDomain(
                    onSuccess = { result ->
                        onResult(
                            BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnRetrySendOTPSuccess(
                                result.data
                            )
                        )
                    },

                    ) {
                    loginUseCase.reSendSmsOtp(
                        method = sideEffect.method,
                        userName = sideEffect.userName,
                        txnId = sideEffect.txnId
                    )
                }
            }

            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTP -> {
                callDomain(
                    isListenAllError = true,
                    onSuccess = { result ->
                        onResult(
                            BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnVerifyOTPSuccess(
                                result.data
                            )
                        )
                    },
                    onFail = { error ->
                        onResult(
                            BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnVerifyOTPError(
                                errorCode = error?.errorCode,
                                errorMessage = error?.errorMessage,
                            )
                        )
                    }

                    ) {
                    loginUseCase.verifyOtp(
                        method = sideEffect.method,
                        userName = sideEffect.userName,
                        txnId = sideEffect.txnId,
                        otpNum = sideEffect.otpNum,
                        fcmId = fcmUseCase.getFcmId()
                    )
                }
            }

            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTPError,
            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.RetryOTPSuccess,
            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTPSuccess -> TODO()
        }
    }

}
