package vn.com.bidv.feature.login.ui.reactivesmartotp

import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.common.patterns.mvi.BaseMviViewModel
import vn.com.bidv.feature.login.domain.ActiveSmartOTPUseCase
import vn.com.bidv.feature.login.ui.reactivesmartotp.ReActiveSmartOtpReducer.ReActiveSmartOtpViewEffect
import vn.com.bidv.feature.login.ui.reactivesmartotp.ReActiveSmartOtpReducer.ReActiveSmartOtpViewEvent
import vn.com.bidv.feature.login.ui.reactivesmartotp.ReActiveSmartOtpReducer.ReActiveSmartOtpViewState
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class ReActiveSmartOtpViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val activeSmartOTPUseCase: ActiveSmartOTPUseCase,
) : ViewModelIBankBase<ReActiveSmartOtpViewState, ReActiveSmartOtpViewEvent, ReActiveSmartOtpViewEffect>(
    initialState = ReActiveSmartOtpViewState(),
    reducer = ReActiveSmartOtpReducer()
) {
    override fun handleEffect(
        sideEffect: ReActiveSmartOtpViewEffect,
        onResult: (ReActiveSmartOtpViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is ReActiveSmartOtpViewEffect.StartReActiveSmartOtp -> {
                callDomain (onSuccess = {
                    onResult(ReActiveSmartOtpViewEvent.OnStartReActiveSmartOtpSuccess(it.data))
                }) {
                    activeSmartOTPUseCase.requestActiveRetry()
                }
            }
            is ReActiveSmartOtpViewEffect.StartReActiveSmartOtpSuccess -> {
                // nothing
            }
        }
    }
}
