package vn.com.bidv.feature.login.domain

import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.constants.Constants.EMAIL_REGEX
import vn.com.bidv.feature.login.constants.LoginErrorCode
import vn.com.bidv.feature.login.data.PasswordRepository
import vn.com.bidv.feature.login.domain.model.CreateForgotPwResponseDMO
import vn.com.bidv.feature.login.ui.forgotpassword.MethodOtp
import vn.com.bidv.network.NetworkConfig
import vn.com.bidv.sdkbase.domain.DomainErrorCode
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class ForgotPasswordUseCase @Inject constructor(
    private val passwordRepository: PasswordRepository,
    private val networkConfig: NetworkConfig,
) {

    fun validPhoneNumber(phoneNumber: String): DomainResult<Boolean> {
        val listClientError = mutableListOf<Pair<String, String>>()
        if (phoneNumber.isBlank()) {
            listClientError.add(Constants.ViewID.PHONE_NUMBER_VIEW_ID to LoginErrorCode.INVALID_INPUT)
        }
        if (!phoneNumber.startsWith("0")) {
            listClientError.add(Constants.ViewID.PHONE_NUMBER_VIEW_ID to LoginErrorCode.NOT_START_WITH_NUMBER_0)
        }
        if (phoneNumber.length < 10) {
            listClientError.add(Constants.ViewID.PHONE_NUMBER_VIEW_ID to LoginErrorCode.NOT_FULL_10_NUMBER)
        }
        return if (listClientError.isNotEmpty()) {
            DomainResult.Error(
                errorCode = DomainErrorCode.CLIENT_ERROR_CODE,
                listClientErrorCode = listClientError
            )
        } else {
            DomainResult.Success(true)
        }
    }

    fun validEmail(email: String): DomainResult<Boolean> {
        val listClientError = mutableListOf<Pair<String, String>>()
        if (email.isBlank()) {
            listClientError.add(Constants.ViewID.EMAIL_VIEW_ID to LoginErrorCode.INVALID_INPUT)
        }
        if (!email.matches(EMAIL_REGEX.toRegex())) {
            listClientError.add(Constants.ViewID.EMAIL_VIEW_ID to LoginErrorCode.INVALID_EMAIL)
        }
        return if (listClientError.isNotEmpty()) {
            DomainResult.Error(
                errorCode = DomainErrorCode.CLIENT_ERROR_CODE,
                listClientErrorCode = listClientError
            )
        } else {
            DomainResult.Success(true)
        }
    }

    fun validateInput(
        username: String,
        idNumber: String,
        expirationDate: String,
        methodOtp: MethodOtp,
        contactInfo: String,
        registrationNumber: String,
        isIndefinite: Boolean
    ): DomainResult<Boolean> {
        val listClientError = mutableListOf<Pair<String, String>>()
        if (username.isBlank()) {
            listClientError.add(Constants.ViewID.USER_NAME_VIEW_ID to LoginErrorCode.INVALID_INPUT)
        }

        if (idNumber.isBlank()) {
            listClientError.add(Constants.ViewID.ID_NUMBER_VIEW_ID to LoginErrorCode.INVALID_INPUT)
        }

        if (!isIndefinite && expirationDate.isBlank()) {
            listClientError.add(Constants.ViewID.EXPIRATION_DATE_VIEW_ID to LoginErrorCode.INVALID_INPUT)
        }

        if (methodOtp == MethodOtp.EMAIL) {
            val validEmail = validEmail(contactInfo)
            if (validEmail is DomainResult.Error) {
                listClientError.addAll(validEmail.listClientErrorCode ?: listOf())
            }
        } else {
            val validPhoneNumber = validPhoneNumber(contactInfo)
            if (validPhoneNumber is DomainResult.Error) {
                listClientError.addAll(validPhoneNumber.listClientErrorCode ?: listOf())
            }
        }


        if (registrationNumber.isBlank()) {
            listClientError.add(Constants.ViewID.REGISTRATION_NUMBER_VIEW_ID to LoginErrorCode.INVALID_INPUT)
        }
        return if (listClientError.isNotEmpty()) {
            DomainResult.Error(
                errorCode = DomainErrorCode.CLIENT_ERROR_CODE,
                listClientErrorCode = listClientError
            )
        } else {
            DomainResult.Success(true)
        }
    }

    suspend fun forgotPwCreate(
        username: String,
        idExpireDate: String,
        idNum: String,
        idReg: String,
        mobile: String?,
        otpEmail: String?,
    ): DomainResult<CreateForgotPwResponseDMO> {
        val deviceId = networkConfig.deviceId
        val result =
            passwordRepository.forgotPwCreate(
                username = username,
                deviceId = deviceId,
                idExpireDate = idExpireDate,
                idNum = idNum,
                idReg = idReg,
                mobile = mobile,
                otpEmail = otpEmail,
            )

        val model = result.convert(CreateForgotPwResponseDMO::class.java)
        return model
    }
}