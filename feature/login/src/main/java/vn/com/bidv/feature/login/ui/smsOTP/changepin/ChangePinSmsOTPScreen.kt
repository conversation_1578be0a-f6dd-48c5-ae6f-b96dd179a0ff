package vn.com.bidv.feature.login.ui.smsOTP.changepin

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.ui.BaseDialogScreen
import vn.com.bidv.feature.login.constants.SmartOTPErrorCode
import vn.com.bidv.feature.login.domain.model.TransUpdateBasicOtpDMO
import vn.com.bidv.feature.login.navigation.AuthRouts.setupSmartOtpRoute
import vn.com.bidv.feature.login.navigation.NavigationHelper
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPContent
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPReducer
import vn.com.bidv.localization.R

@Composable
fun ChangePinModalOTPScreen(
    navController: NavHostController,
    modelConfig: TransUpdateBasicOtpDMO,
) {
    val viewModel: ChangePinModalOTPViewModel = hiltViewModel()
    var isShowErrorPopup by remember { mutableStateOf(false) }
    var isShowErrorMessage by remember { mutableStateOf("") }
    var isPopToInputPin by remember { mutableStateOf(false) }

    BaseDialogScreen(
        viewModel = viewModel,
        renderContent = { uiState, onEvent ->
            if (!uiState.initSuccess) {
                onEvent(
                    BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.InitData(
                        modelConfig
                    )
                )
            }
            BaseIBankModalOTPContent(
                uiState = uiState,
                onEvent = onEvent,
                viewModel = viewModel
            )
            if (isShowErrorPopup) {
                IBankModalConfirm(
                    title = stringResource(R.string.loi),
                    modalConfirmType = ModalConfirmType.Error,
                    supportingText = isShowErrorMessage,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(R.string.close),
                        )
                    ),
                    onDismissRequest = {
                        isShowErrorPopup = false
                        if (isPopToInputPin) {
                            navController.popBackStack(setupSmartOtpRoute, true)

                        } else {
                            onEvent(BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.ClearOTP)
                        }
                    }
                )
            }

        },
        handleSideEffect = { sideEffect ->
            when (sideEffect) {
                is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTPError -> {
                    isShowErrorPopup = true
                    isShowErrorMessage = sideEffect.errorMessage ?: navController.context.getString(
                        R.string.co_loi_xay_ra_trong_qua_trinh_xu_ly_vui_long_thu_lai
                    )
                    isPopToInputPin = sideEffect.errorCode in listOf(
                        SmartOTPErrorCode.OTP_VERIFICATION_IS_TEMPORARILY_LOCKED,
                        SmartOTPErrorCode.INVALID_TRANSACTION_STATUS
                    )
                }

                is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTPSuccess<Boolean> -> {}

                is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.OnDismiss -> {
                    viewModel.clearDataInputPin()
                    NavigationHelper.goBack(navController)
                }

                else -> {

                }
            }

        }
    )
}