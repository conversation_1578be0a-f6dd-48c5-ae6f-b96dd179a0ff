package vn.com.bidv.feature.login.ui.pushreactivesmartotpsuccess

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ClipboardManager
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.fromHtml
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.dataentry.IBankInformation
import vn.com.bidv.designsystem.component.feedback.inlinemessage.InlineMessage
import vn.com.bidv.designsystem.component.feedback.inlinemessage.InlineMessageStatus
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankActionBar
import vn.com.bidv.feature.login.ui.pushreactivesmartotpsuccess.PushReActiveSmartOtpSuccessReducer.PushReActiveSmartOtpSuccessViewEvent
import vn.com.bidv.feature.login.ui.pushreactivesmartotpsuccess.PushReActiveSmartOtpSuccessReducer.PushReActiveSmartOtpSuccessViewState
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.login.domain.model.AuthType
import vn.com.bidv.feature.login.domain.model.SmartOtpApprovePendingDMO
import vn.com.bidv.sdkbase.navigation.IBankMainRouting
import vn.com.bidv.designsystem.R as RDesignSystem
import vn.com.bidv.localization.R as RLocalization

@Composable
fun PushReActiveSmartOtpSuccessScreen(
    navController: NavHostController,
    model: SmartOtpApprovePendingDMO
) {
    val vm: PushReActiveSmartOtpSuccessViewModel = hiltViewModel()
    BaseScreen(
        navController = navController,
        viewModel = vm,
        renderContent = { uiState, onEvent ->
            onEvent(PushReActiveSmartOtpSuccessViewEvent.OnInitScreen(model))
            PushReActiveSmartOtpSuccessContent(uiState, onEvent, navController)
        },
        handleSideEffect = {

        },
        topAppBarType = TopAppBarType.Result,
    )
}

@Composable
private fun PushReActiveSmartOtpSuccessContent(
    uiState: PushReActiveSmartOtpSuccessViewState,
    onEvent: (PushReActiveSmartOtpSuccessViewEvent) -> Unit,
    navController: NavHostController,
) {

    val clipboardManager: ClipboardManager = LocalClipboardManager.current

    Box(
        modifier = Modifier
            .fillMaxSize()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(IBSpacing.spacingM),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                painter = painterResource(id = RDesignSystem.drawable.icon_popup_success_64),
                contentDescription = null,
                modifier = Modifier.size(64.dp),
                tint = Color.Unspecified
            )

            Spacer(Modifier.size(IBSpacing.spacingXs))

            Text(
                modifier = Modifier.testTagIBank("login_text_push_reactive_smart_otp_success"),
                text = stringResource(RLocalization.string.day_duyet_thanh_cong).replace("!", ""),
                style = LocalTypography.current.headlineHeadline_s,
                color = LocalColorScheme.current.contentMainPrimary
            )

            Spacer(Modifier.size(IBSpacing.spacing2xs))
            val message = AnnotatedString.fromHtml(stringResource(RLocalization.string.cdatayeu_cau_kich_hoat_lai_smart_otp_da_duoc_bgui_den_nguoi_dung_co_vai_tro_quan_tri_vien_de_phe_duyetb))

            Text(
                modifier = Modifier
                    .fillMaxWidth(),
                text = message,
                style = LocalTypography.current.bodyBody_m,
                color = LocalColorScheme.current.contentMainTertiary,
                textAlign = TextAlign.Center
            )

            Spacer(Modifier.size(IBSpacing.spacingM))

            if (uiState.model.authType?.contains(AuthType.SMSOTP.value, true) == true) {
                Column(
                    modifier = Modifier
                        .clip(RoundedCornerShape(IBCornerRadius.cornerRadiusL))
                        .background(Color.White)
                        .padding(horizontal = IBSpacing.spacingM, vertical = IBSpacing.spacingS)
                ) {

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                    ) {
                        IBankInformation(
                            modifier = Modifier.weight(1f),
                            label = stringResource(RLocalization.string.ma_giao_dich),
                            dataValue = uiState.model.transKey ?: ""
                        )
                        Spacer(modifier = Modifier.size(IBSpacing.spacingXs))
                        Icon(
                            painter = painterResource(id = RDesignSystem.drawable.copy_outline),
                            contentDescription = null,
                            modifier = Modifier
                                .testTagIBank("login_icon_copy_transaction_key")
                                .padding(top = IBSpacing.spacingXs)
                                .size(IBSpacing.spacingM)
                                .clickable {
                                    clipboardManager.setText(
                                        AnnotatedString(
                                            uiState.model.transKey ?: ""
                                        )
                                    )
                                },
                            tint = Color.Unspecified
                        )
                    }

                    val document = AnnotatedString.fromHtml(stringResource(RLocalization.string.cdatasau_khi_yeu_cau_duoc_phe_duyet_quy_khach_vui_long_lien_he_hotline_bsb_va_cung_cap_ma_giao_dich_tren_de_bidv_xac_thuc, uiState.model.hotline.orEmpty()))

                    Spacer(modifier = Modifier.size(10.dp))

                    Text(
                        modifier = Modifier
                            .testTagIBank("login_text_push_reactive_smart_otp_success_document")
                            .fillMaxWidth(),
                        text = document,
                        style = LocalTypography.current.bodyBody_m,
                        color = LocalColorScheme.current.contentMainPrimary,
                    )
                }

                Spacer(modifier = Modifier.size(IBSpacing.spacingM))

                InlineMessage(
                    message = stringResource(RLocalization.string.vui_long_khong_cung_cap_ma_giao_dich_tren_cho_bat_ky_ai_khac_ngoai_luong_xac_thuc_tai_hotline_cua_bidv),
                    status = InlineMessageStatus.Warning(LocalColorScheme.current)
                )
            }

            if (uiState.model.authType?.contains(AuthType.EMAILOTP.value, true) == true) {
                InlineMessage(
                    message = stringResource(
                        RLocalization.string.de_dam_bao_an_toan_tai_khoan_sau_khi_yeu_cau_kich_hoat_lai_smart_otp_duoc_phe_duyet_quy_khach_can_cho_s_gio_de_thuc_hien_kich_hoat_lai_smart_otp_tren_ung_dung_bidv_direct,
                        uiState.model.waitTime ?: ""
                    ),
                    status = InlineMessageStatus.Info(LocalColorScheme.current)
                )
            }
        }

        IBankActionBar(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth(),
            buttonPositive = DialogButtonInfo(
                label = stringResource(RLocalization.string.quay_ve_trang_chu),
                onClick = {
                    navController.navigate(IBankMainRouting.HomeRoute.HomeMainRoute.route) {
                        popUpTo(IBankMainRouting.HomeRoute.HomeMainRoute.route) {
                            inclusive = true
                        }
                    }
                }
            )
        )
    }
}
