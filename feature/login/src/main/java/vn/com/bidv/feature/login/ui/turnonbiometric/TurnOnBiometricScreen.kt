package vn.com.bidv.feature.login.ui.turnonbiometric

import android.content.ContextWrapper
import androidx.biometric.BiometricPrompt
import androidx.biometric.BiometricPrompt.AuthenticationResult
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.google.gson.Gson
import vn.com.bidv.common.utils.unpackV2
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.ui.BaseDialogScreen
import vn.com.bidv.feature.common.data.VerifyOtpResult
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionTypeConstant
import vn.com.bidv.feature.login.constants.BiometricError
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.navigation.AuthRouts
import vn.com.bidv.feature.login.navigation.NavigationHelper
import vn.com.bidv.feature.login.ui.turnonbiometric.TurnOnBiometricReducer.TurnOnBiometricViewEvent
import vn.com.bidv.log.BLogUtil
import vn.com.bidv.sdkbase.data.ReloadFunctionKey
import vn.com.bidv.sdkbase.data.ReloadKey
import vn.com.bidv.sdkbase.data.ReloadModuleKey
import vn.com.bidv.secure.utils.BiometricUtils
import vn.com.bidv.feature.common.constants.Constants as CommonConstants
import vn.com.bidv.localization.R as RLocalization

@Composable
private fun findFragmentActivity(): FragmentActivity? {
    var context = LocalContext.current
    while (context is ContextWrapper) {
        if (context is FragmentActivity) {
            return context
        }
        context = context.baseContext
    }
    return null
}

@Composable
fun TurnOnBiometricScreen(navController: NavHostController, isTurnOnBiometric: Boolean) {
    val vm: TurnOnBiometricViewModel = hiltViewModel()

    var isConfirmCancelBiometric by rememberSaveable { mutableStateOf(false) }
    var isShowBiometricErrorPopup by rememberSaveable { mutableStateOf(false) }
    var isShowBiometricNotAvailable by rememberSaveable { mutableStateOf(false) }
    var errorTitle by rememberSaveable { mutableStateOf("") }
    var errorMessage by rememberSaveable { mutableStateOf("") }

    val (_uiState, _onEvent, _) = vm.unpackV2()
    var authenticationResult by remember { mutableStateOf<AuthenticationResult?>(null) }
    val fragmentActivity = findFragmentActivity()

    if (isShowBiometricNotAvailable) {
        ShowBiometricNotAvailable(navController)
    }

    if (isShowBiometricErrorPopup) {
        ShowBiometricClosePopup(
            title = errorTitle,
            message = errorMessage,
        ) {
            isShowBiometricErrorPopup = false
            errorTitle = ""
            errorMessage = ""
            navController.popBackStack()
        }
    }

    if (isConfirmCancelBiometric) {
        ShowConfirmPopup(
            modalConfirmType = ModalConfirmType.Info,
            title = stringResource(RLocalization.string.xac_nhan_huy_cai_dat),
            message = stringResource(RLocalization.string.quy_khach_co_chac_chan_huy_cai_dat_dang_nhap_bang_touch_id),
            navController = navController,
            onNextEvent = {
                _onEvent(TurnOnBiometricViewEvent.OnTurnOffBiometric)
            }
        ) {
            isConfirmCancelBiometric = false
        }
    }

    BaseDialogScreen(
        viewModel = vm,
        renderContent = { uiState, onEvent ->
            if(!uiState.isInitSuccess) {
                if (isTurnOnBiometric) {
                    onEvent(TurnOnBiometricViewEvent.OnInitTurnOnBiometric(true))
                } else {
                    isConfirmCancelBiometric = true
                }
            } else {
                authenticationResult?.let {
                    if (uiState.userInfo.isHaveAuthMethod()) {
                        SubscribeBiometricSmartOtp(
                            vm,
                            _onEvent,
                            authenticationResult,
                            navController
                        )
                    } else {
                        SubscribeBiometricSmsOtp(vm, _onEvent, authenticationResult)
                    }
                }
            }
        },
        handleSideEffect = { sideEffect ->
            when (sideEffect) {
                is TurnOnBiometricReducer.TurnOnBiometricViewEffect.BiometricNotAvailable -> {
                    isShowBiometricNotAvailable = true
                    errorTitle = navController.context.getString(RLocalization.string.loi)
                    errorMessage = navController.context.getString(
                        BiometricError.getBiometricError(sideEffect.errorCode)?.resId
                            ?: RLocalization.string.loi
                    )
                }

                is TurnOnBiometricReducer.TurnOnBiometricViewEffect.GetAuthenticationResult -> {
                    BiometricUtils.getAuthenticationResult(
                        context = fragmentActivity ?: return@BaseDialogScreen,
                        title = navController.context.getString(RLocalization.string.xac_thuc_van_tay),
                        subtitle = navController.context.getString(RLocalization.string.vui_long_cham_vao_cam_bien_van_tay_tren_thiet_bi_de_tiep_tuc),
                        negativeButtonText = navController.context.getString(RLocalization.string.dong),
                        onGetResult = { result: AuthenticationResult?, errorCode: Int?, errMessage: String? ->
                            if (errorCode != null) {
                                BLogUtil.d("getAuthenticationResult: errorCode: $errorCode errorMessage: $errMessage")
                                when (errorCode) {
                                    BiometricPrompt.ERROR_NEGATIVE_BUTTON -> {
                                        navController.popBackStack()
                                    }
                                    BiometricPrompt.ERROR_LOCKOUT -> {
                                        isShowBiometricErrorPopup = true
                                        errorTitle = navController.context.getString(RLocalization.string.thong_bao)
                                        errorMessage = navController.context.getString(RLocalization.string.quy_khach_da_nhap_sai_qua_so_lan_cho_phep_vui_long_thu_lai_sau)
                                    }
                                    else -> {
                                        isShowBiometricErrorPopup = true
                                        errorTitle = navController.context.getString(RLocalization.string.thong_bao)
                                        errorMessage = errMessage ?: navController.context.getString(RLocalization.string.co_loi_vua_xay_ra)
                                    }
                                }
                            } else {
                                if (_uiState.value.userInfo.isHaveAuthMethod()) {
                                    _onEvent(TurnOnBiometricViewEvent.OnCreateBiometricOnRequest)
                                } else {
                                    _onEvent(TurnOnBiometricViewEvent.OnStartTurnOnBiometricWithVerifySMS)
                                }
                                authenticationResult = result
                            }
                        }
                    )
                }

                is TurnOnBiometricReducer.TurnOnBiometricViewEffect.StartTurnOnBiometricError,
                is TurnOnBiometricReducer.TurnOnBiometricViewEffect.TurnOffBiometricError -> {
                    isShowBiometricErrorPopup = true
                    errorTitle = navController.context.getString(RLocalization.string.loi)
                    errorMessage = sideEffect.errMessage ?: navController.context.getString(RLocalization.string.co_loi_vua_xay_ra)
                }

                is TurnOnBiometricReducer.TurnOnBiometricViewEffect.VerifyOtpBiometric -> {
                    sideEffect.data.basicOtp?.let {
                        NavigationHelper.navigateToVerifyOtpBiometricModal(
                            navController, it.copy(txnId = sideEffect.data.transId)
                        )
                    }
                }

                is TurnOnBiometricReducer.TurnOnBiometricViewEffect.TurnOnBiometricSuccess -> {
                    navController.popBackStack()
                }

                is TurnOnBiometricReducer.TurnOnBiometricViewEffect.TurnOffBiometricSuccess -> {
                    navController.popBackStack()
                }

                is TurnOnBiometricReducer.TurnOnBiometricViewEffect.CreateBiometricOnRequestSuccess -> {
                    vn.com.bidv.feature.common.navigation.NavigationHelper.navigateToVerifyByTypeCreateTransaction(
                        navController = navController,
                        dataString = Gson().toJson(sideEffect.biometricOnRequestDMO),
                        type = VerifyTransactionTypeConstant.TurnOnBiometric,
                    )
                }

                else -> {
                    // nothing
                }
            }
        },
    )
}

@Composable
private fun ShowBiometricNotAvailable(navController: NavHostController) {
    IBankModalConfirm(
        title = stringResource(RLocalization.string.loi),
        supportingText = stringResource(RLocalization.string.quy_khach_chua_cai_dat_touch_id_tai_thiet_bi_vui_long_kiem_tra_lai),
        modalConfirmType = ModalConfirmType.Error,
        listDialogButtonInfo = listOf(
            DialogButtonInfo(
                label = stringResource(RLocalization.string.di_den_cai_dat),
                onClick = {
                    BiometricUtils.navigateToBiometricSettings(navController.context)
                }
            )
        ),
        onDismissRequest = {
            navController.popBackStack()
        }
    )
}

@Composable
private fun ShowConfirmPopup(
    modalConfirmType: ModalConfirmType = ModalConfirmType.Question,
    title: String,
    message: String,
    navController: NavHostController,
    onNextEvent: () -> Unit,
    onDismiss: () -> Unit,
) {
    IBankModalConfirm(
        title = title,
        supportingText = message,
        modalConfirmType = modalConfirmType,
        listDialogButtonInfo = listOf(
            DialogButtonInfo(
                label = stringResource(RLocalization.string.xac_nhan),
                onClick = {
                    onNextEvent()
                    onDismiss()
                },
                isDismissRequest = false
            ),
            DialogButtonInfo(
                label = stringResource(RLocalization.string.huy),
                onClick = {
                    navController.popBackStack()
                }
            ),
        ),
        onDismissRequest = {
            navController.popBackStack()
        }
    )
}

@Composable
fun SubscribeBiometricSmartOtp(
    viewModel: TurnOnBiometricViewModel,
    onEvent: (TurnOnBiometricViewEvent) -> Unit,
    authenticationResult: AuthenticationResult?,
    navController: NavHostController,
) {
    LaunchedEffect(true) {
        viewModel.subscribeReloadData(ReloadKey(ReloadModuleKey.LOGIN, ReloadFunctionKey.TURN_ON_BIOMETRIC_FAIL)).collect {
            if (viewModel.checkReloadData(it)) {
                navController.popBackStack(AuthRouts.turnOnBiometricRoute, true)
            }
        }

    }
    LaunchedEffect(true) {
        viewModel.subscribeReloadData(ReloadKey(ReloadModuleKey.LOGIN, ReloadFunctionKey.TURN_ON_BIOMETRIC)).collect {
            if (viewModel.checkReloadData(it)) {
                onEvent(TurnOnBiometricViewEvent.OnVerifyOtpBiometricSuccess(authenticationResult))
            }
        }
    }
}

@Composable
private fun SubscribeBiometricSmsOtp(
    viewModel: TurnOnBiometricViewModel,
    onEvent: (TurnOnBiometricViewEvent) -> Unit,
    authenticationResult: AuthenticationResult?
) {
    LaunchedEffect(true) {
        viewModel.subscribeShareData(Constants.VerifyBiometricOtp.VERIFY_BIOMETRIC_SMS_OTP).collect {
            val otpResult = Gson().fromJson(it.data, VerifyOtpResult::class.java)
            if (otpResult.data != null) {
                onEvent(TurnOnBiometricViewEvent.OnVerifyOtpBiometricSuccess(authenticationResult))
            }
        }
    }
}

@Composable
private fun ShowBiometricClosePopup(
    modalConfirmType: ModalConfirmType = ModalConfirmType.Error,
    title: String,
    message: String,
    onDismiss: () -> Unit
) {
    IBankModalConfirm(
        title = title,
        supportingText = message,
        modalConfirmType = modalConfirmType,
        listDialogButtonInfo = listOf(
            DialogButtonInfo(
                label = stringResource(RLocalization.string.dong),
            )
        ),
        onDismissRequest = {
            onDismiss()
        }
    )
}