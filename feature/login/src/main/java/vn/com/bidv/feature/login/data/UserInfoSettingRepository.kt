package vn.com.bidv.feature.login.data

import vn.com.bidv.feature.login.data.login.apis.OTPApi
import vn.com.bidv.feature.login.data.login.apis.OthersApi
import vn.com.bidv.feature.login.data.login.model.OtpSecureVerifyRequest
import vn.com.bidv.feature.login.data.login.model.OtpSecuredVerifyPersonalInfoRequest
import vn.com.bidv.network.domain.BaseRepository
import javax.inject.Inject

class UserInfoSettingRepository @Inject constructor(
    private val service: OthersApi,
    private val otpApi: OTPApi
) : BaseRepository() {

    suspend fun getUserInfoSettings() = launch {
        service.userInfoSettings()
    }

    suspend fun getUserInfoSettingSecure() = launch {
        service.userPersonalInfoSettings()
    }

    suspend fun verifySmartOtpUserInfo(transId: String, otp: String) = launch {
        otpApi.otpSecuredVerifyPersonalInfo(
            OtpSecuredVerifyPersonalInfoRequest(
                transId = transId,
                otpNum = otp,
            )
        )
    }
}