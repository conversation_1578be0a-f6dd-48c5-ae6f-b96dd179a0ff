package vn.com.bidv.feature.login.ui.smsOTP.activeSmartOTP

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.feature.common.domain.data.UserActiveSmartOtpDMO
import vn.com.bidv.feature.common.domain.smartotp.SmartOTPUseCase
import vn.com.bidv.feature.login.domain.ActiveSmartOTPUseCase
import vn.com.bidv.feature.login.domain.FcmUseCase
import vn.com.bidv.feature.login.domain.model.SmartOtpActiveResDMO
import vn.com.bidv.feature.login.domain.model.SmartOtpReqActiveResDMO
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPReducer
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPViewModel
import javax.inject.Inject

@HiltViewModel
class ActiveSmartOTPModalViewModel @Inject constructor(
    private val activeSmartOTPUseCase: ActiveSmartOTPUseCase,
    private val smartOTPUseCase: SmartOTPUseCase,
    private val fcmUseCase: FcmUseCase,
    private val userInfoUseCase: UserInfoUseCase,
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
) : BaseIBankModalOTPViewModel<SmartOtpReqActiveResDMO, SmartOtpReqActiveResDMO, SmartOtpActiveResDMO>(
    initialState = BaseIBankModalOTPReducer.BaseIBankModalOTPViewState(),
    reducer = BaseIBankModalOTPReducer()
) {

    override fun handleEffect(
        sideEffect: BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect<SmartOtpReqActiveResDMO, SmartOtpReqActiveResDMO, SmartOtpActiveResDMO>,
        onResult: (BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent<SmartOtpReqActiveResDMO, SmartOtpReqActiveResDMO, SmartOtpActiveResDMO>) -> Unit
    ) {
        when (sideEffect) {
            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.RetrySendOTP -> {
                callDomain(
                    isListenAllError = true,
                    onSuccess = { result ->
                        onResult(
                            BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnRetrySendOTPSuccess(
                                result.data
                            )
                        )
                    },
                    onFail = { result ->
                        onResult(
                            BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnVerifyOTPError(
                                errorCode = result?.errorCode,
                                errorMessage = result?.errorMessage
                            )
                        )
                    }
                ) {
                    activeSmartOTPUseCase.invoke()
                }
            }

            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTP -> {
                callDomain(
                    isListenAllError = true,
                    onSuccess = { result ->
                        onResult(
                            BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnVerifyOTPSuccess(
                                result.data
                            )
                        )
                    },
                    onFail = { result ->
                        onResult(
                            BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnVerifyOTPError(
                                errorCode = result?.errorCode,
                                errorMessage = result?.errorMessage
                            )
                        )
                    }
                ) {
                    activeSmartOTPUseCase.activeSmartOtp(
                        otpNum = sideEffect.otpNum,
                        fcmId = fcmUseCase.getFcmId()
                    )
                }
            }

            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.RetryOTPSuccess,
            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTPSuccess -> TODO()
        }
    }

    fun saveUserActiveOtp(userActiveSmartOtpDMO: UserActiveSmartOtpDMO) {
        smartOTPUseCase.saveUserActiveSmartOtpDMO(userActiveSmartOtpDMO).also {
            localRepository.updateNeedToSyncTime(true)
        }
    }

    fun clearDataInputPin() {
        viewModelScope.launch(Dispatchers.Default) {
            activeSmartOTPUseCase.clearDataInputPin()
        }
    }

    fun needDeleteAllSmartOtpSuccess() {
        viewModelScope.launch(Dispatchers.Default) {
            activeSmartOTPUseCase.needAllSmartOtpSuccess()
        }
    }

    fun isAdminRole() = userInfoUseCase.isAdminRole()
}
