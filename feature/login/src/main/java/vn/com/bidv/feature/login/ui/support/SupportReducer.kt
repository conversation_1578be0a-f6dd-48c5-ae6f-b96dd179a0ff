package vn.com.bidv.feature.login.ui.support

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.common.constants.Constants

class SupportReducer :
    Reducer<SupportReducer.SupportViewState, SupportReducer.SupportEvent, SupportReducer.SupportViewEffect> {

    @Immutable
    data class SupportViewState(
        val isInitSuccess: Boolean = false,
        val urls: Map<Constants.UrlType, String> = emptyMap()
    ) : ViewState

    @Immutable
    sealed class SupportEvent : ViewEvent {
        data object InitData : SupportEvent()
        data class InitDataSuccess(
            val supportUrls: Map<Constants.UrlType, String>
        ) : SupportEvent()

    }

    @Immutable
    sealed class SupportViewEffect : SideEffect {
        data object InitData : SupportViewEffect()
    }

    override fun reduce(
        previousState: SupportViewState,
        event: SupportEvent
    ): Pair<SupportViewState, SupportViewEffect?> {
        return when (event) {
            is SupportEvent.InitData -> {
                previousState to SupportViewEffect.InitData
            }

            is SupportEvent.InitDataSuccess -> {
                previousState.copy(
                    isInitSuccess = true,
                    urls = event.supportUrls
                ) to null
            }
        }
    }
}
