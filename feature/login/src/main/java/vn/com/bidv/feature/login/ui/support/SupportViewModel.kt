package vn.com.bidv.feature.login.ui.support

import android.content.Context
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import vn.com.bidv.feature.login.domain.SupportUseCase
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject
import vn.com.bidv.feature.login.ui.support.SupportReducer.SupportEvent
import vn.com.bidv.feature.login.ui.support.SupportReducer.SupportViewEffect
import vn.com.bidv.feature.login.ui.support.SupportReducer.SupportViewState
import vn.com.bidv.sdkbase.utils.LocaleManager

@HiltViewModel
class SupportViewModel @Inject constructor(
    private val supportUseCase: SupportUseCase,
    @ApplicationContext private val context: Context
) : ViewModelIBankBase<SupportViewState, SupportEvent, SupportViewEffect>(
    initialState = SupportViewState(),
    reducer = SupportReducer()
) {
    override fun handleEffect(
        sideEffect: SupportViewEffect,
        onResult: (SupportEvent) -> Unit
    ) {
        when (sideEffect) {
            is SupportViewEffect.InitData -> {
                val itemLanguage = LocaleManager.getCurrentLanguage(context)
                onResult(
                    SupportEvent.InitDataSuccess(
                        supportUrls = supportUseCase.getLinkSupport(itemLanguage)
                    )
                )
            }
        }
    }
}
