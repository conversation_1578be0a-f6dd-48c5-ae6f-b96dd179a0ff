package vn.com.bidv.feature.login.domain.model

import com.google.gson.annotations.SerializedName
import vn.com.bidv.feature.common.domain.data.TransAuthDMO

data class CompositeOtpResDMO (
    /* SMARTOTP|CA|SMSOTP|EMAILOTP */
    @SerializedName("authType")
    val authType: String? = null,

    /* Transaction ID */
    @SerializedName("transId")
    val transId: String? = null,

    @SerializedName("basicOtp")
    val basicOtp: ModelCreateOtpResDMO? = null,

    @SerializedName("smartOtp")
    val smartOtp: TransAuthDMO? = null
): IIBankModelOTPDMO {
    override fun getTransactionID(): String? {
        return transId
    }

    override fun getRetryTimeSendOTP(): String? {
        return basicOtp?.resendOtpTime
    }

    override fun getActiveTimeOTP(): String? {
        return basicOtp?.otpActiveCount
    }

    override fun getMaskedPhoneOrEmailOTP(): String? {
        return basicOtp?.maskedPhoneOrEmail
    }

    override fun getContent(): String? {
        return basicOtp?.contentText
    }

}

enum class BiometricAuthType(val value: String) {
    SMARTOTP("SMARTOTP"),
    CA("CA"),
    SMSOTP("SMSOTP"),
    EMAILOTP("EMAILOTP")
}
