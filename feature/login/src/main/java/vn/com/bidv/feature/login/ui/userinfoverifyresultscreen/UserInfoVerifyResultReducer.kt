package vn.com.bidv.feature.login.ui.userinfoverifyresultscreen

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.login.domain.model.UserPersonalInfoDMO

class UserInfoVerifyResultReducer :
    Reducer<UserInfoVerifyResultReducer.UserInfoVerifyResultViewState, UserInfoVerifyResultReducer.UserInfoVerifyResultViewEvent, UserInfoVerifyResultReducer.UserInfoVerifyResultViewEffect> {

    @Immutable
    data class UserInfoVerifyResultViewState(
        val initSuccess: Boolean = false,
    ) : ViewState

    @Immutable
    sealed class UserInfoVerifyResultViewEvent : ViewEvent {
        data class OnInitScreen(
            val dataVerify: UserPersonalInfoDMO,
        ) : UserInfoVerifyResultViewEvent()

        data object OnShareDataVerified : UserInfoVerifyResultViewEvent()
    }

    @Immutable
    sealed class UserInfoVerifyResultViewEffect : SideEffect {
        data class ShareDataVerify(val dataVerify: UserPersonalInfoDMO) :
            UserInfoVerifyResultViewEffect()

        data object ShareDataVerified : UserInfoVerifyResultViewEffect(), UIEffect
    }

    override fun reduce(
        previousState: UserInfoVerifyResultViewState,
        event: UserInfoVerifyResultViewEvent,
    ): Pair<UserInfoVerifyResultViewState, UserInfoVerifyResultViewEffect?> {
        return when (event) {
            is UserInfoVerifyResultViewEvent.OnInitScreen -> {
                previousState.copy(
                    initSuccess = true
                ) to UserInfoVerifyResultViewEffect.ShareDataVerify(
                    event.dataVerify
                )
            }

            is UserInfoVerifyResultViewEvent.OnShareDataVerified -> {
                previousState to UserInfoVerifyResultViewEffect.ShareDataVerified
            }
        }
    }
}
