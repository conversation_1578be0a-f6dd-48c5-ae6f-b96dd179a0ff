package vn.com.bidv.feature.login.domain.model

import com.google.gson.annotations.SerializedName

data class CreateOtpResDMO(
    /* Time for resending OTP */
    @SerializedName("resendOtpTime")
    val resendOtpTime: kotlin.String? = null,

    /* Active OTP count */
    @SerializedName("otpActiveCount")
    val otpActiveCount: kotlin.String? = null,

    /* Masked phone or email */
    @SerializedName("maskedPhoneOrEmail")
    val maskedPhoneOrEmail: kotlin.String? = null,

    /* Transaction ID */
    @SerializedName("transId")
    val transId: kotlin.String? = null
)
