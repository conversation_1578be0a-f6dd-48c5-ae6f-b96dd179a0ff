package vn.com.bidv.feature.login.ui.changepassword

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.domain.VerifyPwOtpErrorType
import vn.com.bidv.feature.login.domain.model.CreatePositiveChangePwResDMO
import vn.com.bidv.feature.login.domain.model.ModelCreateOtpResDMO
import vn.com.bidv.feature.login.domain.model.ModelLoginDMO
import vn.com.bidv.feature.login.ui.changepassword.model.ModelChangPasswordUI
import vn.com.bidv.feature.login.ui.changepassword.model.ModelRuleValidPassword
import vn.com.bidv.sdkbase.domain.DomainErrorCode
import vn.com.bidv.feature.common.constants.Constants as ConstantsCommon

class ChangePasswordReducer :
    Reducer<ChangePasswordReducer.ResultState, ChangePasswordReducer.ChangePasswordViewEvent, ChangePasswordReducer.ChangePasswordViewEffect> {

    @Immutable
    data class ResultState(
        val modelChangPasswordUI: ModelChangPasswordUI = ModelChangPasswordUI.getDefault(),
        val transId: String = "",
    ) : ViewState

    @Immutable
    sealed class ChangePasswordViewEvent : ViewEvent {
        data class InitData(val modelLoginDMO: ModelLoginDMO?) : ChangePasswordViewEvent()
        data class OldPasswordChanged(val oldPassword: String) : ChangePasswordViewEvent()
        data class NewPasswordChanged(val newPassword: String) : ChangePasswordViewEvent()
        data class ConfirmPasswordChanged(val confirmPassword: String) : ChangePasswordViewEvent()
        data object Continue : ChangePasswordViewEvent()
        data class OnValidClientInputDataSuccess(val isHaveAuthMethod: Boolean) : ChangePasswordViewEvent()
        data object ValidOldPassword : ChangePasswordViewEvent()
        data object ValidNewPassword : ChangePasswordViewEvent()
        data object ValidConfirmPassword : ChangePasswordViewEvent()

        data class PerformChangePasswordSuccess(val modelCreateOtpResDMO: ModelCreateOtpResDMO? = null, ) : ChangePasswordViewEvent()
        data class PositiveChangePasswordSuccess(val positiveChangePwResDMO: CreatePositiveChangePwResDMO? = null, ) : ChangePasswordViewEvent()
        data class PerformChangePasswordError(
            val listClientErrorCode: List<Pair<String, String>>,
            val errorCode: String?,
            val message: String
        ) : ChangePasswordViewEvent()
        data class ValidOldPasswordDone(val listClientErrorCode: List<Pair<String, String>>) : ChangePasswordViewEvent()
        data class ValidNewPasswordDone(val listClientErrorCode: List<Pair<String, String>>) : ChangePasswordViewEvent()
        data class ValidNewPasswordRealTimeDone(val listClientErrorCode: List<Pair<String, String>>) : ChangePasswordViewEvent()
        data class ValidConfirmPasswordDone(val listClientErrorCode: List<Pair<String, String>>) : ChangePasswordViewEvent()
        data object VerifyPositiveChangePwSuccess : ChangePasswordViewEvent()
        data class VerifyPositiveChangePwFail(val errorCode: String, val message: String) : ChangePasswordViewEvent()
        data object OnTurnOffBiometric: ChangePasswordViewEvent()
    }

    @Immutable
    sealed class ChangePasswordViewEffect : SideEffect {
        data class PerformChangePassword(
            val method: String,
            val oldPassword: String,
            val newPassword: String,
            val confirmPassword: String,
            val userName: String,
        ) : ChangePasswordViewEffect()

        data class ValidOldPassword(val oldPassword: String) : ChangePasswordViewEffect()
        data class ValidNewPassword(val newPassword: String, val userName: String) : ChangePasswordViewEffect()
        data class ValidNewPasswordRealTime(val newPassword: String, val userName: String) : ChangePasswordViewEffect()
        data class ValidConfirmPassword(val newPassword: String, val confirmPassword: String) :
            ChangePasswordViewEffect()
        data class CreateSmsOtp(val modelLoginDMO: ModelLoginDMO?, val modelCreateOtpResDMO: ModelCreateOtpResDMO?): ChangePasswordViewEffect(), UIEffect

        data class PositiveCreateSmsOtp(
            val modelLoginDMO: ModelLoginDMO?,
            val modelCreateOtpResDMO: ModelCreateOtpResDMO?
        ) : ChangePasswordViewEffect(), UIEffect

        data class PositiveCreateSmartOtp(
            val modelChangPasswordUI: ModelChangPasswordUI
        ) :
            ChangePasswordViewEffect(), UIEffect

        data object VerifyChangePwSuccess : ChangePasswordViewEffect(), UIEffect
        data class ShowDefaultErrorVerifyChangePwFail(val message: String) : ChangePasswordViewEffect(), UIEffect
        data class ShowSmartOtpErrorVerifyChangePwFail(val message: String) : ChangePasswordViewEffect(), UIEffect
        data class ValidClientInputData(
            val method: String,
            val oldPassword: String,
            val newPassword: String,
            val confirmPassword: String,
            val userName: String,
        ) : ChangePasswordViewEffect()
        data object TurnOffBiometric: ChangePasswordViewEffect()
    }

    override fun reduce(
        previousState: ResultState,
        event: ChangePasswordViewEvent,
    ): Pair<ResultState, ChangePasswordViewEffect?> {
        return handleResultState(previousState, event)
    }

    private fun handleResultState(
        previousState: ResultState,
        event: ChangePasswordViewEvent
    ): Pair<ResultState, ChangePasswordViewEffect?> {
        return when(event) {
            is ChangePasswordViewEvent.InitData -> {
                ResultState(modelChangPasswordUI = previousState.modelChangPasswordUI.copy(modelLoginDMO = event.modelLoginDMO)) to null
            }

            is ChangePasswordViewEvent.OldPasswordChanged -> {
                ResultState(modelChangPasswordUI = previousState.modelChangPasswordUI.copy(oldPassword = event.oldPassword)) to null
                previousState.copy(
                    modelChangPasswordUI = previousState.modelChangPasswordUI.copy(oldPassword = event.oldPassword)
                ) to null
            }

            is ChangePasswordViewEvent.ValidOldPassword -> {
                previousState to ChangePasswordViewEffect.ValidOldPassword(previousState.modelChangPasswordUI.oldPassword)
            }

            is ChangePasswordViewEvent.NewPasswordChanged -> {
                previousState.copy(
                    modelChangPasswordUI = previousState.modelChangPasswordUI.copy(newPassword = event.newPassword)
                ) to ChangePasswordViewEffect.ValidNewPasswordRealTime(
                    previousState.modelChangPasswordUI.newPassword,
                    previousState.modelChangPasswordUI.modelLoginDMO?.username ?: ""
                )
            }

            is ChangePasswordViewEvent.ValidNewPassword -> {
                previousState to ChangePasswordViewEffect.ValidNewPassword(
                    previousState.modelChangPasswordUI.newPassword,
                    previousState.modelChangPasswordUI.modelLoginDMO?.username ?: ""
                )
            }

            is ChangePasswordViewEvent.ConfirmPasswordChanged -> {
                previousState.copy(
                    modelChangPasswordUI = previousState.modelChangPasswordUI.copy(confirmPassword = event.confirmPassword)
                ) to null
            }

            is ChangePasswordViewEvent.ValidConfirmPassword -> {
                previousState to ChangePasswordViewEffect.ValidConfirmPassword(
                    previousState.modelChangPasswordUI.newPassword,
                    previousState.modelChangPasswordUI.confirmPassword,
                )
            }

            is ChangePasswordViewEvent.ValidOldPasswordDone -> {
                previousState.copy(
                    modelChangPasswordUI = previousState.modelChangPasswordUI.copy(
                        listOldPWClientErrorCode = event.listClientErrorCode
                    )
                ) to null
            }

            is ChangePasswordViewEvent.ValidNewPasswordDone -> {
                val modelRuleValidPassword = previousState.modelChangPasswordUI.getModelRuleValidPassword(event.listClientErrorCode)
                previousState.copy(
                    modelChangPasswordUI = previousState.modelChangPasswordUI.copy(
                        listNewPWClientErrorCode = event.listClientErrorCode,
                        modelRuleValidPassword = modelRuleValidPassword
                    )
                ) to null
            }

            is ChangePasswordViewEvent.ValidNewPasswordRealTimeDone -> {
                val modelRuleValidPassword = previousState.modelChangPasswordUI.getModelRuleValidPassword(event.listClientErrorCode)
                previousState.copy(
                    modelChangPasswordUI = previousState.modelChangPasswordUI.copy(
                        modelRuleValidPassword = modelRuleValidPassword
                    )
                ) to null
            }


            is ChangePasswordViewEvent.ValidConfirmPasswordDone -> {
                previousState.copy(
                    modelChangPasswordUI = previousState.modelChangPasswordUI.copy(
                        listConfirmPWClientErrorCode = event.listClientErrorCode
                    )
                ) to null
            }

            is ChangePasswordViewEvent.Continue -> {
                previousState to ChangePasswordViewEffect.ValidClientInputData(
                    previousState.modelChangPasswordUI.modelLoginDMO?.status ?: "",
                    previousState.modelChangPasswordUI.oldPassword,
                    previousState.modelChangPasswordUI.newPassword,
                    previousState.modelChangPasswordUI.confirmPassword,
                    previousState.modelChangPasswordUI.modelLoginDMO?.username ?: ""
                )
            }

            is ChangePasswordViewEvent.OnValidClientInputDataSuccess -> {
                val status = previousState.modelChangPasswordUI.modelLoginDMO?.status ?: ""
                val isHaveAuthMethod = event.isHaveAuthMethod
                val newEffect = if (status == Constants.PW_CHANGE && isHaveAuthMethod)
                    ChangePasswordViewEffect.PositiveCreateSmartOtp(
                        modelChangPasswordUI = ModelChangPasswordUI(
                            oldPassword = previousState.modelChangPasswordUI.oldPassword,
                            newPassword = previousState.modelChangPasswordUI.newPassword,
                            confirmPassword = previousState.modelChangPasswordUI.confirmPassword,
                        )
                    )
                else ChangePasswordViewEffect.PerformChangePassword(
                    status,
                    previousState.modelChangPasswordUI.oldPassword,
                    previousState.modelChangPasswordUI.newPassword,
                    previousState.modelChangPasswordUI.confirmPassword,
                    previousState.modelChangPasswordUI.modelLoginDMO?.username ?: ""
                )
                return previousState.copy(
                    modelChangPasswordUI = previousState.modelChangPasswordUI.copy(
                        listOldPWClientErrorCode = null,
                        listNewPWClientErrorCode = null,
                        listConfirmPWClientErrorCode = null,
                        modelRuleValidPassword = ModelRuleValidPassword()
                    )

                ) to newEffect
            }

            is ChangePasswordViewEvent.PerformChangePasswordSuccess -> {
                val modelCreateOtpResDMO = event.modelCreateOtpResDMO
                val newEffect = if (modelCreateOtpResDMO?.trustedDevice != true) {
                    ChangePasswordViewEffect.CreateSmsOtp(
                        previousState.modelChangPasswordUI.modelLoginDMO,
                        event.modelCreateOtpResDMO
                    )
                } else {
                    ChangePasswordViewEffect.VerifyChangePwSuccess
                }
                previousState.copy(
                    modelChangPasswordUI = previousState.modelChangPasswordUI.copy(
                        modelCreateOtpResDMO = event.modelCreateOtpResDMO
                    )
                ) to newEffect
            }

            is ChangePasswordViewEvent.PositiveChangePasswordSuccess -> {
                val eventData = event.positiveChangePwResDMO

                val modelCreateOtpResDMO = createModelCreateOtpResDMO(eventData)

                val newEffect = createChangePasswordViewEffect(
                    eventData?.authType,
                    previousState.modelChangPasswordUI.modelLoginDMO,
                    modelCreateOtpResDMO,
                )

                val newState = when (eventData?.authType) {
                    ConstantsCommon.AuthType.SMS_OTP, ConstantsCommon.AuthType.EMAIL_OTP -> previousState.copy(
                        modelChangPasswordUI = previousState.modelChangPasswordUI.copy(
                            modelCreateOtpResDMO = modelCreateOtpResDMO
                        )
                    )

                    else -> previousState.copy(transId = eventData?.transId ?: "")
                }

                newState to newEffect
            }

            is ChangePasswordViewEvent.PerformChangePasswordError -> {
                val listNewPWClientErrorCode = getListNewPWErrorCode(event.listClientErrorCode)
                val modelChangPasswordUI = previousState.modelChangPasswordUI.copy(
                    listOldPWClientErrorCode = getListOldPWErrorCode(event.listClientErrorCode),
                    listNewPWClientErrorCode = listNewPWClientErrorCode,
                    listConfirmPWClientErrorCode = getListConfirmPWErrorCode(event.listClientErrorCode),
                    modelRuleValidPassword = previousState.modelChangPasswordUI.getModelRuleValidPassword(listNewPWClientErrorCode)
                )
                val newEffect = when (event.errorCode) {
                    DomainErrorCode.CLIENT_ERROR_CODE -> null
                    VerifyPwOtpErrorType.SMART_OTP_HAS_EXPIRED, VerifyPwOtpErrorType.SMS_OTP_BLOCK ->
                        ChangePasswordViewEffect.ShowSmartOtpErrorVerifyChangePwFail(event.message)

                    else ->
                        ChangePasswordViewEffect.ShowDefaultErrorVerifyChangePwFail(event.message)
                }
                ResultState(modelChangPasswordUI = modelChangPasswordUI) to newEffect
            }

            is ChangePasswordViewEvent.VerifyPositiveChangePwFail -> {
                val newEffect = when (event.errorCode) {
                    VerifyPwOtpErrorType.SMART_OTP_HAS_EXPIRED, VerifyPwOtpErrorType.SMS_OTP_BLOCK ->
                        ChangePasswordViewEffect.ShowSmartOtpErrorVerifyChangePwFail(event.message)

                    else ->
                        ChangePasswordViewEffect.ShowDefaultErrorVerifyChangePwFail(event.message)
                }
                previousState to newEffect
            }

            is ChangePasswordViewEvent.VerifyPositiveChangePwSuccess -> {
                previousState to ChangePasswordViewEffect.VerifyChangePwSuccess
            }

            is ChangePasswordViewEvent.OnTurnOffBiometric -> {
                previousState to ChangePasswordViewEffect.TurnOffBiometric
            }
        }
    }

    private fun getListConfirmPWErrorCode(listClientErrorCode: List<Pair<String, String>>): List<Pair<String, String>> {
        return listClientErrorCode.filter { it.first == Constants.ViewID.CONFIRM_PASSWORD_VIEW_ID }
    }

    private fun getListNewPWErrorCode(listClientErrorCode: List<Pair<String, String>>): List<Pair<String, String>> {
        return listClientErrorCode.filter { it.first == Constants.ViewID.NEW_PASSWORD_VIEW_ID }
    }

    private fun getListOldPWErrorCode(listClientErrorCode: List<Pair<String, String>>): List<Pair<String, String>> {
        return listClientErrorCode.filter { it.first == Constants.ViewID.OLD_PASSWORD_VIEW_ID }
    }

    private fun createModelCreateOtpResDMO(eventData: CreatePositiveChangePwResDMO?): ModelCreateOtpResDMO {
        return ModelCreateOtpResDMO(
            txnId = eventData?.transId ?: "",
            maskedPhoneOrEmail = eventData?.basicOtp?.maskedPhoneOrEmail ?: "",
            otpActiveCount = eventData?.basicOtp?.otpActiveCount ?: "",
            resendOtpTime = eventData?.basicOtp?.resendOtpTime ?: "",
            contentText = eventData?.basicOtp?.contentText ?: ""
        )
    }

    private fun createChangePasswordViewEffect(
        authType: String?,
        modelLoginDMO: ModelLoginDMO?,
        modelCreateOtpResDMO: ModelCreateOtpResDMO?,
    ): ChangePasswordViewEffect? {
        return when (authType) {
            ConstantsCommon.AuthType.SMS_OTP, ConstantsCommon.AuthType.EMAIL_OTP -> {
                if (modelCreateOtpResDMO?.trustedDevice != true){
                    ChangePasswordViewEffect.PositiveCreateSmsOtp(
                        modelLoginDMO,
                        modelCreateOtpResDMO
                    )
                } else {
                    ChangePasswordViewEffect.VerifyChangePwSuccess
                }
            }

            else -> null
        }
    }

}
