package vn.com.bidv.feature.login.ui.smsOTP.newdevice

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.ui.BaseDialogScreen
import vn.com.bidv.feature.login.constants.LoginErrorCode
import vn.com.bidv.feature.login.domain.model.ModelLoginDMO
import vn.com.bidv.feature.login.domain.model.ModelSmsOTPConfigDMO
import vn.com.bidv.feature.login.navigation.NavigationHelper
import vn.com.bidv.feature.login.ui.setupsecurityinfo.requestConfirmModal.model.ModelRequestConfirm
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPContent
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPReducer
import vn.com.bidv.localization.R
import vn.com.bidv.feature.common.navigation.NavigationHelper as CommonNavigation
import vn.com.bidv.sdkbase.navigation.NavigationHelper as SdkBaseNavigationHelper

@Composable
fun NewDeviceSmsOTPScreen(
    navController: NavHostController,
    modelConfig: ModelSmsOTPConfigDMO,
) {
    val viewModel: NewDeviceModalOTPViewModel = hiltViewModel()
    var isShowErrorPopup by remember { mutableStateOf(false) }
    var isShowErrorMessage by remember { mutableStateOf("") }
    var isNavigateToLogin by remember { mutableStateOf(false) }
    BaseDialogScreen(
        viewModel = viewModel,
        renderContent = { uiState, onEvent ->
            if (!uiState.initSuccess) {
                onEvent(
                    BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.InitData(
                        modelConfig
                    )
                )
            }
            BaseIBankModalOTPContent(
                uiState = uiState,
                onEvent = onEvent,
                viewModel = viewModel
            )
            if (isShowErrorPopup) {
                IBankModalConfirm(
                    title = stringResource(R.string.loi),
                    modalConfirmType = ModalConfirmType.Error,
                    supportingText = isShowErrorMessage,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(R.string.close),
                        )
                    ),
                    onDismissRequest = {
                        isShowErrorPopup = false
                        if (isNavigateToLogin) {
                            SdkBaseNavigationHelper.navigationToLogin(navController)
                        } else {
                            onEvent(BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.ClearOTP)
                        }
                    }
                )
            }
        },
        handleSideEffect = { sideEffect ->
            when (sideEffect) {
                is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTPError -> {
                    isShowErrorPopup = true
                    isShowErrorMessage = (sideEffect.errorMessage ?: navController.context.getString(
                        R.string.co_loi_xay_ra_trong_qua_trinh_xu_ly_vui_long_thu_lai
                    ))
                    isNavigateToLogin = sideEffect.errorCode in listOf(LoginErrorCode.IM9004, LoginErrorCode.IM9006)
                }
                is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTPSuccess<ModelLoginDMO> -> {
                    when {
                        sideEffect.verifyOTPData?.isShowSecurityQuestionScreen == true && sideEffect.verifyOTPData.isShowSmartOtpScreen -> {
                            NavigationHelper.navigateToSecurityRequirementScreen(navController, sideEffect.verifyOTPData)
                        }
                        sideEffect.verifyOTPData?.isShowSecurityQuestionScreen == true -> {
                            NavigationHelper.navigateToSetupSecurityQuestion(navController, ModelRequestConfirm(
                                modelLoginDMO = sideEffect.verifyOTPData
                            )
                            )

                        }
                        sideEffect.verifyOTPData?.smartOtpDMO?.isNeedConvertActiveSmartOtp == true -> {
                            CommonNavigation.navigateToConvertSmartOtp(navController)
                        }
                        sideEffect.verifyOTPData?.isShowSmartOtpScreen == true -> {
                            CommonNavigation.navigateToActiveSmartOtp(navController, sideEffect.verifyOTPData.smartOtpDMO)
                        }
                        else -> NavigationHelper.navigateToUserInfoScreen(navController)
                    }
                }
                is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.OnDismiss -> {
                    navController.popBackStack()
                }

                else -> {
                    // no thing
                }
            }

        }
    )
}