package vn.com.bidv.feature.login.ui.reactivesmartotp

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankActionBar
import vn.com.bidv.feature.login.ui.reactivesmartotp.ReActiveSmartOtpReducer.ReActiveSmartOtpViewEvent
import vn.com.bidv.feature.login.ui.reactivesmartotp.ReActiveSmartOtpReducer.ReActiveSmartOtpViewState
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.IBTypography.NormalTypography.bodyBody_m
import vn.com.bidv.designsystem.theme.IBTypography.NormalTypography.titleTitle_s
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.login.domain.model.TransUpdateBasicOtpDMO
import vn.com.bidv.feature.login.navigation.NavigationHelper
import vn.com.bidv.designsystem.R as RDesignSystem
import vn.com.bidv.localization.R as RLocalization

@Composable
fun ReActiveSmartOtpScreen(navController: NavHostController, isPopToHome: Boolean) {
    val vm: ReActiveSmartOtpViewModel = hiltViewModel()
    BackHandler {
        handleBackAction(navController, isPopToHome)
    }
    BaseScreen(
        navController = navController,
        viewModel = vm,
        renderContent = { uiState, onEvent ->
            ReActiveSmartOtpContent(
                uiState = uiState,
                onEvent = onEvent,
            )
        },
        handleSideEffect = { sideEffect ->
            when (sideEffect) {
                is ReActiveSmartOtpReducer.ReActiveSmartOtpViewEffect.StartReActiveSmartOtpSuccess -> {
                    NavigationHelper.navigateToReActiveSmartOtpModal(
                        navController,
                        sideEffect.transUpdateBasicOtpDMO ?: TransUpdateBasicOtpDMO()
                    )
                }

                is ReActiveSmartOtpReducer.ReActiveSmartOtpViewEffect.StartReActiveSmartOtp -> {
                    //nothing
                }
            }
        },
        topAppBarType = TopAppBarType.Title,
        topAppBarConfig = TopAppBarConfig(
            titleTopAppBar = stringResource(RLocalization.string.kich_hoat_lai_smart_otp),
            onNavigationClick = {
                handleBackAction(navController, isPopToHome)
            }
        )
    )
}

private fun handleBackAction(navController: NavHostController, isNavigateToHome: Boolean) {
    if (isNavigateToHome) {
        NavigationHelper.navigateToUserInfoScreen(navController)
    } else {
        navController.popBackStack()
    }
}

@Composable
private fun ReActiveSmartOtpContent(
    uiState: ReActiveSmartOtpViewState,
    onEvent: (ReActiveSmartOtpViewEvent) -> Unit,
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
    ) {
        Column(
            modifier = Modifier.padding(
                vertical = IBSpacing.spacingS,
                horizontal = IBSpacing.spacingM
            )
        ) {
            Text(
                text = stringResource(RLocalization.string.huong_dan_kich_hoat_lai_smart_otp),
                style = LocalTypography.current.bodyBody_m,
                color = LocalColorScheme.current.contentMainSecondary
            )
            Spacer(modifier = Modifier.size(IBSpacing.spacingL))

            GuideContentItem(
                number = 1,
                title = stringResource(RLocalization.string.xac_nhan_yeu_cau),
                description = stringResource(RLocalization.string.xac_thuc_yeu_cau_kich_hoat_lai_smart_otp),
                icon = RDesignSystem.drawable.pin_smart_otp
            )
            GuideContentItem(
                number = 2,
                title = stringResource(RLocalization.string.phe_duyet),
                description = stringResource(RLocalization.string.nguoi_dung_co_quyen_quan_tri_vien_cua_khach_hang_phe_duyet_yeu_cau),
                icon = RDesignSystem.drawable.tai_khoan
            )
            GuideContentItem(
                number = 3,
                title = stringResource(RLocalization.string.ngan_hang_xac_thuc),
                description = stringResource(RLocalization.string.tuy_thuoc_phuong_thuc_nhan_otp_ngan_hang_se_huong_dan_quy_khach_lien_he_bo_phan_cham_soc_khach_hang_bidv_de_xac_thuc_hoac_ap_dung_1_khoang_thoi_gian_cho_theo_quy_dinh),
                icon = RDesignSystem.drawable.success,
                isLastItem = true
            )
        }

        IBankActionBar(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth(),
            buttonPositive = DialogButtonInfo(
                label = stringResource(RLocalization.string.bat_dau),
                onClick = {
                    onEvent(ReActiveSmartOtpViewEvent.OnStartReActiveSmartOtp)
                }
            )
        )

    }
}

@Composable
private fun GuideContentItem(
    number: Int,
    title: String,
    description: String,
    icon: Int,
    isLastItem: Boolean = false
) {
    Row(
        verticalAlignment = Alignment.Top,
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight()
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.width(IBSpacing.spacing2xl)
        ) {
            Image(
                painter = painterResource(icon),
                contentDescription = null,
                modifier = Modifier
                    .size(IBSpacing.spacing2xl)
                    .padding(IBSpacing.spacing3xs)
            )

            if (!isLastItem) {
                Spacer(modifier = Modifier.height(IBSpacing.spacing2xs))
                VerticalDivider(
                    modifier = Modifier
                        .width(IBSpacing.spacing2xs)
                        .height(54.dp),
                    color = LocalColorScheme.current.borderMainPrimary
                )
            }
        }
        Spacer(modifier = Modifier.width(IBSpacing.spacing2xs))
        Column(
            modifier = Modifier
                .weight(1f)
                .padding(start = IBSpacing.spacingXs)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .padding(
                            end = IBSpacing.spacingXs,
                            top = IBSpacing.spacing2xs,
                            bottom = IBSpacing.spacing2xs
                        )
                ) {
                    Text(
                        modifier = Modifier
                            .testTagIBank("login_guideContentItem_title_$number. $title"),
                        text = "$number. $title",
                        style = titleTitle_s,
                        color = LocalColorScheme.current.contentMainPrimary,
                    )
                }
            }
            Box(
                modifier = Modifier
                    .padding(
                        top = IBSpacing.spacing2xs,
                        bottom = IBSpacing.spacing2xs
                    )
            ) {
                Text(
                    modifier = Modifier
                        .testTagIBank("login_guideContentItem_des_$description"),
                    text = description,
                    style = bodyBody_m,
                    color = LocalColorScheme.current.contentMainTertiary,
                )
            }

            if (!isLastItem) {
                Spacer(Modifier.height(IBSpacing.spacingM))
            }
        }
    }
    if (!isLastItem) {
        Spacer(Modifier.height(IBSpacing.spacingXs))
    }
}

@Preview
@Composable
private fun ReActiveSmartOtpContentPreview() {
    ReActiveSmartOtpContent(
        uiState = ReActiveSmartOtpViewState(),
        onEvent = {}
    )
}
