package vn.com.bidv.feature.login.domain.model

import com.google.gson.annotations.SerializedName
import kotlinx.serialization.Serializable
import vn.com.bidv.feature.login.constants.Constants

@Serializable
data class ModelForgotPwOTPConfigDMO(
    @SerializedName("userName")
    var userName: String? = null,

    @SerializedName("userRole")
    var userRole: String? = null,

    @SerializedName("modelCreateOtpResDMO")
    var modelCreateOtpResDMO: ModelCreateOtpResDMO? = ModelCreateOtpResDMO(),
) : IIBankModelOTPDMO {

    override fun getUserNameInfo(): String? {
        return userName
    }

    override fun getMethod(): String? {
        return Constants.FORGOT_PW
    }

    override fun getTransactionID(): String? {
        return modelCreateOtpResDMO?.txnId
    }

    override fun getRetryTimeSendOTP(): String? {
        return modelCreateOtpResDMO?.resendOtpTime
    }

    override fun getActiveTimeOTP(): String? {
        return modelCreateOtpResDMO?.otpActiveCount
    }

    override fun getMaskedPhoneOrEmailOTP(): String? {
        return modelCreateOtpResDMO?.maskedPhoneOrEmail
    }

    override fun getContent(): String? {
        return modelCreateOtpResDMO?.contentText
    }
}
