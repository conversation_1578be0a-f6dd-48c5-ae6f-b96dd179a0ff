package vn.com.bidv.feature.login.domain

import vn.com.bidv.feature.common.di.AuthProvider
import vn.com.bidv.feature.login.data.LoginRepository
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class LoginManager @Inject constructor(
    private val loginRepository: LoginRepository
) : AuthProvider {

    override suspend fun logout(): DomainResult<Unit> {
        val result = loginRepository.logout()
        return result.convert(Unit::class.java)
    }
}