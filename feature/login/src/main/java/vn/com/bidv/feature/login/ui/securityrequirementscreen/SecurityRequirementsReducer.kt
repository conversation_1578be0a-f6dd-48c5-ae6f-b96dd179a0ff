package vn.com.bidv.feature.login.ui.securityrequirementscreen

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.common.constants.Constants.WAITING_CONVERT
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.domain.model.ModelLoginDMO
import vn.com.bidv.feature.login.ui.securityrequirementscreen.model.ModelSecurityRequirementsUI

class SecurityRequirementsReducer :
    Reducer<SecurityRequirementsReducer.SecurityRequirementsViewState, SecurityRequirementsReducer.SecurityRequirementsViewEvent, SecurityRequirementsReducer.SecurityRequirementsSideEffect> {

    @Immutable
    sealed class SecurityRequirementsViewState : ViewState {
        data class InitScreen(val modelUI: ModelSecurityRequirementsUI = ModelSecurityRequirementsUI()) :
            SecurityRequirementsViewState()
    }

    @Immutable
    sealed class SecurityRequirementsViewEvent : ViewEvent {
        data class InitData(val data: ModelLoginDMO?) : SecurityRequirementsViewEvent()

        data object ContinueClicked : SecurityRequirementsViewEvent()
    }

    @Immutable
    sealed class SecurityRequirementsSideEffect : SideEffect {
        data class NextToChangePassword(val modelLoginDMO: ModelLoginDMO?) :
            SecurityRequirementsSideEffect(),
            UIEffect

        data object NextToSetupSecurityQuestion : SecurityRequirementsSideEffect(),
            UIEffect

        data object NextToSetupSmartOtp : SecurityRequirementsSideEffect(),
            UIEffect
    }

    override fun reduce(
        previousState: SecurityRequirementsViewState,
        event: SecurityRequirementsViewEvent,
    ): Pair<SecurityRequirementsViewState, SecurityRequirementsSideEffect?> {
        return when (previousState) {
            is SecurityRequirementsViewState.InitScreen -> handleInitScreenState(
                previousState,
                event
            )
        }
    }

    private fun handleInitScreenState(
        previousState: SecurityRequirementsViewState.InitScreen,
        event: SecurityRequirementsViewEvent
    ): Pair<SecurityRequirementsViewState, SecurityRequirementsSideEffect?> {
        return when (event) {
            is SecurityRequirementsViewEvent.InitData -> {
                val (totalStep, stepDone) = getTotalStep(
                    event.data?.status ?: "",
                    event.data?.userRole ?: "",
                    event.data?.isSecurityQuestionSet ?: false,
                    event.data?.smartOtpDMO?.isSmartOtpSet ?: false,
                    event.data?.smartOtpDMO?.regStatus ?: ""
                )
                previousState.copy(
                    modelUI = previousState.modelUI.copy(
                        modelLoginDMO = event.data,
                        totalStep = totalStep,
                        stepDone = stepDone
                    )
                ) to null
            }

            is SecurityRequirementsViewEvent.ContinueClicked -> {
                val nextSideEffect = getNextSideEffect(previousState)
                previousState to nextSideEffect
            }
        }
    }

    private fun getNextSideEffect(previousState: SecurityRequirementsViewState.InitScreen): SecurityRequirementsSideEffect? {

        val status = previousState.modelUI.modelLoginDMO?.status ?: ""
        val securityQuestionSet =
            previousState.modelUI.modelLoginDMO?.isShowSecurityQuestionScreen ?: false
        val smartOtpSet = previousState.modelUI.modelLoginDMO?.isShowSmartOtpScreen ?: false

        return when {
            isNeedToChangePassword(status) -> {
                SecurityRequirementsSideEffect.NextToChangePassword(previousState.modelUI.modelLoginDMO)
            }

            securityQuestionSet -> {
                SecurityRequirementsSideEffect.NextToSetupSecurityQuestion
            }

            smartOtpSet -> {
                SecurityRequirementsSideEffect.NextToSetupSmartOtp
            }

            else -> {
                null
            }
        }
    }

    private fun isNeedToChangePassword(status: String): Boolean {
        return status.contains(Constants.FIRST_TIME_PW_CHANGE, true)
    }

    private fun getTotalStep(
        status: String,
        userRole: String,
        isSecurityQuestionSet: Boolean,
        isSmartOtpSet: Boolean,
        regStatus: String
    ): Pair<Int, Int> {
        var totalStep = 1
        var stepDone = 0
        if (!status.contains(Constants.FIRST_TIME_PW_CHANGE, true)) stepDone += 1
        if (isShowSettingSecurityQuestion(userRole)) {
            totalStep += 1
            if (isSecurityQuestionSet) stepDone += 1
        }
        if (isSmartOtpSet) {
            totalStep += 1
            if (regStatus != Constants.WAITING_ACTIVATION && regStatus != WAITING_CONVERT) {
                stepDone += 1
            }
        }
        return totalStep to stepDone
    }

    private fun isShowSettingSecurityQuestion(userRole: String): Boolean {
        return userRole.contains(Constants.ADMIN_ROLE, true)
    }
}
