package vn.com.bidv.feature.login.constants

object LoginErrorCode {
    const val TIMEOUT_FO_01 = "TIMEOUT_FO_01"
    const val IM2208 = "IM2208"
    const val IM2209 = "IM2209"
    const val IM1204 = "IM1204"
    const val IM9004 = "IM9004" // So dien thoai bi Khoa SMS OTP tam thoi do nhap sai OTP 5 lan lien tiep
    const val IM9006 = "IM9006" // Mã OTP hết hiệu lực. Vui lòng kiểm tra lại.
    const val IM1702 = "IM1702" // Force update application
    const val IM3006 = "IM3006" // Login other device, Turn off Touch ID
    const val IM3005 = "IM3005 " // Invalid touch id status, Turn off Touch ID
    const val USER_NAME_EMPTY = "10000" // Error code for empty user name
    const val PASSWORD_EMPTY = "10001" // Error code for empty password
    const val LESS_THAN_8_CHARACTERS = "10002" // Error code for password less than 8 characters
    const val NOT_CONTAIN_CHARACTER_LOWER = "10004"
    const val NOT_CONTAIN_CHARACTER_UPPER = "10005"
    const val NOT_CONTAIN_NUMBER = "10006"
    const val NOT_CONTAIN_SPECIAL_CHARACTER = "10007"
    const val CONTAIN_REPEATED_CHARACTER = "10008"
    const val OVERLAP_3_CONSECUTIVE_CHARACTERS_USER_NAME = "10009"
    const val CONFIRM_PASS_NOT_MATCH_NEW_PASS = "10010"
    const val INVALID_INPUT = "INVALID_INPUT"
    const val NOT_START_WITH_NUMBER_0 = "START_WITH_NUMBER_0"
    const val NOT_FULL_10_NUMBER = "NOT_FULL_10_NUMBER"
    const val ADD_NEW_FINGERPRINT = 99999999
    const val INVALID_EMAIL = "INVALID_EMAIL"
}