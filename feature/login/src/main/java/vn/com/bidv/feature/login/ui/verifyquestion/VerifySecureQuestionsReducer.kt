package vn.com.bidv.feature.login.ui.verifyquestion

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.domain.model.CodeValueDMO
import vn.com.bidv.feature.login.domain.model.SecQnForgotPwResponseDMO
import vn.com.bidv.feature.login.domain.model.SecurityQuestionAnswerDMO
import vn.com.bidv.feature.login.ui.setupsecurityquestionsscreen.model.QaModel

class VerifySecureQuestionsReducer :
    Reducer<VerifySecureQuestionsReducer.VerifySecureQuestionsViewState, VerifySecureQuestionsReducer.VerifySecureQuestionsViewEvent, VerifySecureQuestionsReducer.VerifySecureQuestionsViewEffect> {

    data class VerifySecureQuestionsViewState(
        val data: List<QaModel> = listOf(
            QaModel(),
            QaModel(),
        ),
        var dataListQuestion: List<CodeValueDMO> = emptyList(),
        val userName: String = "",
        val transId: String = ""
    ) : ViewState {
        fun dataListQuestionCanSelect(index: Int? = -1): List<CodeValueDMO> {
            return when (index) {
                in data.indices -> {
                    val excludedCodes =
                        data.filterIndexed { i, _ -> i != index }.map { it.question.code }

                    dataListQuestion.filter { item -> item.code !in excludedCodes }
                }

                else -> dataListQuestion
            }
        }
    }

    @Immutable
    sealed class VerifySecureQuestionsViewEvent : ViewEvent {

        data class InitEvent(
            val userName: String = "",
            val transId: String = "",
            val initData: List<CodeValueDMO> = emptyList()
        ) : VerifySecureQuestionsViewEvent()

        data class QuestionChangeEvent(val index: Int? = -1, val question: CodeValueDMO) :
            VerifySecureQuestionsViewEvent()

        data class AnswerChangeEvent(val index: Int? = -1, val answer: String) :
            VerifySecureQuestionsViewEvent()

        data object SubmitDataEvent : VerifySecureQuestionsViewEvent()

        data class SubmitDataSuccessEvent(val secQnForgotPwResponseDMO: SecQnForgotPwResponseDMO?) :
            VerifySecureQuestionsViewEvent()

        data class SubmitDataErrorEvent(val listClientErrorCode: List<Pair<String, String>>) :
            VerifySecureQuestionsViewEvent()
    }

    @Immutable
    sealed class VerifySecureQuestionsViewEffect : SideEffect {

        data class SubmitDataEffect(
            val dataSubmit: List<SecurityQuestionAnswerDMO>,
            val userName: String,
            val transId: String
        ) : VerifySecureQuestionsViewEffect()

        data class SubmitDataSuccessEffect(val secQnForgotPwResponseDMO: SecQnForgotPwResponseDMO?) :
            VerifySecureQuestionsViewEffect(), UIEffect
    }

    override fun reduce(
        previousState: VerifySecureQuestionsViewState,
        event: VerifySecureQuestionsViewEvent,
    ): Pair<VerifySecureQuestionsViewState, VerifySecureQuestionsViewEffect?> {
        return when (event) {
            is VerifySecureQuestionsViewEvent.InitEvent -> {
                return previousState.copy(userName = previousState.userName.ifEmpty { event.userName },
                    transId = previousState.transId.ifEmpty { event.transId },
                    dataListQuestion = event.initData
                ) to null
            }

            is VerifySecureQuestionsViewEvent.QuestionChangeEvent -> {
                val updatedData = previousState.data.toMutableList()
                event.index?.let { index ->
                    if (index in updatedData.indices && event.question.code != updatedData[index].question.code) {
                        updatedData[index] = updatedData[index].copy(
                            question = event.question,
                            answerTextFieldValue = "",
                            listErrorCode = updatedData[index].listErrorCode.orEmpty()
                                .filter { it.first != Constants.ViewID.QUESTION_VIEW_ID + index },
                        )
                    }
                }
                val newState = previousState.copy(data = updatedData)
                return newState to null
            }

            is VerifySecureQuestionsViewEvent.AnswerChangeEvent -> {
                val updatedData = previousState.data.toMutableList()
                event.index?.let { index ->
                    if (index in updatedData.indices) {
                        updatedData[index] =
                            updatedData[index].copy(answerTextFieldValue = event.answer,
                                listErrorCode = updatedData[index].listErrorCode.orEmpty()
                                    .filter { it.first != Constants.ViewID.ANSWER_VIEW_ID + index })
                    }
                }
                val newState = previousState.copy(data = updatedData)
                return newState to null
            }

            is VerifySecureQuestionsViewEvent.SubmitDataEvent -> {
                val newEffect =
                    if (previousState.data.isEmpty()) null else VerifySecureQuestionsViewEffect.SubmitDataEffect(
                        userName = previousState.userName,
                        transId = previousState.transId,
                        dataSubmit = previousState.data.map { qaModel ->
                            SecurityQuestionAnswerDMO(
                                questionCode = qaModel.question.code,
                                answer = qaModel.answerTextFieldValue ?: ""
                            )
                        })
                return previousState to newEffect
            }

            is VerifySecureQuestionsViewEvent.SubmitDataSuccessEvent -> {
                val result = event.secQnForgotPwResponseDMO
                previousState.copy(
                    transId = result?.basicOtp?.txnId ?: previousState.transId,
                ) to VerifySecureQuestionsViewEffect.SubmitDataSuccessEffect(
                    secQnForgotPwResponseDMO = result
                )
            }

            is VerifySecureQuestionsViewEvent.SubmitDataErrorEvent -> {
                previousState.copy(data = previousState.data.mapIndexed { index, qaModel ->
                    qaModel.copy(
                        listErrorCode = event.listClientErrorCode.filter { it.first == Constants.ViewID.QUESTION_VIEW_ID + index || it.first == Constants.ViewID.ANSWER_VIEW_ID + index },
                    )
                }) to null
            }
        }

    }
}
