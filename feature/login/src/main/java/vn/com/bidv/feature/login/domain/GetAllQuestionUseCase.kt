package vn.com.bidv.feature.login.domain

import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.constants.LoginErrorCode
import vn.com.bidv.feature.login.data.LoginRepository
import vn.com.bidv.feature.login.data.PasswordRepository
import vn.com.bidv.feature.login.data.login.model.SecurityQuestionAnswerDto
import vn.com.bidv.feature.login.domain.model.SecQnForgotPwResponseDMO
import vn.com.bidv.feature.login.domain.model.SecQnGetAllResponseDMO
import vn.com.bidv.feature.login.domain.model.SecurityQuestionAnswerDMO
import vn.com.bidv.network.NetworkConfig
import vn.com.bidv.sdkbase.domain.DomainErrorCode
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class GetAllQuestionUseCase @Inject constructor(
    private val loginRepository: LoginRepository,
    private val passwordRepository: PasswordRepository,
    private val networkConfig: NetworkConfig,
) {
    suspend operator fun invoke(): DomainResult<SecQnGetAllResponseDMO> {
        val result =
            loginRepository.getAllQuestions()

        val model = result.convert(SecQnGetAllResponseDMO::class.java)
        return model
    }

    suspend fun saveQuestions(
        securityQuestionAnswerDMO: List<SecurityQuestionAnswerDMO>
    ): DomainResult<Boolean> {
        val listClientError = mutableListOf<Pair<String, String>>()

        securityQuestionAnswerDMO.forEachIndexed { index, item ->
            val validInput = validateQuestionAnswerInput(item, index)
            listClientError.addAll(validInput)

        }

        if (listClientError.isNotEmpty()) {
            return DomainResult.Error(
                errorCode = DomainErrorCode.CLIENT_ERROR_CODE,
                listClientErrorCode = listClientError
            )
        }

        val securityQuestionAnswerDto = securityQuestionAnswerDMO.map {
            SecurityQuestionAnswerDto(
                questionCode = it.questionCode,
                answer = it.answer
            )
        }

        val result = loginRepository.saveQuestions(securityQuestionAnswerDto)
        return result.convert { isSuccess() }
    }

    private fun validateQuestionAnswerInput(
        input: SecurityQuestionAnswerDMO,
        index: Int
    ): List<Pair<String, String>> {
        val listClientError = mutableListOf<Pair<String, String>>()
        val question = input.questionCode ?: ""
        if (question.isBlank()) {
            listClientError.add(Constants.ViewID.QUESTION_VIEW_ID + index to LoginErrorCode.INVALID_INPUT)
        }

        val answer = input.answer ?: ""
        if (answer.isBlank()) {
            listClientError.add(Constants.ViewID.ANSWER_VIEW_ID + index to LoginErrorCode.INVALID_INPUT)
        }
        return listClientError
    }

    suspend fun forgotPwQuestion(
        userName: String,
        transId: String,
        securityQuestionAnswerDMO: List<SecurityQuestionAnswerDMO>,
    ): DomainResult<SecQnForgotPwResponseDMO> {
        val listClientError = mutableListOf<Pair<String, String>>()

        securityQuestionAnswerDMO.forEachIndexed { index, item ->
            val validInput = validateQuestionAnswerInput(item, index)
            listClientError.addAll(validInput)

        }

        if (listClientError.isNotEmpty()) {
            return DomainResult.Error(
                errorCode = DomainErrorCode.CLIENT_ERROR_CODE,
                listClientErrorCode = listClientError
            )
        }

        val securityQuestionAnswerDto = securityQuestionAnswerDMO.map {
            SecurityQuestionAnswerDto(
                questionCode = it.questionCode,
                answer = it.answer
            )
        }

        val deviceId = networkConfig.deviceId

        val result = passwordRepository.forgotPwQuestion(
            username = userName,
            deviceId = deviceId,
            transId = transId,
            securityQuestions = securityQuestionAnswerDto
        )
        return result.convert(SecQnForgotPwResponseDMO::class.java)
    }

}