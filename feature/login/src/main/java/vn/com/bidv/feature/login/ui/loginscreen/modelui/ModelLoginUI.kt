package vn.com.bidv.feature.login.ui.loginscreen.modelui

import vn.com.bidv.feature.common.domain.data.GetUserInfoResponseDMO
import vn.com.bidv.feature.login.domain.model.ModelLoginDMO
import vn.com.bidv.sdkbase.utils.ItemLanguage

data class ModelLoginUI(
    val usernameInputText: String = "",
    val passwordInputText: String = "123456789",
    val listClientErrorCode: List<Pair<String, String>>? = null,
    val iconId: Int? = null,
    val itemLanguage: ItemLanguage? = null,
    val modelLoginDMO: ModelLoginDMO? = null,
    val userInfoResponseDMO: GetUserInfoResponseDMO? = null,
    val isBiometricEnable: Boolean = false,

    ) {
    companion object {
        fun getDefault() = ModelLoginUI()
    }
}
