package vn.com.bidv.feature.login.domain.model

import com.google.gson.annotations.SerializedName

data class CreateForgotPwResponseDMO(
    /* Role of the user */
    @SerializedName("userRole")
    val userRole: kotlin.String? = null,

    @SerializedName("basicOtp")
    val basicOtp: ModelCreateOtpResDMO? = null,

    @SerializedName("securityQuestions")
    val securityQuestions: kotlin.collections.List<CodeValueDMO>? = null
)
