package vn.com.bidv.feature.login.ui.changepassword

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.domain.BiometricUseCase
import vn.com.bidv.feature.login.domain.ChangePwUseCase
import vn.com.bidv.feature.login.ui.changepassword.ChangePasswordReducer.ChangePasswordViewEffect
import vn.com.bidv.feature.login.ui.changepassword.ChangePasswordReducer.ChangePasswordViewEvent
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.domain.DomainErrorCode
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class ChangePasswordViewModel @Inject constructor(
    private val changePwUseCase: ChangePwUseCase,
    private val biometricUseCase: BiometricUseCase,
    private val userInfoUseCase: UserInfoUseCase
) : ViewModelIBankBase<ChangePasswordReducer.ResultState, ChangePasswordViewEvent, ChangePasswordViewEffect>(
    initialState = ChangePasswordReducer.ResultState(),
    reducer = ChangePasswordReducer()
) {
    override fun handleEffect(
        sideEffect: ChangePasswordViewEffect,
        onResult: (ChangePasswordViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is ChangePasswordViewEffect.ValidOldPassword -> {
                callDomain(
                    showLoadingIndicator = false,
                    listErrorCodeListen = listOf(DomainErrorCode.CLIENT_ERROR_CODE),
                    onSuccess = {
                        onResult(ChangePasswordViewEvent.ValidOldPasswordDone(listOf()))
                    },
                    onFail = {
                        onResult(
                            ChangePasswordViewEvent.ValidOldPasswordDone(
                                listClientErrorCode = it?.listClientErrorCode ?: listOf()
                            )
                        )
                    }
                ) {
                    changePwUseCase.validOldPassword(sideEffect.oldPassword)
                }
            }

            is ChangePasswordViewEffect.ValidNewPassword -> {
                callDomain(
                    showLoadingIndicator = false,
                    listErrorCodeListen = listOf(DomainErrorCode.CLIENT_ERROR_CODE),
                    onFail = {
                        onResult(
                            ChangePasswordViewEvent.ValidNewPasswordDone(
                                listClientErrorCode = it?.listClientErrorCode ?: listOf()
                            )
                        )
                    },
                    onSuccess = {
                        onResult(ChangePasswordViewEvent.ValidNewPasswordDone(listOf()))
                    }
                ) {
                    changePwUseCase.validNewPassword(
                        sideEffect.newPassword,
                        sideEffect.userName
                    )
                }
            }

            is ChangePasswordViewEffect.ValidNewPasswordRealTime -> {
                callDomain(
                    showLoadingIndicator = false,
                    listErrorCodeListen = listOf(DomainErrorCode.CLIENT_ERROR_CODE),
                    onFail = {
                        onResult(
                            ChangePasswordViewEvent.ValidNewPasswordRealTimeDone(
                                listClientErrorCode = it?.listClientErrorCode ?: listOf()
                            )
                        )
                    },
                    onSuccess = {
                        onResult(ChangePasswordViewEvent.ValidNewPasswordRealTimeDone(listOf()))
                    }
                ) {
                    changePwUseCase.validNewPassword(
                        sideEffect.newPassword,
                        sideEffect.userName
                    )
                }
            }

            is ChangePasswordViewEffect.ValidConfirmPassword -> {
                callDomain(
                    showLoadingIndicator = false,
                    listErrorCodeListen = listOf(DomainErrorCode.CLIENT_ERROR_CODE),
                    onFail = {
                        onResult(
                            ChangePasswordViewEvent.ValidConfirmPasswordDone(
                                listClientErrorCode = it?.listClientErrorCode ?: listOf()
                            )
                        )
                    },
                    onSuccess = {
                        onResult(ChangePasswordViewEvent.ValidConfirmPasswordDone(listOf()))
                    }
                ) {
                    changePwUseCase.validConfirmPassword(
                        sideEffect.newPassword,
                        sideEffect.confirmPassword
                    )
                }
            }

            is ChangePasswordViewEffect.PerformChangePassword -> {
                if (sideEffect.method == Constants.PW_CHANGE) {
                    callDomain(
                        isListenAllError = true,
                        listErrorCodeListen = listOf(DomainErrorCode.CLIENT_ERROR_CODE),
                        onFail = {
                            onResult(
                                ChangePasswordViewEvent.PerformChangePasswordError(
                                    listClientErrorCode = it?.listClientErrorCode ?: listOf(),
                                    errorCode = it?.errorCode,
                                    message = it?.errorMessage ?: resourceProvider.getString(
                                        R.string.co_loi_xay_ra_trong_qua_trinh_xu_ly_vui_long_thu_lai
                                    )
                                )
                            )
                        },
                        onSuccess = {
                            onResult(
                                ChangePasswordViewEvent.PositiveChangePasswordSuccess(it.data)
                            )
                        }
                    ) {
                        changePwUseCase.performPositiveChangePassword(
                            sideEffect.oldPassword,
                            sideEffect.newPassword,
                            sideEffect.confirmPassword,
                            sideEffect.userName
                        )
                    }
                } else {
                    callDomain(
                        isListenAllError = true,
                        listErrorCodeListen = listOf(DomainErrorCode.CLIENT_ERROR_CODE),
                        onFail = {
                            onResult(
                                ChangePasswordViewEvent.PerformChangePasswordError(
                                    listClientErrorCode = it?.listClientErrorCode ?: listOf(),
                                    errorCode = it?.errorCode,
                                    message = it?.errorMessage ?: resourceProvider.getString(
                                        R.string.co_loi_xay_ra_trong_qua_trinh_xu_ly_vui_long_thu_lai
                                ))
                            )
                        },
                        onSuccess = {
                            onResult(
                                ChangePasswordViewEvent.PerformChangePasswordSuccess(it.data)
                            )
                        }
                    ) {
                        changePwUseCase.performChangePassword(
                            sideEffect.method,
                            sideEffect.oldPassword,
                            sideEffect.newPassword,
                            sideEffect.confirmPassword,
                            sideEffect.userName
                        )
                    }
                }
            }

            is ChangePasswordViewEffect.ValidClientInputData -> {
                callDomain(
                    showLoadingIndicator = false,
                    isListenAllError = true,
                    onFail = {
                        onResult(
                            ChangePasswordViewEvent.PerformChangePasswordError(
                                listClientErrorCode = it?.listClientErrorCode ?: listOf(),
                                errorCode = it?.errorCode,
                                message = it?.errorMessage ?: resourceProvider.getString(
                                    R.string.co_loi_xay_ra_trong_qua_trinh_xu_ly_vui_long_thu_lai
                                ))
                        )
                    },
                    onSuccess = {
                        val user = userInfoUseCase.getUserInfoFromStorage().getSafeData()?.user
                        val isHaveAuthMethod = user?.isHaveAuthMethod()
                        onResult(
                            ChangePasswordViewEvent.OnValidClientInputDataSuccess(
                                isHaveAuthMethod = isHaveAuthMethod ?: false
                            )
                        )
                    }
                ) {
                    changePwUseCase.validClientInputData(
                        sideEffect.oldPassword,
                        sideEffect.newPassword,
                        sideEffect.confirmPassword,
                        sideEffect.userName
                    )
                }
            }

            is ChangePasswordViewEffect.TurnOffBiometric -> {
                biometricUseCase.removeBiometricData()
            }

            is ChangePasswordViewEffect.CreateSmsOtp -> {
                // no thing
            }

            is ChangePasswordViewEffect.PositiveCreateSmsOtp -> {}
            is ChangePasswordViewEffect.PositiveCreateSmartOtp -> {}
            is ChangePasswordViewEffect.ShowDefaultErrorVerifyChangePwFail -> {}
            is ChangePasswordViewEffect.VerifyChangePwSuccess -> {}
            is ChangePasswordViewEffect.ShowSmartOtpErrorVerifyChangePwFail -> {}
        }
    }
}
