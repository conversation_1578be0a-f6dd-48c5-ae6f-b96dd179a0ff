package vn.com.bidv.feature.login.ui.changepin

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.google.gson.Gson
import vn.com.bidv.common.extenstion.isNotNullOrEmpty
import vn.com.bidv.common.ui.BaseMVIScreen
import vn.com.bidv.feature.common.constants.Constants.VERIFY_OTP
import vn.com.bidv.feature.common.data.VerifyOtpResult
import vn.com.bidv.feature.common.domain.data.SmartOtpStatus
import vn.com.bidv.feature.common.domain.data.UserActiveSmartOtpDMO
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InitVerifyTransactionResponse
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionTypeConstant
import vn.com.bidv.feature.login.ui.changepin.RequestChangePinModalReducer.RequestChangePinModalViewEffect
import vn.com.bidv.feature.login.ui.changepin.RequestChangePinModalReducer.RequestChangePinModalViewEvent
import vn.com.bidv.sdkbase.navigation.IBankMainRouting
import vn.com.bidv.feature.common.navigation.NavigationHelper as CommonNavigation

@Composable
fun RequestChangePinModal(navController: NavHostController) {
    val vm: RequestChangePinModalViewModel = hiltViewModel()
    BaseMVIScreen(
        viewModel = vm,
        renderContent = { _, onEvent ->
            if (!vm.uiState.value.isInit) {
                onEvent(RequestChangePinModalViewEvent.InitEvent)
            }

            LaunchedEffect(true) {
                vm.subscribeShareData(VERIFY_OTP).collect {
                    val result = Gson().fromJson(it.data, VerifyOtpResult::class.java)
                    if (result.errorCode.isNotNullOrEmpty()) {
                        navController.popBackStack(
                            IBankMainRouting.AuthRoutes.ChangePinRoute.route,
                            inclusive = true
                        )
                    } else {
                        val userActiveSmartOtp = try {
                            Gson().fromJson(result.data, UserActiveSmartOtpDMO::class.java)
                        } catch (ex: Exception) {
                            null
                        }

                        if (userActiveSmartOtp != null) {
                            navController.popBackStack(IBankMainRouting.AuthRoutes.ChangePinRoute.route, true)
                            CommonNavigation.navigateToActiveSmartOtp(
                                navController = navController,
                                userActiveSmartOtpDMO = userActiveSmartOtp,
                                status = SmartOtpStatus.CHANGE_PIN
                            )
                        }
                    }
                }

            }
        },
        handleSideEffect = { sideEffect ->
            if (sideEffect is RequestChangePinModalViewEffect.InitSuccessEffect) {
                CommonNavigation.navigateToVerifyByTypeCreateTransaction(
                    navController = navController,
                    dataString = Gson().toJson(InitVerifyTransactionResponse(transAuth = sideEffect.transAuthDMO)),
                    type = VerifyTransactionTypeConstant.CHANGE_PIN,
                )
            }

        }
    )
}

