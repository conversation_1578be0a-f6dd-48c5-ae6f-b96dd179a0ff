package vn.com.bidv.feature.login.domain

import com.google.gson.Gson
import vn.com.bidv.feature.common.data.utilities.model.TransApproveRequest
import vn.com.bidv.feature.common.domain.data.TransAuthDMO
import vn.com.bidv.feature.common.domain.verifyFlowUseCase.VerifyByTypeTransactionUseCase
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InitVerifyTransactionResponse
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyCreateTransaction
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyTransaction
import vn.com.bidv.feature.login.data.ManageSecurityQuestionRepository
import vn.com.bidv.feature.login.domain.model.DataListSecurityQuestionAnswerDMO
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class ManageQuestionsVerifyUseCase @Inject constructor(
    private val smartOTPRepository: ManageSecurityQuestionRepository,
) : VerifyByTypeTransactionUseCase {

    override suspend fun initTransaction(input: InputVerifyTransaction): DomainResult<InitVerifyTransactionResponse> {
        return DomainResult.Success(InitVerifyTransactionResponse())
    }

    override suspend fun initCreateTransaction(input: InputVerifyCreateTransaction): DomainResult<InitVerifyTransactionResponse> {
        val result = smartOTPRepository.getSecurityQuestionByUser()

        val domain = result.convert(TransAuthDMO::class.java)
        return if (domain is DomainResult.Success) {
            DomainResult.Success(
                InitVerifyTransactionResponse(
                    transAuth = domain.data
                )
            )
        } else {
            val domainError = domain as? DomainResult.Error
            DomainResult.Error(
                errorCode = domainError?.errorCode ?: "",
                errorMessage = domainError?.errorMessage
            )
        }
    }

    override suspend fun verifyTransaction(
        initResponse: InitVerifyTransactionResponse,
        reqValue: String?
    ): DomainResult<String> {
        val result = smartOTPRepository.approveGetSecurityQuestionWithOtp(
            transApproveRequest = TransApproveRequest(
                transKey = initResponse.transKey ?: initResponse.transAuth?.transKey ?: "",
                authValue = reqValue ?: ""
            )
        )
        val domain = result.convert(DataListSecurityQuestionAnswerDMO::class.java)
        return if (domain is DomainResult.Success) {
            DomainResult.Success(data = Gson().toJson(domain.data))
        } else {
            val domainError = domain as? DomainResult.Error
            DomainResult.Error(
                errorCode = domainError?.errorCode ?: "",
                errorMessage = domainError?.errorMessage
            )

        }
    }
}