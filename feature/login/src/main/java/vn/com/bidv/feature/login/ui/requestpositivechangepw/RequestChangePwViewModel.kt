package vn.com.bidv.feature.login.ui.requestpositivechangepw

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.feature.login.domain.ChangePwUseCase
import vn.com.bidv.feature.login.ui.requestpositivechangepw.RequestChangePwReducer.RequestChangePwViewEffect
import vn.com.bidv.feature.login.ui.requestpositivechangepw.RequestChangePwReducer.RequestChangePwViewEvent
import vn.com.bidv.feature.login.ui.requestpositivechangepw.RequestChangePwReducer.RequestChangePwViewState
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class RequestChangePwViewModel @Inject constructor(
    private val changePwUseCase: ChangePwUseCase,
    private val userUseCase: UserInfoUseCase,
) : ViewModelIBankBase<RequestChangePwViewState, RequestChangePwViewEvent, RequestChangePwViewEffect>(
    initialState = RequestChangePwViewState,
    reducer = RequestChangePwReducer()
) {
    override fun handleEffect(
        sideEffect: RequestChangePwViewEffect,
        onResult: (RequestChangePwViewEvent) -> Unit
    ) {
        if (sideEffect is RequestChangePwViewEffect.InitEffect) {
            callDomain(
                isListenAllError = true,
                onSuccess = {
                    val user =
                        userUseCase.getUserInfoFromStorage().getSafeData()?.user
                    onResult(
                        RequestChangePwViewEvent.GetWrongPwAttemptsSuccessEvent(
                            userName = user?.username,
                            userId = user?.userId.toString()
                        )
                    )
                },
                onFail = { error ->
                    onResult(
                        RequestChangePwViewEvent.GetWrongPwAttemptsFailEvent(
                            message = error?.errorMessage ?: ""
                        )
                    )
                },
            ) {
                changePwUseCase.getWrongPwAttempts()
            }
        }
    }
}
