package vn.com.bidv.feature.login.ui.requestConfirmModal

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.common.ui.BaseMVIScreen
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionTypeConstant
import vn.com.bidv.sdkbase.navigation.IBankMainRouting
import vn.com.bidv.feature.common.navigation.NavigationHelper as CommonNavigationHelper

@Composable
fun RequestEditQuestionsModalScreen(navController: NavHostController) {
    val vm: RequestConfirmModalViewModel = hiltViewModel()

    BaseMVIScreen(
        viewModel = vm,
        renderContent = { _, _ ->
            LaunchedEffect(true) {
                navController.popBackStack(
                    IBankMainRouting.AuthRoutes.ManageQuestionsRoute.route,
                    inclusive = true
                )
                CommonNavigationHelper.navigateToVerifyByTypeCreateTransaction(
                    navController = navController,
                    dataString = VerifyTransactionTypeConstant.ManageQuestions.code,
                    type = VerifyTransactionTypeConstant.ManageQuestions,
                )
            }
        },
        handleSideEffect = {}
    )
}


