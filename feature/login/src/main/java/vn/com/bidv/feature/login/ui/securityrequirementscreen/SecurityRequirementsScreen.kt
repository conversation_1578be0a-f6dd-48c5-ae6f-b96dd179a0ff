package vn.com.bidv.feature.login.ui.securityrequirementscreen

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.navigation.button.IBankNormalButton
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonSize
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonType
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.IBTypography.NormalTypography.bodyBody_m
import vn.com.bidv.designsystem.theme.IBTypography.NormalTypography.titleTitle_s
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.common.constants.Constants.WAITING_CONVERT
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.domain.model.ModelLoginDMO
import vn.com.bidv.feature.login.navigation.NavigationHelper
import vn.com.bidv.feature.login.ui.securityrequirementscreen.SecurityRequirementsReducer.SecurityRequirementsViewEvent
import vn.com.bidv.feature.login.ui.securityrequirementscreen.SecurityRequirementsReducer.SecurityRequirementsViewState
import vn.com.bidv.feature.login.ui.securityrequirementscreen.model.ModelSecurityRequirementsUI
import vn.com.bidv.feature.login.ui.setupsecurityinfo.requestConfirmModal.model.ModelRequestConfirm
import vn.com.bidv.log.BLogUtil
import vn.com.bidv.designsystem.R as RDesignsystem
import vn.com.bidv.feature.common.navigation.NavigationHelper as CommonNavigation
import vn.com.bidv.localization.R as RLocalization

@Composable
fun SecurityRequirementsScreen(
    navController: NavHostController,
    data: ModelLoginDMO? = null
) {
    val securityRequirementsViewModel: SecurityRequirementsViewModel = hiltViewModel()
    BaseScreen(
        navController = navController,
        viewModel = securityRequirementsViewModel,
        renderContent = { uiState, onEvent ->
            if (uiState is SecurityRequirementsViewState.InitScreen) {
                onEvent(
                    SecurityRequirementsViewEvent.InitData(data)
                )
            }
            SecurityRequirementsScreenContent(
                uiState,
                onEvent
            )
        },
        topAppBarType = TopAppBarType.Title,
        topAppBarConfig = TopAppBarConfig(
            isShowTopAppBar = true,
            showHomeIcon = false,
            titleTopAppBar = stringResource(RLocalization.string.yeu_cau_thiet_lap_bao_mat),
            actionItems = {}
        ),
        handleSideEffect = { securityRequirementSideEffect ->
            BLogUtil.d("setupGuideScreenSideEffect: $securityRequirementSideEffect")
            if (securityRequirementSideEffect is SecurityRequirementsReducer.SecurityRequirementsSideEffect.NextToChangePassword) {
                NavigationHelper.navigateToChangePassword(
                    navController,
                    securityRequirementSideEffect.modelLoginDMO
                )
            }

            if (securityRequirementSideEffect is SecurityRequirementsReducer.SecurityRequirementsSideEffect.NextToSetupSecurityQuestion) {
                NavigationHelper.navigateToSetupSecurityQuestion(navController, ModelRequestConfirm(
                    modelLoginDMO = data
                )
                )
            }

            if (securityRequirementSideEffect is SecurityRequirementsReducer.SecurityRequirementsSideEffect.NextToSetupSmartOtp) {
                val smartOtp = data?.smartOtpDMO
                if (smartOtp?.isNeedConvertActiveSmartOtp == true) {
                    CommonNavigation.navigateToConvertSmartOtp(navController)
                } else {
                    CommonNavigation.navigateToActiveSmartOtp(
                        navController,
                        smartOtp,
                    )
                }
            }

        },
    )
}

@Composable
private fun SecurityRequirementsScreenContent(
    uiState: SecurityRequirementsViewState,
    onEvent: (SecurityRequirementsViewEvent) -> Unit,
) {
    val initState = uiState as SecurityRequirementsViewState.InitScreen
    val description = if (initState.modelUI.stepDone == 0) {
        stringResource(RLocalization.string.quy_khach_vui_long_thuc_hien_s_buoc_thiet_lap_bao_mat_sau_nham_dam_bao_an_toan_cho_tai_khoan).format(initState.modelUI.totalStep)
    } else {
        stringResource(RLocalization.string.quy_khach_da_hoan_thanh_ss_buoc_thiet_lap_vui_long_hoan_thanh_cac_buoc_con_lai_nham_bao_mat_tai_khoan_tot_hon).format(
            initState.modelUI.stepDone,
            initState.modelUI.totalStep
        )
    }
    Box(
        modifier = Modifier
            .fillMaxSize()
            .padding(
                top = IBSpacing.spacingS,
                start = IBSpacing.spacingM,
                end = IBSpacing.spacingM,
                bottom = IBSpacing.spacingM
            )
    ) {
        Column {
            Text(
                modifier = Modifier.fillMaxWidth(),
                style = bodyBody_m,
                color = LocalColorScheme.current.contentMainSecondary,
                text = description
            )
            Spacer(Modifier.height(IBSpacing.spacingL))

            GuideContentItem(
                1,
                stringResource(RLocalization.string.doi_mat_khau),
                stringResource(RLocalization.string.tao_mat_khau_moi_an_toan_hon_de_bao_ve_tai_khoan),
                RDesignsystem.drawable.thay_doi_mat_khau_dang_nhap,
                isStepHasDone = initState.modelUI.modelLoginDMO?.status?.contains(
                    Constants.FIRST_TIME_PW_CHANGE,
                    true
                ) != true
            )

            if (initState.modelUI.modelLoginDMO?.userRole?.contains(
                    Constants.ADMIN_ROLE,
                    true
                ) == true
            ) {
                GuideContentItem(
                    2,
                    stringResource(RLocalization.string.cai_dat_cau_hoi_bao_mat),
                    stringResource(RLocalization.string.thiet_lap_cau_hoi_bao_mat_de_giup_khoi_phuc_tai_khoan_neu_can),
                    RDesignsystem.drawable.question,
                    isStepHasDone = initState.modelUI.modelLoginDMO.isSecurityQuestionSet == true,
                    isLastItem = initState.modelUI.modelLoginDMO.smartOtpDMO?.isSmartOtpSet != true
                )
            }

            if (initState.modelUI.modelLoginDMO?.smartOtpDMO?.isSmartOtpSet == true) {
                val keywords = listOf(Constants.WAITING_ACTIVATION, WAITING_CONVERT)
                GuideContentItem(
                    initState.modelUI.totalStep,
                    stringResource(RLocalization.string.kich_hoat_smart_otp),
                    stringResource(RLocalization.string.kich_hoat_smart_otp_de_xac_thuc_giao_dich_truc_tuyen),
                    RDesignsystem.drawable.pin_smart_otp,
                    isStepHasDone = keywords.none {
                        initState.modelUI.modelLoginDMO.smartOtpDMO.regStatus?.contains(it, ignoreCase = true) == true
                    },
                    isLastItem = true
                )
            }

        }
        Box(modifier = Modifier.align(Alignment.BottomCenter)) {
            IBankNormalButton(
                type = NormalButtonType.PRIMARY(LocalColorScheme.current),
                size = NormalButtonSize.L(LocalTypography.current),
                text = stringResource(RLocalization.string.bat_dau),
                modifier = Modifier
                    .fillMaxWidth()
            ) {
                onEvent(
                    SecurityRequirementsViewEvent.ContinueClicked
                )
            }
        }
    }

}

@Composable
private fun GuideContentItem(
    number: Int,
    title: String,
    description: String,
    icon: Int,
    isStepHasDone: Boolean = false,
    isLastItem: Boolean = false
) {
    Row(
        verticalAlignment = Alignment.Top,
        modifier = Modifier
            .testTagIBank("login_guideContentItem_$number. $title")
            .fillMaxWidth()
            .wrapContentHeight()
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.width(IBSpacing.spacing2xl)
        ) {
            Image(
                painter = painterResource(icon),
                contentDescription = null,
                modifier = Modifier
                    .size(IBSpacing.spacing2xl)
                    .padding(IBSpacing.spacing3xs)
            )

            if (!isLastItem) {
                Spacer(modifier = Modifier.height(IBSpacing.spacing2xs))
                VerticalDivider(
                    modifier = Modifier
                        .width(IBSpacing.spacing2xs)
                        .height(54.dp),
                    color = LocalColorScheme.current.borderMainPrimary
                )
            }
        }
        Spacer(modifier = Modifier.width(IBSpacing.spacing2xs))
        Column(
            modifier = Modifier
                .weight(1f)
                .padding(start = IBSpacing.spacingXs)
        ) {
            val textAlpha = if (isStepHasDone) 0.5f else 1f
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .padding(
                            end = IBSpacing.spacingXs,
                            top = IBSpacing.spacing2xs,
                            bottom = IBSpacing.spacing2xs
                        )
                ) {
                    Text(
                        text = "$number. $title",
                        style = titleTitle_s,
                        color = LocalColorScheme.current.contentMainPrimary,
                        modifier = Modifier.alpha(textAlpha),
                    )
                }
                if (isStepHasDone) {
                    Image(
                        painter = painterResource(RDesignsystem.drawable.success),
                        contentDescription = "Completed",
                        modifier = Modifier.size(IBSpacing.spacingL)
                    )
                }
            }
            Box(
                modifier = Modifier
                    .padding(
                        top = IBSpacing.spacing2xs,
                        bottom = IBSpacing.spacing2xs
                    )
            ) {
                Text(
                    text = description,
                    style = bodyBody_m,
                    color = LocalColorScheme.current.contentMainTertiary,
                    modifier = Modifier.alpha(textAlpha)
                )
            }

            if (!isLastItem) {
                Spacer(Modifier.height(IBSpacing.spacingM))
            }
        }
    }
    if (!isLastItem) {
        Spacer(Modifier.height(IBSpacing.spacingXs))
    }
}

@Preview
@Composable
fun test() {
    SecurityRequirementsScreenContent(
        SecurityRequirementsViewState.InitScreen(
            ModelSecurityRequirementsUI()
        ),
        {},
    )
}

