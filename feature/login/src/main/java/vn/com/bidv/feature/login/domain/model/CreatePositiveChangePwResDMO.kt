package vn.com.bidv.feature.login.domain.model

import com.google.gson.annotations.SerializedName
import vn.com.bidv.feature.common.domain.data.TransAuthDMO

data class CreatePositiveChangePwResDMO(
    /* SMARTOTP|CA|SMSOTP|EMAILOTP */
    @SerializedName("authType")
    val authType: kotlin.String? = null,

    /* Transaction ID */
    @SerializedName("transId")
    val transId: kotlin.String? = null,

    @SerializedName("basicOtp")
    val basicOtp: ModelCreateOtpResDMO? = null,

    @SerializedName("smartOtp")
    val smartOtp: TransAuthDMO? = null
)

