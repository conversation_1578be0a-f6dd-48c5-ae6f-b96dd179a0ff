package vn.com.bidv.feature.login.ui.userinfo.modelui

import vn.com.bidv.feature.login.domain.model.UserPersonalInfoDMO
import vn.com.bidv.feature.login.domain.model.UserInfoSettingsDMO
import vn.com.bidv.designsystem.R as RDesignSystem
import vn.com.bidv.localization.R as RLocalization

data class UserInfoModelUI(
    val userInfoSettingsDMO: UserInfoSettingsDMO? = null,
    val userInfoSecureDMO: UserPersonalInfoDMO? = null
) {
    companion object {
        fun getDefault() = UserInfoModelUI()
    }
}


val iconMap = mapOf(
    RLocalization.string.so_dien_thoai to RDesignSystem.drawable.mobilephone_outline,
    RLocalization.string.email_nhan_otp to RDesignSystem.drawable.lien_he_outline,
    RLocalization.string.email to RDesignSystem.drawable.lien_he_outline,
    RLocalization.string.so_can_cuoc_cong_dan to RDesignSystem.drawable.information_circle_outline,
    RLocalization.string.ngay_cap to RDesignSystem.drawable.calendar_outline,
    RLocalization.string.ngay_het_han to RDesignSystem.drawable.calendar_outline,
    RLocalization.string.quoc_tich to RDesignSystem.drawable.quoc_te_outline,
    RLocalization.string.so_ho_chieu to RDesignSystem.drawable.information_circle_outline,
)




