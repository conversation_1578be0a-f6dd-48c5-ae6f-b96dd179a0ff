package vn.com.bidv.feature.login.ui.verifyquestion

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.feature.login.domain.GetAllQuestionUseCase
import vn.com.bidv.feature.login.ui.verifyquestion.VerifySecureQuestionsReducer.VerifySecureQuestionsViewEffect
import vn.com.bidv.feature.login.ui.verifyquestion.VerifySecureQuestionsReducer.VerifySecureQuestionsViewEvent
import vn.com.bidv.feature.login.ui.verifyquestion.VerifySecureQuestionsReducer.VerifySecureQuestionsViewState
import vn.com.bidv.sdkbase.domain.DomainErrorCode
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class VerifySecureQuestionsViewModel @Inject constructor(
    private val getAllQuestionUseCase: GetAllQuestionUseCase,
) : ViewModelIBankBase<VerifySecureQuestionsViewState, VerifySecureQuestionsViewEvent, VerifySecureQuestionsViewEffect>(
    initialState = VerifySecureQuestionsViewState(), reducer = VerifySecureQuestionsReducer()
) {
    override fun handleEffect(
        sideEffect: VerifySecureQuestionsViewEffect,
        onResult: (VerifySecureQuestionsViewEvent) -> Unit
    ) {

        if (sideEffect is VerifySecureQuestionsViewEffect.SubmitDataEffect) {
            callDomain(showLoadingIndicator = sideEffect.dataSubmit.any { it.questionCode?.isNotEmpty() == true && it.answer?.isNotEmpty() == true },
                listErrorCodeListen = listOf(DomainErrorCode.CLIENT_ERROR_CODE),
                onFail = {
                    onResult(
                        VerifySecureQuestionsViewEvent.SubmitDataErrorEvent(
                            listClientErrorCode = it?.listClientErrorCode ?: listOf(),
                        )
                    )
                },
                onSuccess = { result ->
                    onResult(
                        VerifySecureQuestionsViewEvent.SubmitDataSuccessEvent(
                            secQnForgotPwResponseDMO = result.data
                        )
                    )
                }) {
                getAllQuestionUseCase.forgotPwQuestion(
                    userName = sideEffect.userName,
                    transId = sideEffect.transId,
                    securityQuestionAnswerDMO = sideEffect.dataSubmit
                )
            }
        }
    }
}
