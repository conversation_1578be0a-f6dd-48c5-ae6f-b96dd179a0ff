package vn.com.bidv.feature.login.navigation

import android.content.Context
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionFlowScreenBuilder
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionTypeConstant
import vn.com.bidv.feature.login.domain.EditQuestionsVerifyUseCase
import vn.com.bidv.localization.R
import javax.inject.Inject

class EditQuestionsVerifyFlowScreenBuilder @Inject constructor(
    useCase: EditQuestionsVerifyUseCase,
    context: Context
) : VerifyTransactionFlowScreenBuilder(
    useCase = useCase,
    type = VerifyTransactionTypeConstant.EditQuestions,
    messageSnackBar = context.getString(R.string.thay_doi_cau_hoi_bao_mat_thanh_cong),
)
