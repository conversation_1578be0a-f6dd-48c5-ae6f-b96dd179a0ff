package vn.com.bidv.feature.login.ui.requestpositivechangepw

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.common.ui.BaseMVIScreen
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.feature.login.navigation.NavigationHelper
import vn.com.bidv.feature.login.ui.requestpositivechangepw.RequestChangePwReducer.RequestChangePwViewEffect
import vn.com.bidv.feature.login.ui.requestpositivechangepw.RequestChangePwReducer.RequestChangePwViewEvent
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.navigation.IBankMainRouting

@Composable
fun RequestChangePwScreen(navController: NavHostController) {
    val vm: RequestChangePwViewModel = hiltViewModel()

    val errorMessage = remember { mutableStateOf("") }
    BaseMVIScreen(
        viewModel = vm,
        renderContent = { _, onEvent ->
            LaunchedEffect(true) {
                onEvent(RequestChangePwViewEvent.InitData)
            }

            if (errorMessage.value.isNotEmpty()) {
                IBankModalConfirm(
                    title = stringResource(R.string.loi),
                    modalConfirmType = ModalConfirmType.Error,
                    supportingText = errorMessage.value,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(R.string.close),
                        )
                    ),
                    onDismissRequest = {
                        errorMessage.value = ""
                        navController.popBackStack(
                            IBankMainRouting.AuthRoutes.PositiveChangePwRoute.route,
                            inclusive = true
                        )
                    }
                )
            }
        },
        handleSideEffect = { sideEffect ->
            if (sideEffect is RequestChangePwViewEffect.GetWrongPwAttemptsSuccessEffect) {
                NavigationHelper.navigateToChangePassword(
                    navController,
                    sideEffect.data
                )
            }

            if (sideEffect is RequestChangePwViewEffect.GetWrongPwAttemptsFailEffect) {
                errorMessage.value = sideEffect.message
            }
        }
    )
}


