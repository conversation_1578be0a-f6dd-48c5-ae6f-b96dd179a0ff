package vn.com.bidv.feature.login.domain.model

import com.google.gson.annotations.SerializedName
import vn.com.bidv.feature.common.domain.data.SmartOtpDMO
import vn.com.bidv.feature.common.domain.data.UserResDMO

data class SmartOtpInfoDMO(
    /* Role of the user */
    @SerializedName("userRole")
    val userRole: String? = null,

    @SerializedName("smartOtp")
    val smartOtp: SmartOtpDMO? = null,

    @SerializedName("user")
    val user: UserResDMO? = null,
)
