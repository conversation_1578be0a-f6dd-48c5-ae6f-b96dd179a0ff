package vn.com.bidv.feature.login.domain.utils

import com.google.gson.Gson
import vn.com.bidv.log.BLogUtil

object Utils {
    fun <T>convertToJsonString(data: T): String {
        return Gson().toJson(data)
    }
    fun <T>convertFromJsonString(json: String, clazz: Class<T>): T? {
        return try {
            Gson().fromJson(json, clazz)
        } catch (ex: Exception) {
            ex.message?.let { BLogUtil.e(it) }
            null
        }
    }
}