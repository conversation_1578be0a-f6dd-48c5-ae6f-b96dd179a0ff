package vn.com.bidv.feature.login.domain.model

import com.google.gson.annotations.SerializedName
import kotlinx.serialization.Serializable

@Serializable
data class TransUpdateBasicOtpDMO(
    @SerializedName("authType")
    val authType: String? = null,

    /* auth Id */
    @SerializedName("authId")
    val authId: String? = null,

    /* transKey */
    @SerializedName("transKey")
    val transKey: String? = null,

    /* Resend Otp time */
    @SerializedName("resendOtp")
    val resendOtp: Long? = null,

    /* count time */
    @SerializedName("countTime")
    val countTime: Long? = null,

    /* text view onscreen user */
    @SerializedName("content")
    val contentText: String? = null
): IIBankModelOTPDMO {
    override fun getUserNameInfo(): String = ""
    override fun getMethod(): String = ""
    override fun getTransactionID(): String? = transKey
    override fun getRetryTimeSendOTP(): String = resendOtp.toString()
    override fun getActiveTimeOTP(): String = countTime.toString()
    override fun getMaskedPhoneOrEmailOTP(): String = ""
    override fun getContent(): String? = contentText
}
