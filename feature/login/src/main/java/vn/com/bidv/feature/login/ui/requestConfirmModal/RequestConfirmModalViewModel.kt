package vn.com.bidv.feature.login.ui.requestConfirmModal

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.feature.login.ui.requestConfirmModal.RequestConfirmModalReducer.RequestConfirmModalViewEffect
import vn.com.bidv.feature.login.ui.requestConfirmModal.RequestConfirmModalReducer.RequestConfirmModalViewEvent
import vn.com.bidv.feature.login.ui.requestConfirmModal.RequestConfirmModalReducer.RequestConfirmModalViewState
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class RequestConfirmModalViewModel @Inject constructor(
) : ViewModelIBankBase<RequestConfirmModalViewState, RequestConfirmModalViewEvent, RequestConfirmModalViewEffect>(
    initialState = RequestConfirmModalViewState,
    reducer = RequestConfirmModalReducer(),
) {

    override fun handleEffect(
        sideEffect: RequestConfirmModalViewEffect,
        onResult: (RequestConfirmModalViewEvent) -> Unit
    ) {
    }
}
