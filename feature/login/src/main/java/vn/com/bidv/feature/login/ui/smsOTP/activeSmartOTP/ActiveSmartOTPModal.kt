package vn.com.bidv.feature.login.ui.smsOTP.activeSmartOTP

import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.ui.BaseDialogScreen
import vn.com.bidv.feature.common.domain.data.UserActiveSmartOtpDMO
import vn.com.bidv.feature.login.domain.SmartOtpErrorType
import vn.com.bidv.feature.login.domain.model.SmartOtpActiveResDMO
import vn.com.bidv.feature.login.domain.model.SmartOtpReqActiveResDMO
import vn.com.bidv.feature.login.navigation.NavigationHelper
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPContent
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPReducer
import vn.com.bidv.localization.R
import vn.com.bidv.feature.common.navigation.NavigationHelper as CommonNavigationHelper

@Composable
fun ActiveSmartOTPModal(
    navController: NavHostController,
    modelConfig: SmartOtpReqActiveResDMO,
) {
    val viewModel: ActiveSmartOTPModalViewModel = hiltViewModel()
    val lockedErrorMessage = remember { mutableStateOf("") }
    val activationDifferentUserSmartOtpErrorMessage = remember { mutableStateOf("") }
    val commonErrorMessage = remember { mutableStateOf("") }
    val defaultErrorMessage = remember { mutableStateOf("") }
    BaseDialogScreen(
        viewModel = viewModel,
        renderContent = { uiState, onEvent ->
            if (!uiState.initSuccess) {
                onEvent(
                    BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.InitData(
                        modelConfig
                    )
                )
            }
            BaseIBankModalOTPContent(
                uiState = uiState,
                onEvent = onEvent,
                viewModel = viewModel
            )
            if (lockedErrorMessage.value.isNotEmpty()) {
                IBankModalConfirm(
                    title = stringResource(R.string.loi),
                    modalConfirmType = ModalConfirmType.Error,
                    supportingText = lockedErrorMessage.value,
                    listDialogButtonInfo = getListButtonLockedError(viewModel.isAdminRole(), navController),
                    onDismissRequest = {
                        NavigationHelper.navigateFromActiveSmartOtpToHomeScreen(navController)
                    }
                )
            }

            if (activationDifferentUserSmartOtpErrorMessage.value.isNotEmpty()) {
                IBankModalConfirm(
                    title = stringResource(R.string.canh_bao),
                    modalConfirmType = ModalConfirmType.Warning,
                    supportingText = activationDifferentUserSmartOtpErrorMessage.value,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(R.string.yes),
                            isDismissRequest = false,
                            onClick = {
                                activationDifferentUserSmartOtpErrorMessage.value = ""
                                viewModel.needDeleteAllSmartOtpSuccess()
                                navController.popBackStack()
                            }
                        ),
                        DialogButtonInfo(
                            label = stringResource(R.string.de_sau),
                        ),
                    ),
                    onDismissRequest = {
                        activationDifferentUserSmartOtpErrorMessage.value = ""
                        NavigationHelper.navigateFromActiveSmartOtpToHomeScreen(navController)
                    }
                )
            }

            if (commonErrorMessage.value.isNotEmpty()) {
                IBankModalConfirm(
                    title = stringResource(R.string.loi),
                    modalConfirmType = ModalConfirmType.Error,
                    supportingText = commonErrorMessage.value,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(R.string.close),
                        )
                    ),
                    onDismissRequest = {
                        commonErrorMessage.value = ""
                        NavigationHelper.navigateFromActiveSmartOtpToHomeScreen(navController)
                    }
                )
            }

            if (defaultErrorMessage.value.isNotEmpty()) {
                IBankModalConfirm(
                    title = stringResource(R.string.loi),
                    modalConfirmType = ModalConfirmType.Error,
                    supportingText = defaultErrorMessage.value,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(R.string.close),
                        )
                    ),
                    onDismissRequest = {
                        defaultErrorMessage.value = ""
                        onEvent(BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.ClearOTP)
                    }
                )
            }

        },
        handleSideEffect = { sideEffect ->
            when (sideEffect) {

                is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTPError -> {
                    when (sideEffect.errorCode) {
                        SmartOtpErrorType.SMART_OTP_HAS_BEEN_LOCKED -> {
                            lockedErrorMessage.value = sideEffect.errorMessage ?: ""
                        }

                        SmartOtpErrorType.ACTIVATION_DIFFERENT_USER_SMART_OTP -> {
                            activationDifferentUserSmartOtpErrorMessage.value =
                                sideEffect.errorMessage ?: ""
                        }

                        SmartOtpErrorType.COMMON_ERROR -> {
                            commonErrorMessage.value = sideEffect.errorMessage ?: ""
                        }

                        else -> {
                            defaultErrorMessage.value = sideEffect.errorMessage ?: ""
                        }
                    }

                }

                is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.OnDismiss -> {
                    viewModel.clearDataInputPin()
                    NavigationHelper.goBack(navController)
                }

                is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTPSuccess<SmartOtpActiveResDMO> -> {
                    val result = sideEffect.verifyOTPData
                    viewModel.saveUserActiveOtp(
                        userActiveSmartOtpDMO = UserActiveSmartOtpDMO(
                            u = modelConfig.userId ?: "",
                            p = modelConfig.pinCode ?: "",
                            s = result?.secretKey ?: "",
                            st = result?.smToken ?: "",
                            c = modelConfig.cifName ?: "",
                            un = modelConfig.username ?: "",
                            ts = System.currentTimeMillis().toString()
                        )
                    )

                    NavigationHelper.navigateFromActiveSmartOtpToHomeScreen(navController)
                }

                else -> {
                    // no thing
                }
            }

        }
    )
}

@Composable
private fun getListButtonLockedError(isAdminRole: Boolean, navController: NavHostController): List<DialogButtonInfo> {
    return if (!isAdminRole) {
        listOf(
            DialogButtonInfo(
                label = stringResource(R.string.kich_hoat_lai_smart_otp),
                onClick = {
                    CommonNavigationHelper.navigateToReActiveSmartOtpScreen(navController, true)
                },
                isDismissRequest = false
            ),
            DialogButtonInfo(
                label = stringResource(R.string.de_sau),
            )
        )
    } else {
        listOf(
            DialogButtonInfo(
                label = stringResource(R.string.dong),
            )
        )
    }
}
