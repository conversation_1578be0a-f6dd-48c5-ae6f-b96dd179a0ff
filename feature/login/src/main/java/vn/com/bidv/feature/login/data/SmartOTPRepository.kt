package vn.com.bidv.feature.login.data

import vn.com.bidv.feature.common.data.utilities.apis.UtilitiesApi
import vn.com.bidv.feature.common.data.utilities.model.InitChangePinRequest
import vn.com.bidv.feature.common.data.utilities.model.OtpConvertRequest
import vn.com.bidv.feature.common.data.utilities.model.SmartOtpActiveRequest
import vn.com.bidv.feature.common.data.utilities.model.SmartOtpActiveRes
import vn.com.bidv.feature.common.data.utilities.model.SmartOtpApproveActiveRetryRequest
import vn.com.bidv.feature.common.data.utilities.model.SmartOtpDeleteUserRequest
import vn.com.bidv.feature.common.data.utilities.model.SmartOtpDeletedRequest
import vn.com.bidv.feature.common.data.utilities.model.SmartOtpReqActiveRes
import vn.com.bidv.feature.common.data.utilities.model.SmartOtpReqActiveRetryRequest
import vn.com.bidv.feature.common.data.utilities.model.SmartOtpRqActiveRequest
import vn.com.bidv.feature.common.data.utilities.model.TransApproveRequest
import vn.com.bidv.feature.common.data.utilities.model.TransUpdateBasicOtpRes
import vn.com.bidv.feature.login.data.login.apis.InfoApi
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.network.domain.BaseRepository
import javax.inject.Inject

class SmartOTPRepository @Inject constructor(
    private val service: UtilitiesApi,
    private val infoApi: InfoApi

) : BaseRepository() {

    suspend fun requestActiveSmartOtp(
        smartOtpRqActiveRequest: SmartOtpRqActiveRequest
    ): NetworkResult<SmartOtpReqActiveRes> = launch {
        service.requestActive(smartOtpRqActiveRequest = smartOtpRqActiveRequest)
    }

    suspend fun activeSmartOtp(
        smartOtpActiveRequest: SmartOtpActiveRequest
    ): NetworkResult<SmartOtpActiveRes> = launch {
        service.active(smartOtpActiveRequest = smartOtpActiveRequest)
    }

    suspend fun deleteUserSmartOtp(
        userId: String, smToken: String
    ) = launch {
        service.deleteUser(
            SmartOtpDeleteUserRequest(
                userId = userId.toLongOrNull() ?: 0,
                deviceId = "",
                smToken = smToken
            )
        )
    }

    suspend fun requestActiveRetry() = launch {
        service.requestActiveRetry(SmartOtpReqActiveRetryRequest(deviceId = ""))
    }

    suspend fun approvePending(transKey: String, authValue: String) = launch {
        service.approvePending(SmartOtpApproveActiveRetryRequest(transKey, authValue, ""))
    }

    suspend fun deleteAllSmartOtpInDevice() = launch {
        service.delete(SmartOtpDeletedRequest())
    }

    suspend fun changePin(
        deviceId: String
    ): NetworkResult<TransUpdateBasicOtpRes> = launch {
        service.changePin(InitChangePinRequest(deviceId))
    }

    suspend fun verifyBasicOtp(
        transKey: String,
        authValue: String
    ) = launch {
        service.verifyBasicOtp(
            TransApproveRequest(
                transKey = transKey,
                authValue = authValue,
            )
        )
    }

    suspend fun getSmartOtpInfo() = launch {
        infoApi.getSmartOtpInfo()
    }

    suspend fun convertActive(otp: String) = launch {
        service.convertActive(otpConvertRequest = OtpConvertRequest(otp = otp))
    }

}