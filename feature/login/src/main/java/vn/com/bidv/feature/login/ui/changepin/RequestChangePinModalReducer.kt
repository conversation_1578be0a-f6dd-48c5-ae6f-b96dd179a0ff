package vn.com.bidv.feature.login.ui.changepin

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.common.domain.data.TransAuthDMO
import vn.com.bidv.feature.common.domain.data.UserResDMO

class RequestChangePinModalReducer :
    Reducer<RequestChangePinModalReducer.RequestChangePinModalViewState, RequestChangePinModalReducer.RequestChangePinModalViewEvent, RequestChangePinModalReducer.RequestChangePinModalViewEffect> {

    data class RequestChangePinModalViewState(
        val isInit: Boolean = false,
    ) : ViewState

    @Immutable
    sealed class RequestChangePinModalViewEvent : ViewEvent {
        data object InitEvent : RequestChangePinModalViewEvent()

        data class InitSuccessEvent(
            val user: UserResDMO?,
            val transAuthDMO: TransAuthDMO?
        ) : RequestChangePinModalViewEvent()

    }

    @Immutable
    sealed class RequestChangePinModalViewEffect : SideEffect {
        data object InitEffect : RequestChangePinModalViewEffect()

        data class InitSuccessEffect(
            val user: UserResDMO?,
            val transAuthDMO: TransAuthDMO?
        ) : RequestChangePinModalViewEffect(), UIEffect

    }

    override fun reduce(
        previousState: RequestChangePinModalViewState,
        event: RequestChangePinModalViewEvent,
    ): Pair<RequestChangePinModalViewState, RequestChangePinModalViewEffect?> {
        return when (event) {
            is RequestChangePinModalViewEvent.InitEvent -> {
                previousState.copy(
                    isInit = true
                ) to RequestChangePinModalViewEffect.InitEffect
            }

            is RequestChangePinModalViewEvent.InitSuccessEvent -> {
                previousState to RequestChangePinModalViewEffect.InitSuccessEffect(
                    user = event.user,
                    transAuthDMO = event.transAuthDMO
                )
            }

        }
    }
}
