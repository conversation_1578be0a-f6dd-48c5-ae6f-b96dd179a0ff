package vn.com.bidv.feature.login.navigation

import androidx.compose.ui.window.DialogProperties
import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.compose.dialog
import androidx.navigation.navArgument
import androidx.navigation.navigation
import androidx.navigation.toRoute
import com.google.gson.Gson
import kotlinx.serialization.Serializable
import kotlinx.serialization.builtins.ListSerializer
import vn.com.bidv.feature.common.domain.data.ModelRequestSmartOtp
import vn.com.bidv.feature.login.domain.model.AdminTransVerifyDMO
import vn.com.bidv.feature.login.domain.model.ModelCreateOtpResDMO
import vn.com.bidv.feature.login.domain.model.ModelForgotPwOTPConfigDMO
import vn.com.bidv.feature.login.domain.model.ModelLoginDMO
import vn.com.bidv.feature.login.domain.model.ModelSmsOTPConfigDMO
import vn.com.bidv.feature.login.domain.model.SmartOtpApprovePendingDMO
import vn.com.bidv.feature.login.domain.model.SmartOtpReqActiveResDMO
import vn.com.bidv.feature.login.domain.model.TransRequestApprovalDMO
import vn.com.bidv.feature.login.domain.model.TransUpdateBasicOtpDMO
import vn.com.bidv.feature.login.navigation.AuthKeyNavigate.data
import vn.com.bidv.feature.login.navigation.AuthRouts.RE_ACTIVE_SMART_OTP_ROUTE
import vn.com.bidv.feature.login.navigation.AuthRouts.forgotPasswordScreen
import vn.com.bidv.feature.login.navigation.AuthRouts.manageApprovalRequestsRoute
import vn.com.bidv.feature.login.navigation.AuthRouts.manageUserSmartOtpRoute
import vn.com.bidv.feature.login.navigation.AuthRouts.requestChangePinModal
import vn.com.bidv.feature.login.navigation.AuthRouts.requestChangePwModal
import vn.com.bidv.feature.login.navigation.AuthRouts.requestConfirmModal
import vn.com.bidv.feature.login.navigation.AuthRouts.settingLanguageRoute
import vn.com.bidv.feature.login.navigation.AuthRouts.supportRoute
import vn.com.bidv.feature.login.ui.approvalrequestsresult.ApprovalRequestsResultScreen
import vn.com.bidv.feature.login.ui.changepassword.ChangePasswordScreen
import vn.com.bidv.feature.login.ui.changepin.RequestChangePinModal
import vn.com.bidv.feature.login.ui.convertsmartotp.ConvertSmartOtpScreen
import vn.com.bidv.feature.login.ui.forgotpassword.ForgotPasswordScreen
import vn.com.bidv.feature.login.ui.loginscreen.LoginScreen
import vn.com.bidv.feature.login.ui.manageapprovalrequest.ManageApprovalRequestsScreen
import vn.com.bidv.feature.login.ui.manageusersmartotp.ManageUserSmartOtpScreen
import vn.com.bidv.feature.login.ui.pushreactivesmartotpsuccess.PushReActiveSmartOtpSuccessScreen
import vn.com.bidv.feature.login.ui.reactivesmartotp.ReActiveSmartOtpScreen
import vn.com.bidv.feature.login.ui.requestConfirmModal.RequestEditQuestionsModalScreen
import vn.com.bidv.feature.login.ui.requestpositivechangepw.RequestChangePwScreen
import vn.com.bidv.feature.login.ui.securityrequirementscreen.SecurityRequirementsScreen
import vn.com.bidv.feature.login.ui.settinglanguage.SettingLanguageScreen
import vn.com.bidv.feature.login.ui.setupsecurityinfo.requestConfirmModal.model.ModelRequestConfirm
import vn.com.bidv.feature.login.ui.setupsecurityquestionsscreen.SetupSecurityQuestionScreen
import vn.com.bidv.feature.login.ui.setupsmartotp.SetupSmartOtpScreen
import vn.com.bidv.feature.login.ui.setupsmartotp.changepinsuccess.ChangePinSuccessScreen
import vn.com.bidv.feature.login.ui.smsOTP.activeSmartOTP.ActiveSmartOTPModal
import vn.com.bidv.feature.login.ui.smsOTP.changepassword.PWIBankModalOTPScreen
import vn.com.bidv.feature.login.ui.smsOTP.changepin.ChangePinModalOTPScreen
import vn.com.bidv.feature.login.ui.smsOTP.forgotPwOtp.ForgotPwModalOTPScreen
import vn.com.bidv.feature.login.ui.smsOTP.newdevice.NewDeviceSmsOTPScreen
import vn.com.bidv.feature.login.ui.smsOTP.positivechangepw.PositiveChangePwModalOTP
import vn.com.bidv.feature.login.ui.smsOTP.reactivesmartotp.ReActiveSmartOtpModal
import vn.com.bidv.feature.login.ui.smsOTP.userinfo.UserInfoModalOTP
import vn.com.bidv.feature.login.ui.smsOTP.verifyotpbiometric.VerifyOtpBiometricModal
import vn.com.bidv.feature.login.ui.support.SupportScreen
import vn.com.bidv.feature.login.ui.turnonbiometric.TurnOnBiometricScreen
import vn.com.bidv.feature.login.ui.userInfoScreenLoading.UserInfoScreenLoadingScreen
import vn.com.bidv.feature.login.ui.userinfo.UserInfoScreen
import vn.com.bidv.feature.login.ui.verifyquestion.VerifySecureQuestionsScreen
import vn.com.bidv.feature.login.ui.verifyquestion.model.VerifyQuestionModel
import vn.com.bidv.sdkbase.navigation.FeatureGraphBuilder
import vn.com.bidv.sdkbase.navigation.IBankMainRouting
import vn.com.bidv.sdkbase.navigation.IBankMainRouting.CommonRoute.Companion.ARG_TRANS_AUTH
import vn.com.bidv.sdkbase.navigation.LOGIN_SCREEN
import vn.com.bidv.sdkbase.utils.CustomSafeArgs
import javax.inject.Inject
import kotlin.reflect.typeOf

internal object AuthRouts {
    const val loginRoute = "LoginScreen"
    const val changePasswordRoute = "ChangePasswordRoute"
    const val setupSecurityQuestionRoute = "SetupSecurityQuestionScreen"
    const val setupSmartOtpRoute = "setupSmartOtpScreen"
    const val convertSmartOtpRoute = "convertSmartOtpRoute"
    const val activeSmartOTPModal = "activeSmartOTPModal"
    const val requestConfirmModal = "requestConfirmModal"
    const val requestChangePwModal = "requestChangePwModal"
    const val RE_ACTIVE_SMART_OTP_ROUTE = "ReActiveSmartOtpRoute"
    const val manageUserSmartOtpRoute = "manageUserSmartOtpRoute"
    const val manageApprovalRequestsRoute = "manageApprovalRequestsRoute"
    const val modalPositiveChangePwSms = "modalPositiveChangePwSms"
    const val forgotPasswordScreen = "forgotPasswordScreen"
    const val forgotPasswordVerifyModal = "forgotPasswordVerifyModal"
    const val verifyOtpBiometricModalRoute = "verifyOtpBiometricModalRoute"
    const val verifyQuestionScreen = "verifyQuestionScreen"
    const val turnOnBiometricRoute = "turnOnBiometricRoute"
    const val requestChangePinModal = "requestChangePinModal"
    const val changePinSmsOtpModal = "changePinSmsOtpModal"
    const val changePinSuccessRoute = "changePinSuccessRoute"
    const val settingLanguageRoute = "settingLanguageRoute"
    const val supportRoute = "supportRoute"
    const val userInfoRoute = "userInfoRoute"
    const val userInfoModalOTPRoute = "userInfoModalOTPRoute"
    const val USER_INFO_SCREEN_ROUTE = "UserInfoScreenRoute"

    @Serializable
    data class ApprovalRequestsResultRoute(
        val data: String
    )

    @Serializable
    data class ReActiveSmartOtpModalRoute(val model: TransUpdateBasicOtpDMO)

    @Serializable
    data class PushReActiveSmartOtpSuccessScreenRoute(val model: SmartOtpApprovePendingDMO)

    @Serializable
    data class VerifySmsOtpBiometricModalRoute(val model: ModelCreateOtpResDMO)

    val allRoutes = listOf(
        LOGIN_SCREEN,
        changePasswordRoute,
        setupSecurityQuestionRoute,
        setupSmartOtpRoute,
        activeSmartOTPModal,
        requestConfirmModal,
        requestChangePwModal,
        modalPositiveChangePwSms,
        forgotPasswordScreen,
        forgotPasswordVerifyModal,
        verifyQuestionScreen,
        requestChangePinModal,
        changePinSmsOtpModal,
        changePinSuccessRoute,
        userInfoRoute,
        convertSmartOtpRoute
    )
}

internal object AuthKeyNavigate {
    const val data = "data"
}

fun isInLoginFlow(navController: NavController): Boolean {
    val currentRoute = navController.currentBackStackEntry?.destination?.route
    return currentRoute in AuthRouts.allRoutes
}

@Serializable
sealed class SetupSecurityQuestionRoute(val route: String) {
    @Serializable
    data class SecurityQuestionRoute(val modelRequestConfirm: ModelRequestConfirm?) :
        SetupSecurityQuestionRoute(AuthRouts.setupSecurityQuestionRoute)
}

@Serializable
sealed class ChangePwRoute(val route: String) {
    @Serializable
    data class ChangePwScreenRoute(val modelLoginDMO: ModelLoginDMO?) :
        ChangePwRoute(AuthRouts.changePasswordRoute)

    @Serializable
    data class PositiveChangePwSmsModalRoute(val modelSmsOTPConfigDMO: ModelSmsOTPConfigDMO) :
        ChangePwRoute(AuthRouts.modalPositiveChangePwSms)
}

@Serializable
data class RequestSecuritySetupRoute(val modelLoginDMO: ModelLoginDMO?)

@Serializable
sealed class UserInfoRoute(val route: String) {
    @Serializable
    data object UserInfoScreenRoute :
        UserInfoRoute(AuthRouts.userInfoRoute)

    @Serializable
    data class UserInfoModalRoute(val modelSmsOTPConfigDMO: ModelSmsOTPConfigDMO) :
        UserInfoRoute(AuthRouts.userInfoModalOTPRoute)
}

@Serializable
sealed class SetupSmartOtpRoute(val route: String) {

    @Serializable
    data class ActiveSmsModalRoute(val smartOtpReqActiveResDMO: SmartOtpReqActiveResDMO) :
        SetupSmartOtpRoute(AuthRouts.activeSmartOTPModal)

    @Serializable
    data class ChangePinModalRoute(val transUpdateBasicOtpDMO: TransUpdateBasicOtpDMO) :
        SetupSmartOtpRoute(AuthRouts.changePinSmsOtpModal)

    @Serializable
    data object ChangePinSuccessRoute :
        SetupSmartOtpRoute(AuthRouts.changePinSuccessRoute)

    @Serializable
    data object ConvertSmartOtpRoute :
        SetupSmartOtpRoute(AuthRouts.convertSmartOtpRoute)
}

@Serializable
sealed class ForgotPwRoute(val route: String) {
    @Serializable
    data class VerifyQuestionRoute(val verifyQuestionModel: VerifyQuestionModel) :
        ForgotPwRoute(AuthRouts.verifyQuestionScreen)

    @Serializable
    data class ForgotPwSmsModalRoute(val modelForgotPwOTPConfigDMO: ModelForgotPwOTPConfigDMO) :
        ForgotPwRoute(AuthRouts.forgotPasswordVerifyModal)
}

@Serializable
data class NewDeviceSmsOTPRoute(val modelSmsOTPConfigDMO:  ModelSmsOTPConfigDMO)

@Serializable
data class IBankModalOTPScreenRoute(val modelSmsOTPConfigDMO: ModelSmsOTPConfigDMO)

class LoginNavigation @Inject constructor() : FeatureGraphBuilder {

    override fun buildGraph(
        navGraphBuilder: NavGraphBuilder,
        navController: NavHostController,
        registeredRoutes: (List<String>) -> Unit
    ) {
        navGraphBuilder.navigation(
            startDestination = LOGIN_SCREEN,
            route = IBankMainRouting.AuthRoutes.AuthMainRoute.route
        ) {
            composable(
                route = LOGIN_SCREEN,
            ) {
                LoginScreen(navController)
            }
            composable<RequestSecuritySetupRoute>(
                typeMap = mapOf(
                    typeOf<ModelLoginDMO?>() to CustomSafeArgs.SerializableNavType(
                        ModelLoginDMO.serializer()
                    )
                )
            ) {
                val modelLoginDMO = it.toRoute<RequestSecuritySetupRoute>().modelLoginDMO
                SecurityRequirementsScreen(
                    navController, modelLoginDMO
                )
            }

            composable<SetupSecurityQuestionRoute.SecurityQuestionRoute>(
                typeMap = mapOf(
                    typeOf<ModelRequestConfirm?>() to CustomSafeArgs.SerializableNavType(
                        ModelRequestConfirm.serializer()
                    )
                )
            ) {
                val modelRequestConfirm =
                    it.toRoute<SetupSecurityQuestionRoute.SecurityQuestionRoute>().modelRequestConfirm
                SetupSecurityQuestionScreen(navController, modelRequestConfirm)
            }

            composable<ChangePwRoute.ChangePwScreenRoute>(
                typeMap = mapOf(
                    typeOf<ModelLoginDMO?>() to CustomSafeArgs.SerializableNavType(
                        ModelLoginDMO.serializer()
                    )
                )
            ) {
                val modelLoginDMO =
                    it.toRoute<ChangePwRoute.ChangePwScreenRoute>().modelLoginDMO
                ChangePasswordScreen(navController, modelLoginDMO)
            }

            dialog<IBankModalOTPScreenRoute>(
                typeMap = mapOf(
                    typeOf<ModelSmsOTPConfigDMO>() to CustomSafeArgs.SerializableNavType(
                        ModelSmsOTPConfigDMO.serializer()
                    )
                ),
                 dialogProperties = DialogProperties(
                    dismissOnBackPress = false,
                    dismissOnClickOutside = false,
                    usePlatformDefaultWidth = false
                )
            ) {
                val data = it.toRoute<NewDeviceSmsOTPRoute>().modelSmsOTPConfigDMO
                PWIBankModalOTPScreen(navController, data)
            }

            dialog<ForgotPwRoute.ForgotPwSmsModalRoute>(
                typeMap = mapOf(
                    typeOf<ModelForgotPwOTPConfigDMO>() to CustomSafeArgs.SerializableNavType(
                        ModelForgotPwOTPConfigDMO.serializer()
                    )
                ),
                dialogProperties = DialogProperties(
                    dismissOnBackPress = false,
                    dismissOnClickOutside = false,
                    usePlatformDefaultWidth = false
                )
            ) {
                ForgotPwModalOTPScreen(
                    navController,
                    it.toRoute<ForgotPwRoute.ForgotPwSmsModalRoute>().modelForgotPwOTPConfigDMO
                )
            }

            dialog<NewDeviceSmsOTPRoute>(
                typeMap = mapOf(
                    typeOf<ModelSmsOTPConfigDMO>() to CustomSafeArgs.SerializableNavType(
                        ModelSmsOTPConfigDMO.serializer()
                    )
                ),
                 dialogProperties = DialogProperties(
                    dismissOnBackPress = false,
                    dismissOnClickOutside = false,
                    usePlatformDefaultWidth = false
                )
            ) {
                val data = it.toRoute<NewDeviceSmsOTPRoute>().modelSmsOTPConfigDMO
                NewDeviceSmsOTPScreen(navController, data)
            }

            dialog<SetupSmartOtpRoute.ActiveSmsModalRoute>(
                typeMap = mapOf(
                    typeOf<SmartOtpReqActiveResDMO>() to CustomSafeArgs.SerializableNavType(
                        SmartOtpReqActiveResDMO.serializer()
                    )
                ),
                dialogProperties = DialogProperties(
                    dismissOnBackPress = false,
                    dismissOnClickOutside = false,
                    usePlatformDefaultWidth = false
                )
            ) {

                ActiveSmartOTPModal(
                    navController,
                    it.toRoute<SetupSmartOtpRoute.ActiveSmsModalRoute>().smartOtpReqActiveResDMO
                )
            }

            composable(route = manageUserSmartOtpRoute) {
                ManageUserSmartOtpScreen(navController)
            }

            composable(route = forgotPasswordScreen) {
                ForgotPasswordScreen(navController)
            }

            composable<ForgotPwRoute.VerifyQuestionRoute>(
                typeMap = mapOf(
                    typeOf<VerifyQuestionModel>() to CustomSafeArgs.SerializableNavType(
                        VerifyQuestionModel.serializer()
                    )
                )
            ) {
                VerifySecureQuestionsScreen(
                    navController,
                    it.toRoute<ForgotPwRoute.VerifyQuestionRoute>().verifyQuestionModel
                )
            }

            composable(route = supportRoute) {
                SupportScreen(navController)
            }

            dialog(
                dialogProperties = DialogProperties(
                    dismissOnClickOutside = false,
                    usePlatformDefaultWidth = false
                ),
                route = AuthRouts.USER_INFO_SCREEN_ROUTE,
            ) {
                UserInfoScreenLoadingScreen(navController)
            }
        }

        navGraphBuilder.navigation(
            route = IBankMainRouting.AuthRoutes.ReActiveSmartOtpRoute.routeWithArgument,
            startDestination = RE_ACTIVE_SMART_OTP_ROUTE
        ) {
            composable(
                route = RE_ACTIVE_SMART_OTP_ROUTE,
                arguments = listOf(navArgument(ARG_TRANS_AUTH) {
                    type = NavType.StringType
                    nullable = true
                })
            ) {
                val isPopToHome = it.arguments?.getString(ARG_TRANS_AUTH)?.toBoolean() ?: false
                ReActiveSmartOtpScreen(navController, isPopToHome)
            }

            dialog<AuthRouts.ReActiveSmartOtpModalRoute>(
                typeMap = mapOf(
                    typeOf<TransUpdateBasicOtpDMO>() to CustomSafeArgs.SerializableNavType(
                        TransUpdateBasicOtpDMO.serializer()
                    )
                ),
                dialogProperties = DialogProperties(
                    dismissOnBackPress = false,
                    dismissOnClickOutside = false,
                    usePlatformDefaultWidth = false
                )
            ) {
                ReActiveSmartOtpModal(
                    navController,
                    it.toRoute<AuthRouts.ReActiveSmartOtpModalRoute>().model
                )
            }

            composable<AuthRouts.PushReActiveSmartOtpSuccessScreenRoute>(
                typeMap = mapOf(
                    typeOf<SmartOtpApprovePendingDMO>() to CustomSafeArgs.SerializableNavType(
                        SmartOtpApprovePendingDMO.serializer()
                    )
                )
            ) {
                val model = it.toRoute<AuthRouts.PushReActiveSmartOtpSuccessScreenRoute>().model
                PushReActiveSmartOtpSuccessScreen(navController, model)
            }
        }

        navGraphBuilder.navigation(
            route = IBankMainRouting.AuthRoutes.ManageQuestionsRoute.route,
            startDestination = requestConfirmModal,
        ) {
            dialog(
                route = requestConfirmModal,
                dialogProperties = DialogProperties(
                    dismissOnBackPress = false,
                    dismissOnClickOutside = false,
                    usePlatformDefaultWidth = false
                )
            ) {
                RequestEditQuestionsModalScreen(
                    navController
                )
            }
        }

        navGraphBuilder.navigation(
            route = IBankMainRouting.AuthRoutes.PositiveChangePwRoute.route,
            startDestination = requestChangePwModal,
        ) {
            dialog(
                route = requestChangePwModal,
                dialogProperties = DialogProperties(
                    dismissOnBackPress = false,
                    dismissOnClickOutside = false,
                    usePlatformDefaultWidth = false
                )
            ) {
                RequestChangePwScreen(
                    navController
                )
            }

            composable<ChangePwRoute.ChangePwScreenRoute>(
                typeMap = mapOf(
                    typeOf<ModelLoginDMO?>() to CustomSafeArgs.SerializableNavType(
                        ModelLoginDMO.serializer()
                    )
                )
            ) {
                val modelLoginDMO =
                    it.toRoute<ChangePwRoute.ChangePwScreenRoute>().modelLoginDMO
                ChangePasswordScreen(navController, modelLoginDMO)
            }

            dialog<ChangePwRoute.PositiveChangePwSmsModalRoute>(
                typeMap = mapOf(
                    typeOf<ModelSmsOTPConfigDMO>() to CustomSafeArgs.SerializableNavType(
                        ModelSmsOTPConfigDMO.serializer()
                    )
                ),
                dialogProperties = DialogProperties(
                    dismissOnBackPress = false,
                    dismissOnClickOutside = false,
                    usePlatformDefaultWidth = false
                )
            ) {
                PositiveChangePwModalOTP(
                    navController,
                    it.toRoute<ChangePwRoute.PositiveChangePwSmsModalRoute>().modelSmsOTPConfigDMO
                )
            }

        }

        navGraphBuilder.navigation(
            route = IBankMainRouting.AuthRoutes.ChangePinRoute.route,
            startDestination = requestChangePinModal,
        ) {
            dialog(
                route = requestChangePinModal,
                dialogProperties = DialogProperties(
                    dismissOnBackPress = false,
                    dismissOnClickOutside = false,
                    usePlatformDefaultWidth = false
                )
            ) {
                RequestChangePinModal(
                    navController
                )
            }

            dialog<SetupSmartOtpRoute.ChangePinModalRoute>(
                typeMap = mapOf(
                    typeOf<TransUpdateBasicOtpDMO>() to CustomSafeArgs.SerializableNavType(
                        TransUpdateBasicOtpDMO.serializer()
                    )
                ),
                dialogProperties = DialogProperties(
                    dismissOnBackPress = false,
                    dismissOnClickOutside = false,
                    usePlatformDefaultWidth = false
                )
            ) {
                ChangePinModalOTPScreen(
                    navController,
                    it.toRoute<SetupSmartOtpRoute.ChangePinModalRoute>().transUpdateBasicOtpDMO
                )
            }

            composable<SetupSmartOtpRoute.ChangePinSuccessRoute> {
                ChangePinSuccessScreen(navController)
            }

        }

        navGraphBuilder.navigation(
            route = IBankMainRouting.AuthRoutes.ManageApprovalRequestsRoute.route,
            startDestination = manageApprovalRequestsRoute
        ) {
            composable(
                route = manageApprovalRequestsRoute
            ) {
                ManageApprovalRequestsScreen(navController)
            }
            composable<AuthRouts.ApprovalRequestsResultRoute>(
                typeMap = mapOf(
                    typeOf<AdminTransVerifyDMO>() to CustomSafeArgs.SerializableNavType(
                        AdminTransVerifyDMO.serializer()
                    ),
                    typeOf<List<TransRequestApprovalDMO>>() to CustomSafeArgs.ListSerializableNavType(
                        ListSerializer(TransRequestApprovalDMO.serializer())
                    )
                )
            ) {
                val argument = it.toRoute<AuthRouts.ApprovalRequestsResultRoute>()
                ApprovalRequestsResultScreen(
                    navController,
                    argument.data
                )
            }
        }

        navGraphBuilder.navigation(
            route = IBankMainRouting.AuthRoutes.TurnOnBiometricRoute.routeWithArgument,
            startDestination = AuthRouts.turnOnBiometricRoute
        ) {
            dialog(
                route = AuthRouts.turnOnBiometricRoute,
                dialogProperties = DialogProperties(
                    dismissOnBackPress = true,
                    dismissOnClickOutside = false,
                    usePlatformDefaultWidth = false
                ),
                arguments = listOf(navArgument(IBankMainRouting.AuthRoutes.Companion.ARG_TRANS_AUTH) {
                    type = NavType.StringType
                    nullable = true
                }
                )
            ) {
                val data =
                    it.arguments?.getString(IBankMainRouting.AuthRoutes.Companion.ARG_TRANS_AUTH)
                        .toBoolean()
                TurnOnBiometricScreen(navController = navController, isTurnOnBiometric = data)
            }

            dialog<AuthRouts.VerifySmsOtpBiometricModalRoute>(
                dialogProperties = DialogProperties(
                    dismissOnBackPress = false,
                    dismissOnClickOutside = false,
                    usePlatformDefaultWidth = false
                ),
                typeMap = mapOf(
                    typeOf<ModelCreateOtpResDMO>() to CustomSafeArgs.SerializableNavType(
                        ModelCreateOtpResDMO.serializer()
                    )
                )
            ) {
                VerifyOtpBiometricModal(
                    navController,
                    it.toRoute<AuthRouts.VerifySmsOtpBiometricModalRoute>().model
                )
            }
        }

        navGraphBuilder.navigation(
            route = IBankMainRouting.AuthRoutes.SettingLanguageRoute.route,
            startDestination = settingLanguageRoute
        ) {
            composable(
                route = settingLanguageRoute
            ) {
                SettingLanguageScreen(navController)
            }
        }

        navGraphBuilder.navigation(
            route = IBankMainRouting.AuthRoutes.UserInfoRoute.route,
            startDestination = UserInfoRoute.UserInfoScreenRoute.route
        ) {
            composable(
                route = UserInfoRoute.UserInfoScreenRoute.route
            ) {
                UserInfoScreen(navController)
            }

            dialog<UserInfoRoute.UserInfoModalRoute>(
                typeMap = mapOf(
                    typeOf<ModelSmsOTPConfigDMO>() to CustomSafeArgs.SerializableNavType(
                        ModelSmsOTPConfigDMO.serializer()
                    )
                ),
                dialogProperties = DialogProperties(
                    dismissOnBackPress = false,
                    dismissOnClickOutside = false,
                    usePlatformDefaultWidth = false
                )
            ) {
                UserInfoModalOTP(
                    navController,
                    it.toRoute<UserInfoRoute.UserInfoModalRoute>().modelSmsOTPConfigDMO
                )
            }
        }

        navGraphBuilder.navigation(
            route = IBankMainRouting.AuthRoutes.ActiveSmartOtpRoute.routeWithArgument,
            startDestination = AuthRouts.setupSmartOtpRoute
        ) {
            composable(
                route = AuthRouts.setupSmartOtpRoute,
                arguments = listOf(navArgument(ARG_TRANS_AUTH) {
                    type = NavType.StringType
                    nullable = true
                }
                )
            ) { backStackEntry ->
                val model =
                    backStackEntry.arguments?.getString(IBankMainRouting.AuthRoutes.Companion.ARG_TRANS_AUTH)
                        ?.let { Gson().fromJson(it, ModelRequestSmartOtp::class.java) }
                SetupSmartOtpScreen(
                    navController,
                    model ?: ModelRequestSmartOtp(),
                )
            }
        }

        navGraphBuilder.navigation(
            route = IBankMainRouting.AuthRoutes.ConvertActiveSmartOtpRoute.route,
            startDestination = SetupSmartOtpRoute.ConvertSmartOtpRoute.route
        ) {
            composable(
                route = SetupSmartOtpRoute.ConvertSmartOtpRoute.route,
            ) {
                ConvertSmartOtpScreen(navController)
            }
        }

        registeredRoutes(
            listOf(
                IBankMainRouting.AuthRoutes.AuthMainRoute.route,
                IBankMainRouting.AuthRoutes.ManageApprovalRequestsRoute.route
            )
        )

    }
}