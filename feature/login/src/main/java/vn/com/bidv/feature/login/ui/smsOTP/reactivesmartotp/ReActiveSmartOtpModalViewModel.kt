package vn.com.bidv.feature.login.ui.smsOTP.reactivesmartotp

import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.feature.login.constants.SmartOTPErrorCode
import vn.com.bidv.feature.login.domain.ActiveSmartOTPUseCase
import vn.com.bidv.feature.login.domain.ReActiveSmartOtpUseCase
import vn.com.bidv.feature.login.domain.model.SmartOtpApprovePendingDMO
import vn.com.bidv.feature.login.domain.model.TransUpdateBasicOtpDMO
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPReducer
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPViewModel
import javax.inject.Inject

@HiltViewModel
class ReActiveSmartOtpModalViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val activeSmartOtpUseCase: ActiveSmartOTPUseCase,
) :
    BaseIBankModalOTPViewModel<TransUpdateBasicOtpDMO, TransUpdateBasicOtpDMO, SmartOtpApprovePendingDMO>(
        initialState = BaseIBankModalOTPReducer.BaseIBankModalOTPViewState(),
        reducer = BaseIBankModalOTPReducer()
    ) {
    override fun handleEffect(
        sideEffect: BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect<TransUpdateBasicOtpDMO, TransUpdateBasicOtpDMO, SmartOtpApprovePendingDMO>,
        onResult: (BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent<TransUpdateBasicOtpDMO, TransUpdateBasicOtpDMO, SmartOtpApprovePendingDMO>) -> Unit
    ) {
        when (sideEffect) {
            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.RetrySendOTP -> {
                callDomain(
                    onSuccess = {
                        onResult(
                            BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnRetrySendOTPSuccess(
                                it.data
                            )
                        )
                    }
                ) {
                    activeSmartOtpUseCase.requestActiveRetry()
                }
            }

            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTP -> {
                callDomain(
                    onSuccess = {
                        onResult(BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnVerifyOTPSuccess(it.data))
                    },
                    isListenAllError = true,
                    onFail = {
                        onResult(
                            BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnVerifyOTPError(
                                it?.errorCode,
                                it?.errorMessage
                            )
                        )
                    }
                ) {
                    activeSmartOtpUseCase.approvePending(
                        transKey = sideEffect.txnId,
                        authValue = sideEffect.otpNum
                    )
                }
            }
        }
    }
}