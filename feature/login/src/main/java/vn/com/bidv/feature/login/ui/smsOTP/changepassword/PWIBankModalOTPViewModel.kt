package vn.com.bidv.feature.login.ui.smsOTP.changepassword

import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.feature.login.domain.BiometricUseCase
import vn.com.bidv.feature.login.domain.FcmUseCase
import vn.com.bidv.feature.login.domain.LoginUseCase
import vn.com.bidv.feature.login.domain.model.ModelCreateOtpResDMO
import vn.com.bidv.feature.login.domain.model.ModelLoginDMO
import vn.com.bidv.feature.login.domain.model.ModelSmsOTPConfigDMO
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPReducer
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPViewModel
import javax.inject.Inject

@HiltViewModel
class PWIBankModalOTPViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val loginUseCase: LoginUseCase,
    private val fcmUseCase: FcmUseCase,
    private val biometricUseCase: BiometricUseCase
) : BaseIBankModalOTPViewModel<ModelSmsOTPConfigDMO, ModelCreateOtpResDMO, ModelLoginDMO>(
    initialState = BaseIBankModalOTPReducer.BaseIBankModalOTPViewState(),
    reducer = BaseIBankModalOTPReducer()
) {

    override fun handleEffect(
        sideEffect: BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect<ModelSmsOTPConfigDMO, ModelCreateOtpResDMO, ModelLoginDMO>,
        onResult: (BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent<ModelSmsOTPConfigDMO, ModelCreateOtpResDMO, ModelLoginDMO>) -> Unit
    ) {
        when (sideEffect) {
            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.RetrySendOTP -> {
                callDomain(
                    onSuccess = {
                        onResult(
                            BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnRetrySendOTPSuccess(
                                it.data
                            )
                        )
                    }
                ) {
                    loginUseCase.reSendSmsOtp(
                        method = sideEffect.method,
                        userName = sideEffect.userName,
                        txnId = sideEffect.txnId
                    )
                }
            }

            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTP -> {
                callDomain(
                    isListenAllError = true,
                    onFail = {
                        onResult(
                            BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnVerifyOTPError(
                                it?.errorCode,
                                it?.errorMessage
                            )
                        )
                    },
                    onSuccess = {
                        biometricUseCase.removeBiometricData()
                        onResult(
                            BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnVerifyOTPSuccess(
                                it.data
                            )
                        )
                    },
                ) {
                    loginUseCase.verifyOtp(
                        method = sideEffect.method,
                        userName = sideEffect.userName,
                        txnId = sideEffect.txnId,
                        otpNum = sideEffect.otpNum,
                        fcmId = fcmUseCase.getFcmId()
                    )
                }
            }

            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTPError,
            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.RetryOTPSuccess,
            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTPSuccess -> TODO()
        }
    }

}
