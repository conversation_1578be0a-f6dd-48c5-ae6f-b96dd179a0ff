package vn.com.bidv.feature.login.domain

import com.google.gson.Gson
import vn.com.bidv.feature.common.data.VerifyOtpResult
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.constants.LoginErrorCode
import vn.com.bidv.feature.login.data.VerifySecureOtpRepository
import vn.com.bidv.feature.login.domain.model.ModelCreateOtpResDMO
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.sdkbase.data.LocalRepository
import vn.com.bidv.sdkbase.data.ShareDataDTO
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class VerifySecureOtpUseCase @Inject constructor(
    private val verifySecureOtpRepository: VerifySecureOtpRepository,
    private val localRepository: LocalRepository
) {

    suspend fun otpSecuredVerify(
        transId: String,
        otpNum: String,
        method: String
    ): DomainResult<Boolean> {
        val result = verifySecureOtpRepository.otpSecuredVerify(
            transId = transId,
            otpNum = otpNum,
            method = method
        )
        if (result is NetworkResult.Error) {
            return DomainResult.Error(
                errorCode = handleError(result.errorCode),
                errorMessage = result.errorMessage
            )
        }
        return DomainResult.Success(true)
    }

    suspend fun otpSecuredResend(
        transId: String,
        method: String
    ): DomainResult<ModelCreateOtpResDMO> {
        val result =
            verifySecureOtpRepository.otpSecuredResend(transId = transId, method = method)

        if (result is NetworkResult.Error) {
            return DomainResult.Error(
                errorCode = handleError(result.errorCode),
                errorMessage = result.errorMessage
            )
        }

        val model = result.convert(ModelCreateOtpResDMO::class.java)
        return model
    }

    suspend fun shareVerifyBiometricData() {
        localRepository.shareDataTo(
            Constants.VerifyBiometricOtp.VERIFY_BIOMETRIC_SMS_OTP, ShareDataDTO(
                Constants.VerifyBiometricOtp.VERIFY_BIOMETRIC_SMS_OTP,
                Gson().toJson(VerifyOtpResult(data = true.toString()))
            )
        )
    }

    private fun handleError(errorCode: String): String {
        return when (errorCode) {
            LoginErrorCode.IM9004 -> VerifyPwOtpErrorType.SMS_OTP_BLOCK
            LoginErrorCode.IM9006 -> VerifyPwOtpErrorType.SMART_OTP_HAS_EXPIRED

            else -> VerifyPwOtpErrorType.DEFAULT_ERROR
        }
    }

}

object VerifyPwOtpErrorType {
    const val SMART_OTP_HAS_EXPIRED = "SMART_OTP_HAS_EXPIRED"
    const val SMS_OTP_BLOCK = "SMS_OTP_BLOCK"
    const val DEFAULT_ERROR = "DEFAULT_ERROR"

}