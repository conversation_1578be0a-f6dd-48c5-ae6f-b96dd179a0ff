package vn.com.bidv.feature.login.ui.smsOTP.userinfo

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.domain.UserInfoSettingUseCase
import vn.com.bidv.feature.login.domain.model.CompositeOtpResDMO
import vn.com.bidv.feature.login.domain.model.ModelSmsOTPConfigDMO
import vn.com.bidv.feature.login.domain.model.UserPersonalInfoDMO
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPReducer
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPViewModel
import javax.inject.Inject

@HiltViewModel
class UserInfoModalOTPViewModel @Inject constructor(
    private val userInfoSettingUseCase: UserInfoSettingUseCase,
) : BaseIBankModalOTPViewModel<ModelSmsOTPConfigDMO, CompositeOtpResDMO, UserPersonalInfoDMO>(
    initialState = BaseIBankModalOTPReducer.BaseIBankModalOTPViewState(),
    reducer = BaseIBankModalOTPReducer()
) {

    override fun handleEffect(
        sideEffect: BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect<ModelSmsOTPConfigDMO, CompositeOtpResDMO, UserPersonalInfoDMO>,
        onResult: (BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent<ModelSmsOTPConfigDMO, CompositeOtpResDMO, UserPersonalInfoDMO>) -> Unit
    ) {
        when (sideEffect) {
            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.RetrySendOTP -> {
                callDomain(
                    onSuccess = { result ->
                        onResult(
                            BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnRetrySendOTPSuccess(
                                result.data
                            )
                        )
                    },
                ) {
                    userInfoSettingUseCase.getUserInfoSettingSecure()
                }

            }

            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTP -> {
                callDomain(
                    isListenAllError = true,
                    onSuccess = { result ->
                        shareUserInfoSuccessAction {
                            userInfoSettingUseCase.shareUserInfoSuccessAction(
                                Constants.USER_INFO_SUCCESS,
                                result.data
                            )
                        }
                        onResult(
                            BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnVerifyOTPSuccess(
                                result.data
                            )
                        )
                    },
                    onFail = { error ->
                        onResult(
                            BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnVerifyOTPError(
                                error?.errorCode,
                                error?.errorMessage
                            )
                        )
                    }
                ) {
                    userInfoSettingUseCase.verifySmartOtpUserInfo(
                        transId = sideEffect.txnId,
                        otp = sideEffect.otpNum,
                    )
                }
            }

            else -> {
                // Do nothing
            }
        }
    }

    private fun shareUserInfoSuccessAction(onShare: suspend () -> Unit) {
        viewModelScope.launch {
            onShare()
        }
    }
}
