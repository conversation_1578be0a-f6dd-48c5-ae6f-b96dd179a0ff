package vn.com.bidv.feature.login.ui.loginscreen

import androidx.biometric.BiometricPrompt
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.google.gson.Gson
import vn.com.bidv.common.extenstion.isNotNullOrEmpty
import vn.com.bidv.common.utils.CollectSideEffect
import vn.com.bidv.common.utils.unpack
import vn.com.bidv.designsystem.component.datadisplay.avatar.AvatarInfo
import vn.com.bidv.designsystem.component.datadisplay.avatar.IBankAvatar
import vn.com.bidv.designsystem.component.dataentry.IBFrameState
import vn.com.bidv.designsystem.component.dataentry.IBankInputFieldBase
import vn.com.bidv.designsystem.component.dataentry.IBankInputPassword
import vn.com.bidv.designsystem.component.dataentry.RemoveSpaceFilter
import vn.com.bidv.designsystem.component.dataentry.RemoveSpecialCharacterPasswordFilter
import vn.com.bidv.designsystem.component.dataentry.SpecialCharacterFilter
import vn.com.bidv.designsystem.component.dataentry.UpperCaseFilter
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.component.navigation.button.IBankNormalButton
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonSize
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonType
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankTopAppBar
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.IBTypography.NormalTypography.bodyBody_m
import vn.com.bidv.designsystem.theme.IBTypography.NormalTypography.labelLabel_l
import vn.com.bidv.designsystem.theme.IBTypography.NormalTypography.labelLabel_m
import vn.com.bidv.designsystem.theme.IBTypography.NormalTypography.titleTitle_l
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.common.domain.notificationcommon.TabNotiType
import vn.com.bidv.feature.login.R
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.constants.LoginErrorCode
import vn.com.bidv.feature.login.constants.LoginErrorCode.IM1204
import vn.com.bidv.feature.login.constants.LoginErrorCode.IM1702
import vn.com.bidv.feature.login.constants.LoginErrorCode.IM2208
import vn.com.bidv.feature.login.constants.LoginErrorCode.IM2209
import vn.com.bidv.feature.login.domain.model.BiometricData
import vn.com.bidv.feature.login.navigation.LoginNotificationRoute
import vn.com.bidv.feature.login.navigation.NavigationHelper
import vn.com.bidv.feature.login.ui.loginscreen.LoginReducer.LoginViewEvent
import vn.com.bidv.feature.login.ui.loginscreen.LoginReducer.LoginViewState
import vn.com.bidv.feature.login.ui.setupsecurityinfo.requestConfirmModal.model.ModelRequestConfirm
import vn.com.bidv.log.BLogUtil
import vn.com.bidv.sdkbase.data.NotificationData
import vn.com.bidv.sdkbase.navigation.notifyroute.NotificationBuilderUtils
import vn.com.bidv.sdkbase.utils.ItemLanguage
import vn.com.bidv.sdkbase.utils.NotificationUtils
import vn.com.bidv.sdkbase.utils.VietnamAccentRemoverFilter
import vn.com.bidv.sdkbase.utils.VietnameseAccentRemoverFilter
import vn.com.bidv.sdkbase.utils.exts.ContextExt.openAppInGooglePlay
import vn.com.bidv.secure.utils.BiometricUtils
import vn.com.bidv.designsystem.R as RDesignSystem
import vn.com.bidv.feature.common.navigation.NavigationHelper as CommonNavigationHelper
import vn.com.bidv.localization.R as RLocalization

@Composable
fun LoginScreen(navController: NavHostController) {
    val loginViewModel: LoginViewModel = hiltViewModel()
    val (_, _onEvent) = loginViewModel.unpack()
    val isShowErrorPopup = remember { mutableStateOf(false to "") }
    val isShowPWWrongDetailPopup = remember { mutableStateOf(false to "") }
    val isShowExpiredWarningPopup = remember { mutableStateOf(false to "") }
    val isForceUpdateErrorPopup = remember { mutableStateOf(false to "") }
    val fragmentActivity = LocalContext.current as FragmentActivity
    BaseScreen(
        navController = navController,
        viewModel = loginViewModel,
        isLightStatusBar = false,
        renderContent = { uiState, onEvent ->
            if (uiState is LoginViewState.Init) {
                onEvent(LoginViewEvent.InitData)
                onEvent(LoginViewEvent.OnUpdateLoginStatus(false))
                onEvent(LoginViewEvent.OnResetStatusPopupLoaded)
            }
            LoginScreenContent(
                uiState = uiState,
                onEvent = onEvent,
                fragmentActivity = fragmentActivity,
                navController = navController
            )

            HandleNavigateNotification(
                navController = navController,
                loginViewModel = loginViewModel
            )

            if (isForceUpdateErrorPopup.value.first) {
                IBankModalConfirm(
                    modalConfirmType = ModalConfirmType.Info,
                    supportingText = isForceUpdateErrorPopup.value.second,
                    title = stringResource(RLocalization.string.thong_bao),
                    isShowIconClose = false,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(RLocalization.string.cap_nhat),
                            onClick = {
                                fragmentActivity.openAppInGooglePlay()
                            }
                        )
                    ),
                )
            }

            if (isShowErrorPopup.value.first) {
                IBankModalConfirm(
                    modalConfirmType = ModalConfirmType.Error,
                    supportingText = isShowErrorPopup.value.second,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(RLocalization.string.close),
                        )
                    ),
                    onDismissRequest = {
                        isShowErrorPopup.value = false to ""
                    }
                )
            }

            if (isShowPWWrongDetailPopup.value.first) {
                IBankModalConfirm(
                    modalConfirmType = ModalConfirmType.Error,
                    supportingText = isShowPWWrongDetailPopup.value.second,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(RLocalization.string.quen_mat_khau),
                            onClick = {
                                _onEvent(LoginViewEvent.OnResetInputData)
                                NavigationHelper.navigateToForgotPasswordScreen(navController)
                            }
                        )
                    ),
                    onDismissRequest = {
                        isShowPWWrongDetailPopup.value = false to ""
                    }
                )
            }

            if (isShowExpiredWarningPopup.value.first) {
                IBankModalConfirm(
                    modalConfirmType = ModalConfirmType.Warning,
                    supportingText = stringResource(RLocalization.string.da_den_han_thay_doi_mat_khau_theo_quy_dinh_quy_khach_vui_long_doi_mat_khau_de_tiep_tuc_su_dung_dich_vu),
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(RLocalization.string.doi_mat_khau),
                            onClick = {
                                _onEvent(LoginViewEvent.OnResetInputData)
                                NavigationHelper.navigateToChangePassword(
                                    navController,
                                    uiState.modelLoginUI.modelLoginDMO
                                )
                            }
                        )
                    ),
                    onDismissRequest = {
                        isShowExpiredWarningPopup.value = false to ""
                    }
                )
            }

        },
        topAppBarConfig = TopAppBarConfig(isShowTopAppBar = false),
        handleSideEffect = { loginScreenViewEffect ->
            BLogUtil.d("loginScreenViewEffect = $loginScreenViewEffect")
            when (loginScreenViewEffect) {
                is LoginReducer.LoginSideEffect.ShowErrorPopup -> {
                    val errorMessage = loginScreenViewEffect.errorMessage
                        ?: navController.context.getString(RLocalization.string.co_loi_xay_ra_trong_qua_trinh_xu_ly_vui_long_thu_lai)
                    when (loginScreenViewEffect.errorCode) {
                        IM2208, IM2209, IM1204 -> {
                            isShowPWWrongDetailPopup.value = true to errorMessage
                        }

                        IM1702 -> {
                            isForceUpdateErrorPopup.value = true to errorMessage
                        }

                        else -> {
                            isShowErrorPopup.value = true to errorMessage
                        }
                    }
                }

                is LoginReducer.LoginSideEffect.ShowExpiredWarningPopup -> {
                    isShowExpiredWarningPopup.value =
                        true to navController.context.getString(RLocalization.string.da_den_han_thay_doi_mat_khau_theo_quy_dinh_quy_khach_vui_long_doi_mat_khau_de_tiep_tuc_su_dung_dich_vu)
                }

                is LoginReducer.LoginSideEffect.NavigateToChangePasswordScreen -> {
                    _onEvent(LoginViewEvent.OnResetInputData)
                    NavigationHelper.navigateToChangePassword(
                        navController,
                        loginScreenViewEffect.modelLoginDMO
                    )
                }

                is LoginReducer.LoginSideEffect.NavigateToSecurityRequirementScreen -> {
                    _onEvent(LoginViewEvent.OnResetInputData)
                    NavigationHelper.navigateToSecurityRequirementScreen(
                        navController = navController,
                        modelLoginDMO = loginScreenViewEffect.modelLoginDMO
                    )
                }

                is LoginReducer.LoginSideEffect.NavigateToSettingSecurityQuestionScreen -> {
                    _onEvent(LoginViewEvent.OnResetInputData)
                    _onEvent(LoginViewEvent.OnUpdateLoginStatus(true))
                    NavigationHelper.navigateToSetupSecurityQuestion(
                        navController,
                        ModelRequestConfirm(
                            modelLoginDMO = loginScreenViewEffect.modelLoginDMO
                        )
                    )
                }

                is LoginReducer.LoginSideEffect.NavigateToSettingSmartOtpScreen -> {
                    _onEvent(LoginViewEvent.OnResetInputData)
                    _onEvent(LoginViewEvent.OnUpdateLoginStatus(true))
                    CommonNavigationHelper.navigateToActiveSmartOtp(
                        navController,
                        loginScreenViewEffect.modelLoginDMO?.smartOtpDMO,
                    )
                }

                is LoginReducer.LoginSideEffect.NavigateToConvertSmartOtpScreen -> {
                    CommonNavigationHelper.navigateToConvertSmartOtp(navController)
                }

                is LoginReducer.LoginSideEffect.LoginStatusSuccess -> {
                    _onEvent(LoginViewEvent.OnResetInputData)
                    NavigationHelper.navigateToUserInfoScreen(navController)
                }

                is LoginReducer.LoginSideEffect.ShowSmsOTPNewDevice -> {
                    NavigationHelper.navigateToNewDeviceSmsOTP(
                        navController,
                        loginScreenViewEffect.modelLoginDMO,
                        loginScreenViewEffect.modelLoginDMO?.otp
                    )
                }

                is LoginReducer.LoginSideEffect.OnChangeUserStatusSuccess -> {
                    NotificationUtils.clearAllNotifications(fragmentActivity)
                }

                else -> {
                    // Nothing to do
                }
            }
        }
    )
}

@Composable
fun HandleNavigateNotification(navController: NavHostController, loginViewModel: LoginViewModel) {
    CollectSideEffect(loginViewModel.localRepository.notificationModel) {
        it.notificationData?.run {
            val navigateID = getNavigateIdString(it.notificationData)
            val loginNotificationRoute = LoginNotificationRoute.mapToLoginNotificationRoute(navigateID)
            if (loginNotificationRoute?.isNeedToLogin == false) {
                loginViewModel.cleanNotificationData()
                TabNotiType.safeValueOf(notiType)?.let { tabNotiType ->
                    loginViewModel.markNotificationAsRead(
                        notifyId = notifyId,
                        displayTab = displayTab,
                        tabNotiType = tabNotiType
                    )
                }
                NotificationBuilderUtils.routeToDestinationByNotyInfo(
                    navController = navController,
                    navigateId = navigateId,
                    displayTab = displayTab,
                    listParam = listParam,
                    isRedirectDefault = true
                )
            }
        }
    }
}

private fun getNavigateIdString(notificationData: NotificationData?): String {
    val urlStr = buildString {
        append(notificationData?.navigateId)
        notificationData?.listParam?.forEach { param ->
            append(param.key)
        }
    }
    return urlStr
}


@Composable
private fun TopAppBarActionItems(
    language: ItemLanguage?,
    navController: NavHostController,
    onEvent: (LoginViewEvent) -> Unit
) {
    Row(
        modifier = Modifier
            .wrapContentWidth()
            .padding(end = IBSpacing.spacingM),
        horizontalArrangement = Arrangement.Center
    ) {
        LanguageView(
            modifier = Modifier
                .wrapContentWidth()
                .clickable {
                    onEvent(LoginViewEvent.OnResetInputData)
                    NavigationHelper.navigateToSettingLanguage(navController = navController)
                },
            language
        )
    }
}

@Composable
fun LanguageView(
    modifier: Modifier,
    language: ItemLanguage?,
) {
    Row(
        modifier = modifier.testTagIBank("login_language_view_${language?.localeDef}"),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(modifier = Modifier.size(IBSpacing.spacingL), contentAlignment = Alignment.Center) {
            Icon(
                painter = painterResource(
                    id = language?.icon ?: RDesignSystem.drawable.ic_country
                ),
                contentDescription = null,
                modifier = Modifier.size(IBSpacing.spacingL),
                tint = Color.Unspecified
            )
        }
        Spacer(modifier = Modifier.width(IBSpacing.spacing2xs))
        Text(
            text = language?.mapToShortedName() ?: "",
            modifier = Modifier.wrapContentWidth(),
            color = LocalColorScheme.current.contentOn_specialPrimary,
            style = labelLabel_m
        )
    }
}

@Composable
fun UtilitiesItem(modifier: Modifier, iconId: Int, nameUtilities: String) {
    Column(
        modifier = modifier.testTagIBank("login_utilitiesItem_$nameUtilities"),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            painter = painterResource(
                id = iconId
            ),
            contentDescription = null,
            modifier = Modifier
                .padding(IBSpacing.spacingXs)
                .defaultMinSize(minWidth = IBSpacing.spacing2xl, minHeight = IBSpacing.spacing2xl),
            tint = LocalColorScheme.current.contentOn_specialPrimary
        )
        Text(
            modifier = Modifier.wrapContentWidth(),
            text = nameUtilities,
            style = bodyBody_m,
            color = LocalColorScheme.current.contentOn_specialPrimary
        )
    }
}

@Composable
private fun LoginScreenContent(
    uiState: LoginViewState,
    onEvent: (LoginViewEvent) -> Unit,
    fragmentActivity: FragmentActivity,
    navController: NavHostController
) {

    val lstFilterUserName = listOf(
        SpecialCharacterFilter(),
        VietnamAccentRemoverFilter(30),
        RemoveSpaceFilter(),
        UpperCaseFilter()
    )

    val lstFilterPassword = listOf(
        VietnameseAccentRemoverFilter(),
        RemoveSpaceFilter(),
        RemoveSpecialCharacterPasswordFilter()
    )

    val listViewIdError = uiState.modelLoginUI.listClientErrorCode?.map { listClientErrorCode ->
        listClientErrorCode.first
    }

    val listClientErrorCode =
        uiState.modelLoginUI.listClientErrorCode?.map { list ->
            list.second
        }

    val (userNameInputState, userNameMessageWarning) = getUserNameStateMessageWarning(
        listViewIdError,
        listClientErrorCode
    )

    val (passwordInputState, passwordMessageWarning) = getPasswordStateMessageWarning(
        listViewIdError,
        listClientErrorCode
    )

    var isShowChangeUserPopup by remember { mutableStateOf(false) }

    Box(modifier = Modifier.fillMaxSize()) {
        Image(
            painter = painterResource(id = RDesignSystem.drawable.background),
            contentDescription = "Full Screen Image",
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop,
        )

        Column(
            modifier = Modifier
                .fillMaxSize()
        ) {
            IBankTopAppBar(
                navController = null,
                topAppBarType = TopAppBarType.Result,
                topAppBarConfig = TopAppBarConfig(
                    isShowTopAppBar = false,
                    isShowNavigationIcon = true,
                    iconLeading = RDesignSystem.drawable.bidv_logo
                ) {
                    TopAppBarActionItems(
                        uiState.modelLoginUI.itemLanguage,
                        navController,
                        onEvent
                    )
                }
            )

            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(start = IBSpacing.spacingM, end = IBSpacing.spacingM)
                    .background(color = Color.Transparent)
            ) {

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f), verticalArrangement = Arrangement.Center
                ) {
                    Box {
                        if (uiState.modelLoginUI.userInfoResponseDMO?.user?.username.isNotNullOrEmpty()) {
                            Column(modifier = Modifier.fillMaxWidth()) {

                                Box(
                                    modifier = Modifier
                                        .height(56.dp)
                                        .width(59.dp)
                                        .align(Alignment.CenterHorizontally)
                                ) {
                                    IBankAvatar(
                                        modifier = Modifier
                                            .size(56.dp)
                                            .align(Alignment.TopStart),
                                        avatarInfo = AvatarInfo(
                                            uiState.modelLoginUI.userInfoResponseDMO?.user?.fullname,
                                            uiState.modelLoginUI.userInfoResponseDMO?.user?.profileImage
                                        ),
                                        placeholderIcon = RDesignSystem.drawable.anonymous_avatar
                                    )

                                    Box(
                                        modifier = Modifier.testTagIBank("login_box_change_user")
                                            .clickable(onClick = {
                                                isShowChangeUserPopup = true
                                            }, indication = null, interactionSource = remember {
                                                MutableInteractionSource()
                                            })
                                            .size(22.dp)
                                            .clip(CircleShape)
                                            .align(Alignment.BottomEnd)
                                            .background(LocalColorScheme.current.bgOverlay),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Icon(
                                            painter = painterResource(id = R.drawable.icon_switch),
                                            contentDescription = null,
                                            tint = LocalColorScheme.current.contentOn_specialPrimary
                                        )
                                    }

                                }
                                Spacer(Modifier.height(IBSpacing.spacingXs))
                                Text(
                                    modifier = Modifier.testTagIBank("login_text_welcome_user_fullname")
                                        .wrapContentWidth()
                                        .align(Alignment.CenterHorizontally),
                                    text = stringResource(RLocalization.string.xin_chao_s).format(
                                        uiState.modelLoginUI.userInfoResponseDMO?.user?.fullname
                                    ),
                                    textAlign = TextAlign.Center,
                                    style = titleTitle_l,
                                    color = LocalColorScheme.current.contentOn_specialPrimary
                                )
                            }
                        } else {
                            Column(modifier = Modifier.fillMaxWidth()) {
                                Text(
                                    modifier = Modifier
                                        .wrapContentWidth()
                                        .align(Alignment.CenterHorizontally),
                                    text = stringResource(RLocalization.string.chao_mung_toi_bidv_direct),
                                    style = titleTitle_l,
                                    color = LocalColorScheme.current.contentOn_specialPrimary
                                )

                                Spacer(modifier = Modifier.height(16.dp))

                                IBankInputFieldBase(
                                    placeholderText = stringResource(RLocalization.string.ten_dang_nhap),
                                    state = userNameInputState,
                                    text = uiState.modelLoginUI.usernameInputText,
                                    filters = lstFilterUserName,
                                    maxLengthText = 30,
                                    helpTextLeft = userNameMessageWarning,
                                    onClickClear = {
                                        onEvent(
                                            LoginViewEvent.UserNameChanged("")
                                        )
                                    }
                                ) { textFieldValue ->
                                    onEvent(
                                        LoginViewEvent.UserNameChanged(textFieldValue.text)
                                    )
                                }

                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(IBSpacing.spacingM))

                    IBankInputPassword(
                        placeholderText = stringResource(RLocalization.string.mat_khau),
                        text = uiState.modelLoginUI.passwordInputText,
                        state = passwordInputState,
                        filters = lstFilterPassword,
                        maxLengthText = 20,
                        helpTextLeft = passwordMessageWarning,
                        onClickClear = {
                            onEvent(
                                LoginViewEvent.PasswordChanged("")
                            )
                        },
                        onSubmitText = {
                            onEvent(LoginViewEvent.LoginClicked)
                        }
                    ) { textFieldValue ->
                        onEvent(
                            LoginViewEvent.PasswordChanged(textFieldValue.text)
                        )
                    }

                    Spacer(modifier = Modifier.height(IBSpacing.spacingXs))
                    Text(
                        modifier = Modifier.testTagIBank("login_text_forgot_password")
                            .wrapContentWidth()
                            .align(Alignment.End)
                            .clickable {
                                onEvent(LoginViewEvent.OnResetInputData)
                                NavigationHelper.navigateToForgotPasswordScreen(navController)
                            },
                        text = stringResource(RLocalization.string.quen_mat_khau),
                        color = LocalColorScheme.current.contentOn_specialSecondary,
                        style = labelLabel_l,
                    )

                    Spacer(modifier = Modifier.height(IBSpacing.spacingM))

                    Row(modifier = Modifier.fillMaxWidth()) {
                        IBankNormalButton(
                            modifier = Modifier.weight(1f),
                            size = NormalButtonSize.L(LocalTypography.current),
                            type = NormalButtonType.PRIMARY(LocalColorScheme.current),
                            text = stringResource(RLocalization.string.dang_nhap),
                        ) {
                            onEvent(LoginViewEvent.LoginClicked)
                        }

                        if (uiState.modelLoginUI.isBiometricEnable) {

                            Spacer(modifier = Modifier.size(IBSpacing.spacingXs))

                            BiometricTouchId(fragmentActivity, navController, onEvent)

                        }
                    }

                    Spacer(modifier = Modifier.height(IBSpacing.spacingM))

                    /*Text(
                        modifier = Modifier
                            .fillMaxWidth(),
                        textAlign = TextAlign.Center,
                        text = stringResource(RLocalization.string.mo_tai_khoan_doanh_nghiep_online),
                        color = LocalColorScheme.current.contentOn_specialSecondary,
                        style = labelLabel_l,
                    )*/
                }

                ShowUtilitiesExtension(navController, onEvent)
            }
        }
    }

    if (isShowChangeUserPopup) {
        IBankModalConfirm(
            modalConfirmType = ModalConfirmType.Warning,
            title = stringResource(RLocalization.string.quy_khach_muon_thay_doi_nguoi_dung),
            supportingText = stringResource(RLocalization.string.nhan_xac_nhan_de_quay_ve_trang_dang_nhap_nguoi_dung_moi),
            listDialogButtonInfo = listOf(
                DialogButtonInfo(
                    label = stringResource(RLocalization.string.xac_nhan),
                    onClick = {
                        onEvent(LoginViewEvent.OnChangeUser)
                    }
                ),
                DialogButtonInfo(
                    label = stringResource(RLocalization.string.huy),
                )
            ),
            onDismissRequest = { isShowChangeUserPopup = false }
        )
    }
}

@Composable
private fun BiometricTouchId(
    fragmentActivity: FragmentActivity,
    navController: NavHostController,
    onEvent: (LoginViewEvent) -> Unit
) {

    var isShowBiometricErrorPopup by remember { mutableStateOf(false) }
    var errorTitle by remember { mutableStateOf("") }
    var errorMessage by remember { mutableStateOf("") }

    if (isShowBiometricErrorPopup) {
        IBankModalConfirm(
            modalConfirmType = ModalConfirmType.Error,
            title = errorTitle,
            supportingText = errorMessage,
            listDialogButtonInfo = listOf(
                DialogButtonInfo(
                    label = stringResource(RLocalization.string.dong),
                ),

                ),
            onDismissRequest = {
                isShowBiometricErrorPopup = false
            }
        )
    }

    IBankNormalButton(
        modifier = Modifier.testTagIBank("login_button_biometric_touch_id"),
        size = NormalButtonSize.L(LocalTypography.current),
        type = NormalButtonType.PRIMARY(LocalColorScheme.current),
        leadingIcon = ImageVector.vectorResource(id = RDesignSystem.drawable.dang_nhap_bang_touch_id_outline)
    ) {
        BiometricUtils.getBiometricData(
            context = fragmentActivity,
            title = navController.context.getString(RLocalization.string.xac_thuc_van_tay),
            subtitle = navController.context.getString(RLocalization.string.vui_long_cham_vao_cam_bien_van_tay_tren_thiet_bi_de_tiep_tuc),
            negativeButtonText = navController.context.getString(RLocalization.string.dong),
            onGetResult = { data, errorCode, errMessage ->
                val biometricData = try {
                    Gson().fromJson(data, BiometricData::class.java)
                } catch (ex: Exception) {
                    BLogUtil.logException(ex)
                    null
                }
                if (biometricData != null) {
                    BLogUtil.d("data = $data")
                    onEvent(LoginViewEvent.OnLoginViaBiometric(biometricData))
                } else {
                    isShowBiometricErrorPopup = true
                    errorTitle = navController.context.getString(RLocalization.string.loi)
                    errorMessage = errMessage
                        ?: navController.context.getString(RLocalization.string.co_loi_vua_xay_ra)
                    when (errorCode) {
                        LoginErrorCode.ADD_NEW_FINGERPRINT -> {
                            errorMessage =
                                navController.context.getString(RLocalization.string.da_co_thay_doi_touch_id_tren_thiet_bi_vui_long_dang_nhap_bang_mat_khau_va_bat_lai_cai_dat_dang_nhap_bang_touch_id)
                        }

                        BiometricPrompt.ERROR_LOCKOUT -> {
                            errorMessage =
                                navController.context.getString(RLocalization.string.quy_khach_da_nhap_sai_qua_so_lan_cho_phep_vui_long_thu_lai_sau)
                        }
                    }
                }
            }
        )
    }
}

@Composable
private fun ShowUtilitiesExtension(
    navController: NavHostController,
    onEvent: (LoginViewEvent) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 8.dp, bottom = IBSpacing.spacing3xl)
            .verticalScroll(rememberScrollState())
    ) {
        Row(modifier = Modifier.fillMaxWidth()) {
            UtilitiesItem(
                modifier = Modifier
                    .weight(1f)
                    .clickable {
                        onEvent(LoginViewEvent.OnResetInputData)
                        NavigationHelper.navigateToManageUserSmartOtpScreen(navController)
                    },
                iconId = RDesignSystem.drawable.pin_smart_otp_outline,
                nameUtilities = stringResource(RLocalization.string.smart_otp)
            )
            UtilitiesItem(
                modifier = Modifier
                    .weight(1f)
                    .clickable {
                        onEvent(LoginViewEvent.OnResetInputData)
                        CommonNavigationHelper.navigateToScanQRScreen(navController = navController)
                    },
                iconId = RDesignSystem.drawable.qr_code_outline,
                nameUtilities = stringResource(RLocalization.string.quet_qr)
            )
        }
        Spacer(Modifier.height(IBSpacing.spacing2xl))
        Row(modifier = Modifier.fillMaxWidth()) {
            UtilitiesItem(
                modifier = Modifier
                    .weight(1f),
                iconId = RDesignSystem.drawable.form_outline,
                nameUtilities = stringResource(RLocalization.string.mau_dang_ky)
            )
            UtilitiesItem(
                modifier = Modifier
                    .weight(1f)
                    .clickable {
                        onEvent(LoginViewEvent.OnResetInputData)
                        NavigationHelper.navigateToSupport(navController)
                    },
                iconId = RDesignSystem.drawable.support_outline,
                nameUtilities = stringResource(RLocalization.string.ho_tro)
            )
        }
    }
}

@Composable
private fun getUserNameStateMessageWarning(
    listViewIdError: List<String>?,
    listClientErrorCode: List<String>?
): Pair<IBFrameState, String> {
    var state: IBFrameState = IBFrameState.DEFAULT(LocalColorScheme.current)
    var message = ""
    if (listViewIdError?.contains(Constants.ViewID.USER_NAME_VIEW_ID) == true) {
        state = IBFrameState.ERROR(LocalColorScheme.current)
        message =
            if (listClientErrorCode?.contains(LoginErrorCode.USER_NAME_EMPTY) == true) {
                stringResource(RLocalization.string.vui_long_nhap_thong_tin)
            } else {
                ""
            }
    }
    return state to message
}

@Composable
private fun getPasswordStateMessageWarning(
    listViewIdError: List<String>?,
    listClientErrorCode: List<String>?
): Pair<IBFrameState, String> {
    var state: IBFrameState = IBFrameState.DEFAULT(LocalColorScheme.current)
    var message = ""
    if (listViewIdError?.contains(Constants.ViewID.PASSWORD_VIEW_ID) == true) {
        state = IBFrameState.ERROR(LocalColorScheme.current)
        message =
            if (listClientErrorCode?.contains(LoginErrorCode.PASSWORD_EMPTY) == true) {
                stringResource(RLocalization.string.vui_long_nhap_thong_tin)
            } else if (listClientErrorCode?.contains(LoginErrorCode.LESS_THAN_8_CHARACTERS) == true) {
                stringResource(RLocalization.string.mat_khau_toi_thieu_gom_8_ky_tu)
            } else {
                ""
            }
    }
    return state to message
}

