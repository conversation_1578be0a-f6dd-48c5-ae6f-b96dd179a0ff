package vn.com.bidv.feature.login.ui.securityrequirementscreen

import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.common.patterns.mvi.BaseMviViewModel
import vn.com.bidv.feature.login.ui.securityrequirementscreen.SecurityRequirementsReducer.SecurityRequirementsViewEvent
import vn.com.bidv.feature.login.ui.securityrequirementscreen.SecurityRequirementsReducer.SecurityRequirementsViewState
import javax.inject.Inject

@HiltViewModel
class SecurityRequirementsViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
) : BaseMviViewModel<SecurityRequirementsViewState, SecurityRequirementsViewEvent, SecurityRequirementsReducer.SecurityRequirementsSideEffect>(
    initialState = SecurityRequirementsViewState.InitScreen(),
    reducer = SecurityRequirementsReducer()
) {
    override fun handleEffect(
        sideEffect: SecurityRequirementsReducer.SecurityRequirementsSideEffect,
        onResult: (SecurityRequirementsViewEvent) -> Unit
    ) {
        // nothing
    }
}
