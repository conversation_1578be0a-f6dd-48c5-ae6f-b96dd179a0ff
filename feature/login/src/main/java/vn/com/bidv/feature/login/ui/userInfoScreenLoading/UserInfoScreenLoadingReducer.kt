package vn.com.bidv.feature.login.ui.userInfoScreenLoading

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.common.domain.data.GetUserInfoResponseDMO

class UserInfoScreenLoadingReducer :
    Reducer<UserInfoScreenLoadingReducer.UserInfoScreenLoadingViewState, UserInfoScreenLoadingReducer.UserInfoScreenLoadingViewEvent, UserInfoScreenLoadingReducer.UserInfoScreenLoadingViewEffect> {

    @Immutable
    data class UserInfoScreenLoadingViewState(
        val isInitializer: Boolean = false
    ) : ViewState

    @Immutable
    sealed class UserInfoScreenLoadingViewEvent : ViewEvent {
        data object OnGetUserInfo : UserInfoScreenLoadingViewEvent()
        data class OnGetUserInfoSuccess(val getUserInfoResponseDMO: GetUserInfoResponseDMO?) :
            UserInfoScreenLoadingViewEvent()
        data class OnGetUserInfoFail(val errorMessage: String) : UserInfoScreenLoadingViewEvent()
        data object OnLogout : UserInfoScreenLoadingViewEvent()
        data object OnLogoutSuccess : UserInfoScreenLoadingViewEvent()
    }

    @Immutable
    sealed class UserInfoScreenLoadingViewEffect : SideEffect {
        data object OnGetUserInfoSideEffect: UserInfoScreenLoadingViewEffect()
        data object OnLogoutSideEffect: UserInfoScreenLoadingViewEffect()
        data object OnLogoutSuccessSideEffect: UserInfoScreenLoadingViewEffect(), UIEffect
        data class OnGetUserInfoSuccessSideEffect(val getUserInfoResponseDMO: GetUserInfoResponseDMO?): UserInfoScreenLoadingViewEffect(), UIEffect
        data class OnGetUserInfoFailSideEffect(val errorMessage: String): UserInfoScreenLoadingViewEffect(), UIEffect
    }

    override fun reduce(
        previousState: UserInfoScreenLoadingViewState,
        event: UserInfoScreenLoadingViewEvent,
    ): Pair<UserInfoScreenLoadingViewState, UserInfoScreenLoadingViewEffect?> {
        return when (event) {
            is UserInfoScreenLoadingViewEvent.OnGetUserInfo -> {
                return previousState.copy(isInitializer = true) to UserInfoScreenLoadingViewEffect.OnGetUserInfoSideEffect
            }
            is UserInfoScreenLoadingViewEvent.OnGetUserInfoFail -> {
                return previousState to UserInfoScreenLoadingViewEffect.OnGetUserInfoFailSideEffect(
                    event.errorMessage
                )
            }
            is UserInfoScreenLoadingViewEvent.OnGetUserInfoSuccess -> {
                return previousState to UserInfoScreenLoadingViewEffect.OnGetUserInfoSuccessSideEffect(
                    event.getUserInfoResponseDMO
                )
            }

            is UserInfoScreenLoadingViewEvent.OnLogout -> {
                return previousState to UserInfoScreenLoadingViewEffect.OnLogoutSideEffect
            }

            is UserInfoScreenLoadingViewEvent.OnLogoutSuccess -> {
                return previousState to UserInfoScreenLoadingViewEffect.OnLogoutSuccessSideEffect
            }
        }

    }
}
