package vn.com.bidv.feature.login.domain.model

import com.google.gson.annotations.SerializedName
import kotlinx.serialization.Serializable

@Serializable
data class ModelSmsOTPConfigDMO(
    @SerializedName("modelLoginDMO")
    val modelLoginDMO: ModelLoginDMO? = ModelLoginDMO(),
    @SerializedName("modelCreateOtpResDMO")
    var modelCreateOtpResDMO: ModelCreateOtpResDMO? = ModelCreateOtpResDMO(),
) : IIBankModelOTPDMO {
    override fun getUserNameInfo(): String? {
        return modelLoginDMO?.username
    }

    override fun getMethod(): String? {
        return modelLoginDMO?.status
    }

    override fun getTransactionID(): String? {
        return modelCreateOtpResDMO?.txnId
    }

    override fun getRetryTimeSendOTP(): String? {
        return modelCreateOtpResDMO?.resendOtpTime
    }

    override fun getActiveTimeOTP(): String? {
        return modelCreateOtpResDMO?.otpActiveCount
    }

    override fun getMaskedPhoneOrEmailOTP(): String? {
        return modelCreateOtpResDMO?.maskedPhoneOrEmail
    }

    override fun getContent(): String? {
        return modelCreateOtpResDMO?.contentText
    }
}
