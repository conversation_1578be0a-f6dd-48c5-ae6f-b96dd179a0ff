package vn.com.bidv.feature.login.domain

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import vn.com.bidv.feature.common.data.utilities.model.SecurityQuestionAnswerDto
import vn.com.bidv.feature.common.data.utilities.model.TransApproveRequest
import vn.com.bidv.feature.common.domain.data.TransAuthDMO
import vn.com.bidv.feature.common.domain.verifyFlowUseCase.VerifyByTypeTransactionUseCase
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InitVerifyTransactionResponse
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyCreateTransaction
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyTransaction
import vn.com.bidv.feature.login.data.ManageSecurityQuestionRepository
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class EditQuestionsVerifyUseCase @Inject constructor(
    private val smartOTPRepository: ManageSecurityQuestionRepository,
) : VerifyByTypeTransactionUseCase {

    override suspend fun initTransaction(input: InputVerifyTransaction): DomainResult<InitVerifyTransactionResponse> {
        return DomainResult.Success(InitVerifyTransactionResponse())
    }

    override suspend fun initCreateTransaction(input: InputVerifyCreateTransaction): DomainResult<InitVerifyTransactionResponse> {

        val listSecurityQuestionAnswerDto: List<SecurityQuestionAnswerDto> = try {
            val type = object : TypeToken<List<SecurityQuestionAnswerDto>>() {}.type
            Gson().fromJson(input.dataString, type)
        } catch (e: Exception) {
            emptyList()
        }

        val result =
            smartOTPRepository.saveQuestions(securityQuestionAnswerDto = listSecurityQuestionAnswerDto)

        val domain = result.convert(TransAuthDMO::class.java)
        return if (domain is DomainResult.Success) {
            DomainResult.Success(
                InitVerifyTransactionResponse(
                    transAuth = domain.data
                )
            )
        } else {
            val domainError = domain as? DomainResult.Error
            DomainResult.Error(
                errorCode = domainError?.errorCode ?: "",
                errorMessage = domainError?.errorMessage
            )
        }
    }

    override suspend fun verifyTransaction(
        initResponse: InitVerifyTransactionResponse,
        reqValue: String?,
    ): DomainResult<String> {
        val result = smartOTPRepository.approveSaveQuestionWithOtp(
            transApproveRequest = TransApproveRequest(
                transKey = initResponse.transKey ?: initResponse.transAuth?.transKey ?: "",
                authValue = reqValue ?: ""
            )
        )

        return when (result) {
            is NetworkResult.Success -> DomainResult.Success(true.toString())
            is NetworkResult.Error -> DomainResult.Error(result.errorCode, result.errorMessage)
        }
    }
}