package vn.com.bidv.feature.login.ui.convertsmartotp

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.feature.login.domain.ActiveSmartOTPUseCase
import vn.com.bidv.feature.login.ui.convertsmartotp.ConvertSmartOtpReducer.ConvertSmartOtpViewEffect
import vn.com.bidv.feature.login.ui.convertsmartotp.ConvertSmartOtpReducer.ConvertSmartOtpViewEvent
import vn.com.bidv.feature.login.ui.convertsmartotp.ConvertSmartOtpReducer.ConvertSmartOtpViewState
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class ConvertSmartOtpViewModel @Inject constructor(
    private val userUseCase: UserInfoUseCase,
    private val activeSmartOTPUseCase: ActiveSmartOTPUseCase,
) : ViewModelIBankBase<ConvertSmartOtpViewState, ConvertSmartOtpViewEvent, ConvertSmartOtpViewEffect>(
    initialState = ConvertSmartOtpViewState(),
    reducer = ConvertSmartOtpReducer()
) {
    override fun handleEffect(
        sideEffect: ConvertSmartOtpViewEffect,
        onResult: (ConvertSmartOtpViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is ConvertSmartOtpViewEffect.InitEffect -> {
                onResult(
                    ConvertSmartOtpViewEvent.CheckUserAdminRoleEvent(
                        isAdminRole = userUseCase.isAdminRole()
                    )
                )
            }

            is ConvertSmartOtpViewEffect.SubmitEffect -> {
                callDomain(
                    isListenAllError = true,
                    onSuccess = {
                        onResult(ConvertSmartOtpViewEvent.ConvertSuccessEvent)
                    },
                    onFail = {
                        onResult(
                            ConvertSmartOtpViewEvent.ConvertFailEvent(
                                errorCode = it?.errorCode ?: "",
                                errorMessage = it?.errorMessage ?: ""
                            )
                        )
                    }
                ) {
                    activeSmartOTPUseCase.convertActive(otp = sideEffect.otp)
                }
            }

            else -> {}
        }
    }
}
