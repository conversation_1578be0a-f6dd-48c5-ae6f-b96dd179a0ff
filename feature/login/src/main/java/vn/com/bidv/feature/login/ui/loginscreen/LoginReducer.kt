package vn.com.bidv.feature.login.ui.loginscreen

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.common.domain.data.GetUserInfoResponseDMO
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.constants.LoginErrorCode
import vn.com.bidv.feature.login.domain.model.BiometricData
import vn.com.bidv.feature.login.domain.model.ModelLoginDMO
import vn.com.bidv.feature.login.ui.loginscreen.modelui.ModelLoginUI
import vn.com.bidv.sdkbase.domain.DomainErrorCode
import vn.com.bidv.sdkbase.utils.ItemLanguage

class LoginReducer :
    Reducer<LoginReducer.LoginViewState, LoginReducer.LoginViewEvent, LoginReducer.LoginSideEffect> {

    @Immutable
    sealed class LoginViewState(val modelLoginUI: ModelLoginUI) : ViewState {

        data class Init(val viewData: ModelLoginUI = ModelLoginUI.getDefault()) : LoginViewState(viewData)

        data class Result(
            val viewData: ModelLoginUI = ModelLoginUI.getDefault(),
        ) : LoginViewState(viewData)

        data class Loading(
            val viewData: ModelLoginUI = ModelLoginUI.getDefault(),
        ) : LoginViewState(viewData)
    }

    @Immutable
    sealed class LoginViewEvent : ViewEvent {
        data object InitData : LoginViewEvent()
        data class UserNameChanged(
            val userName: String,
        ) : LoginViewEvent()

        data class PasswordChanged(
            val password: String,
        ) : LoginViewEvent()

        data object LoginClicked : LoginViewEvent()
        data class LoginSuccess(val data: ModelLoginDMO?) : LoginViewEvent()
        data class LoginFail(
            val errorCode: String?,
            val errorMessage: String? = null,
            val listClientErrorCode: List<Pair<String, String>>? = null
        ) : LoginViewEvent()

        data object ChangeLanguage : LoginViewEvent()
        data class GetUserProfileFromStorageSuccess(val modelUserProfileDMO: GetUserInfoResponseDMO?) : LoginViewEvent()
        data object OnChangeUser : LoginViewEvent()
        data object OnChangeUserSuccess : LoginViewEvent()
        data object OnResetInputData: LoginViewEvent()
        data class OnCheckBiometricEnableSuccess(val isBiometricEnable: Boolean): LoginViewEvent()
        data class OnLoginViaBiometric(val biometricData: BiometricData?): LoginViewEvent()
        data class OnUpdateLoginStatus(val isLoginSuccess: Boolean): LoginViewEvent()
        data object OnGetCurrentLanguage: LoginViewEvent()
        data class GetCurrentLanguageSuccess(val itemLanguage: ItemLanguage): LoginViewEvent()
        data object OnResetStatusPopupLoaded : LoginViewEvent()
    }

    @Immutable
    sealed class LoginSideEffect : SideEffect {
        data class DoLogin(val username: String, val password: String) : LoginSideEffect()
        data class LoginStatusFail(val errorMessage: String) : LoginSideEffect(), UIEffect

        data class LoginStatusSuccess(val data: ModelLoginDMO? = null) : LoginSideEffect(), UIEffect
        data class NavigateToSecurityRequirementScreen(val modelLoginDMO: ModelLoginDMO?) :
            LoginSideEffect(), UIEffect

        data class NavigateToChangePasswordScreen(val modelLoginDMO: ModelLoginDMO?) :
            LoginSideEffect(), UIEffect

        data class NavigateToSettingSecurityQuestionScreen(val modelLoginDMO: ModelLoginDMO?) : LoginSideEffect(), UIEffect
        data class NavigateToSettingSmartOtpScreen(val modelLoginDMO: ModelLoginDMO?) : LoginSideEffect(), UIEffect
        data object NavigateToConvertSmartOtpScreen : LoginSideEffect(), UIEffect
        data object ShowExpiredWarningPopup : LoginSideEffect(), UIEffect
        data class ShowSmsOTPNewDevice(val modelLoginDMO: ModelLoginDMO?) : LoginSideEffect(), UIEffect
        data class ShowErrorPopup(val errorCode: String, val errorMessage: String?) :
            LoginSideEffect(), UIEffect

        data object GetUserProfileFromStorage: LoginSideEffect()
        data object ChangeUser: LoginSideEffect()
        data object CheckBiometricEnable: LoginSideEffect()
        data class BiometricLoginVerify(val userName: String, val biometricData: BiometricData?): LoginSideEffect()
        data object OnChangeUserStatusSuccess : LoginSideEffect(), UIEffect
        data class UpdateLoginStatus(val isLoginSuccess: Boolean): LoginSideEffect()
        data object GetCurrentLanguage: LoginSideEffect()
        data object ResetStatusPopupLoaded : LoginSideEffect()
    }

    override fun reduce(
        previousState: LoginViewState,
        event: LoginViewEvent,
    ): Pair<LoginViewState, LoginSideEffect?> {
        return when (previousState) {
            is LoginViewState.Init -> handleInitScreenState(previousState, event)
            is LoginViewState.Loading -> handleLoadingState(previousState, event)
            is LoginViewState.Result -> handleResultScreenState(previousState, event)
        }
    }

    private fun handleInitScreenState(
        previousState: LoginViewState.Init,
        event: LoginViewEvent
    ): Pair<LoginViewState, LoginSideEffect?> {
        return when (event) {
            is LoginViewEvent.InitData -> {
                previousState to LoginSideEffect.GetUserProfileFromStorage
            }

            is LoginViewEvent.GetUserProfileFromStorageSuccess -> {
                previousState.copy(
                    viewData = previousState.modelLoginUI.copy(
                        userInfoResponseDMO = event.modelUserProfileDMO,
                        usernameInputText = event.modelUserProfileDMO?.user?.username ?: ""
                    )
                ) to LoginSideEffect.GetCurrentLanguage
            }

            is LoginViewEvent.GetCurrentLanguageSuccess -> {
                previousState.copy(
                    viewData = previousState.modelLoginUI.copy(
                        itemLanguage = event.itemLanguage
                    )
                ) to LoginSideEffect.CheckBiometricEnable
            }

            is LoginViewEvent.OnCheckBiometricEnableSuccess -> {
                LoginViewState.Result(
                    viewData = ModelLoginUI(
                        itemLanguage = previousState.modelLoginUI.itemLanguage,
                        userInfoResponseDMO = previousState.modelLoginUI.userInfoResponseDMO,
                        usernameInputText = previousState.modelLoginUI.userInfoResponseDMO?.user?.username ?: "",
                        isBiometricEnable = event.isBiometricEnable
                    )
                ) to null
            }

            is LoginViewEvent.OnUpdateLoginStatus -> {
                previousState to LoginSideEffect.UpdateLoginStatus(event.isLoginSuccess)
            }

            is LoginViewEvent.OnResetStatusPopupLoaded -> {
                previousState to LoginSideEffect.ResetStatusPopupLoaded
            }

            else -> previousState to null
        }
    }

    private fun handleLoadingState(
        previousState: LoginViewState.Loading,
        event: LoginViewEvent
    ): Pair<LoginViewState, LoginSideEffect?> {
        return when (event) {
            is LoginViewEvent.LoginSuccess -> {
                val nextEffectSuccess = getNextSideEffectLoginSuccess(event.data)
                LoginViewState.Result(
                    viewData = previousState.modelLoginUI.copy(
                        modelLoginDMO = event.data?.copy(
                            username = previousState.modelLoginUI.usernameInputText
                        ),
                    )
                ) to nextEffectSuccess
            }

            is LoginViewEvent.LoginFail -> {
                if (event.errorCode == DomainErrorCode.CLIENT_ERROR_CODE) {
                    LoginViewState.Result(
                        viewData = previousState.modelLoginUI.copy(
                            listClientErrorCode = event.listClientErrorCode
                        )
                    ) to null
                } else {
                    LoginViewState.Result(
                        viewData = previousState.modelLoginUI.copy(
                            isBiometricEnable = if (event.errorCode in listOf(
                                    LoginErrorCode.IM3005,
                                    LoginErrorCode.IM3006
                                )
                            ) false else previousState.modelLoginUI.isBiometricEnable,
                        )
                    ) to if (event.errorCode == null) null else LoginSideEffect.ShowErrorPopup(
                        event.errorCode,
                        event.errorMessage
                    )
                }
            }

            else -> previousState to null
        }
    }

    private fun handleResultScreenState(
        previousState: LoginViewState.Result,
        event: LoginViewEvent
    ): Pair<LoginViewState, LoginSideEffect?> {
        return when (event) {
            is LoginViewEvent.UserNameChanged -> {
                previousState.copy(
                    viewData = previousState.modelLoginUI.copy(
                        usernameInputText = event.userName,
                    )
                ) to null
            }

            is LoginViewEvent.PasswordChanged -> {
                previousState.copy(
                    viewData = previousState.modelLoginUI.copy(
                        passwordInputText = event.password,
                    )
                ) to null
            }

            is LoginViewEvent.LoginClicked -> {
                val nextState = previousState.copy(
                    viewData = previousState.modelLoginUI.copy(
                        listClientErrorCode = listOf()
                    )
                )
                LoginViewState.Loading(
                    viewData = nextState.modelLoginUI
                ) to LoginSideEffect.DoLogin(
                    nextState.modelLoginUI.userInfoResponseDMO?.user?.username ?: nextState.modelLoginUI.usernameInputText,
                    nextState.modelLoginUI.passwordInputText
                )
            }

            is LoginViewEvent.OnChangeUser -> previousState to LoginSideEffect.ChangeUser
            is LoginViewEvent.OnChangeUserSuccess -> previousState.copy(
                viewData = previousState.modelLoginUI.copy(
                    usernameInputText = "",
                    passwordInputText = "",
                    userInfoResponseDMO = null,
                    isBiometricEnable = false
                )
            ) to LoginSideEffect.OnChangeUserStatusSuccess

            is LoginViewEvent.OnResetInputData -> previousState.copy(
                viewData = previousState.modelLoginUI.copy(
                    usernameInputText = "",
                    passwordInputText = "",
                    listClientErrorCode = listOf()
                )
            ) to null

            is LoginViewEvent.OnLoginViaBiometric -> {
                LoginViewState.Loading(viewData = previousState.modelLoginUI) to LoginSideEffect.BiometricLoginVerify(
                    userName = previousState.modelLoginUI.userInfoResponseDMO?.user?.username ?: "",
                    biometricData = event.biometricData
                )
            }

            is LoginViewEvent.OnUpdateLoginStatus -> {
                previousState to LoginSideEffect.UpdateLoginStatus(event.isLoginSuccess)
            }

            else -> previousState to null
        }
    }

    private fun getNextSideEffectLoginSuccess(data: ModelLoginDMO?): LoginSideEffect {
        val isNeedFirstTimePWChange =
            data?.status?.contains(Constants.FIRST_TIME_PW_CHANGE, true) == true
        val isNeedSecurityQuestionSet =
            data?.isShowSecurityQuestionScreen == true
        val isNeedConvertSmartOtp =
            data?.smartOtpDMO?.isNeedConvertActiveSmartOtp == true
        val isNeedSmartOtpSet =
            data?.isShowSmartOtpScreen == true
        val trueCount = listOf(
            isNeedFirstTimePWChange,
            isNeedSecurityQuestionSet,
            isNeedConvertSmartOtp,
            isNeedSmartOtpSet
        ).count { it }
        return when (trueCount) {
            // nếu còn 2, 3 bước thì cần hiện màn hình quy trình thiết lập
            // nếu còn 1 bước thì vào luôn màn hình thực hiện
            2, 3 -> {
                LoginSideEffect.NavigateToSecurityRequirementScreen(data)
            }

            1 -> {
                when {
                    isNeedFirstTimePWChange -> {
                        LoginSideEffect.NavigateToChangePasswordScreen(data)
                    }

                    isNeedSecurityQuestionSet -> {
                        LoginSideEffect.NavigateToSettingSecurityQuestionScreen(data)
                    }

                    isNeedConvertSmartOtp -> {
                        LoginSideEffect.NavigateToConvertSmartOtpScreen
                    }

                    else -> {
                        LoginSideEffect.NavigateToSettingSmartOtpScreen(data)
                    }
                }
            }

            else -> {
                when {
                    isShowPasswordExpired(data) -> {
                        LoginSideEffect.ShowExpiredWarningPopup
                    }

                    isNewDevice(data) -> {
                        LoginSideEffect.ShowSmsOTPNewDevice(data)
                    }

                    else -> {
                        LoginSideEffect.LoginStatusSuccess(data)
                    }
                }
            }
        }
    }

    private fun isShowPasswordExpired(data: ModelLoginDMO?): Boolean {
        return data?.status?.contains(Constants.EXPIRED_PW, true) == true
    }

    private fun isNewDevice(data: ModelLoginDMO?): Boolean {
        return data?.status?.contains(Constants.NEW_DEVICE, true) == true
    }
}
