package vn.com.bidv.feature.login.ui.manageusersmartotp

import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.common.patterns.mvi.BaseMviViewModel
import vn.com.bidv.feature.common.domain.smartotp.SmartOTPUseCase
import vn.com.bidv.feature.login.domain.ManageUserSmartOtpUseCase
import vn.com.bidv.feature.login.ui.manageusersmartotp.ManageUserSmartOtpReducer.ManageUserSmartOtpViewEffect
import vn.com.bidv.feature.login.ui.manageusersmartotp.ManageUserSmartOtpReducer.ManageUserSmartOtpViewEvent
import vn.com.bidv.feature.login.ui.manageusersmartotp.ManageUserSmartOtpReducer.ManageUserSmartOtpViewState
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class ManageUserSmartOtpViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val smartOTPUseCase: SmartOTPUseCase,
    private val manageUserSmartOtpUseCase: ManageUserSmartOtpUseCase
) : ViewModelIBankBase<ManageUserSmartOtpViewState, ManageUserSmartOtpViewEvent, ManageUserSmartOtpViewEffect>(
    initialState = ManageUserSmartOtpViewState(),
    reducer = ManageUserSmartOtpReducer()
) {
    override fun handleEffect(
        sideEffect: ManageUserSmartOtpViewEffect,
        onResult: (ManageUserSmartOtpViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is ManageUserSmartOtpViewEffect.GetListUserSmartOtp -> {
                val result = smartOTPUseCase.getListUserSmartOtpDMO()
                onResult(
                    ManageUserSmartOtpViewEvent.OnGetListUserSmartOtpSuccess(
                        result
                    )
                )
            }

            is ManageUserSmartOtpViewEffect.DeleteUserSmartOtp -> {
                callDomain(onSuccess = {
                    // xóa user smart otp local
                    smartOTPUseCase.deleteUserSmartOtp(sideEffect.model.userId)
                    onResult(ManageUserSmartOtpViewEvent.OnDeleteUserSmartOtpSuccess(sideEffect.model))
                }, onFail = {

                }) {
                    manageUserSmartOtpUseCase.deleteUserSmartOtp(
                        sideEffect.model.userId,
                        sideEffect.model.smToken
                    )
                }
            }

            else -> {}
        }
    }
}
