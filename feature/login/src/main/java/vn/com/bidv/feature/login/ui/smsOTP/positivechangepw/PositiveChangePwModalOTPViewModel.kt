package vn.com.bidv.feature.login.ui.smsOTP.positivechangepw

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.domain.BiometricUseCase
import vn.com.bidv.feature.login.domain.VerifySecureOtpUseCase
import vn.com.bidv.feature.login.domain.model.ModelCreateOtpResDMO
import vn.com.bidv.feature.login.domain.model.ModelSmsOTPConfigDMO
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPReducer
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPViewModel
import javax.inject.Inject

@HiltViewModel
class PositiveChangePwModalOTPViewModel @Inject constructor(
    private val verifySecureOtpUseCase: VerifySecureOtpUseCase,
    private val biometricUseCase: BiometricUseCase
) : BaseIBankModalOTPViewModel<ModelSmsOTPConfigDMO, ModelCreateOtpResDMO, Boolean>(
    initialState = BaseIBankModalOTPReducer.BaseIBankModalOTPViewState(),
    reducer = BaseIBankModalOTPReducer()
) {

    override fun handleEffect(
        sideEffect: BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect<ModelSmsOTPConfigDMO, ModelCreateOtpResDMO, Boolean>,
        onResult: (BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent<ModelSmsOTPConfigDMO, ModelCreateOtpResDMO, Boolean>) -> Unit
    ) {
        when (sideEffect) {
            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.RetrySendOTP -> {
                callDomain(
                    onSuccess = { result ->
                        onResult(
                            BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnRetrySendOTPSuccess(
                                result.data
                            )
                        )
                    },
                ) {
                    verifySecureOtpUseCase.otpSecuredResend(
                        transId = sideEffect.txnId,
                        method = Constants.PW_CHANGE
                    )
                }

            }

            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTP -> {
                callDomain(
                    isListenAllError = true,
                    onSuccess = {
                        biometricUseCase.removeBiometricData()
                        onResult(
                            BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnVerifyOTPSuccess(
                                true
                            )
                        )
                    },
                    onFail = { error ->
                        onResult(
                            BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnVerifyOTPError(
                                error?.errorCode,
                                error?.errorMessage
                            )
                        )
                    }
                ) {
                    verifySecureOtpUseCase.otpSecuredVerify(
                        transId = sideEffect.txnId,
                        otpNum = sideEffect.otpNum,
                        Constants.PW_CHANGE
                    )
                }

            }

            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTPError,
            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.RetryOTPSuccess,
            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTPSuccess -> TODO()
        }
    }

}
