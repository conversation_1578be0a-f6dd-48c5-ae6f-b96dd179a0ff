package vn.com.bidv.feature.login.ui.settinglanguage

import android.content.Context
import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.sdkbase.utils.ItemLanguage

class SettingLanguageReducer :
    Reducer<SettingLanguageReducer.SettingLanguageViewState, SettingLanguageReducer.SettingLanguageViewEvent, SettingLanguageReducer.SettingLanguageViewEffect> {

    @Immutable
    data class SettingLanguageViewState(
        val isInitSuccess: Boolean = false,
        val listLanguage: List<ItemLanguage> = listOf(),
        val isLoginSuccess: Boolean = false,
    ) : ViewState

    @Immutable
    sealed class SettingLanguageViewEvent : ViewEvent {
        data object OnInitSettingLanguage: SettingLanguageViewEvent()
        data class OnInitSettingLanguageSuccess(val listLanguage: List<ItemLanguage>, val isLoginSuccess: Boolean): SettingLanguageViewEvent()
        data class OnItemLanguageSelected(val itemLanguage: ItemLanguage): SettingLanguageViewEvent()
        data class OnChangeLanguage(val context: Context): SettingLanguageViewEvent()
        data object OnChangeLanguageSuccess: SettingLanguageViewEvent()
    }

    @Immutable
    sealed class SettingLanguageViewEffect : SideEffect {
        data object InitSettingLanguage : SettingLanguageViewEffect()
        data class ChangeLanguage(val context: Context, val locale: String) : SettingLanguageViewEffect()
        data object ChangeLanguageSuccess: SettingLanguageViewEffect(), UIEffect
    }

    override fun reduce(
        previousState: SettingLanguageViewState,
        event: SettingLanguageViewEvent,
    ): Pair<SettingLanguageViewState, SettingLanguageViewEffect?> {
        return when (event) {
            is SettingLanguageViewEvent.OnInitSettingLanguage -> {
                previousState to SettingLanguageViewEffect.InitSettingLanguage
            }
            is SettingLanguageViewEvent.OnInitSettingLanguageSuccess -> {
                previousState.copy(
                    isInitSuccess = true,
                    listLanguage = event.listLanguage,
                    isLoginSuccess = event.isLoginSuccess
                ) to null
            }
            is SettingLanguageViewEvent.OnItemLanguageSelected -> {
                previousState.copy(
                    listLanguage = previousState.listLanguage.map {
                        it.copy(isSelected = it.localeDef == event.itemLanguage.localeDef)
                    }
                ) to null
            }

            is SettingLanguageViewEvent.OnChangeLanguage -> {
                previousState to SettingLanguageViewEffect.ChangeLanguage(
                    context = event.context,
                    locale = previousState.listLanguage.first { it.isSelected }.localeDef
                )
            }

            is SettingLanguageViewEvent.OnChangeLanguageSuccess -> {
                previousState to SettingLanguageViewEffect.ChangeLanguageSuccess
            }
        }
    }
}
