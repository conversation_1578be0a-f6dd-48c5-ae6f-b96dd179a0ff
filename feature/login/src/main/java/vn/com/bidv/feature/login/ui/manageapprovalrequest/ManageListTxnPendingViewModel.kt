package vn.com.bidv.feature.login.ui.manageapprovalrequest

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.designsystem.ui.listwithloadmorev2.ListAutoLoadMoreReducer
import vn.com.bidv.designsystem.ui.listwithloadmorev2.ListAutoLoadMoreViewModel
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.SimpleSearchRuleFilters
import vn.com.bidv.feature.login.domain.ManageApprovalRequestsUseCase
import vn.com.bidv.feature.login.domain.model.TransRequestApprovalDMO
import vn.com.bidv.sdkbase.domain.DomainResult
import javax.inject.Inject

@HiltViewModel
class ManageListTxnPendingViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val manageApprovalRequestsUseCase: ManageApprovalRequestsUseCase,
    reducer: ListAutoLoadMoreReducer<TransRequestApprovalDMO, SimpleSearchRuleFilters>,
) : ListAutoLoadMoreViewModel<TransRequestApprovalDMO, SimpleSearchRuleFilters>(reducer) {
    override fun fetchData(
        pageIndex: Int,
        pageSize: Int,
        rule: SimpleSearchRuleFilters?,
        onLoadSuccess: (data: List<TransRequestApprovalDMO>, total: Int?) -> Unit,
        onLoadFail: (String?) -> Unit
    ): Job {
        return viewModelScope.launch(dispatcher) {
            when (val result = manageApprovalRequestsUseCase.getPendingApprovalRequests(
                pageIndex = pageIndex,
                pageSize = pageSize,
                searchText = rule?.searchText ?: ""
            )) {
                is DomainResult.Success -> {
                    result.data?.let {
                        onLoadSuccess(it.items ?: listOf(), it.items?.size)
                    }
                }

                is DomainResult.Error -> {
                    onLoadFail(result.errorMessage)
                }
            }
        }
    }
}