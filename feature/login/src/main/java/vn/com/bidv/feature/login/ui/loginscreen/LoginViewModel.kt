package vn.com.bidv.feature.login.ui.loginscreen

import android.content.Context
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.launch
import vn.com.bidv.feature.common.domain.notificationcommon.CommonNotyMarkAsReadUseCase
import vn.com.bidv.feature.common.domain.notificationcommon.TabNotiType
import vn.com.bidv.feature.login.constants.LoginErrorCode
import vn.com.bidv.feature.login.domain.BiometricUseCase
import vn.com.bidv.feature.login.domain.LoginUseCase
import vn.com.bidv.feature.login.ui.loginscreen.LoginReducer.LoginSideEffect
import vn.com.bidv.feature.login.ui.loginscreen.LoginReducer.LoginViewEvent
import vn.com.bidv.feature.login.ui.loginscreen.LoginReducer.LoginViewState
import vn.com.bidv.sdkbase.domain.DomainErrorCode
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import vn.com.bidv.sdkbase.utils.LocaleManager
import javax.inject.Inject

@HiltViewModel
class LoginViewModel @Inject constructor(
    private val loginUseCase: LoginUseCase,
    @ApplicationContext private val context: Context,
    private val biometricUseCase: BiometricUseCase,
    private val commonNotyMarkAsReadUseCase: CommonNotyMarkAsReadUseCase,
    ) : ViewModelIBankBase<LoginViewState, LoginViewEvent, LoginSideEffect>(
    initialState = LoginViewState.Init(),
    reducer = LoginReducer(),
) {

    override fun handleEffect(
        sideEffect: LoginSideEffect,
        onResult: (LoginViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is LoginSideEffect.DoLogin -> callDomain(
                listErrorCodeListen = listOf(
                    DomainErrorCode.CLIENT_ERROR_CODE,
                    LoginErrorCode.IM2208,
                    LoginErrorCode.IM2209,
                    LoginErrorCode.IM1204,
                    LoginErrorCode.IM1702
                ),
                onFail = { error ->
                    onResult(
                        LoginViewEvent.LoginFail(
                            errorCode = error?.errorCode,
                            errorMessage = error?.errorMessage,
                            error?.listClientErrorCode
                        )
                    )
                },
                onSuccess = { result ->
                    onResult(LoginViewEvent.LoginSuccess(data = result.data))
                }
            ) {
                loginUseCase.invoke(sideEffect.username, sideEffect.password)
            }

            is LoginSideEffect.GetUserProfileFromStorage -> {
                callDomain(
                    showLoadingIndicator = false,
                    onSuccess = { result ->
                        onResult(LoginViewEvent.GetUserProfileFromStorageSuccess(modelUserProfileDMO = result.data))
                        localRepository.decreaseInitDataCount()
                    },
                ) {
                    loginUseCase.getDataFromStorage()
                }
            }

            is LoginSideEffect.ChangeUser -> {
                callDomain(
                    onSuccess = {
                        onResult(LoginViewEvent.OnChangeUserSuccess)
                    },
                ) {
                    loginUseCase.changeUser()
                }
            }

            is LoginSideEffect.CheckBiometricEnable -> {
                callDomain(
                    onSuccess = {
                        onResult(LoginViewEvent.OnCheckBiometricEnableSuccess(isBiometricEnable = it.data ?: false))
                    },
                ) {
                    loginUseCase.checkBiometricEnable()
                }
            }

            is LoginSideEffect.BiometricLoginVerify -> {
                callDomain(
                    listErrorCodeListen = listOf(
                        LoginErrorCode.IM1702,
                        LoginErrorCode.IM3005,
                        LoginErrorCode.IM3006,
                    ),
                    onSuccess = {
                        onResult(LoginViewEvent.LoginSuccess(data = it.data))
                    },
                    onFail = { error ->
                        if (error?.errorCode in listOf(LoginErrorCode.IM3005, LoginErrorCode.IM3006)) {
                            biometricUseCase.removeBiometricData()
                        }
                        onResult(
                            LoginViewEvent.LoginFail(
                                errorCode = error?.errorCode,
                                errorMessage = error?.errorMessage,
                            )
                        )
                    }
                ) {
                    loginUseCase.biometricLoginVerify(sideEffect.userName, sideEffect.biometricData)
                }
            }

            is LoginSideEffect.UpdateLoginStatus -> {
                localRepository.setLoginStatus(isSuccess = sideEffect.isLoginSuccess)
            }

            is LoginSideEffect.GetCurrentLanguage -> {
                onResult(LoginViewEvent.GetCurrentLanguageSuccess(itemLanguage = LocaleManager.getCurrentLanguage(context)))
            }

            is LoginSideEffect.ResetStatusPopupLoaded -> {
                localRepository.setPopupLoadedStatus(isLoaded = false)
            }

            is LoginSideEffect.LoginStatusFail,
            is LoginSideEffect.LoginStatusSuccess,
            is LoginSideEffect.NavigateToChangePasswordScreen,
            is LoginSideEffect.NavigateToSettingSecurityQuestionScreen,
            is LoginSideEffect.NavigateToSettingSmartOtpScreen,
            is LoginSideEffect.NavigateToSecurityRequirementScreen,
            is LoginSideEffect.ShowExpiredWarningPopup,
            is LoginSideEffect.ShowErrorPopup,
            is LoginSideEffect.ShowSmsOTPNewDevice,
            is LoginSideEffect.NavigateToConvertSmartOtpScreen,
            is LoginSideEffect.OnChangeUserStatusSuccess -> {
                // nothing
            }
        }
    }

    fun cleanNotificationData() {
        viewModelScope.launch {
            localRepository.pushNotification(null)
        }
    }

    fun markNotificationAsRead(notifyId: Long?, displayTab: String?, tabNotiType: TabNotiType) {
        viewModelScope.launch {
            commonNotyMarkAsReadUseCase.markNotyItemRead(
                notifyId = notifyId,
                displayTab = displayTab,
                tabNotiType = tabNotiType
            )
        }
    }
}
