package vn.com.bidv.feature.login.ui.forgotpassword

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.ParagraphStyle
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.R.drawable
import vn.com.bidv.designsystem.component.datadisplay.tooltip.IBankTooltip
import vn.com.bidv.designsystem.component.datadisplay.tooltip.TooltipPosition
import vn.com.bidv.designsystem.component.dataentry.IBFrameExtendState
import vn.com.bidv.designsystem.component.dataentry.IBFrameState
import vn.com.bidv.designsystem.component.datepicker.IBankInputDatePicker
import vn.com.bidv.designsystem.component.dataentry.IBankInputFieldBase
import vn.com.bidv.designsystem.component.dataentry.IBankRadioButtonWithText
import vn.com.bidv.designsystem.component.dataentry.InputFieldEmailFilter
import vn.com.bidv.designsystem.component.dataentry.RemoveSpaceFilter
import vn.com.bidv.designsystem.component.dataentry.RemoveTextFilter
import vn.com.bidv.designsystem.component.dataentry.SpecialCharacterFilter
import vn.com.bidv.designsystem.component.dataentry.UpperCaseFilter
import vn.com.bidv.designsystem.component.datepicker.IBankDatePickerDialog
import vn.com.bidv.designsystem.component.datepicker.model.DatePickerConfig
import vn.com.bidv.designsystem.component.datepicker.utils.getStartDate
import vn.com.bidv.designsystem.component.feedback.inlinemessage.InlineMessage
import vn.com.bidv.designsystem.component.feedback.inlinemessage.InlineMessageStatus
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankActionBar
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.designsystem.ui.extension.noRippleClickable
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.constants.LoginErrorCode
import vn.com.bidv.feature.login.domain.model.DetailError
import vn.com.bidv.feature.login.domain.model.ModelForgotPwOTPConfigDMO
import vn.com.bidv.feature.login.navigation.NavigationHelper
import vn.com.bidv.feature.login.ui.forgotpassword.ForgotPasswordReducer.ForgotPasswordViewEffect
import vn.com.bidv.feature.login.ui.forgotpassword.ForgotPasswordReducer.ForgotPasswordViewEvent
import vn.com.bidv.feature.login.ui.forgotpassword.ForgotPasswordReducer.ForgotPasswordViewState
import vn.com.bidv.feature.login.ui.verifyquestion.model.VerifyQuestionModel
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.VietnameseAccentRemoverFilter
import vn.com.bidv.sdkbase.utils.exts.dateToString
import java.util.Date

@Composable
fun ForgotPasswordScreen(navController: NavHostController) {
    val vm: ForgotPasswordViewModel = hiltViewModel()
    BaseScreen(
        navController = navController,
        viewModel = vm,
        topAppBarConfig = TopAppBarConfig(
            titleTopAppBar = stringResource(R.string.quen_mat_khau),
            showHomeIcon = false,
        ),
        renderContent = { uiState, onEvent ->
            ForgotPasswordContent(
                uiState = uiState,
                onEvent = onEvent,
                navController = navController
            )
        },
        handleSideEffect = { sideEffect ->
            if (sideEffect is ForgotPasswordViewEffect.GoToVerifyQuestionEffect) {
                NavigationHelper.navigateToVerifyQuestionScreen(
                    navController, VerifyQuestionModel(
                        userName = sideEffect.username ?: "",
                        transId = sideEffect.txnId ?: "",
                        listQuestions = sideEffect.listQuestions
                    )
                )

            }

            if (sideEffect is ForgotPasswordViewEffect.GoToSmsOtpEffect) {
                NavigationHelper.navigateToForgotPWSmsOTP(
                    navController, modelForgotPwOTPConfigDMO = ModelForgotPwOTPConfigDMO(
                        userName = sideEffect.username,
                        userRole = sideEffect.data?.userRole,
                        modelCreateOtpResDMO = sideEffect.data?.basicOtp
                    )
                )
            }
        },
    )
}

@Composable
private fun getErrorDetails(
    listClientErrorCode: List<Pair<String, String>>?,
    viewIdError: String
): DetailError {
    val hasError = listClientErrorCode?.any { it.first == viewIdError } == true
    val errorCode = listClientErrorCode?.find { it.first == viewIdError }?.second

    val state =
        if (hasError) IBFrameState.ERROR(LocalColorScheme.current) else IBFrameState.DEFAULT(
            LocalColorScheme.current
        )
    val message = if (hasError) {
        when {
            (viewIdError in listOf(
                Constants.ViewID.PHONE_NUMBER_VIEW_ID, Constants.ViewID.EMAIL_VIEW_ID
            ) && errorCode == LoginErrorCode.INVALID_INPUT) -> stringResource(R.string.vui_long_nhap_thong_tin)

            viewIdError in listOf(
                Constants.ViewID.PHONE_NUMBER_VIEW_ID, Constants.ViewID.EMAIL_VIEW_ID
            ) -> stringResource(R.string.dinh_dang_khong_hop_le)

            else -> stringResource(R.string.vui_long_nhap_thong_tin)
        }
    } else ""
    return DetailError(state, message)
}

@Composable
private fun ForgotPasswordContent(
    navController: NavHostController,
    uiState: ForgotPasswordViewState,
    onEvent: (ForgotPasswordViewEvent) -> Unit,
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current

    val isShowCalendar = remember { mutableStateOf(false) }

    val phoneNumberError = getErrorDetails(
        uiState.listOtherInputClientErrorCode,
        Constants.ViewID.PHONE_NUMBER_VIEW_ID
    )

    val emailError = getErrorDetails(
        uiState.listOtherInputClientErrorCode,
        Constants.ViewID.EMAIL_VIEW_ID
    )

    val userNameError = getErrorDetails(
        uiState.listOtherInputClientErrorCode,
        Constants.ViewID.USER_NAME_VIEW_ID
    )

    val idNumberError = getErrorDetails(
        uiState.listOtherInputClientErrorCode,
        Constants.ViewID.ID_NUMBER_VIEW_ID
    )

    val expirationDateError = getErrorDetails(
        uiState.listOtherInputClientErrorCode,
        Constants.ViewID.EXPIRATION_DATE_VIEW_ID
    )

    val registrationNumberError = getErrorDetails(
        uiState.listOtherInputClientErrorCode,
        Constants.ViewID.REGISTRATION_NUMBER_VIEW_ID
    )

    Column(
        modifier = Modifier
            .fillMaxSize(),
        verticalArrangement = Arrangement.Top,
        horizontalAlignment = Alignment.Start
    ) {
        Column(
            modifier = Modifier
                .weight(1f)
                .padding(horizontal = IBSpacing.spacingM)
                .verticalScroll(rememberScrollState())
                .imePadding(),
        ) {
            Spacer(modifier = Modifier.height(IBSpacing.spacingS))
            InlineMessage(
                status = InlineMessageStatus.Brand(LocalColorScheme.current),
                message = stringResource(R.string.quy_khach_vui_long_cung_cap_cac_thong_tin_can_thiet_de_xac_thuc_yeu_cau),
            )
            Spacer(modifier = Modifier.height(IBSpacing.spacingS))
            IBankInputFieldBase(
                placeholderText = stringResource(R.string.ten_dang_nhap),
                text = uiState.username,
                maxLengthText = 30,
                state = userNameError.state,
                helpTextLeft = userNameError.message,
                filters = listOf(
                    VietnameseAccentRemoverFilter(),
                    RemoveSpaceFilter(),
                    SpecialCharacterFilter(),
                    UpperCaseFilter(),
                ),
                onClickClear = {
                    onEvent(
                        ForgotPasswordViewEvent.OnInputUsernameChangeEvent(username = "")
                    )
                },
                onValueChange = {
                    onEvent(
                        ForgotPasswordViewEvent.OnInputUsernameChangeEvent(
                            username = it.text
                        )
                    )
                }
            )
            Spacer(modifier = Modifier.height(IBSpacing.spacingM))
            IBankInputFieldBase(
                placeholderText = stringResource(R.string.so_cccdho_chieu),
                text = uiState.idNumber,
                maxLengthText = 12,
                state = idNumberError.state,
                helpTextLeft = idNumberError.message,
                filters = listOf(
                    RemoveSpaceFilter(),
                    UpperCaseFilter(),
                    VietnameseAccentRemoverFilter(),
                    RemoveSpaceFilter(),
                    SpecialCharacterFilter()
                ),
                onClickClear = {
                    onEvent(
                        ForgotPasswordViewEvent.OnInputIdNumberChangeEvent(idNumber = "")
                    )
                },
                onValueChange = {
                    onEvent(
                        ForgotPasswordViewEvent.OnInputIdNumberChangeEvent(
                            idNumber = it.text
                        )
                    )
                }
            )
            Spacer(modifier = Modifier.height(IBSpacing.spacingM))
            Text(
                stringResource(R.string.ngay_het_han_cccdho_chieu),
                textAlign = TextAlign.Start,
                color = colorScheme.contentMainPrimary,
                style = typography.bodyBody_l
            )
            Spacer(modifier = Modifier.height(IBSpacing.spacingXs))
            Box(modifier = Modifier.noRippleClickable {
                if (uiState.isIndefinite) {
                    onEvent(
                        ForgotPasswordViewEvent.OnInputIsIndefiniteChangeEvent(
                            isIndefinite = false
                        )
                    )
                }
            }) {
                IBankRadioButtonWithText(
                    modifier = Modifier.noRippleClickable {
                        if (uiState.isIndefinite) {
                            onEvent(
                                ForgotPasswordViewEvent.OnInputIsIndefiniteChangeEvent(
                                    isIndefinite = false
                                )
                            )
                        }
                    },
                    checked = !uiState.isIndefinite,
                    textAlignLeft = false,
                    content = stringResource(R.string.co_xac_dinh_thoi_han),
                ) {
                    if (it) {
                        onEvent(
                            ForgotPasswordViewEvent.OnInputIsIndefiniteChangeEvent(
                                isIndefinite = false
                            )
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(IBSpacing.spacingS))

            Box(modifier = Modifier.noRippleClickable {
                if (!uiState.isIndefinite) {
                    onEvent(
                        ForgotPasswordViewEvent.OnInputIsIndefiniteChangeEvent(
                            isIndefinite = true
                        )
                    )
                }
            }) {
                IBankRadioButtonWithText(
                    modifier = Modifier.noRippleClickable {
                        if (!uiState.isIndefinite) {
                            onEvent(
                                ForgotPasswordViewEvent.OnInputIsIndefiniteChangeEvent(
                                    isIndefinite = true
                                )
                            )
                        }
                    },
                    checked = uiState.isIndefinite,
                    textAlignLeft = false,
                    content = stringResource(R.string.khong_xac_dinh_thoi_han),
                ) {
                    if (it) {
                        onEvent(
                            ForgotPasswordViewEvent.OnInputIsIndefiniteChangeEvent(
                                isIndefinite = true
                            )
                        )
                    }
                }
            }
            if (!uiState.isIndefinite){
                Spacer(modifier = Modifier.height(IBSpacing.spacingS))
                IBankInputDatePicker(
                    labelText = stringResource(R.string.ngay_het_han_cccdho_chieu),
                    text = uiState.expirationDate,
                    state = if (uiState.expirationDate.isNotEmpty()) {
                        IBFrameExtendState.FILLED(colorScheme)
                    } else {
                        expirationDateError.state
                    },
                    onClickEnd = {
                        isShowCalendar.value = true
                    },
                    onClickClear = {
                        onEvent(
                            ForgotPasswordViewEvent.OnInputExpirationDateChangeEvent(
                                expirationDate = ""
                            )
                        )
                    },
                    hintTextStart = expirationDateError.message,
                )
            }
            Spacer(modifier = Modifier.height(IBSpacing.spacingM))
            Row(
                modifier = Modifier.wrapContentSize(),
                horizontalArrangement = Arrangement.Start,
                verticalAlignment = Alignment.Bottom
            ) {

                val richTextContent = buildAnnotatedString {

                    withStyle(
                        style = textStyleToSpanStyle(
                            typography.labelLabel_l,
                            colorScheme.contentOn_specialPrimary
                        )
                    ) {
                        append("• ${navController.context.getString(R.string.nguoi_viet_nam)}: ")
                    }
                    withStyle(
                        style = textStyleToSpanStyle(
                            typography.bodyBody_m,
                            colorScheme.contentOn_specialPrimary
                        )
                    ) {
                        append(navController.context.getString(R.string.so_dien_thoai))
                    }

                    append("\n")

                    withStyle(
                        style = textStyleToSpanStyle(
                            typography.labelLabel_l,
                            colorScheme.contentOn_specialPrimary
                        )
                    ) {
                        append("• ${navController.context.getString(R.string.nguoi_nuoc_ngoai)}: ")
                    }
                    withStyle(
                        style = textStyleToSpanStyle(
                            typography.bodyBody_m,
                            colorScheme.contentOn_specialPrimary
                        )
                    ) {
                        append(navController.context.getString(R.string.so_dien_thoai_hoac_email))
                    }

                    append("\n")

                    withStyle(
                        style = textStyleToSpanStyle(
                            typography.bodyBody_m,
                            colorScheme.contentOn_specialSecondary
                        )
                    ) {
                        append("  ${navController.context.getString(R.string.chi_chon_email_khi_khong_dang_ky_so_dien_thoai_voi_bidv)}")
                    }
                }

                Text(
                    text = stringResource(R.string.phuong_thuc_nhan_otp_da_dang_ky_voi_bidv),
                    color = colorScheme.contentMainPrimary,
                    style = typography.bodyBody_m,
                    textAlign = TextAlign.Start,
                    overflow = TextOverflow.Ellipsis
                )
                Spacer(modifier = Modifier.width(IBSpacing.spacing2xs))
                IBankTooltip(
                    isSupportingText = true,
                    subContent = {
                        Text(
                            text = richTextContent,
                            lineHeight = typography.bodyBody_m.lineHeight,
                        )
                    },
                    showCloseButton = false,
                    tooltipPosition = TooltipPosition.ABOVE,
                    showIconView = { modifier ->
                        Icon(
                            painter = painterResource(id = drawable.information_circle),
                            contentDescription = "Tooltip Icon",
                            tint = colorScheme.contentMainTertiary,
                            modifier = modifier
                                .size(20.dp)
                        )
                    }
                )

            }
            Spacer(modifier = Modifier.height(IBSpacing.spacingS))
            Box(modifier = Modifier.noRippleClickable {
                if (uiState.methodOtp == MethodOtp.EMAIL) {
                    onEvent(
                        ForgotPasswordViewEvent.OnInputMethodOtpChangeEvent(
                            methodOtp = MethodOtp.SMS
                        )
                    )
                }
            }) {
                IBankRadioButtonWithText(
                    modifier = Modifier.noRippleClickable {
                        if (uiState.methodOtp == MethodOtp.EMAIL) {
                            onEvent(
                                ForgotPasswordViewEvent.OnInputMethodOtpChangeEvent(
                                    methodOtp = MethodOtp.SMS
                                )
                            )
                        }
                    },
                    checked = uiState.methodOtp == MethodOtp.SMS,
                    textAlignLeft = false,
                    content = stringResource(R.string.so_dien_thoai),
                ) {
                    if (it) {
                        onEvent(
                            ForgotPasswordViewEvent.OnInputMethodOtpChangeEvent(
                                methodOtp = MethodOtp.SMS
                            )
                        )
                    }
                }
            }
            Spacer(modifier = Modifier.height(IBSpacing.spacingM))
            Box(modifier = Modifier.noRippleClickable {
                if (uiState.methodOtp == MethodOtp.SMS) {
                    onEvent(
                        ForgotPasswordViewEvent.OnInputMethodOtpChangeEvent(
                            methodOtp = MethodOtp.EMAIL
                        )
                    )
                }
            }) {
                IBankRadioButtonWithText(
                    modifier = Modifier.noRippleClickable {
                        if (uiState.methodOtp == MethodOtp.SMS) {
                            onEvent(
                                ForgotPasswordViewEvent.OnInputMethodOtpChangeEvent(
                                    methodOtp = MethodOtp.EMAIL
                                )
                            )
                        }
                    },
                    checked = uiState.methodOtp == MethodOtp.EMAIL,
                    textAlignLeft = false,
                    content = stringResource(R.string.email),
                ) {
                    if (it) {
                        onEvent(
                            ForgotPasswordViewEvent.OnInputMethodOtpChangeEvent(
                                methodOtp = MethodOtp.EMAIL
                            )
                        )
                    }
                }
            }
            Spacer(modifier = Modifier.height(IBSpacing.spacingM))
            if (uiState.methodOtp == MethodOtp.SMS) {
                IBankInputFieldBase(
                    placeholderText = stringResource(R.string.so_dien_thoai),
                    text = uiState.contactInfo,
                    inputType = KeyboardOptions(keyboardType = KeyboardType.Phone),
                    maxLengthText = 10,
                    filters = listOf(
                        RemoveSpaceFilter(),
                        SpecialCharacterFilter(),
                        RemoveTextFilter()
                    ),
                    state = phoneNumberError.state,
                    helpTextLeft = phoneNumberError.message,
                    onFocusChange = {
                        if (!it) {
                            onEvent(
                                ForgotPasswordViewEvent.OnInputContactInfoValidEvent
                            )
                        }
                    },
                    onClickClear = {
                        onEvent(
                            ForgotPasswordViewEvent.OnInputContactInfoChangeEvent(contactInfo = "")
                        )
                    },
                    onValueChange = {
                        onEvent(
                            ForgotPasswordViewEvent.OnInputContactInfoChangeEvent(
                                contactInfo = it.text
                            )
                        )
                    }
                )
            } else {
                IBankInputFieldBase(
                    placeholderText = stringResource(R.string.email_nhan_otp),
                    text = uiState.contactInfo,
                    inputType = KeyboardOptions(keyboardType = KeyboardType.Email),
                    maxLengthText = 50,
                    filters = listOf(
                        VietnameseAccentRemoverFilter(),
                        RemoveSpaceFilter(),
                        InputFieldEmailFilter()
                    ),
                    state = emailError.state,
                    helpTextLeft = emailError.message,
                    onFocusChange = {
                        if (!it) {
                            onEvent(
                                ForgotPasswordViewEvent.OnInputContactInfoValidEvent
                            )
                        }
                    },
                    onClickClear = {
                        onEvent(
                            ForgotPasswordViewEvent.OnInputContactInfoChangeEvent(contactInfo = "")
                        )
                    },
                    onValueChange = {
                        onEvent(
                            ForgotPasswordViewEvent.OnInputContactInfoChangeEvent(
                                contactInfo = it.text
                            )
                        )
                    }
                )
            }
            Spacer(modifier = Modifier.height(IBSpacing.spacingM))
            IBankInputFieldBase(
                placeholderText = stringResource(R.string.so_dang_ky_kinh_doanh_doanh_nghiep),
                text = uiState.registrationNumber,
                maxLengthText = 20,
                state = registrationNumberError.state,
                helpTextLeft = registrationNumberError.message,
                onClickClear = {
                    onEvent(
                        ForgotPasswordViewEvent.OnInputRegistrationNumberChangeEvent(
                            registrationNumber = ""
                        )
                    )
                },
                onValueChange = {
                    onEvent(
                        ForgotPasswordViewEvent.OnInputRegistrationNumberChangeEvent(
                            registrationNumber = it.text
                        )
                    )
                }
            )
            Spacer(modifier = Modifier.height(IBSpacing.spacingM))

        }



        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(color = LocalColorScheme.current.bgMainTertiary),
        ) {
            IBankActionBar(modifier = Modifier,
                isVertical = false,
                buttonNegative = DialogButtonInfo(stringResource(R.string.thoat),
                    onClick = {
                        navController.popBackStack()
                    }),
                buttonPositive = DialogButtonInfo(stringResource(R.string.tiep_tuc),
                    onClick = {
                        onEvent(ForgotPasswordViewEvent.ValidateDataEvent)
                    }

                )
            )
        }
    }

    if (isShowCalendar.value) {
        IBankDatePickerDialog(
            modifier = Modifier,
            dateSelected = null,
            title = stringResource(R.string.chon_ngay),
            config = DatePickerConfig.build().copy(
                minDate = getStartDate(Date())
            ),
            negativeButtonText = stringResource(R.string.huy),
            onDateSelected = {
                onEvent(
                    ForgotPasswordViewEvent.OnInputExpirationDateChangeEvent(
                        expirationDate = it?.dateToString() ?: ""
                    )
                )
            },
            onDismissRequest = {
                isShowCalendar.value = false
            }
        )
    }

}

fun textStyleToSpanStyle(textStyle: TextStyle, color: Color): SpanStyle {
    return SpanStyle(
        fontSize = textStyle.fontSize,
        textDecoration = textStyle.textDecoration,
        fontFamily = textStyle.fontFamily,
        fontWeight = textStyle.fontWeight,
        fontStyle = textStyle.fontStyle,
        letterSpacing = textStyle.letterSpacing,
        color = color
    )
}

fun textStyleToParagraphStyle(textStyle: TextStyle): ParagraphStyle {
    return ParagraphStyle(
        lineHeight = textStyle.lineHeight
    )
}

@Preview
@Composable
fun PreviewForgotPasswordScreen() {
    ForgotPasswordContent(
        uiState = ForgotPasswordViewState(),
        navController = NavHostController(LocalContext.current),
        onEvent = {}
    )
}
