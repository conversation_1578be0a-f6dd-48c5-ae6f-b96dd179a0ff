package vn.com.bidv.feature.login.navigation

import androidx.compose.ui.res.stringResource
import com.google.gson.Gson
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionFlowScreenBuilder
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionTypeConstant
import vn.com.bidv.feature.login.domain.PositiveChangePwVerifyUseCase
import vn.com.bidv.feature.login.domain.VerifyPwOtpErrorType
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.navigation.IBankMainRouting
import vn.com.bidv.sdkbase.navigation.NavigationHelper
import javax.inject.Inject

class PositiveChangePwVerifyFlowScreenBuilder @Inject constructor(
    useCase: PositiveChangePwVerifyUseCase,
) : VerifyTransactionFlowScreenBuilder(
    useCase = useCase,
    type = VerifyTransactionTypeConstant.ChangePassword,
    isShowResultPopup = true,
    content = { navController, data, _ ->
        val isSuccess = runCatching {
            Gson().fromJson(data, Boolean::class.java)
        }.getOrDefault(false)

        val error = runCatching {
            Gson().fromJson(data, DomainResult.Error::class.java)
        }.getOrNull()
        if (isSuccess) {
            IBankModalConfirm(title = stringResource(R.string.doi_mat_khau_thanh_cong),
                isShowIconClose = true,
                modalConfirmType = ModalConfirmType.Success,
                supportingText = navController.context.getString(R.string.quy_khach_vui_long_dang_nhap_lai_de_tiep_tuc_su_dung_dich_vu),
                listDialogButtonInfo = listOf(
                    DialogButtonInfo(
                        label = stringResource(R.string.dang_nhap_lai),
                    )
                ),
                onDismissRequest = {
                    NavigationHelper.navigationToLogin(navController)
                })

        } else {
            IBankModalConfirm(
                title = stringResource(R.string.doi_mat_khau_khong_thanh_cong),
                modalConfirmType = ModalConfirmType.Error,
                supportingText = error?.errorMessage
                    ?: navController.context.getString(R.string.co_loi_xay_ra_quy_khach_vui_long_thu_lai),
                listDialogButtonInfo = listOf(
                    DialogButtonInfo(
                        label = stringResource(R.string.close),
                    )
                ),
                onDismissRequest = {
                    if (error?.errorCode == VerifyPwOtpErrorType.SMS_OTP_BLOCK) {
                        navController.popBackStack(
                            IBankMainRouting.AuthRoutes.PositiveChangePwRoute.route,
                            inclusive = true
                        )
                    } else navController.popBackStack(
                        route = IBankMainRouting.CommonRoute.VerifyByTypeTransactionRoute.routeWithArgument,
                        true
                    )
                }
            )
        }

    })