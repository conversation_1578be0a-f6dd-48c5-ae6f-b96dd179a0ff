package vn.com.bidv.feature.login.domain

import android.content.Context
import androidx.biometric.BiometricPrompt
import com.google.gson.Gson
import dagger.hilt.android.qualifiers.ApplicationContext
import vn.com.bidv.feature.login.constants.BiometricError
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.common.constants.Constants as CommonConstants
import vn.com.bidv.feature.login.data.BiometricRepository
import vn.com.bidv.feature.login.domain.model.BiometricData
import vn.com.bidv.feature.login.domain.model.BiometricOnRequestDMO
import vn.com.bidv.feature.login.domain.model.CompositeOtpResDMO
import vn.com.bidv.sdkbase.data.LocalRepository
import vn.com.bidv.sdkbase.data.ShareDataDTO
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import vn.com.bidv.secure.utils.BiometricUtils
import java.security.KeyPair
import java.util.UUID
import javax.inject.Inject

class BiometricUseCase @Inject constructor(
    private val biometricRepository: BiometricRepository,
    @ApplicationContext private val context: Context,
    private val localRepository: LocalRepository
) {

    private var credentialId: String = ""
    private var keyPair: KeyPair? = null

    init {
        credentialId = UUID.randomUUID().toString().replace("-", "")
        keyPair = BiometricUtils.generateKeyPair()
    }

    fun createBiometricOnRequest(): BiometricOnRequestDMO {
        val publicKey = keyPair?.public?.let { BiometricUtils.keyToBase64(it) } ?: ""
        return BiometricOnRequestDMO(
            credentialId = credentialId,
            type = Constants.Biometric.BIOMETRIC_TOUCH_ID,
            publicKey = publicKey
        )
    }

    fun checkBiometricAvailable(): DomainResult<Boolean> {
        val isBiometricAvailable = BiometricUtils.isBiometricAvailable(context)
        if (isBiometricAvailable) {
            return DomainResult.Success(BiometricUtils.isBiometricAvailable(context))
        }
        return DomainResult.Error(
            errorCode = BiometricError.FACE_TOUCH_ID_01.name
        )
    }

    suspend fun turnOnBiometric(): DomainResult<CompositeOtpResDMO> {
        val publicKey = keyPair?.public?.let { BiometricUtils.keyToBase64(it) } ?: ""
        val result = biometricRepository.turnOnBiometric(
            credentialId = credentialId,
            type = Constants.Biometric.BIOMETRIC_TOUCH_ID,
            publicKey = publicKey
        )
        val domain = result.convert(CompositeOtpResDMO::class.java)
        return domain
    }

    fun verifyOtpBiometricSuccess(authenticationResult: BiometricPrompt.AuthenticationResult?): DomainResult<Boolean> {
        val biometricData = BiometricData(
            pr = BiometricUtils.keyToBase64(keyPair?.private ?: return DomainResult.Error("")),
            cr = credentialId,
        )
        BiometricUtils.saveBiometricData(
            Gson().toJson(biometricData),
            authenticationResult?.cryptoObject?.cipher ?: return DomainResult.Error("")
        )
        return DomainResult.Success(true)
    }

    suspend fun shareBiometricAction(action: String) {
        localRepository.shareDataTo(
            key = CommonConstants.Biometric.BIOMETRIC_ACTION_SUCCESS,
            data = ShareDataDTO(
                key = action,
                data = action
            )
        )
    }

    suspend fun turnOffBiometric(): DomainResult<Boolean> {
        val result = biometricRepository.turnOffBiometric()
        val domain = result.convert {
            isSuccess()
        }
        if (domain is DomainResult.Success) {
            BiometricUtils.removeBiometricData()
        }
        return domain
    }

    fun removeBiometricData() {
        BiometricUtils.removeBiometricData()
    }
}