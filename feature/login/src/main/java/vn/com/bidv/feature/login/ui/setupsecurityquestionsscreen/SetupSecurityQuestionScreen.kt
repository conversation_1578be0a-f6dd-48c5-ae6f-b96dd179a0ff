package vn.com.bidv.feature.login.ui.setupsecurityquestionsscreen

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.google.gson.Gson
import vn.com.bidv.common.extenstion.isNotNullOrEmpty
import vn.com.bidv.common.utils.CollectSideEffect
import vn.com.bidv.designsystem.component.IBankEmptyState
import vn.com.bidv.designsystem.component.dataentry.AllowSpecificCharactersFilter
import vn.com.bidv.designsystem.component.dataentry.IBFrameExtendState
import vn.com.bidv.designsystem.component.dataentry.IBFrameState
import vn.com.bidv.designsystem.component.dataentry.IBankDropDown
import vn.com.bidv.designsystem.component.dataentry.IBankInputPassword
import vn.com.bidv.designsystem.component.dataentry.RemoveDoubleSpaceFilter
import vn.com.bidv.designsystem.component.feedback.bottomsheet.IBankBottomSheet
import vn.com.bidv.designsystem.component.feedback.inlinemessage.InlineMessage
import vn.com.bidv.designsystem.component.feedback.inlinemessage.InlineMessageStatus
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.component.navigation.button.IBankNormalButton
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonSize
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonType
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankActionBar
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionTypeConstant
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.domain.model.CodeValueDMO
import vn.com.bidv.feature.login.navigation.NavigationHelper
import vn.com.bidv.feature.login.ui.setupsecurityinfo.requestConfirmModal.model.ModelRequestConfirm
import vn.com.bidv.feature.login.ui.setupsecurityquestionsscreen.SetupSecurityQuestionsReducer.SetupSecurityQuestionsSideEffect
import vn.com.bidv.feature.login.ui.setupsecurityquestionsscreen.SetupSecurityQuestionsReducer.SetupSecurityQuestionsViewEvent
import vn.com.bidv.feature.login.ui.setupsecurityquestionsscreen.SetupSecurityQuestionsReducer.SetupSecurityQuestionsViewState
import vn.com.bidv.feature.login.ui.setupsecurityquestionsscreen.model.QaModel
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.data.ReloadFunctionKey
import vn.com.bidv.sdkbase.data.ReloadKey
import vn.com.bidv.sdkbase.data.ReloadModuleKey
import vn.com.bidv.sdkbase.ui.component.snackbar.SnackBarPosition
import vn.com.bidv.sdkbase.ui.component.snackbar.pinSnackBarPosition
import vn.com.bidv.sdkbase.utils.VietnameseAccentRemoverFilter
import java.util.Locale
import vn.com.bidv.feature.common.navigation.NavigationHelper as CommonNavigation
import vn.com.bidv.sdkbase.navigation.NavigationHelper as SdkBaseNavigationHelper

enum class SetupQuestionsStatus {
    ONLY_VIEW,
    EDIT,
    CREATE
}

@Composable
fun SetupSecurityQuestionScreen(
    navController: NavHostController,
    modelRequestConfirm: ModelRequestConfirm? = null
) {
    val vm: SetupSecurityQuestionsContentViewModel = hiltViewModel()
    val onlyView = modelRequestConfirm?.modelLoginDMO?.isSecurityQuestionSet == true
    val status = remember { mutableStateOf(SetupQuestionsStatus.CREATE) }

    val isBottomSheetVisible = remember { mutableStateOf(false) }
    val selectedIndex: MutableState<Int> = remember { mutableIntStateOf(-1) }
    val messageVerifyError = remember { mutableStateOf("") }
    val messageVerifyTransactionExpiredError = remember { mutableStateOf("") }

    LaunchedEffect(Unit) {
        status.value = when {
            onlyView -> SetupQuestionsStatus.ONLY_VIEW
            else -> SetupQuestionsStatus.CREATE
        }
    }
    BaseScreen(
        navController = navController,
        viewModel = vm,
        backgroundColor = LocalColorScheme.current.bgMainTertiary,
        topAppBarConfig = TopAppBarConfig(
            onNavigationClick = {
                when (status.value) {
                    SetupQuestionsStatus.EDIT -> {
                        status.value = SetupQuestionsStatus.ONLY_VIEW
                    }

                    else -> navController.popBackStack()
                }
            },
            isShowNavigationIcon = when (status.value) {
                SetupQuestionsStatus.CREATE -> false
                else -> true
            },
            titleTopAppBar = when (status.value) {
                SetupQuestionsStatus.EDIT -> stringResource(R.string.chinh_sua_cau_hoi_bao_mat)
                SetupQuestionsStatus.ONLY_VIEW -> stringResource(R.string.danh_sach_cau_hoi_bao_mat)
                else -> stringResource(R.string.cai_dat_cau_hoi_bao_mat)
            },
            showHomeIcon = status.value != SetupQuestionsStatus.CREATE,
        ),
        renderContent = { uiState, onEvent ->

            CollectSideEffect(
                vm.subscribeReloadData(
                    ReloadKey(
                        ReloadModuleKey.LOGIN,
                        ReloadFunctionKey.EDIT_QUESTIONS
                    )
                )
            ) {
                if (vm.checkReloadData(it)) {
                    navController.popBackStack()
                }
            }

            LaunchedEffect(true) {
                onEvent(
                    SetupSecurityQuestionsViewEvent.InitEvent(
                        initData = modelRequestConfirm?.listQuestion ?: emptyList(
                        )
                    )
                )
            }

            if (messageVerifyError.value.isNotEmpty()) {
                IBankModalConfirm(
                    title = stringResource(R.string.loi),
                    modalConfirmType = ModalConfirmType.Error,
                    supportingText = messageVerifyError.value,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(R.string.close),
                        )
                    ),
                    onDismissRequest = {
                        messageVerifyError.value = ""
                    }
                )
            }

            if (messageVerifyTransactionExpiredError.value.isNotEmpty()) {
                IBankModalConfirm(
                    title = stringResource(R.string.loi),
                    modalConfirmType = ModalConfirmType.Error,
                    supportingText = messageVerifyTransactionExpiredError.value,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(R.string.thu_lai),
                            onClick = {
                                onEvent(SetupSecurityQuestionsViewEvent.SubmitDataWithAuthenticationEvent)
                            }
                        )
                    ),
                    onDismissRequest = {
                        messageVerifyTransactionExpiredError.value = ""
                    }
                )
            }

            if (status.value == SetupQuestionsStatus.ONLY_VIEW) {
                onEvent(
                    SetupSecurityQuestionsViewEvent.BackToOnlyViewEvent
                )
            }

            Content(
                navController,
                uiState,
                onEvent,
                status = status.value,
                onAnswerChange = { index ->

                    if (uiState.dataListQuestion.isEmpty()) {
                        onEvent(SetupSecurityQuestionsViewEvent.InitEvent())
                    } else {
                        isBottomSheetVisible.value = true
                    }
                    selectedIndex.value = index
                },
                onEdit = {
                    if (status.value == SetupQuestionsStatus.ONLY_VIEW) {
                        status.value = SetupQuestionsStatus.EDIT
                    }
                }
            )

            if (isBottomSheetVisible.value) {
                ShowBottomSheet(
                    title = "${stringResource(R.string.cau_hoi)} ${selectedIndex.value + 1}",
                    selectedItem = uiState.data[selectedIndex.value],
                    listQuestion = uiState.dataListQuestionCanSelect(selectedIndex.value),
                    onDismiss = {
                        isBottomSheetVisible.value = false
                        if (it != null) {
                            onEvent(
                                SetupSecurityQuestionsViewEvent.QuestionChangeEvent(
                                    selectedIndex.value,
                                    it
                                )
                            )
                        }
                    }
                )
            }

        },
        handleSideEffect = { sideEffect ->

            if (sideEffect is SetupSecurityQuestionsSideEffect.GetAllQuestionSuccessEffect) {
                if (selectedIndex.value != -1) {
                    isBottomSheetVisible.value = true
                }
            }

            if (sideEffect is SetupSecurityQuestionsSideEffect.SubmitDataSuccessEffect) {
                val smartOtp = modelRequestConfirm?.modelLoginDMO?.smartOtpDMO
                when {
                    smartOtp?.isNeedConvertActiveSmartOtp == true -> {
                        CommonNavigation.navigateToConvertSmartOtp(navController)
                    }

                    modelRequestConfirm?.modelLoginDMO?.isShowSmartOtpScreen == true -> {
                        CommonNavigation.navigateToActiveSmartOtp(
                            navController,
                            smartOtp
                        )
                    }

                    else -> {
                        NavigationHelper.navigateToUserInfoScreen(navController)
                    }
                }

            }

            if (sideEffect is SetupSecurityQuestionsSideEffect.SubmitDataWithAuthenticationSuccessEffect) {
                CommonNavigation.navigateToVerifyByTypeCreateTransaction(
                    navController = navController,
                    dataString = Gson().toJson(sideEffect.listSubmit),
                    type = VerifyTransactionTypeConstant.EditQuestions,
                )

            }

            if (sideEffect is SetupSecurityQuestionsSideEffect.ApproveSaveDataWithAuthenticationSuccessEffect) {
                navController.popBackStack()
            }

        }

    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ShowBottomSheet(
    title: String,
    selectedItem: QaModel?,
    listQuestion: List<CodeValueDMO>,
    onDismiss: ((itemSelected: CodeValueDMO?) -> Unit) = {},
) {

    val selectedValue = listQuestion.indexOfFirst { it.code == selectedItem?.question?.code }
    IBankBottomSheet(
        title = title,
        onDismiss = {
            onDismiss(null)
        },

        bottomSheetContent = {
            if (listQuestion.isEmpty())
                IBankEmptyState(
                    modifier = Modifier.fillMaxWidth(1 / 3f),
                    supportingText = stringResource(id = R.string.khong_co_du_lieu),
                    textButton = stringResource(id = R.string.dong),
                )
            else LazyColumn {
                items(listQuestion.size) { index ->
                    val item = listQuestion[index]

                    val value = item.value
                    if (value != null) {
                        val baseModifier = Modifier
                            .clickable {
                                onDismiss(item)
                            }
                            .padding(start = IBSpacing.spacingM, end = IBSpacing.spacingM)

                        Box(
                            modifier = if (index == selectedValue) {
                                baseModifier
                                    .fillMaxWidth()
                                    .background(
                                        color = LocalColorScheme.current.bgBrand_01Tertiary,
                                        shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL)
                                    )
                                    .wrapContentHeight()
                            } else {
                                baseModifier
                            }
                        ) {
                            Row(
                                horizontalArrangement = Arrangement.SpaceBetween,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(end = IBSpacing.spacingXs)
                            ) {
                                Text(
                                    text = value,
                                    style = LocalTypography.current.bodyBody_l,
                                    color = LocalColorScheme.current.contentMainPrimary,
                                    modifier = Modifier
                                        .wrapContentWidth()
                                        .padding(IBSpacing.spacingM)
                                )

                                if (index == selectedValue) {
                                    Icon(
                                        imageVector = ImageVector.vectorResource(id = vn.com.bidv.designsystem.R.drawable.check_outline),
                                        contentDescription = null,
                                        tint = LocalColorScheme.current.contentBrand_01Primary,
                                        modifier = Modifier
                                            .size(20.dp)
                                            .align(Alignment.CenterVertically)
                                    )
                                }
                            }
                        }
                    }
                }
            }

        }
    )

}

@Composable
private fun Content(
    navHostController: NavHostController,
    uiState: SetupSecurityQuestionsViewState,
    onEvent: (SetupSecurityQuestionsViewEvent) -> Unit,
    status: SetupQuestionsStatus = SetupQuestionsStatus.CREATE,
    onAnswerChange: (Int) -> Unit = {},
    onEdit: (() -> Unit) = {},
) {

    Column(
        modifier = Modifier
            .fillMaxSize(),
        verticalArrangement = Arrangement.Top,
        horizontalAlignment = Alignment.Start
    ) {
        Column(
            modifier = Modifier
                .weight(1f)
                .verticalScroll(rememberScrollState())
                .imePadding()
        ) {
            Spacer(modifier = Modifier.height(IBSpacing.spacingS))
            InlineMessage(
                modifier = Modifier.padding(horizontal = IBSpacing.spacingM),
                status = InlineMessageStatus.Brand(LocalColorScheme.current),
                message = if (status == SetupQuestionsStatus.CREATE) stringResource(R.string.truong_hop_quen_mat_khau_chung_toi_se_yeu_cau_quy_khach_tra_loi_cau_hoi_bao_mat_de_xac_minh_danh_tinh) else stringResource(
                    R.string.cau_hoi_bao_mat_se_duoc_su_dung_khi_yeu_cau_cap_lai_mat_khau_online_quy_khach_vui_long_ghi_nho_cau_tra_loi_cho_3_cau_hoi_da_cai_dat_khong_tiet_lo_cau_tra_loi_de_tranh_bi_ke_gian_loi_dung
                ),
            )
            Spacer(modifier = Modifier.height(IBSpacing.spacingL))
            repeat(uiState.data.size) { index ->
                ItemQuestion(
                    title = "${stringResource(R.string.cau_hoi)} ${index + 1}",
                    qaModel = uiState.data[index].copy(editAble = status != SetupQuestionsStatus.ONLY_VIEW),
                    questionViewId = Constants.ViewID.QUESTION_VIEW_ID + index,
                    answerViewId = Constants.ViewID.ANSWER_VIEW_ID + index,
                    onChoiceRequest = {
                        onAnswerChange(index)
                    },
                    onAnswerChange = { newValue ->
                        onEvent(
                            SetupSecurityQuestionsViewEvent.AnswerChangeEvent(
                                index,
                                newValue
                            )
                        )
                    }
                )
                Spacer(modifier = Modifier.height(IBSpacing.spacingL))
            }
        }

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(color = LocalColorScheme.current.bgMainTertiary),
        ) {

            when (status) {
                SetupQuestionsStatus.ONLY_VIEW -> {
                    IBankNormalButton(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = IBSpacing.spacingM),
                        size = NormalButtonSize.L(LocalTypography.current),
                        type = NormalButtonType.PRIMARY(LocalColorScheme.current),
                        leadingIcon = ImageVector.vectorResource(id = vn.com.bidv.designsystem.R.drawable.edit),
                        text = stringResource(R.string.chinh_sua),
                        onClick = onEdit,
                    )
                }

                SetupQuestionsStatus.EDIT -> {
                    IBankNormalButton(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = IBSpacing.spacingM)
                            .pinSnackBarPosition(snackBarPosition = SnackBarPosition.TopOut),
                        size = NormalButtonSize.L(LocalTypography.current),
                        type = NormalButtonType.PRIMARY(LocalColorScheme.current),
                        text = stringResource(R.string.luu),
                        onClick = {
                            onEvent(SetupSecurityQuestionsViewEvent.SubmitDataWithAuthenticationEvent)
                        },
                    )
                }

                SetupQuestionsStatus.CREATE -> {
                    IBankActionBar(
                        modifier = Modifier,
                        isVertical = false,
                        buttonNegative = DialogButtonInfo(stringResource(R.string.thoat),
                            onClick = {
                                SdkBaseNavigationHelper.navigationToLogin(navHostController)
                            }
                        ),
                        buttonPositive = DialogButtonInfo(stringResource(R.string.tiep_tuc),
                            onClick = {
                                onEvent(SetupSecurityQuestionsViewEvent.SubmitDataEvent)
                            }

                        )
                    )
                }
            }

        }
    }
}

@Composable
private fun getErrorDetails(
    listClientErrorCode: List<Pair<String, String>>?,
    viewIdError: String
): Pair<IBFrameState, String> {
    val hasError = listClientErrorCode?.any { it.first == viewIdError } == true
    val state =
        if (hasError) IBFrameState.ERROR(LocalColorScheme.current) else IBFrameExtendState.FILLED(
            LocalColorScheme.current
        )
    val message = if (hasError) stringResource(R.string.vui_long_nhap_thong_tin) else ""
    return state to message
}

@Composable
private fun ItemQuestion(
    title: String,
    qaModel: QaModel,
    questionViewId: String,
    answerViewId: String,
    onChoiceRequest: () -> Unit,
    onAnswerChange: (String) -> Unit,
) {
    val colorScheme = LocalColorScheme.current

    val (questionState, questionMessage) = getErrorDetails(qaModel.listErrorCode, questionViewId)
    val (answerState, answerMessage) = getErrorDetails(qaModel.listErrorCode, answerViewId)


    Column(
        modifier = Modifier
            .wrapContentHeight()
            .padding(horizontal = IBSpacing.spacingM),
        horizontalAlignment = Alignment.Start
    ) {
        Text(text = title, style = LocalTypography.current.titleTitle_s)
        Spacer(modifier = Modifier.height(IBSpacing.spacingXs))
        IBankDropDown(
            state =
            if (qaModel.editAble) {
                if (qaModel.question.value.isNullOrEmpty() && qaModel.listErrorCode.isNullOrEmpty())
                    IBFrameState.DEFAULT(colorScheme)
                else questionState
            } else IBFrameState.DISABLE(
                colorScheme
            ),
            hintTextStart = questionMessage,
            labelText = if (qaModel.question.value.isNotNullOrEmpty()) "${qaModel.question.value}" else stringResource(
                R.string.lua_chon_cau_hoi
            ),
            modifier = Modifier
                .align(Alignment.Start),
            onClickEnd = {
                if (qaModel.editAble) {
                    onChoiceRequest()
                }
            }
        )

        Spacer(modifier = Modifier.height(IBSpacing.spacingS))
        IBankInputPassword(
            state = if (qaModel.editAble) answerState else IBFrameState.DISABLE(
                colorScheme
            ),
            helpTextLeft = answerMessage,
            text = (qaModel.answerTextFieldValue?.uppercase(Locale.getDefault()) ?: ""),
            placeholderText = stringResource(R.string.nhap_cau_tra_loi),
            maxLengthText = 30,
            filters = listOf(
                VietnameseAccentRemoverFilter(),
                RemoveDoubleSpaceFilter(),
                AllowSpecificCharactersFilter()
            ),
            onClickClear = {
                onAnswerChange("")
            },
            onValueChange = { newValue ->
                onAnswerChange(newValue.text.uppercase(Locale.getDefault()).trim())
            },
        )
    }
}


