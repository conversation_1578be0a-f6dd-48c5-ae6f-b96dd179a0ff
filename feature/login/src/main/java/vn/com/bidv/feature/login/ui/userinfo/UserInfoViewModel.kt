package vn.com.bidv.feature.login.ui.userinfo

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.feature.login.domain.UserInfoSettingUseCase
import vn.com.bidv.feature.login.ui.userinfo.UserInfoReducer.UserInfoViewEffect
import vn.com.bidv.feature.login.ui.userinfo.UserInfoReducer.UserInfoViewEvent
import vn.com.bidv.feature.login.ui.userinfo.UserInfoReducer.UserInfoViewState
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class UserInfoViewModel @Inject constructor(
    private val userInfoSettingUseCase: UserInfoSettingUseCase,
    private val userInfoUseCase: UserInfoUseCase
) : ViewModelIBankBase<UserInfoViewState, UserInfoViewEvent, UserInfoViewEffect>(
    initialState = UserInfoViewState(),
    reducer = UserInfoReducer()
) {
    override fun handleEffect(
        sideEffect: UserInfoViewEffect,
        onResult: (UserInfoViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is UserInfoViewEffect.GetUserInfoSettings -> {
                callDomain(
                    isListenAllError = true,
                    onSuccess = { result ->
                        val userInfoSettingsDMO = result.data
                        onResult(
                            UserInfoViewEvent.OnGetUserInfoSettingSuccess(userInfoSettingsDMO),
                        )
                    },
                    onFail = {
                        onResult(UserInfoViewEvent.OnGetUserInfoSettingFail(it?.errorMessage))
                    }
                ) {
                    userInfoSettingUseCase.getUserInfoSettings()
                }

            }

            is UserInfoViewEffect.GetUserInfoSecure -> {
                val userInfo = userInfoUseCase.getUserInfoFromStorage().getSafeData()?.user
                if (userInfo?.isHaveAuthMethod() == true) {
                    onResult(
                        UserInfoViewEvent.OnPositivePerformStrongAuth
                    )
                } else {
                    callDomain(
                        onSuccess = { result ->
                            onResult(
                                UserInfoViewEvent.OnGetUserInfoSecureSuccess(result.data, userInfo)
                            )
                        },
                    ) {
                        userInfoSettingUseCase.getUserInfoSettingSecure()
                    }
                }

            }

            is UserInfoViewEffect.ApprovalTransaction -> {
                callDomain(
                    onSuccess = { result ->
                        onResult(
                            UserInfoViewEvent.OnApprovalTransactionSuccess(result.data)
                        )
                    }
                ) {
                    userInfoSettingUseCase.verifySmartOtpUserInfo(
                        sideEffect.transId,
                        sideEffect.otp,
                    )
                }
            }

            else -> {}
        }
    }
}
