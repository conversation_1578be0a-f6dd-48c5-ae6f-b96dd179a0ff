@Suppress("DSL_SCOPE_VIOLATION") // TODO: Remove once KTIJ-19369 is fixed
plugins {
    alias(libs.plugins.local.ibank.feature)
    alias(libs.plugins.kotlin.serialization)
}

android {
    namespace = "vn.com.bidv.feature.login"
}

dependencies {
    implementation(libs.gson)
    implementation(libs.core.network)
    implementation(project(":feature:common"))
//    implementation(project(":core:private:network"))
    implementation(libs.androidx.compose.ui.tooling)
    implementation(libs.retrofit.core)
    implementation(libs.kotlinx.serialization.json)
    implementation(libs.core.secure)
//    implementation(project(":core:private:secure"))
    implementation(libs.biometric)
    implementation(libs.play.services.auth)
    implementation(libs.play.services.auth.api.phone)
    implementation(libs.firebase.cloud.messaging)
    implementation(platform(libs.firebase.bom))
}