<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ucrop_photobox"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <FrameLayout
        android:id="@+id/ucrop_frame"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginBottom="-12dp">

        <ImageView
            android:id="@+id/image_view_logo"
            android:layout_width="@dimen/ucrop_default_crop_logo_size"
            android:layout_height="@dimen/ucrop_default_crop_logo_size"
            android:layout_gravity="center"
            app:srcCompat="@drawable/ucrop_vector_ic_crop"
            tools:background="@drawable/ucrop_vector_ic_crop"
            tools:ignore="ContentDescription,MissingPrefix" />

        <com.yalantis.ucrop.view.UCropView
            android:id="@+id/ucrop"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:alpha="0" />

    </FrameLayout>


    <Button
        android:id="@+id/btn_close"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/ucrop_ic_btn_close"
        android:contentDescription=" "
        android:stateListAnimator="@null"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/btn_crop"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/ucrop_ic_btn_check"
        android:contentDescription=" "
        android:stateListAnimator="@null"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/controls_wrapper"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>
