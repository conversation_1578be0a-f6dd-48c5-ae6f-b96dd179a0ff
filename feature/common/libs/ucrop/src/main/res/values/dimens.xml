<resources>

    <!--uCrop Activity-->
    <dimen name="ucrop_padding_crop_frame">16dp</dimen>
    <dimen name="ucrop_size_dot_scale_text_view">8dp</dimen>
    <dimen name="ucrop_height_horizontal_wheel_progress_line">20dp</dimen>
    <dimen name="ucrop_width_horizontal_wheel_progress_line">2dp</dimen>
    <dimen name="ucrop_width_middle_wheel_progress_line">4dp</dimen>
    <dimen name="ucrop_margin_horizontal_wheel_progress_line">10dp</dimen>
    <dimen name="ucrop_height_wrapper_controls">64dp</dimen>
    <dimen name="ucrop_height_wrapper_states">72dp</dimen>
    <dimen name="ucrop_height_divider_shadow">3dp</dimen>
    <dimen name="ucrop_text_size_widget_text">13sp</dimen>
    <dimen name="ucrop_text_size_controls_text">11sp</dimen>
    <dimen name="ucrop_margin_top_widget_text">10dp</dimen>
    <dimen name="ucrop_margin_top_controls_text">4dp</dimen>
    <dimen name="ucrop_size_wrapper_rotate_button">50dp</dimen>
    <dimen name="ucrop_height_crop_aspect_ratio_text">40dp</dimen>
    <dimen name="ucrop_progress_size">30dp</dimen>

    <!--Crop View-->
    <dimen name="ucrop_default_crop_logo_size">200dp</dimen>
    <dimen name="ucrop_default_crop_grid_stoke_width">1dp</dimen>
    <dimen name="ucrop_default_crop_frame_stoke_width">1dp</dimen>
    <dimen name="ucrop_default_crop_rect_corner_touch_threshold">30dp</dimen>
    <dimen name="ucrop_default_crop_rect_min_size">100dp</dimen>
    <dimen name="ucrop_default_crop_rect_corner_touch_area_line_length">10dp</dimen>

</resources>
