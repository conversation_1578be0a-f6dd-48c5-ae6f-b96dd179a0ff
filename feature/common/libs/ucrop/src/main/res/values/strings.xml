<resources>

    <string name="ucrop_label_original">Original</string>
    <string name="ucrop_label_edit_photo">Edit Photo</string>

    <string name="ucrop_menu_crop">Crop</string>

    <string name="ucrop_error_input_data_is_absent" translatable="false">Both input and output Uri must be specified</string>
    <string name="ucrop_mutate_exception_hint" translatable="false">Therefore, override color resource (ucrop_color_toolbar_widget) in your app to make it work on pre-L devices</string>
    <string name="ucrop_rotate">Rotate</string>
    <string name="ucrop_scale">Scale</string>
    <string name="ucrop_crop">Crop</string>

</resources>
