package com.yalantis.ucrop.model;

import android.graphics.Bitmap;
import android.net.Uri;

/**
 * Created by <PERSON><PERSON><PERSON> [https://github.com/shliama] on 6/21/16.
 */
public class CropParameters {

    private int mMaxResultImageSizeX, mMaxResultImageSizeY;

    private Bitmap.CompressFormat mCompressFormat;
    private int mCompressQuality;
    private String mImageInputPath, mImageOutputPath;
    private ExifInfo mExifInfo;

    private Uri mContentImageInputUri, mContentImageOutputUri;


    public CropParameters(int maxResultImageSizeX, int maxResultImageSizeY,
                          Bitmap.CompressFormat compressFormat, int compressQuality,
                          String imageInputPath, String imageOutputPath, ExifInfo exifInfo) {
        mMaxResultImageSizeX = maxResultImageSizeX;
        mMaxResultImageSizeY = maxResultImageSizeY;
        mCompressFormat = compressFormat;
        mCompressQuality = compressQuality;
        mImageInputPath = imageInputPath;
        mImageOutputPath = imageOutputPath;
        mExifInfo = exifInfo;
    }

    public int getMaxResultImageSizeX() {
        return mMaxResultImageSizeX;
    }

    public int getMaxResultImageSizeY() {
        return mMaxResultImageSizeY;
    }

    public Bitmap.CompressFormat getCompressFormat() {
        return mCompressFormat;
    }

    public int getCompressQuality() {
        return mCompressQuality;
    }

    public String getImageInputPath() {
        return mImageInputPath;
    }

    public String getImageOutputPath() {
        return mImageOutputPath;
    }

    public ExifInfo getExifInfo() {
        return mExifInfo;
    }

    public Uri getContentImageInputUri() {
        return mContentImageInputUri;
    }

    public void setContentImageInputUri(Uri mContentImageInputUri) {
        this.mContentImageInputUri = mContentImageInputUri;
    }

    public Uri getContentImageOutputUri() {
        return mContentImageOutputUri;
    }

    public void setContentImageOutputUri(Uri mContentImageOutputUri) {
        this.mContentImageOutputUri = mContentImageOutputUri;
    }
}
