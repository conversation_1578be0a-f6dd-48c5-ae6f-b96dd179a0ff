{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "http://************:8089", "description": "Generated server url"}], "paths": {"/masterdata/user/set-default-account/1.0": {"post": {"tags": ["masterData"], "summary": "Set account default for user", "description": "Set account default for user", "operationId": "setDefaultAccountUser", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SetAccountDefaultDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/masterdata/user/product/list/1.0": {"post": {"tags": ["masterData"], "summary": "User Product list", "description": "User Product list", "operationId": "getSubProducts", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListSubProductDto"}}}}}}}, "/masterdata/user/pmt-account/detail/1.0": {"post": {"tags": ["masterData"], "summary": "get fin account detail", "description": "get fin account detail", "operationId": "pmtAccountDetail", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountDetailCriteriaDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BalanceAccountDto"}}}}}}}, "/masterdata/user/pmt-account/detail-to-cif/1.0": {"post": {"tags": ["masterData"], "summary": "get fin account detail to CIF", "description": "get fin account detail to CIF", "operationId": "pmtAccountDetailToCif", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountDetailCriteriaDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FeeBalanceAccountDto"}}}}}}}, "/masterdata/user/inq-account/list/1.0": {"post": {"tags": ["masterData"], "summary": "get Authorized Inq Account List", "description": "get Authorized Inq Account List", "operationId": "findInqAccountList", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListAccountDto"}}}}}}}, "/masterdata/user/get-sub-prod-code/1.0": {"post": {"tags": ["masterData"], "summary": "get sub prod code by user", "description": "get sub prod code by user", "operationId": "getSubProdCodeByUser", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListString"}}}}}}}, "/masterdata/user/get-default-account/1.0": {"post": {"tags": ["masterData"], "summary": "Get account default for user", "description": "Get account default for user", "operationId": "getDefaultAccountUser", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountDefaultCriteriaDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/masterdata/user/fin-account/list/1.0": {"post": {"tags": ["masterData"], "summary": "get fin account List", "description": "get fin account List", "operationId": "finAccountList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountListCriteriaDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListBalanceAccountDto"}}}}}}}, "/masterdata/user/check-role-transfer-type/1.0": {"post": {"tags": ["masterData"], "summary": "Check role transfer type by user permission", "description": "Check role transfer type by user permission", "operationId": "checkTransferTypeByUserPermission", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckTransferTypeByPermissionDto"}}}}}}}, "/masterdata/user/check-restrict-user/1.0": {"post": {"tags": ["masterData"], "summary": "check restrict user", "description": "check restrict user", "operationId": "checkRestrictUser", "requestBody": {"content": {"application/json": {"schema": {"type": "integer", "format": "int64"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultBoolean"}}}}}}}, "/masterdata/user/authorized-account/list/1.0": {"post": {"tags": ["masterData"], "summary": "authorized account List", "description": "authorized account List", "operationId": "authorizedAccountList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthorizedAccountListCriteriaDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListAccountDto"}}}}}}}, "/masterdata/user/authorized-account/by-accttypes/list/1.0": {"post": {"tags": ["masterData"], "summary": "authorized account List", "description": "authorized account List", "operationId": "getAuthorizedAccountAsList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthorizedAccLstCriteriaDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListAccountDto"}}}}}}}, "/masterdata/user/authorized-account-reg-all/list/1.0": {"post": {"tags": ["masterData"], "summary": "authorized account List", "description": "authorized account List", "operationId": "getAuthorizedAccountRegAllList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthorizedAccListRegAllCriteriaDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListAccountDto"}}}}}}}, "/masterdata/user/authorized-account-cif/list/1.0": {"post": {"tags": ["masterData"], "summary": "authorized account List", "description": "authorized account List", "operationId": "getAuthorizedAccountCifList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthorizedAccListRegAllCriteriaDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListAccountDto"}}}}}}}, "/masterdata/user/account/check-authorization/1.0": {"post": {"tags": ["masterData"], "summary": "check authorized account", "description": "check authorized account", "operationId": "checkAuthorizationAccount", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckAuthorizedAccountCriteriaDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultBoolean"}}}}}}}, "/masterdata/txn-transfer/inq/1.0": {"post": {"tags": ["masterData"], "summary": "Txn fund transfer init transaction inquiry", "description": "Txn fund transfer init transaction inquiry", "operationId": "txnTransferInitInquiry", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnFundTransferInqDto"}}}}}}}, "/masterdata/transaction/validate-flow/1.0": {"post": {"tags": ["masterData"], "summary": "validate account", "description": "validate account", "operationId": "transactionValidateFlow", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidateAccountCriteriaDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidateAccountDto"}}}}}}}, "/masterdata/target-trade/get-all/1.0": {"post": {"tags": ["masterData"], "summary": "Param target trade", "description": "Query list of target transaction", "operationId": "getAll", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TargetFxTransactionRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListTargetFxTransactionResponse"}}}}}}}, "/masterdata/par/txn-agreement-list/1.0": {"post": {"tags": ["masterData"], "summary": "Transaction Agreement", "description": "get list transactionAgreement", "operationId": "getTxnAgreementByFilter", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransactionAgreementFilterRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListTransactionAgreementDto"}}}}}}}, "/masterdata/par/txn-agreement-detail/1.0": {"post": {"tags": ["masterData"], "summary": "Transaction Agreement", "description": "get list detail transactionAgreement", "operationId": "getTxnAgreementDetail", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetTransactionAgreementRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransactionAgreementDto"}}}}}}}, "/masterdata/par/transfer-trans-data/1.0": {"post": {"tags": ["masterData"], "summary": "Param data transfer transaction", "description": "Param data transfer transaction", "operationId": "parTransData", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferTransReqDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ParTransferTransDto"}}}}}}}, "/masterdata/par/transaction-value/1.0": {"post": {"tags": ["masterData"], "summary": "Get Param Transaction Value By Txn Code", "description": "Get Param Transaction Value By Txn Code", "operationId": "getParTransactionValue", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ParTransValueReqDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ParTransValueDto"}}}}}}}, "/masterdata/par/transaction-value-by-mapping-type/1.0": {"post": {"tags": ["masterData"], "summary": "Get Param Transaction Value By Mapping Type", "description": "Get Param Transaction Value By Mapping Type", "operationId": "getParTransValueTxn", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListParTransValueDto"}}}}}}}, "/masterdata/par/trans-value/list/1.0": {"post": {"tags": ["masterData"], "summary": "Param value for trans with mapping txn code", "description": "Param value for trans with mapping txn code", "operationId": "parTransValueMapTxn", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListParTransValueMapTxnDto"}}}}}}}, "/masterdata/par/trade-finance/1.0": {"post": {"tags": ["masterData"], "summary": "List Trade Document", "description": "List Trade Document", "operationId": "getTradeFinanceDocumentsByCif", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TradeFinanceDocumentsRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListTradeFinanceDocumentsDto"}}}}}}}, "/masterdata/par/product-day-off/validate-pay-date/1.0": {"post": {"tags": ["masterData"], "summary": "Product Day Off ", "description": "<PERSON><PERSON><PERSON> tra ngày thanh toán có phải ngày nghỉ không", "operationId": "checkIfPaymentDateIsHoliday", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckPaymentHolidayRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckPaymentHolidayResultDto"}}}}}}}, "/masterdata/par/product-day-off/list/in-range/by-type/1.0": {"post": {"tags": ["masterData"], "summary": "Product Day Off ", "description": "<PERSON><PERSON><PERSON> danh sách day off của hệ thống theo product code, range date và type", "operationId": "getListDayOffInRange", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDayOffListInRangeRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDayOffListInRangeResponseDto"}}}}}}}, "/masterdata/par/product-day-off/list/in-range/1.0": {"post": {"tags": ["masterData"], "summary": "Product Day Off ", "description": "<PERSON><PERSON>y thông tin day off của hệ thống và product theo product code và range date", "operationId": "getDayOffInRange", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDayOffInRangeRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDayOffInRangeResponseDto"}}}}}}}, "/masterdata/par/product-day-off/list/1.0": {"post": {"tags": ["masterData"], "summary": "Product Day Off ", "description": "<PERSON><PERSON>y thông tin Date Off của hệ thống và của Product theo product code", "operationId": "getDayOffByProductCodeAnDate", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDayOffRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListProductDayOffResponseDto"}}}}}}}, "/masterdata/par/process-transaction/list/1.0": {"post": {"tags": ["masterData"], "summary": "get list par process transaction", "description": "get list par process transaction", "operationId": "getParProcessTransaction", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListParProcessTransactionDto"}}}}}}}, "/masterdata/par/process-transaction/inquiry/1.0": {"post": {"tags": ["masterData"], "summary": "inquiry process transaction", "description": "inquiry process transaction", "operationId": "getProcessTransaction", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListParProcessTransactionDto"}}}}}}}, "/masterdata/par/process-reject-transaction-agreement/1.0": {"post": {"tags": ["masterData"], "summary": "Transaction Agreement", "description": "Transaction Agreement", "operationId": "processRejectTransactionAgreement", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RejectTransactionAgreementRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RejectTransactionAgreementDto"}}}}}}}, "/masterdata/par/par-prod-mapping/list/1.0": {"post": {"tags": ["masterData"], "summary": "Get All Par Prod Mapping", "description": "Get All Par Prod Mapping", "operationId": "getAllParProductMapping", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListParProductMappingDto"}}}}}}}, "/masterdata/par/interrupt-time/list/1.0": {"post": {"tags": ["masterData"], "summary": "Get List Param Interrupt Time", "description": "Get List Param Interrupt Time", "operationId": "getParInterruptTime", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListParInterruptTimeDto"}}}}}}}, "/masterdata/par/get-user-product/1.0": {"post": {"tags": ["masterData"], "summary": "Param data transfer transaction", "description": "Param data transfer transaction", "operationId": "getProductUser", "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDto"}}}}}}}, "/masterdata/par/fx-rate/1.0": {"post": {"tags": ["masterData"], "summary": "Get details fxRate by ccys", "description": "Get details fxRate by ccys", "operationId": "getDetailFxRateByCcys", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DetailFxRateByCcyReqDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListForExRateDto"}}}}}}}, "/masterdata/par/deposit-product/list/1.0": {"post": {"tags": ["masterData"], "summary": "List productCode", "description": "Deposit Product", "operationId": "getDepositProductList", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListDepositProductDto"}}}}}}}, "/masterdata/par/deposit-prod-line/list/1.0": {"post": {"tags": ["masterData"], "summary": "List product line", "description": "List product line", "operationId": "getDepositProdLineList", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDto"}}}}}}}}, "/masterdata/par/day-off-by-product/list/1.0": {"post": {"tags": ["masterData"], "summary": "Param Product Day Off By Product", "description": "Param Product Day Off By Product", "operationId": "parProdDayOffByProdCode", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListProductDayOffResponseDto"}}}}}}}, "/masterdata/par/cus-debit-acc/1.0": {"post": {"tags": ["masterData"], "summary": "Get customer info by debit account no", "description": "Get customer info by debit account no", "operationId": "getCusInfoFromDebitAccNo", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferTransReqDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerResponseDto"}}}}}}}, "/masterdata/par/currency/1.0": {"post": {"tags": ["masterData"], "summary": "Get Param Currency", "description": "Get Param Currency", "operationId": "getParCurrency", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListParCurrencyDto"}}}}}}}, "/masterdata/par/cot/inquiry/1.0": {"post": {"tags": ["masterData"], "summary": "inquiry COT", "description": "inquiry COT", "operationId": "parBankCotDto", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ParCotReqDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ParCotDto"}}}}}}}, "/masterdata/par/collect-account/list/1.0": {"post": {"tags": ["masterData"], "summary": "Param collection account", "description": "Param collection account", "operationId": "par<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferTransReqDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListParCollectAccDto"}}}}}}}, "/masterdata/par/category-by-type/list/1.0": {"post": {"tags": ["masterData"], "summary": "Deposit - get category list ", "description": "Danh sách Category theo Type", "operationId": "getCategoriesByType", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListCategoryResponseDto"}}}}}}}, "/masterdata/erp/user/product-function/validate/1.0": {"post": {"tags": ["masterData"], "summary": "erp validate product function", "description": "erp validate product function", "operationId": "validateErpProductFunction", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidateErpProdFunctionCriteriaDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidateErpProdFunctionResDto"}}}}}}}, "/masterdata/erp/user/product-function/get-by-parent-code/1.0": {"post": {"tags": ["masterData"], "summary": "List products by parent code", "description": "List products by parent code", "operationId": "getListProductsByParentCodes", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ParProductsRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListParProductsResponeDto"}}}}}}}, "/masterdata/erp/user/account/check-authorization/1.0": {"post": {"tags": ["masterData"], "summary": "H2H check authorized account", "description": "H2H check authorized account", "operationId": "checkErpAuthorizationAccount", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckErpAuthorizedAccountCriteriaDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultBoolean"}}}}}}}, "/masterdata/deposit/txn/pending/init-search-data/1.0": {"post": {"tags": ["masterData"], "summary": "List data", "description": "List data", "operationId": "initSearchData", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InitSearchDataResponseDto"}}}}}}}, "/masterdata/customer/resource-account/list/1.0": {"post": {"tags": ["masterData"], "summary": "Get list resource account", "description": "Get list resource account", "operationId": "getResourceAccountList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountResourceCriteriaReqDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListAccountDto"}}}}}}}, "/masterdata/customer/product/spec/1.0": {"post": {"tags": ["masterData"], "summary": "Customer Product Spec", "description": "Customer Product Spec", "operationId": "getProductSpecs", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InqCustomerSpecReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GeneralCustomerProductSpecDTO"}}}}}}}, "/masterdata/customer/product-pmt-spec/list/1.0": {"post": {"tags": ["masterData"], "summary": "Customer Product Pmt Spec", "description": "Customer Product Pmt Spec", "operationId": "getCustomerPmtSpecByCusId", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerPmtSpecReqDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListCustomerPmtSpecResDto"}}}}}}}, "/masterdata/customer/par-product/detail/1.0": {"post": {"tags": ["masterData"], "summary": "Par Product Mapping Detail", "description": "Par Product Mapping Detail", "operationId": "getDetailParProdMapping", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InqParProductMappingReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ParProductMappingDto"}}}}}}}, "/masterdata/customer/limit-product/list/1.0": {"post": {"tags": ["masterData"], "summary": "Par Product Mapping Detail", "description": "Par Product Mapping Detail", "operationId": "getListCheckLimitProduct", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InqParLimitProductReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListParProductMappingDto"}}}}}}}, "/masterdata/customer/info/1.0": {"post": {"tags": ["masterData"], "summary": "Customer", "description": "Get customer Info", "operationId": "getCustomerInfo", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InqCustomerCriteriaDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerDto"}}}}}}}, "/masterdata/customer/current-customer/detail/1.0": {"post": {"tags": ["masterData"], "summary": "Customer ", "description": "<PERSON><PERSON><PERSON> thông tin khách hàng hiện tại đang đăng nhập", "operationId": "getCurrentCustomerDetail", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerResponseDto"}}}}}}}, "/masterdata/customer/current-customer/channel/1.0": {"post": {"tags": ["masterData"], "summary": "Customer channel", "description": "Customer channel", "operationId": "getCurrentCustomerChannel", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListString"}}}}}}}, "/masterdata/customer/check-restriction/1.0": {"post": {"tags": ["masterData"], "summary": "Customer Restriction", "description": "<PERSON><PERSON><PERSON> thông tin hạn chế CIF", "operationId": "checkCustomerRestrict", "requestBody": {"content": {"application/json": {"schema": {"type": "integer", "format": "int64"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultBoolean"}}}}}}}, "/masterdata/branch/inquiry/1.0": {"post": {"tags": ["masterData"], "summary": "Branch list", "description": "Branch list", "operationId": "inquiryBranchByCodes", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BranchDto"}}}}}}}}, "/masterdata/branch/list/1.0": {"get": {"tags": ["masterData"], "summary": "Branch list", "description": "Branch list", "operationId": "getListBranch", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BranchDto"}}}}}}}}}, "components": {"schemas": {"SetAccountDefaultDto": {"required": ["accountNo", "grpType", "rescrType"], "type": "object", "properties": {"grpType": {"type": "string"}, "accountNo": {"type": "string"}, "rescrType": {"type": "string", "description": "DD_ACC/CD_ACC/LN_ACC/BG_ACC/.."}}}, "MessageResponse": {"type": "object", "properties": {"message": {"type": "string"}}, "description": "Data"}, "ResponseError": {"type": "object", "properties": {"errorCode": {"type": "string"}, "errorDesc": {"type": "string"}, "refVal": {"type": "object"}}, "description": "Addition Error List"}, "ResultMessageResponse": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/MessageResponse"}}}, "UserDTO": {"type": "object", "properties": {"userId": {"type": "string"}, "cusId": {"type": "integer", "format": "int64"}}}, "DataListSubProductDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/SubProductDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "FuntionDto": {"type": "object", "properties": {"code": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "status": {"type": "string"}, "changeRequestStatus": {"type": "string"}, "description": {"type": "string"}, "channelCode": {"type": "string"}, "classCode": {"type": "string"}, "parentCode": {"type": "string"}, "grandParentCode": {"type": "string"}, "updatedDate": {"type": "string"}, "updatedBy": {"type": "string"}, "approvedDate": {"type": "string"}, "approvedBy": {"type": "string"}}}, "ResultListSubProductDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListSubProductDto"}}}, "SubProductDto": {"type": "object", "properties": {"code": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "status": {"type": "string"}, "changeRequestStatus": {"type": "string"}, "description": {"type": "string"}, "channelCode": {"type": "string"}, "classCode": {"type": "string"}, "parentCode": {"type": "string"}, "grandParentCode": {"type": "string"}, "updatedDate": {"type": "string"}, "updatedBy": {"type": "string"}, "approvedDate": {"type": "string"}, "approvedBy": {"type": "string"}, "functions": {"type": "array", "items": {"$ref": "#/components/schemas/FuntionDto"}}}, "description": "List data"}, "AccountDetailCriteriaDto": {"required": ["accountNo", "transCode"], "type": "object", "properties": {"accountNo": {"type": "string"}, "transCode": {"type": "string"}}}, "BalanceAccountDto": {"type": "object", "properties": {"accountNo": {"type": "string"}, "accountName": {"type": "string"}, "status": {"type": "string"}, "cif": {"type": "string"}, "branchCode": {"type": "string"}, "currCode": {"type": "string"}, "holdBalance": {"type": "string"}, "minBalance": {"type": "string"}, "overdraftBalance": {"type": "string"}, "availableBalance": {"type": "string"}, "acctRelation": {"type": "string"}, "currentBalance": {"type": "string"}, "maturityDate": {"type": "string"}, "parentBoo": {"type": "string"}, "boo": {"type": "string"}, "overdraftAcctNo": {"type": "array", "items": {"type": "string"}}, "productCode": {"type": "string"}, "purposeCode": {"type": "string"}, "default": {"type": "boolean"}}, "description": "Data"}, "ResultBalanceAccountDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/BalanceAccountDto"}}}, "FeeBalanceAccountDto": {"type": "object", "properties": {"accountNo": {"type": "string"}, "accountName": {"type": "string"}, "status": {"type": "string"}, "cif": {"type": "string"}, "branchCode": {"type": "string"}, "currCode": {"type": "string"}, "holdBalance": {"type": "string"}, "minBalance": {"type": "string"}, "overdraftBalance": {"type": "string"}, "availableBalance": {"type": "string"}, "acctRelation": {"type": "string"}, "currentBalance": {"type": "string"}, "maturityDate": {"type": "string"}, "parentBoo": {"type": "string"}, "boo": {"type": "string"}, "overdraftAcctNo": {"type": "array", "items": {"type": "string"}}, "productCode": {"type": "string"}, "purposeCode": {"type": "string"}, "accountType": {"type": "string"}, "default": {"type": "boolean"}}, "description": "Data"}, "ResultFeeBalanceAccountDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/FeeBalanceAccountDto"}}}, "AccountDto": {"type": "object", "properties": {"accountNo": {"type": "string"}, "accountName": {"type": "string"}, "currCode": {"type": "string"}, "productCode": {"type": "string"}, "purposeCode": {"type": "string"}, "accRelation": {"type": "string"}, "accType": {"type": "string"}, "default": {"type": "boolean"}}, "description": "List data"}, "DataListAccountDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/AccountDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListAccountDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListAccountDto"}}}, "DataListString": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"type": "string", "description": "List data"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListString": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListString"}}}, "AccountDefaultCriteriaDto": {"required": ["grpType", "rescrType"], "type": "object", "properties": {"grpType": {"type": "string"}, "rescrType": {"type": "string", "description": "DD_ACC/CD_ACC/LN_ACC/BG_ACC/.."}}}, "ResultString": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"type": "string", "description": "Data"}}}, "AccountListCriteriaDto": {"required": ["accType", "transCode"], "type": "object", "properties": {"accType": {"type": "string", "description": "Loại tài k<PERSON>n : DDA/CD/LN"}, "transCode": {"type": "string", "description": "Transaction code tương ứng với product code, sub product code"}, "onlyCifAcc": {"type": "boolean", "description": "Check tài kho<PERSON>n phân quyền CIF only"}}}, "DataListBalanceAccountDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/BalanceAccountDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListBalanceAccountDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListBalanceAccountDto"}}}, "CheckTransferTypeByPermissionDto": {"type": "object", "properties": {"allowExternalTransfer": {"type": "boolean"}, "allowInternalTransfer": {"type": "boolean"}}, "description": "Data"}, "ResultCheckTransferTypeByPermissionDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/CheckTransferTypeByPermissionDto"}}}, "ResultBoolean": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"type": "boolean", "description": "Data"}}}, "AuthorizedAccountListCriteriaDto": {"required": ["accType", "grpType"], "type": "object", "properties": {"accType": {"type": "string", "description": "Loại tài k<PERSON>n : DDA/CD/LN"}, "grpType": {"type": "string", "description": "ACC_FIN/ACC_INQ :<PERSON><PERSON><PERSON> ch<PERSON>h/phi tài chính"}}}, "AuthorizedAccLstCriteriaDto": {"required": ["accType", "grpType"], "type": "object", "properties": {"accType": {"type": "array", "description": "Loại tài k<PERSON>n : DDA/CD/LN", "items": {"type": "string", "description": "Loại tài k<PERSON>n : DDA/CD/LN"}}, "grpType": {"type": "string", "description": "ACC_FIN/ACC_INQ :<PERSON><PERSON><PERSON> ch<PERSON>h/phi tài chính"}}}, "AuthorizedAccListRegAllCriteriaDto": {"type": "object", "properties": {"accType": {"type": "string", "description": "Loại tài k<PERSON>n : DDA/CD/LN"}, "grpType": {"type": "string", "description": "ACC_FIN/ACC_INQ :<PERSON><PERSON><PERSON> ch<PERSON>h/phi tài chính"}}}, "CheckAuthorizedAccountCriteriaDto": {"required": ["accType", "accountNo", "grpType"], "type": "object", "properties": {"accountNo": {"type": "string", "description": "Số TK"}, "accType": {"type": "string", "description": "Loại tài k<PERSON>n : DDA/CD/LN"}, "grpType": {"type": "string", "description": "ACC_FIN/ACC_INQ :<PERSON><PERSON><PERSON> ch<PERSON>h/phi tài chính"}}}, "CodeValueDto": {"type": "object", "properties": {"code": {"type": "string"}, "value": {"type": "string"}, "descr": {"type": "string"}}}, "CustomerPmtSpecDto": {"type": "object", "properties": {"descr": {"type": "string", "description": "<PERSON><PERSON>"}, "chargeFeeOpt": {"type": "string", "description": "<PERSON><PERSON><PERSON> thức thu phí"}, "chargeFreq": {"type": "string", "description": "<PERSON><PERSON><PERSON> suất thu phí"}, "chargeAcc": {"type": "string", "description": "TK thu phí"}, "contAmtTotal": {"type": "integer", "description": "<PERSON><PERSON>ng tiền kho<PERSON> phí", "format": "int64"}, "contAmt": {"type": "integer", "description": "<PERSON><PERSON> tiền khoán phí mỗi kỳ", "format": "int64"}, "retryInsfctBal": {"type": "string", "description": "<PERSON><PERSON><PERSON> ký chuyển tiền dưới số dư"}, "custOverCot": {"type": "string", "description": "Đăng ký xử lý quá <PERSON>"}, "contFrom": {"type": "string", "description": "<PERSON>h<PERSON><PERSON> gian kho<PERSON> phí từ"}, "contTo": {"type": "string", "description": "<PERSON>h<PERSON><PERSON> gian kho<PERSON> phí đến"}, "currCode": {"type": "string", "description": "<PERSON><PERSON><PERSON> tiền tệ"}}}, "ParTransValueDto": {"type": "object", "properties": {"productCode": {"type": "string"}, "subProductCode": {"type": "string"}, "mappingValue": {"type": "string"}, "txnType": {"type": "string"}, "pmtMethod": {"type": "string"}, "currencyCode": {"type": "string"}, "minVal": {"type": "string"}, "maxVal": {"type": "string"}, "startEffectDate": {"type": "string"}, "endEffectDate": {"type": "string"}}}, "ResultTxnFundTransferInqDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/TxnFundTransferInqDto"}}}, "TxnFundTransferInqDto": {"type": "object", "properties": {"effDate": {"type": "string"}, "sysConfig": {"$ref": "#/components/schemas/CodeValueDto"}, "sysConfigs": {"type": "array", "items": {"$ref": "#/components/schemas/CodeValueDto"}}, "cusPmtSpecDto": {"$ref": "#/components/schemas/CustomerPmtSpecDto"}, "parTransValues": {"type": "array", "items": {"$ref": "#/components/schemas/ParTransValueDto"}}}, "description": "Data"}, "ValidateAccountCriteriaDto": {"required": ["transCode"], "type": "object", "properties": {"debitAccountNo": {"type": "string", "description": "<PERSON><PERSON> tài k<PERSON> chuy<PERSON>n"}, "creditAccountNo": {"type": "string", "description": "<PERSON><PERSON> tài k<PERSON>n n<PERSON>n"}, "accType": {"type": "string", "description": "Loại tài k<PERSON>n: DDA/CD/LN"}, "transCode": {"type": "string"}, "ccy": {"type": "string", "description": "<PERSON><PERSON><PERSON> tiền tệ"}, "amount": {"type": "string", "description": "<PERSON><PERSON> tiền chuyển"}, "checkBalance": {"type": "string", "description": "Check số dư : Y/N"}}}, "AccountInfo": {"type": "object", "properties": {"debitAcctNo": {"type": "string"}, "debitAcctName": {"type": "string"}, "debitAcctType": {"type": "string"}, "debitBrcd": {"type": "string"}, "debitCcy": {"type": "string"}, "debitCif": {"type": "string"}, "creditAcctNo": {"type": "string"}, "creditAcctName": {"type": "string"}, "creditAcctType": {"type": "string"}, "creditBrcd": {"type": "string"}, "creditCcy": {"type": "string"}, "creditCif": {"type": "string"}}}, "ResultValidateAccountDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/ValidateAccountDto"}}}, "ValidateAccountDto": {"type": "object", "properties": {"processMethod": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> thức xử lý: 0:t<PERSON> động; 1:<PERSON><PERSON> chối; 2: <PERSON><PERSON><PERSON> và<PERSON> hàng chờ xử lý thủ công; 3: <PERSON><PERSON><PERSON> tra số dư thấu chi "}, "errorCode": {"type": "string"}, "errorDesc": {"type": "string"}, "account": {"$ref": "#/components/schemas/AccountInfo"}}, "description": "Data"}, "TargetFxTransactionRequest": {"required": ["applicableProducts", "transactionDirection"], "type": "object", "properties": {"applicableProducts": {"type": "string"}, "transactionDirection": {"type": "string"}}, "description": "TargetFxTransactionRequest"}, "DataListTargetFxTransactionResponse": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/TargetFxTransactionResponse"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListTargetFxTransactionResponse": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListTargetFxTransactionResponse"}}}, "TargetFxTransactionResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "targetName": {"type": "string"}, "status": {"type": "string"}, "transactionDirection": {"type": "string"}, "channel": {"type": "string"}, "applicableProducts": {"type": "string"}, "purposeTransfer": {"type": "string"}}, "description": "List data"}, "TransactionAgreementFilterRequest": {"required": ["cif", "isUsed", "status", "type", "usageSubsystem"], "type": "object", "properties": {"cif": {"type": "string"}, "type": {"type": "string", "enum": ["SELL", "BUY"]}, "status": {"type": "string", "enum": ["DRAFT", "PENDING_APPROVAL", "APPROVED", "REJECTED", "CANCELLED", "DELETED", "EXPIRED", "PENDING_DELETED"]}, "isUsed": {"type": "boolean"}, "usageSubsystem": {"type": "string", "enum": ["FX", "PAYMENT", "LOAN"]}}}, "DataListTransactionAgreementDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/TransactionAgreementDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListTransactionAgreementDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListTransactionAgreementDto"}}}, "TransactionAgreementDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "code": {"type": "string"}, "status": {"type": "string", "enum": ["DRAFT", "PENDING_APPROVAL", "APPROVED", "REJECTED", "CANCELLED", "DELETED", "EXPIRED", "PENDING_DELETED"]}, "cifNo": {"type": "string"}, "cifName": {"type": "string"}, "type": {"type": "string", "enum": ["SELL", "BUY"]}, "txnType": {"type": "string", "enum": ["SPOT", "FORWARD"]}, "paymentDate": {"type": "string"}, "payCcy": {"type": "string"}, "payAmount": {"type": "string"}, "exchangeRate": {"type": "string"}, "branchCode": {"type": "string"}, "guaranteeType": {"type": "string", "enum": ["TXN_LIMIT", "DEPOSIT", "BOTH"]}, "txnLimit": {"type": "string"}, "depositCcy": {"type": "string"}, "depositAmount": {"type": "string"}, "source": {"type": "string", "enum": ["BIDV", "OTHER"]}, "sourceChild": {"type": "string", "enum": ["CTTN", "CTQT", "REPAY_LOAN", "LC_PAYMENT"]}, "sourceDetail": {"type": "string"}, "tradeCcy": {"type": "string"}, "tradeAmount": {"type": "string"}, "tradeDocumentCode": {"type": "string"}, "createdBy": {"type": "string"}, "createdDate": {"type": "string"}, "updatedBy": {"type": "string"}, "updatedDate": {"type": "string"}, "approvedBy": {"type": "string"}, "approvedDate": {"type": "string"}, "version": {"type": "integer", "format": "int64"}, "used": {"type": "boolean"}}, "description": "List data"}, "GetTransactionAgreementRequest": {"required": ["code"], "type": "object", "properties": {"code": {"type": "string"}}}, "ResultTransactionAgreementDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/TransactionAgreementDto"}}}, "TransferTransReqDto": {"required": ["ccy", "debitAccNo", "debitAccType"], "type": "object", "properties": {"txnCode": {"type": "string"}, "debitAccType": {"type": "string"}, "debitAccNo": {"type": "string"}, "ccy": {"type": "string"}}}, "ParTransferTransDto": {"type": "object", "properties": {"product": {"$ref": "#/components/schemas/ProductDto"}, "accounts": {"type": "array", "items": {"$ref": "#/components/schemas/AccountDto"}}, "parTransValue": {"$ref": "#/components/schemas/ParTransValueDto"}}, "description": "Data"}, "ProductDto": {"type": "object", "properties": {"prodCode": {"type": "string"}, "subProdCode": {"type": "string"}}}, "ResultParTransferTransDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/ParTransferTransDto"}}}, "ParTransValueReqDto": {"required": ["ccy", "txnCode"], "type": "object", "properties": {"txnCode": {"type": "string"}, "ccy": {"type": "string"}}}, "ResultParTransValueDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/ParTransValueDto"}}}, "DataListParTransValueDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/ParTransValueDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListParTransValueDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListParTransValueDto"}}}, "DataListParTransValueMapTxnDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/ParTransValueMapTxnDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ParTransValueMapTxnDto": {"type": "object", "properties": {"txnCode": {"type": "string"}, "productCode": {"type": "string"}, "subProductCode": {"type": "string"}, "currencyCode": {"type": "string"}, "minVal": {"type": "integer", "format": "int64"}, "maxVal": {"type": "integer", "format": "int64"}, "startEffectDate": {"type": "string"}, "endEffectDate": {"type": "string"}, "status": {"type": "string"}}, "description": "List data"}, "ResultListParTransValueMapTxnDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListParTransValueMapTxnDto"}}}, "TradeFinanceDocumentsRequest": {"required": ["cifNo", "paymentDate", "tradeCcy"], "type": "object", "properties": {"cifNo": {"type": "string"}, "paymentDate": {"type": "string"}, "tradeCcy": {"type": "string"}}}, "DataListTradeFinanceDocumentsDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/TradeFinanceDocumentsDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListTradeFinanceDocumentsDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListTradeFinanceDocumentsDto"}}}, "TradeFinanceDocumentsDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "transCode": {"type": "string"}, "expiryDate": {"type": "string"}, "balanceInOriginalCurrency": {"type": "string"}, "isUsed": {"type": "string"}, "tradeCcy": {"type": "string"}}, "description": "List data"}, "CheckPaymentHolidayRequest": {"required": ["toDate"], "type": "object", "properties": {"toDate": {"type": "string"}, "transType": {"type": "string", "enum": ["SELL", "BUY"]}}}, "CheckPaymentHolidayResultDto": {"type": "object", "properties": {"holidayFlag": {"type": "boolean"}}, "description": "Data"}, "ResultCheckPaymentHolidayResultDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/CheckPaymentHolidayResultDto"}}}, "ProductDayOffListInRangeRequestDto": {"required": ["fromDate", "toDate", "transType"], "type": "object", "properties": {"transType": {"type": "string", "enum": ["SELL", "BUY"]}, "fromDate": {"type": "string"}, "toDate": {"type": "string"}}}, "ProductDayOffListInRangeResponseDto": {"type": "object", "properties": {"dayOffList": {"type": "array", "items": {"type": "string", "format": "date"}}, "dayOffCount": {"type": "integer", "format": "int64"}}, "description": "Data"}, "ResultProductDayOffListInRangeResponseDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/ProductDayOffListInRangeResponseDto"}}}, "ProductDayOffInRangeRequestDto": {"required": ["fromDate", "productCode", "toDate"], "type": "object", "properties": {"productCode": {"type": "string"}, "type": {"type": "string"}, "fromDate": {"type": "string"}, "toDate": {"type": "string"}}}, "ProductDayOffInRangeResponseDto": {"type": "object", "properties": {"dayOffList": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDayOffResponseDto"}}, "dayOffCount": {"type": "integer", "format": "int32"}}, "description": "Data"}, "ProductDayOffResponseDto": {"type": "object", "properties": {"subProductCode": {"type": "string"}, "dayOff": {"type": "string"}, "type": {"type": "string"}}}, "ResultProductDayOffInRangeResponseDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/ProductDayOffInRangeResponseDto"}}}, "ProductDayOffRequestDto": {"required": ["dayOff", "productCode"], "type": "object", "properties": {"productCode": {"type": "string"}, "dayOff": {"type": "string"}}}, "DataListProductDayOffResponseDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/ProductDayOffResponseDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListProductDayOffResponseDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListProductDayOffResponseDto"}}}, "DataListParProcessTransactionDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/ParProcessTransactionDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ParProcessTransactionDto": {"type": "object", "properties": {"code": {"type": "string"}, "name": {"type": "string"}, "productCode": {"type": "string"}, "subProductCode": {"type": "string"}, "processMethod": {"type": "string"}, "status": {"type": "string"}, "entryAccType": {"type": "string"}, "processTransDtl": {"type": "array", "items": {"$ref": "#/components/schemas/ProcessTransDtl"}}}, "description": "List data"}, "ProcessTransDtl": {"type": "object", "properties": {"code": {"type": "string"}, "value": {"type": "string"}}}, "ResultListParProcessTransactionDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListParProcessTransactionDto"}}}, "RejectTransactionAgreementRequest": {"required": ["txnAgreementCode"], "type": "object", "properties": {"txnAgreementCode": {"type": "string"}}}, "RejectTransactionAgreementDto": {"type": "object", "properties": {"code": {"type": "string"}, "messageResponse": {"type": "string"}}, "description": "Data"}, "ResultRejectTransactionAgreementDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/RejectTransactionAgreementDto"}}}, "DataListParProductMappingDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/ParProductMappingDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ParProductMappingDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "productCode": {"type": "string"}, "subProductCode": {"type": "string"}, "mappingType": {"type": "string"}, "detailCode": {"type": "string"}, "mappingValue": {"type": "string"}}, "description": "List data"}, "ResultListParProductMappingDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListParProductMappingDto"}}}, "DataListParInterruptTimeDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/ParInterruptTimeDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ParInterruptTimeDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "prodCode": {"type": "string"}, "subProdCode": {"type": "string"}, "startTime": {"type": "string"}, "endTime": {"type": "string"}, "status": {"type": "string"}, "funcCode": {"type": "string"}}, "description": "List data"}, "ResultListParInterruptTimeDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListParInterruptTimeDto"}}}, "ResultProductDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/ProductDto"}}}, "DetailFxRateByCcyReqDto": {"type": "object", "properties": {"ccys": {"type": "array", "items": {"type": "string"}}}}, "DataListForExRateDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/ForExRateDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ForExRateDto": {"type": "object", "properties": {"jfxCod": {"type": "string"}, "cash": {"type": "string"}, "mkBuy": {"type": "string"}, "mkSell": {"type": "string"}, "bidBuy": {"type": "string"}, "bidSel": {"type": "string"}, "spread": {"type": "string"}, "vmidRt": {"type": "string"}}, "description": "List data"}, "ResultListForExRateDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListForExRateDto"}}}, "DataListDepositProductDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/DepositProductDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "DepositProductDto": {"type": "object", "properties": {"code": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "productItems": {"type": "array", "items": {"$ref": "#/components/schemas/ProductItemDto"}}}, "description": "List data"}, "ProductItemDto": {"type": "object", "properties": {"productCode": {"type": "string"}, "productName": {"type": "string"}, "purposeCode": {"type": "string"}, "pretermWithdrawal": {"type": "string"}, "termType": {"type": "string"}, "currCodes": {"type": "string"}, "periodicCurrCodes": {"type": "string"}}}, "ResultListDepositProductDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListDepositProductDto"}}}, "CategoryDto": {"type": "object", "properties": {"name": {"type": "string"}, "code": {"type": "string"}}, "description": "Data"}, "ResultListCategoryDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"type": "array", "description": "Data", "items": {"$ref": "#/components/schemas/CategoryDto"}}}}, "CustomerResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "cifNo": {"type": "string"}, "name": {"type": "string"}, "nameEn": {"type": "string"}, "cusType": {"type": "string"}, "ownBrn": {"type": "string"}, "ownSubBrn": {"type": "string"}, "address": {"type": "string"}, "resident": {"type": "string"}}, "description": "Data"}, "ResultCustomerResponseDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/CustomerResponseDto"}}}, "DataListParCurrencyDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/ParCurrencyDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ParCurrencyDto": {"type": "object", "properties": {"code": {"type": "string"}, "name": {"type": "string"}, "nameEn": {"type": "string"}}, "description": "List data"}, "ResultListParCurrencyDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListParCurrencyDto"}}}, "ParCotReqDto": {"type": "object", "properties": {"externalBankCode": {"type": "string"}, "ccy": {"type": "string"}}}, "ParCotDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "externalBankCode": {"type": "string"}, "ccy": {"type": "string"}, "cot": {"type": "string"}}, "description": "Data"}, "ResultParCotDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/ParCotDto"}}}, "DataListParCollectAccDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/ParCollectAccDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ParCollectAccDto": {"type": "object", "properties": {"subProductCode": {"type": "string"}, "accNo": {"type": "string"}}, "description": "List data"}, "ResultListParCollectAccDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListParCollectAccDto"}}}, "CategoryRequestDto": {"required": ["categoryType"], "type": "object", "properties": {"categoryType": {"type": "array", "items": {"type": "string"}}}}, "CategoryResponseDto": {"type": "object", "properties": {"code": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}}, "description": "List data"}, "DataListCategoryResponseDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/CategoryResponseDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListCategoryResponseDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListCategoryResponseDto"}}}, "UserFunctionDto": {"type": "object", "properties": {"userIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "productCode": {"type": "string"}, "function": {"type": "string"}}}, "ValidateErpProdFunctionCriteriaDto": {"type": "object", "properties": {"userFunctions": {"type": "array", "items": {"$ref": "#/components/schemas/UserFunctionDto"}}}}, "ResultValidateErpProdFunctionResDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/ValidateErpProdFunctionResDto"}}}, "ValidateErpProdFunctionResDto": {"type": "object", "properties": {"userPermissionFails": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "userPermissionSuccess": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "description": "Data"}, "ParProductsRequestDto": {"type": "object", "properties": {"parentCodes": {"type": "array", "items": {"type": "string"}}}}, "DataListParProductsResponeDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/ParProductsResponeDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ParProductsResponeDto": {"type": "object", "properties": {"code": {"type": "string"}, "parentCode": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}}, "description": "List data"}, "ResultListParProductsResponeDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListParProductsResponeDto"}}}, "CheckErpAuthorizedAccountCriteriaDto": {"required": ["accType", "accountNo", "grpType"], "type": "object", "properties": {"accountNo": {"type": "string", "description": "Số TK"}, "accType": {"type": "string", "description": "Loại tài k<PERSON>n : DDA/CD/LN"}, "grpType": {"type": "string", "description": "ACC_FIN/ACC_INQ :<PERSON><PERSON><PERSON> ch<PERSON>h/phi tài chính"}, "cusId": {"type": "integer", "format": "int64"}, "userIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "InitSearchDataResponseDto": {"type": "object", "properties": {"lineProductList": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDto"}}, "status": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryResponseDto"}}}, "description": "Data"}, "ResultInitSearchDataResponseDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/InitSearchDataResponseDto"}}}, "AccountResourceCriteriaReqDto": {"required": ["accType", "grpType"], "type": "object", "properties": {"accType": {"type": "string", "description": "Loại tài k<PERSON>n : DDA/CD/LN"}, "grpType": {"type": "string", "description": "ACC_FIN/ACC_INQ :<PERSON><PERSON><PERSON> ch<PERSON>h/phi tài chính"}, "currCode": {"type": "string", "description": "<PERSON><PERSON> tền tệ"}}}, "InqCustomerSpecReq": {"type": "object", "properties": {"cusId": {"type": "integer", "format": "int64"}, "type": {"type": "string"}}}, "CustomerBalalertDtlDto": {"type": "object", "properties": {"alertAcc": {"type": "string", "description": "TK nhận biến động số dư"}}}, "CustomerBalalertSpecDto": {"type": "object", "properties": {"alertFreq": {"type": "string", "description": "<PERSON><PERSON><PERSON> su<PERSON>t gửi thông tin biến động"}, "alertFreqVal": {"type": "integer", "description": "<PERSON><PERSON><PERSON> su<PERSON>t gửi thông tin biến động", "format": "int64"}, "acct": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerBalalertDtlDto"}}}}, "CustomerErpSpecDto": {"type": "object", "properties": {"descr": {"type": "string"}, "clientGroup": {"type": "string", "description": "Nhóm kết nối - H2H"}, "clientCode": {"type": "string", "description": "Mã đơn vị kết nối - H2H"}, "grpType": {"type": "string", "description": "Nhóm kết nối - ibank"}}}, "CustomerStmtSpecDto": {"type": "object", "properties": {"stmtFreq": {"type": "string", "description": "<PERSON><PERSON><PERSON>"}, "stmtAcc": {"type": "string", "description": "Số tài <PERSON>n"}, "stmtFile": {"type": "string", "description": "Loại file"}}}, "GeneralCustomerProductSpecDTO": {"type": "object", "properties": {"pmt": {"$ref": "#/components/schemas/CustomerPmtSpecDto"}, "balAlert": {"$ref": "#/components/schemas/CustomerBalalertSpecDto"}, "stmt": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerStmtSpecDto"}}, "erp": {"$ref": "#/components/schemas/CustomerErpSpecDto"}}, "description": "Data"}, "ResultGeneralCustomerProductSpecDTO": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/GeneralCustomerProductSpecDTO"}}}, "CustomerPmtSpecReqDto": {"type": "object", "properties": {"cusId": {"type": "integer", "format": "int64"}}}, "CustomerPmtSpecResDto": {"type": "object", "properties": {"chargeFeeOpt": {"type": "string"}, "chargeFreq": {"type": "string"}, "chargeAcc": {"type": "string"}}, "description": "List data"}, "DataListCustomerPmtSpecResDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/CustomerPmtSpecResDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListCustomerPmtSpecResDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListCustomerPmtSpecResDto"}}}, "InqParProductMappingReq": {"type": "object", "properties": {"mappingType": {"type": "string"}, "detailCode": {"type": "string"}, "subProductCode": {"type": "string"}}}, "ResultParProductMappingDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/ParProductMappingDto"}}}, "InqParLimitProductReq": {"required": ["subProdCodes"], "type": "object", "properties": {"subProdCodes": {"type": "array", "items": {"type": "string"}}}}, "InqCustomerCriteriaDto": {"required": ["cifNo"], "type": "object", "properties": {"cifNo": {"type": "string"}}}, "CustomerDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "cifNo": {"type": "string"}, "name": {"type": "string"}, "nameEn": {"type": "string"}, "cusType": {"type": "string"}, "ownBrn": {"type": "string"}, "ownSubBrn": {"type": "string"}, "address": {"type": "string"}, "resident": {"type": "string"}, "idReg": {"type": "string"}, "idIssuePlace": {"type": "string"}, "idIssueDate": {"type": "string"}, "country": {"type": "string"}, "taxCode": {"type": "string"}}, "description": "Data"}, "ResultCustomerDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/CustomerDto"}}}, "BranchDto": {"type": "object", "properties": {"code": {"type": "string"}, "name": {"type": "string"}, "nameEn": {"type": "string"}, "address": {"type": "string"}, "phone": {"type": "string"}, "parentCode": {"type": "string"}}, "description": "Data"}, "ResultListBranchDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"type": "array", "description": "Data", "items": {"$ref": "#/components/schemas/BranchDto"}}}}}}, "tags": ["masterData"], "formated": "1"}