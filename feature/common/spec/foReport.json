{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "http://*************", "description": "Generated server url"}], "paths": {"/fo-report/withdraw-deposit/txn/report/1.0": {"post": {"tags": ["foReport"], "summary": "excel report", "description": "API xuất báo cáo giao dịch rút tiền", "operationId": "exportWithdrawDepositList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnWithdrawDepositRptCriteriaDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportDepositDto"}}}}}}}, "/fo-report/withdraw-deposit/txn/list/1.0": {"post": {"tags": ["foReport"], "summary": "withdraw deposit Transaction report list", "description": "API vấn tin danh sách giao dịch rút tiền báo cáo", "operationId": "getReportWithdrawDepositList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnWithdrawDepositRptCriteriaDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListTxnWithdrawDepositRptDto"}}}}}}}, "/fo-report/withdraw-deposit/txn/detail/1.0": {"post": {"tags": ["foReport"], "summary": "Deposit Transaction Details", "description": "API chi tiết giao dịch rút tiền báo cáo", "operationId": "getReportWithdrawDepositDetail", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnWithdrawDepositRptDetailCriteriaDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnWithdrawDepositRptDetailDto"}}}}}}}, "/fo-report/widget/txn-count/list/1.0": {"post": {"tags": ["foReport"], "summary": "term deposit Transaction report list", "description": "API vấn tin danh sách giao dịch báo cáo", "operationId": "getTxnCount", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListTxnCountResponseDto"}}}}}}}, "/fo-report/widget/txn-count-checker/list/1.0": {"post": {"tags": ["foReport"], "summary": "term deposit Transaction report list", "description": "API vấn tin danh sách giao dịch báo cáo", "operationId": "getTxnCountPendingApprovalChecker", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListTxnCountResponseDto"}}}}}}}, "/fo-report/transfer/export/1.0": {"post": {"tags": ["foReport"], "summary": "Export fund transfer all list", "description": "Export fund transfer all list", "operationId": "exportFundTransferList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FundTransferBaseCriteriaRequestDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportDocumentResponseDto"}}}}}}}, "/fo-report/transfer/detail/1.0": {"post": {"tags": ["foReport"], "summary": "Fund transfer detail", "description": "Fund transfer detail", "operationId": "getFundTransferDetail", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FundTransferDetailRequestDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FundTransferDetailDto"}}}}}}}, "/fo-report/transfer/all/list/1.0": {"post": {"tags": ["foReport"], "summary": "Fund transfer all list", "description": "Fund transfer all list", "operationId": "getFundTransferList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FundTransferBaseCriteriaRequestDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListTxnFundTransferDto"}}}}}}}, "/fo-report/term-deposit/txn/report/1.0": {"post": {"tags": ["foReport"], "summary": "excel report", "description": "API xuất báo cáo giao dịch tiền gửi", "operationId": "exportTermDepositList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnTermDepositRptCriteriaDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportDepositDto"}}}}}}}, "/fo-report/term-deposit/txn/list/1.0": {"post": {"tags": ["foReport"], "summary": "term deposit Transaction report list", "description": "API vấn tin danh sách giao dịch báo cáo", "operationId": "getReportTermDepositList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnTermDepositRptCriteriaDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListTxnTermDepositRptDto"}}}}}}}, "/fo-report/term-deposit/txn/detail/1.0": {"post": {"tags": ["foReport"], "summary": "Deposit Transaction Details", "description": "API chi tiết giao dịch báo cáo", "operationId": "getReportTermDepositDetail", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnDepositRptDetailCriteriaDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnDepositRptDetailDto"}}}}}}}, "/fo-report/term-deposit/rev-term-dep/txn/list/1.0": {"post": {"tags": ["foReport"], "summary": "Get Revoke Transaction Deposit List ", "description": "<PERSON> <PERSON> sách giao dịch thu hồi chờ du<PERSON> (báo c<PERSON>o thu hồi)", "operationId": "getRevokeTransactionDepositListReport", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RevTxnDepositReportCriteriaDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListRevTxnDepositDto"}}}}}}}, "/fo-report/term-deposit/rev-term-dep/txn/detail/1.0": {"post": {"tags": ["foReport"], "summary": "Get Revoke Transaction Deposit Detail ", "description": "API Chi tiết giao dịch thu hồi chờ duyệt (báo c<PERSON>o thu hồi)", "operationId": "getRevokeTransactionDepositDetail", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RevTxnDepositDetailCriteriaDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RevTxnDepositDetailDto"}}}}}}}, "/fo-report/payroll/maker/list/1.0": {"post": {"tags": ["foReport"], "summary": "List Maker Report Payroll", "description": "List Maker Report Payroll", "operationId": "listMakerReportPayroll", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnBulkTransferRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListTxnBulkTransferListResponseDto"}}}}}}}, "/fo-report/payroll/maker/export/1.0": {"post": {"tags": ["foReport"], "summary": "Export Maker Report Payroll", "description": "Export Maker Report Payroll", "operationId": "exportMakerReportPayroll", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnBulkTransferRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnPrintResponseDto"}}}}}}}, "/fo-report/payroll/maker/detail/export/1.0": {"post": {"tags": ["foReport"], "summary": "API Export Report Maker Payroll", "description": "API Export Report Maker Payroll", "operationId": "getMakerReportPayroll", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportReportRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportReportResponseDto"}}}}}}}, "/fo-report/payroll/checker/list/1.0": {"post": {"tags": ["foReport"], "summary": "List Checker Report Payroll", "description": "List Checker Report Payroll", "operationId": "listCheckerReportPayroll", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnBulkTransferRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListTxnBulkTransferListResponseDto"}}}}}}}, "/fo-report/payroll/checker/export/1.0": {"post": {"tags": ["foReport"], "summary": "Export Checker Report Payroll", "description": "Export Checker Report Payroll", "operationId": "exportCheckerReportPayroll", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnBulkTransferRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnPrintResponseDto"}}}}}}}, "/fo-report/payroll/checker/detail/export/1.0": {"post": {"tags": ["foReport"], "summary": "API Export Report Checker Payroll", "description": "API Export Report Checker Payroll", "operationId": "getCheckerReportPayroll", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportReportRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportReportResponseDto"}}}}}}}, "/fo-report/payroll/all/list/1.0": {"post": {"tags": ["foReport"], "summary": "Payroll transfer all list", "description": "Payroll transfer all list", "operationId": "getPayrollList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkTransferBaseCriteriaRequestDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListTxnBulkTransferDto"}}}}}}}, "/fo-report/log/txn/inquiry/1.0": {"post": {"tags": ["foReport"], "summary": "log transaction", "description": "API log transaction", "operationId": "inquiryTxnLogs", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnLogCriteriaDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListTxnLogDto"}}}}}}}, "/fo-report/inquiry/transfer/report/detail/1.0": {"post": {"tags": ["foReport"], "summary": "Get Detail Payment Inquiry (Report)", "description": "Get Detail Payment Inquiry (Report)", "operationId": "getReportDetailInquiry", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnInquiryDetailRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TxnInquiryDetailResponseDto"}}}}}}}, "/fo-report/inquiry/report/list/1.0": {"post": {"tags": ["foReport"], "summary": "Get List Payment Inquiry Report", "description": "Get List Payment Inquiry Report", "operationId": "getListInquiryList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnInquiryListRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataListTxnInquiryResponseDto"}}}}}}}, "/fo-report/inquiry/print/1.0": {"post": {"tags": ["foReport"], "summary": "Get Print Payment Inquiry", "description": "Get Print Payment Inquiry", "operationId": "getPrintInquiry", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnPrintRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TxnPrintResponseDto"}}}}}}}, "/fo-report/inquiry/export/1.0": {"post": {"tags": ["foReport"], "summary": "Get Report Payment Inquiry", "description": "Get Report Payment Inquiry", "operationId": "getReportInquiry", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnInquiryListRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TxnPrintResponseDto"}}}}}}}, "/fo-report/inquiry/bulk/report/detail/1.0": {"post": {"tags": ["foReport"], "summary": "Get Detail Payment Inquiry Bulk (Report)", "description": "Get Detail Payment Inquiry Bulk (Report)", "operationId": "getReportDetailInquiryBulk", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnInquiryDetailRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TxnInquiryBulkDetailResponseDto"}}}}}}}, "/fo-report/inquiry/bulk-child/report/detail/1.0": {"post": {"tags": ["foReport"], "summary": "Get Detail Payment Inquiry BulkChild (Report)", "description": "Get Detail Payment Inquiry BulkChild (Report)", "operationId": "getReportDetailInquiryBulkChild", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnInquiryDetailRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TxnInquiryBulkChildDetailResponseDto"}}}}}}}, "/fo-report/bulk/report/maker/list/1.0": {"post": {"tags": ["foReport"], "summary": "List Maker Report Bulk", "description": "List Maker Report Bulk", "operationId": "listMakerReportBulk", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnBulkTransferRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListTxnBulkTransferListResponseDto"}}}}}}}, "/fo-report/bulk/report/checker/export/1.0": {"post": {"tags": ["foReport"], "summary": "Export Checker Report Bulk", "description": "Export Checker Report Bulk", "operationId": "exportCheckerReportBulk", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnBulkTransferRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnPrintResponseDto"}}}}}}}, "/fo-report/bulk/maker/export/1.0": {"post": {"tags": ["foReport"], "summary": "Export Maker Report Bulk", "description": "Export Maker Report Bulk", "operationId": "exportMakerReportBulk", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnBulkTransferRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnPrintResponseDto"}}}}}}}, "/fo-report/bulk/maker/detail/export/1.0": {"post": {"tags": ["foReport"], "summary": "API Export Report Maker Bulk", "description": "API Export Report Maker Bulk", "operationId": "getMakerReportBulk", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportReportRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportReportResponseDto"}}}}}}}, "/fo-report/bulk/checker/list/1.0": {"post": {"tags": ["foReport"], "summary": "List Checker Report Bulk", "description": "List Checker Report Bulk", "operationId": "listCheckerReportBulk", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnBulkTransferRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListTxnBulkTransferListResponseDto"}}}}}}}, "/fo-report/bulk/checker/detail/export/1.0": {"post": {"tags": ["foReport"], "summary": "API Export Report Checker Bulk", "description": "API Export Report Checker Bulk", "operationId": "getCheckerReportBulk", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportReportRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportReportResponseDto"}}}}}}}, "/fo-report/bulk/checker/detail/1.0": {"post": {"tags": ["foReport"], "summary": "Detail Checker Report Bulk", "description": "Detail Checker Report Bulk", "operationId": "detailCheckerReportBulk", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnBullkTransferDetailRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnBulkTransferDetailResponseDto"}}}}}}}, "/fo-report/bulk-transfer/all/list/1.0": {"post": {"tags": ["foReport"], "summary": "Bulk transfer all list", "description": "Bulk transfer all list", "operationId": "getBulkTransferList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkTransferBaseCriteriaRequestDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListTxnBulkTransferDto"}}}}}}}}, "components": {"schemas": {"Filter": {"type": "object", "properties": {"operator": {"type": "string", "description": "ct/ eq/ neq/ gt/ gte/ lt/ lte"}, "field": {"type": "string"}, "value": {"type": "string"}}}, "Order": {"type": "object", "properties": {"field": {"type": "string"}, "direction": {"type": "string"}}}, "Page": {"type": "object", "properties": {"pageSize": {"minimum": 1, "type": "integer", "description": "Row number/ page, min = 1", "format": "int32"}, "pageNum": {"minimum": 1, "type": "integer", "description": "Page index (start from 1), min = 1", "format": "int32"}, "getTotal": {"type": "boolean", "description": "Get total record size flag"}}}, "TxnWithdrawDepositRptCriteriaDto": {"type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/components/schemas/Order"}}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/Filter"}}, "page": {"$ref": "#/components/schemas/Page"}, "statuses": {"type": "array", "items": {"type": "string"}}, "withdrawTypes": {"type": "array", "items": {"type": "string"}}, "fromEffDate": {"type": "string"}, "toEffDate": {"type": "string"}, "keyword": {"type": "string"}}}, "ExportDepositDto": {"type": "object", "properties": {"url": {"type": "string"}}, "description": "Data"}, "ResponseError": {"type": "object", "properties": {"errorCode": {"type": "string"}, "errorDesc": {"type": "string"}, "refVal": {"type": "object"}}, "description": "Addition Error List"}, "ResultExportDepositDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/ExportDepositDto"}}}, "DataListTxnWithdrawDepositRptDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/TxnWithdrawDepositRptDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListTxnWithdrawDepositRptDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListTxnWithdrawDepositRptDto"}}}, "TxnWithdrawDepositRptDto": {"type": "object", "properties": {"id": {"type": "string"}, "status": {"type": "string"}, "statusDesc": {"type": "string"}, "effDate": {"type": "string"}, "debitAccNo": {"type": "string"}, "amount": {"type": "string"}, "earnedInterestAmt": {"type": "string"}, "ccy": {"type": "string"}, "creditAccNo": {"type": "string"}, "createdBy": {"type": "string"}, "approvalUsers": {"type": "string"}, "withdrawType": {"type": "string"}, "withdrawTypeDesc": {"type": "string"}, "remainingAmt": {"type": "string"}, "createdDate": {"type": "string"}}, "description": "List data"}, "TxnWithdrawDepositRptDetailCriteriaDto": {"required": ["txnId"], "type": "object", "properties": {"txnId": {"type": "string"}}}, "ResultTxnWithdrawDepositRptDetailDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/TxnWithdrawDepositRptDetailDto"}}}, "TxnWithdrawDepositRptDetailDto": {"type": "object", "properties": {"debitAccNo": {"type": "string"}, "debitAccName": {"type": "string"}, "creditAccNo": {"type": "string"}, "creditAccName": {"type": "string"}, "amount": {"type": "string"}, "amountDesc": {"type": "string"}, "ccy": {"type": "string"}, "nettSettlementAmt": {"type": "string"}, "nettSettlementAmtDesc": {"type": "string"}, "id": {"type": "string"}, "status": {"type": "string"}, "statusDesc": {"type": "string"}, "effDate": {"type": "string"}, "earnedInterestAmt": {"type": "string"}, "penaltyAmt": {"type": "string"}, "remainingAmt": {"type": "string"}, "withdrawType": {"type": "string"}, "withdrawTypeDesc": {"type": "string"}, "channel": {"type": "string"}, "txnCode": {"type": "string"}}, "description": "Data"}, "DataListTxnCountResponseDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/TxnCountResponseDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListTxnCountResponseDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListTxnCountResponseDto"}}}, "TxnCountDto": {"type": "object", "properties": {"txnCode": {"type": "string"}, "menuCode": {"type": "string"}, "count": {"type": "integer", "format": "int32"}, "status": {"type": "string"}, "priority": {"type": "integer", "format": "int32"}}}, "TxnCountResponseDto": {"type": "object", "properties": {"txnCountDtos": {"type": "array", "items": {"$ref": "#/components/schemas/TxnCountDto"}}, "code": {"type": "string"}, "total": {"type": "integer", "format": "int32"}}, "description": "List data"}, "FundTransferBaseCriteriaRequestDto": {"type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/components/schemas/Order"}}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/Filter"}}, "page": {"$ref": "#/components/schemas/Page"}, "keyword": {"maxLength": 70, "minLength": 0, "type": "string", "description": "Using keyword for filter by some fields"}, "startDate": {"type": "string", "description": "Filter by createDate greater than or equal to input value"}, "endDate": {"type": "string", "description": "Filter by createDate less than or equal to input value"}, "startEffDate": {"type": "string", "description": "Filter by effDate greater than or equal to input value"}, "endEffDate": {"type": "string", "description": "Filter by effDate less than or equal to input value"}, "debitAccNo": {"maxLength": 14, "minLength": 0, "type": "string", "description": "Filter by debitAccNo"}, "benValue": {"type": "string", "description": "Filter by ben ident/card/acc No"}, "amountMin": {"type": "string", "description": "Filter by amountMin"}, "amountMax": {"type": "string", "description": "Filter by amountMax"}, "batchNo": {"maxLength": 20, "minLength": 0, "type": "string", "description": "Filter batch No"}, "channel": {"type": "array", "description": "Filter by channel", "items": {"type": "string", "description": "Filter by channel"}}, "ccy": {"maxLength": 3, "minLength": 0, "type": "string", "description": "Filter by transaction currency"}, "benBankCode": {"type": "array", "description": "Filter by benBankCode", "items": {"type": "string", "description": "Filter by benBankCode"}}, "txnType": {"type": "array", "description": "Filter by txnType", "items": {"type": "string", "description": "Filter by txnType"}}, "status": {"maxItems": 20, "minItems": 0, "type": "array", "description": "Filter by status, leave empty to get all", "items": {"type": "string", "description": "Filter by status, leave empty to get all"}}, "coreRef": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Filter by core ref, leave empty to get all"}}}, "ExportDocumentResponseDto": {"type": "object", "properties": {"url": {"type": "string"}}, "description": "Data"}, "ResultExportDocumentResponseDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/ExportDocumentResponseDto"}}}, "FundTransferDetailRequestDto": {"required": ["id"], "type": "object", "properties": {"id": {"type": "string"}}}, "FundTransferDetailDto": {"type": "object", "properties": {"id": {"type": "string"}, "debitAccNo": {"type": "string"}, "debitAccName": {"type": "string"}, "benBankCode": {"type": "string"}, "benBankName": {"type": "string"}, "benBankNameShort": {"type": "string"}, "benAccNo": {"type": "string"}, "benName": {"type": "string"}, "benId": {"type": "string"}, "benIssueDate": {"type": "string"}, "benIssueAddr": {"type": "string"}, "benCardNo": {"type": "string"}, "benBrcd": {"type": "string"}, "feeAccNo": {"type": "string"}, "feeMethod": {"type": "string"}, "feeOpt": {"type": "string"}, "feeTotal": {"type": "string"}, "txnType": {"type": "string"}, "txnCode": {"type": "string"}, "channel": {"type": "string"}, "effDate": {"type": "string"}, "status": {"type": "string"}, "statusDesc": {"type": "string"}, "txnDesc": {"type": "string"}, "createdBy": {"type": "string"}, "raNote": {"type": "string"}, "approvalNote": {"type": "string"}, "orgId": {"type": "string"}, "priority": {"type": "boolean"}, "amount": {"type": "string"}, "batchNo": {"type": "string"}, "ccy": {"type": "string"}, "feeCcy": {"type": "string"}, "remark": {"type": "string"}, "coreRef": {"type": "string"}, "pmhId": {"type": "string"}, "amountText": {"type": "string"}, "pmtMethod": {"type": "string"}, "bankLogoUrl": {"type": "string"}, "ibftBankCode": {"type": "string"}, "benBrcdName": {"type": "string"}, "bankTransferType": {"type": "integer", "format": "int64"}, "stateDescription": {"type": "string"}}, "description": "Data"}, "ResultFundTransferDetailDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/FundTransferDetailDto"}}}, "DataListTxnFundTransferDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/TxnFundTransferDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListTxnFundTransferDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListTxnFundTransferDto"}}}, "TxnFundTransferDto": {"type": "object", "properties": {"id": {"type": "string"}, "debitAccNo": {"type": "string"}, "debitAccName": {"type": "string"}, "status": {"type": "string"}, "state": {"type": "string"}, "statusDesc": {"type": "string"}, "amount": {"type": "string"}, "ccy": {"type": "string"}, "benAccNo": {"type": "string"}, "benId": {"type": "string"}, "benCardNo": {"type": "string"}, "benName": {"type": "string"}, "createdDate": {"type": "string"}, "createdBy": {"type": "string"}, "benBankCode": {"type": "string"}, "benBankNameShort": {"type": "string"}, "remark": {"type": "string"}, "raNote": {"type": "string"}, "effDate": {"type": "string"}, "pmtMethod": {"type": "string"}, "txnType": {"type": "string"}, "channel": {"type": "string"}, "orgId": {"type": "string"}, "batchNo": {"type": "string"}, "benIssueDate": {"type": "string"}, "benIssueAddr": {"type": "string"}, "feeID": {"type": "string"}, "feeOpt": {"type": "string"}, "feeTotal": {"type": "string"}, "benBrcd": {"type": "string"}, "txnDesc": {"type": "string"}, "txnCode": {"type": "string"}, "approvalUsers": {"type": "string"}, "approvedBy": {"type": "string"}, "coreRef": {"type": "string"}, "pmtRef": {"type": "string"}, "feeMethod": {"type": "string", "enum": ["O", "B"]}, "approvedDate": {"type": "string"}, "feeAccNo": {"type": "string"}, "priority": {"type": "string"}, "pmhId": {"type": "string"}, "errorCode": {"type": "string"}}, "description": "List data"}, "TxnTermDepositRptCriteriaDto": {"type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/components/schemas/Order"}}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/Filter"}}, "page": {"$ref": "#/components/schemas/Page"}, "statuses": {"type": "array", "items": {"type": "string"}}, "prdLineCodes": {"type": "array", "items": {"type": "string"}}, "fromEffDate": {"type": "string"}, "toEffDate": {"type": "string"}, "keyword": {"type": "string"}}}, "DataListTxnTermDepositRptDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/TxnTermDepositRptDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListTxnTermDepositRptDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListTxnTermDepositRptDto"}}}, "TxnTermDepositRptDto": {"type": "object", "properties": {"id": {"type": "string"}, "status": {"type": "string"}, "statusDesc": {"type": "string"}, "createdDate": {"type": "string"}, "effDate": {"type": "string"}, "amount": {"type": "string"}, "ccy": {"type": "string"}, "interestRate": {"type": "string"}, "term": {"type": "string"}, "termType": {"type": "string"}, "dpProdLineName": {"type": "string"}, "debitAccNo": {"type": "string"}, "intPaymentFreq": {"type": "string"}, "approvalUsers": {"type": "string"}, "withdrawPart": {"type": "string"}, "createdBy": {"type": "string"}, "fdRptNo": {"type": "string"}, "termDesc": {"type": "string"}, "intPaymentFreqDesc": {"type": "string"}}, "description": "List data"}, "TxnDepositRptDetailCriteriaDto": {"required": ["txnId"], "type": "object", "properties": {"txnId": {"type": "string"}}}, "ResultTxnDepositRptDetailDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/TxnDepositRptDetailDto"}}}, "TxnDepositRptDetailDto": {"type": "object", "properties": {"id": {"type": "string"}, "status": {"type": "string"}, "statusDesc": {"type": "string"}, "createdDate": {"type": "string"}, "effDate": {"type": "string"}, "maturityDate": {"type": "string"}, "amount": {"type": "string"}, "ccy": {"type": "string"}, "termType": {"type": "string"}, "interestRate": {"type": "string"}, "interestAmount": {"type": "string"}, "dpProdLineName": {"type": "string"}, "dpProdLineCode": {"type": "string"}, "dpProdCode": {"type": "string"}, "dpProdName": {"type": "string"}, "debitAccNo": {"type": "string"}, "debitAccName": {"type": "string"}, "creditAccNo": {"type": "string"}, "creditAccNane": {"type": "string"}, "intPaymentFreq": {"type": "string"}, "maturityMethod": {"type": "string"}, "withdrawPart": {"type": "string"}, "maturityMethodDesc": {"type": "string"}, "fdRptNo": {"type": "string"}, "amountDesc": {"type": "string"}, "termDesc": {"type": "string"}, "channel": {"type": "string"}, "txnCode": {"type": "string"}, "intPaymentFreqDesc": {"type": "string"}}, "description": "Data"}, "RevTxnDepositReportCriteriaDto": {"type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/components/schemas/Order"}}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/Filter"}}, "page": {"$ref": "#/components/schemas/Page"}, "keyword": {"type": "string"}, "dpProdLineCodes": {"type": "array", "items": {"type": "string"}}, "statuses": {"type": "array", "items": {"type": "string"}}, "startEffDate": {"type": "string"}, "endEffDate": {"type": "string"}}}, "DataListRevTxnDepositDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/RevTxnDepositDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListRevTxnDepositDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListRevTxnDepositDto"}}}, "RevTxnDepositDto": {"type": "object", "properties": {"orgTxnId": {"type": "string"}, "revTxnId": {"type": "string"}, "status": {"type": "string"}, "statusDesc": {"type": "string"}, "createdDate": {"type": "string"}, "effDate": {"type": "string"}, "orgEffDate": {"type": "string"}, "orgAmount": {"type": "string"}, "ccy": {"type": "string"}, "orgTerm": {"type": "integer", "format": "int64"}, "orgTermDesc": {"type": "string"}, "orgTermType": {"type": "string"}, "orgInterestRate": {"type": "string"}, "dpProdLineName": {"type": "string"}, "orgDebitAccNo": {"type": "string"}, "orgPaymentFreq": {"type": "string"}, "orgPaymentFreqDesc": {"type": "string"}, "orgWithdrawPart": {"type": "string"}, "orgWithdrawPartDesc": {"type": "string"}, "createdUser": {"type": "string"}}, "description": "List data"}, "RevTxnDepositDetailCriteriaDto": {"required": ["revTxnId"], "type": "object", "properties": {"revTxnId": {"type": "string"}}}, "ResultRevTxnDepositDetailDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/RevTxnDepositDetailDto"}}}, "RevTxnDepositDetailDto": {"type": "object", "properties": {"orgTxnId": {"type": "string"}, "revTxnId": {"type": "string"}, "orgCcy": {"type": "string"}, "status": {"type": "string"}, "statusDesc": {"type": "string"}, "createdDate": {"type": "string"}, "effDate": {"type": "string"}, "orgEffDate": {"type": "string"}, "orgMaturityDate": {"type": "string"}, "orgAmount": {"type": "string"}, "orgAmountDesc": {"type": "string"}, "orgTermType": {"type": "string"}, "orgTerm": {"type": "integer", "format": "int64"}, "orgTermDesc": {"type": "string"}, "orgInterestRate": {"type": "string"}, "orgExpectedInterest": {"type": "string"}, "productLineName": {"type": "string"}, "dpProductCode": {"type": "string"}, "dpProductName": {"type": "string"}, "orgDebitAccNo": {"type": "string"}, "orgDebitAccName": {"type": "string"}, "orgCreditAccNo": {"type": "string"}, "orgCreditAccName": {"type": "string"}, "orgPaymentFreq": {"type": "string"}, "orgPaymentFreqDesc": {"type": "string"}, "orgMaturityMethod": {"type": "string"}, "orgWithdrawPart": {"type": "string"}, "orgWithdrawPartDesc": {"type": "string"}, "channel": {"type": "string"}, "txnCode": {"type": "string"}, "productLineCode": {"type": "string"}}, "description": "Data"}, "TxnBulkTransferRequestDto": {"type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/components/schemas/Order"}}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/Filter"}}, "page": {"$ref": "#/components/schemas/Page"}, "keyword": {"type": "string"}, "status": {"type": "array", "items": {"type": "string"}}, "debitAccNo": {"type": "string"}, "createdDateFrom": {"type": "string"}, "createdDateTo": {"type": "string"}, "effDateFrom": {"type": "string"}, "effDateTo": {"type": "string"}, "amountFrom": {"type": "string"}, "amountTo": {"type": "string"}, "ccy": {"type": "string"}, "channel": {"type": "array", "items": {"type": "string"}}}}, "DataListTxnBulkTransferListResponseDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/TxnBulkTransferListResponseDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListTxnBulkTransferListResponseDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListTxnBulkTransferListResponseDto"}}}, "TxnBulkTransferListResponseDto": {"type": "object", "properties": {"createdDate": {"type": "string"}, "status": {"type": "string"}, "id": {"type": "string"}, "debitAccNo": {"type": "string"}, "debitAccName": {"type": "string"}, "amount": {"type": "string"}, "ccy": {"type": "string"}, "totalRow": {"type": "integer", "format": "int64"}, "batchId": {"type": "string"}, "fileName": {"type": "string"}, "effDate": {"type": "string"}, "orgId": {"type": "string"}, "createdBy": {"type": "string"}, "approvalUsers": {"type": "string"}, "channel": {"type": "string"}, "remark": {"type": "string"}, "feeAccNo": {"type": "string"}, "successRow": {"type": "integer", "format": "int64"}, "errorRow": {"type": "integer", "format": "int64"}, "feeOpt": {"type": "string"}, "feeTotal": {"type": "string"}, "approvedDate": {"type": "string"}, "statusDesc": {"type": "string"}}, "description": "List data"}, "ResultTxnPrintResponseDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/TxnPrintResponseDto"}}}, "TxnPrintResponseDto": {"type": "object", "properties": {"url": {"type": "string"}}, "description": "Data"}, "ExportReportRequestDto": {"required": ["id"], "type": "object", "properties": {"id": {"maxLength": 50, "minLength": 0, "type": "string"}}}, "ExportReportResponseDto": {"type": "object", "properties": {"url": {"type": "string"}}, "description": "Data"}, "ResultExportReportResponseDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/ExportReportResponseDto"}}}, "BulkTransferBaseCriteriaRequestDto": {"type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/components/schemas/Order"}}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/Filter"}}, "page": {"$ref": "#/components/schemas/Page"}, "keyword": {"maxLength": 70, "minLength": 0, "type": "string", "description": "Using keyword for filter by some fields"}, "startEffDate": {"type": "string", "description": "Filter by effDate greater than or equal to input value"}, "endEffDate": {"type": "string", "description": "Filter by effDate less than or equal to input value"}, "debitAccNo": {"type": "string", "description": "Filter by debitAccNo"}, "benAccNo": {"type": "string", "description": "Filter by <PERSON><PERSON><PERSON>"}, "amountMin": {"type": "string", "description": "Filter by amountMin"}, "amountMax": {"type": "string", "description": "Filter by amountMax"}, "batchNo": {"type": "string", "description": "Filter batch No"}, "channel": {"type": "array", "description": "Filter by channel", "items": {"type": "string", "description": "Filter by channel"}}, "ccy": {"type": "string", "description": "Filter by transaction currency"}, "benBankCode": {"type": "array", "description": "Filter by benBankCode", "items": {"type": "string", "description": "Filter by benBankCode"}}, "txnType": {"type": "array", "description": "Filter by txnType", "items": {"type": "string", "description": "Filter by txnType"}}, "startDate": {"type": "string", "description": "Filter by createDate greater than or equal to input value"}, "endDate": {"type": "string", "description": "Filter by createDate less than or equal to input value"}, "status": {"maxItems": 20, "minItems": 0, "type": "array", "description": "Filter by status, leave empty to get all", "items": {"type": "string", "description": "Filter by status, leave empty to get all"}}}}, "DataListTxnBulkTransferDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/TxnBulkTransferDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListTxnBulkTransferDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListTxnBulkTransferDto"}}}, "TxnBulkTransferDto": {"type": "object", "properties": {"id": {"type": "string"}, "totalRow": {"type": "integer", "format": "int32"}, "amount": {"type": "string"}, "ccy": {"type": "string"}, "debitAccNo": {"type": "string"}, "debitAccType": {"type": "string"}, "debitAccName": {"type": "string"}, "feeAccNo": {"type": "string"}, "feeTotal": {"type": "string"}, "feeOpt": {"type": "string"}, "feeCcy": {"type": "string"}, "feeMethod": {"type": "string"}, "effDate": {"type": "string"}, "channel": {"type": "string"}, "txnCode": {"type": "string"}, "status": {"type": "string"}, "raNote": {"type": "string"}, "approvalNote": {"type": "string"}, "orgId": {"type": "string"}, "batchNo": {"type": "string"}, "batchId": {"type": "string"}, "priority": {"type": "string"}, "createdBy": {"type": "string"}, "totalAmountText": {"type": "string"}, "processDate": {"type": "string"}, "fileName": {"type": "string"}, "createdDate": {"type": "string"}, "remark": {"type": "string"}}, "description": "List data"}, "TxnLogCriteriaDto": {"required": ["txnCode", "txnId"], "type": "object", "properties": {"txnCode": {"type": "string"}, "txnId": {"type": "string"}}}, "DataListTxnLogDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/TxnLogDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListTxnLogDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListTxnLogDto"}}}, "TxnLogDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "txnId": {"type": "string"}, "txnCode": {"type": "string"}, "logType": {"type": "string"}, "logTypeDesc": {"type": "string"}, "status": {"type": "string"}, "statusDesc": {"type": "string"}, "createdDate": {"type": "string"}, "createdBy": {"type": "string"}, "channel": {"type": "string"}, "appcode": {"type": "string"}, "source": {"type": "string"}, "logNote": {"type": "string"}}, "description": "List data"}, "TxnInquiryDetailRequestDto": {"required": ["id"], "type": "object", "properties": {"id": {"maxLength": 50, "minLength": 0, "type": "string"}}}, "ResultTxnInquiryDetailResponseDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/TxnInquiryDetailResponseDto"}}}, "TxnInquiryDetailResponseDto": {"type": "object", "properties": {"id": {"type": "string"}, "status": {"type": "string"}, "createdDate": {"type": "string"}, "feeTotal": {"type": "string"}, "inquiryReason": {"type": "string"}, "createdBy": {"type": "string"}, "benIssueDate": {"type": "string"}, "benIssueAddr": {"type": "string"}, "bankLogoUrl": {"type": "string"}, "inquiryResult": {"type": "string"}, "inquiryAnswer": {"type": "string"}, "feeMethod": {"type": "string"}, "amount": {"type": "string"}, "ccy": {"type": "string"}, "debitAccNo": {"type": "string"}, "debitAccName": {"type": "string"}, "orgTxnId": {"type": "string"}, "txnType": {"type": "string"}, "txnStatus": {"type": "string"}, "remark": {"type": "string"}, "inquiryDesc": {"type": "string"}, "txnCode": {"type": "string"}, "txnTypeInquiry": {"type": "string"}, "feeOpt": {"type": "string"}, "statusDesc": {"type": "string"}, "inquiryReasonDesc": {"type": "string"}, "inquiryResultDesc": {"type": "string"}, "txnTypeInquiryDesc": {"type": "string"}, "txnStatusDesc": {"type": "string"}, "feeMethodDesc": {"type": "string"}, "benBankNameShort": {"type": "string"}, "benAccNo": {"type": "string"}, "benName": {"type": "string"}, "effDate": {"type": "string"}, "benId": {"type": "string"}, "benCardNo": {"type": "string"}}, "description": "Data"}, "TxnInquiryListRequestDto": {"type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/components/schemas/Order"}}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/Filter"}}, "page": {"$ref": "#/components/schemas/Page"}, "createdDateFrom": {"type": "string"}, "createdDateTo": {"type": "string"}, "txnType": {"maxItems": 30, "minItems": 0, "type": "array", "items": {"maxLength": 30, "minLength": 0, "type": "string"}}, "inquiryReason": {"maxLength": 30, "minLength": 0, "type": "string"}, "createdBy": {"maxLength": 30, "minLength": 0, "type": "string"}, "status": {"maxItems": 20, "minItems": 0, "type": "array", "items": {"maxLength": 20, "minLength": 0, "type": "string"}}, "keyword": {"maxLength": 4000, "minLength": 0, "type": "string"}}}, "DataListTxnInquiryResponseDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/TxnInquiryResponseDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListTxnInquiryResponseDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListTxnInquiryResponseDto"}}}, "TxnInquiryResponseDto": {"type": "object", "properties": {"id": {"type": "string"}, "orgId": {"type": "string"}, "txnType": {"type": "string"}, "status": {"type": "string"}, "createdDate": {"type": "string"}, "createdBy": {"type": "string"}, "approvedBy": {"type": "string"}, "inquiryReason": {"type": "string"}, "amount": {"type": "integer", "format": "int64"}, "ccy": {"type": "string"}, "txnTypeDesc": {"type": "string"}, "inquiryReasonDesc": {"type": "string"}, "statusDesc": {"type": "string"}}, "description": "List data"}, "TxnPrintRequestDto": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string"}}}}, "ResultTxnInquiryBulkDetailResponseDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/TxnInquiryBulkDetailResponseDto"}}}, "TxnInquiryBulkDetailResponseDto": {"type": "object", "properties": {"id": {"type": "string"}, "status": {"type": "string"}, "createdDate": {"type": "string"}, "feeTotal": {"type": "string"}, "inquiryReason": {"type": "string"}, "createdBy": {"type": "string"}, "benIssueDate": {"type": "string"}, "benIssueAddr": {"type": "string"}, "bankLogoUrl": {"type": "string"}, "inquiryResult": {"type": "string"}, "inquiryAnswer": {"type": "string"}, "feeMethod": {"type": "string"}, "amount": {"type": "string"}, "ccy": {"type": "string"}, "debitAccNo": {"type": "string"}, "debitAccName": {"type": "string"}, "orgTxnId": {"type": "string"}, "txnType": {"type": "string"}, "txnStatus": {"type": "string"}, "remark": {"type": "string"}, "inquiryDesc": {"type": "string"}, "txnCode": {"type": "string"}, "txnTypeInquiry": {"type": "string"}, "feeOpt": {"type": "string"}, "statusDesc": {"type": "string"}, "inquiryReasonDesc": {"type": "string"}, "inquiryResultDesc": {"type": "string"}, "txnTypeInquiryDesc": {"type": "string"}, "txnStatusDesc": {"type": "string"}, "feeMethodDesc": {"type": "string"}, "totalRow": {"type": "integer", "format": "int32"}, "textAmount": {"type": "string"}, "effDate": {"type": "string"}}, "description": "Data"}, "ResultTxnInquiryBulkChildDetailResponseDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/TxnInquiryBulkChildDetailResponseDto"}}}, "TxnInquiryBulkChildDetailResponseDto": {"type": "object", "properties": {"id": {"type": "string"}, "status": {"type": "string"}, "createdDate": {"type": "string"}, "feeTotal": {"type": "string"}, "inquiryReason": {"type": "string"}, "createdBy": {"type": "string"}, "benIssueDate": {"type": "string"}, "benIssueAddr": {"type": "string"}, "bankLogoUrl": {"type": "string"}, "inquiryResult": {"type": "string"}, "inquiryAnswer": {"type": "string"}, "feeMethod": {"type": "string"}, "amount": {"type": "string"}, "ccy": {"type": "string"}, "debitAccNo": {"type": "string"}, "debitAccName": {"type": "string"}, "orgTxnId": {"type": "string"}, "txnType": {"type": "string"}, "txnStatus": {"type": "string"}, "remark": {"type": "string"}, "inquiryDesc": {"type": "string"}, "txnCode": {"type": "string"}, "txnTypeInquiry": {"type": "string"}, "feeOpt": {"type": "string"}, "statusDesc": {"type": "string"}, "inquiryReasonDesc": {"type": "string"}, "inquiryResultDesc": {"type": "string"}, "txnTypeInquiryDesc": {"type": "string"}, "txnStatusDesc": {"type": "string"}, "feeMethodDesc": {"type": "string"}, "benAccNo": {"type": "string"}, "benName": {"type": "string"}, "bulkId": {"type": "string"}, "benBankNameShort": {"type": "string"}, "benId": {"type": "string"}, "effDate": {"type": "string"}}, "description": "Data"}, "TxnBullkTransferDetailRequestDto": {"required": ["id"], "type": "object", "properties": {"id": {"maxLength": 50, "minLength": 0, "type": "string"}}}, "ResultTxnBulkTransferDetailResponseDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/TxnBulkTransferDetailResponseDto"}}}, "TxnBulkTransferDetailResponseDto": {"type": "object", "properties": {"id": {"type": "string"}, "totalRow": {"type": "integer", "format": "int32"}, "amount": {"type": "string"}, "ccy": {"type": "string"}, "debitAccNo": {"type": "string"}, "debitAccType": {"type": "string"}, "debitAccName": {"type": "string"}, "feeAccNo": {"type": "string"}, "feeTotal": {"type": "string"}, "feeOpt": {"type": "string"}, "feeCcy": {"type": "string"}, "feeMethod": {"type": "string", "enum": ["O", "B"]}, "effDate": {"type": "string"}, "channel": {"type": "string"}, "txnCode": {"type": "string"}, "status": {"type": "string"}, "raNote": {"type": "string"}, "approvalNote": {"type": "string"}, "orgId": {"type": "string"}, "batchNo": {"type": "string"}, "batchId": {"type": "string"}, "priority": {"type": "boolean"}, "createdBy": {"type": "string"}, "totalAmountText": {"type": "string"}, "fileName": {"type": "string"}, "createdDate": {"type": "string"}, "remark": {"type": "string"}, "approvalUsers": {"type": "string"}, "approvedBy": {"type": "string"}, "state": {"type": "string"}, "errorCode": {"type": "string"}, "statusDesc": {"type": "string"}, "description": {"type": "string"}, "approvedDate": {"type": "string"}}, "description": "Data"}}}, "tags": ["foReport"], "formated": "1"}