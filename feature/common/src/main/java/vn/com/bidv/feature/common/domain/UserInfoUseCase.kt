package vn.com.bidv.feature.common.domain

import com.google.gson.Gson
import vn.com.bidv.common.sharePreference.Storage
import vn.com.bidv.feature.common.constants.FunctionCode
import vn.com.bidv.feature.common.constants.MenuCode
import vn.com.bidv.feature.common.constants.TypePermission
import vn.com.bidv.feature.common.domain.data.GetUserInfoResponseDMO
import vn.com.bidv.feature.common.domain.data.MenuDMO
import vn.com.bidv.feature.common.domain.data.PermissionResDMO
import vn.com.bidv.feature.common.domain.data.TemplateTransactionTabDMO
import vn.com.bidv.feature.common.domain.data.TransactionRouteID
import vn.com.bidv.feature.common.domain.data.TransactionTabRouteID
import vn.com.bidv.feature.common.domain.data.toTransactionRouteID
import vn.com.bidv.feature.common.utils.CommonDataKeys
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.domain.DomainResult
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class UserInfoUseCase @Inject constructor() {

    private var cachedUserInfo: GetUserInfoResponseDMO? = null

    fun isHavePermission(permissions: List<PermissionResDMO>): Boolean {
        return isCheckPermission(permissions)
    }

    private fun isCheckPermission(
        permissions: List<PermissionResDMO>,
        listPermission: List<PermissionResDMO> = getPermissionRes()
    ): Boolean {
        if (permissions.isEmpty()) return false
        return if (permissions.size == 1) {
            listPermission.any { it.code == permissions[0].code && it.type == permissions[0].type }
        } else {
            val permission =
                listPermission.find { it.code == permissions[0].code && it.type == permissions[0].type }
            if (permission != null) {
                isCheckPermission(permissions.drop(1), permission.children ?: listPermission)
            } else {
                false
            }
        }
    }

    fun getListFunctionCode(menuCode: MenuCode): List<FunctionCode> {
        val dataMenu = extractMenuList().find {
            it.type.equals(TypePermission.MENU.value, true)
                    && menuCode.code.equals(it.code, true)
        }
        return dataMenu?.children?.filter {
            it.type.equals(TypePermission.FUNCTION.value, true)
        }?.mapNotNull { FunctionCode.from(it.code) } ?: emptyList()
    }

    private fun extractMenuList(listPermission: List<PermissionResDMO>? = getPermissionRes()): List<PermissionResDMO> {
        if (listPermission.isNullOrEmpty()) return emptyList()
        val listExtract = mutableListOf<PermissionResDMO>()
        listPermission.filter { it.getTypePermission() == TypePermission.MENU }.forEach {
            listExtract.add(it)
            listExtract.addAll(extractMenuList(it.children))
        }
        return listExtract
    }

    fun getPermissionRes(): List<PermissionResDMO> {
        val userInfo = getUserInfoFromStorage()
        return if (userInfo is DomainResult.Success) {
            userInfo.data?.permissions ?: emptyList()
        } else {
            emptyList()
        }
    }

    fun isAdminRole(): Boolean {
        return getUserInfoFromStorage().getSafeData()?.isAdminRole() == true
    }

    fun isMakerRole(): Boolean {
        return getUserInfoFromStorage().getSafeData()?.isMakerRole() == true
    }

    private fun findPermissionsByCode(
        permissions: List<PermissionResDMO>?,
        targetCode: String
    ): List<PermissionResDMO>? {
        return permissions?.find { it.code == targetCode }?.children
    }

    fun getReportMenus(): DomainResult<List<MenuDMO>> {
        val utilityMenus =
            findPermissionsByCode(getPermissionRes(), MenuCode.M_UTILITY()) ?: emptyList()
        val reportMenus =
            findPermissionsByCode(utilityMenus, MenuCode.M_UTILITY_LUP_RPT()) ?: emptyList()
        val menus = mutableListOf<MenuDMO>()
        reportMenus.forEach { menu ->
            if (menu.code?.toTransactionRouteID() != TransactionRouteID.DEFAULT) {
                menus.add(MenuDMO(code = menu.code ?: ""))
            }
        }
        return DomainResult.Success(menus)
    }

    fun getApprovalMenus(): List<MenuDMO> {
        val approvalMenus =
            findPermissionsByCode(getPermissionRes(), MenuCode.M_APPROVE()) ?: emptyList()
        val menus = mutableListOf<MenuDMO>()
        approvalMenus.forEach { menu ->
            if (menu.code?.toTransactionRouteID() != TransactionRouteID.DEFAULT) {
                menus.add(
                    MenuDMO(
                        isHeader = true,
                        code = menu.code ?: "",
                    )
                )
                menu.children?.forEach { child ->
                    if (child.code?.toTransactionRouteID() != TransactionRouteID.DEFAULT) {
                        menus.add(
                            MenuDMO(
                                code = child.code ?: "",
                            )
                        )
                    }
                }
            }
        }
        return menus
    }

    fun getUserName(): String {
        val userInfo = getUserInfoFromStorage()
        return if (userInfo is DomainResult.Success) {
            userInfo.data?.user?.fullname ?: ""
        } else {
            ""
        }
    }

    fun getCifName(): String {
        val userInfo = getUserInfoFromStorage()
        return if (userInfo is DomainResult.Success) {
            userInfo.data?.user?.cifName ?: ""
        } else {
            ""
        }
    }

    fun getQuickLinks(): List<PermissionResDMO> {
        val userInfo = getUserInfoFromStorage()
        return if (userInfo is DomainResult.Success) {
            userInfo.data?.quickLinks?.map { code ->
                PermissionResDMO(
                    code = code,
                    type = TypePermission.MENU.value
                )
            } ?: emptyList()
        } else {
            emptyList()
        }
    }

    fun getWidgets(): List<String> {
        val userInfo = getUserInfoFromStorage()
        return if (userInfo is DomainResult.Success) {
            userInfo.data?.widgets ?: emptyList()
        } else {
            emptyList()
        }
    }

    fun getUsId(): String {
        val userInfo = getUserInfoFromStorage()
        return if (userInfo is DomainResult.Success) {
            userInfo.data?.user?.usid ?: ""
        } else {
            ""
        }
    }

    fun getUserInfoFromStorage(): DomainResult<GetUserInfoResponseDMO> {
        if (cachedUserInfo == null) {
            cachedUserInfo = try {
                Gson().fromJson(
                    Storage.get(CommonDataKeys.USER_PROFILE),
                    GetUserInfoResponseDMO::class.java
                )
            } catch (e: Exception) {
                null
            }
        }
        return DomainResult.Success(cachedUserInfo)
    }

    fun changeQuickLinkUserInfoFromStorage(quickLinks: List<String>) {
        cachedUserInfo = cachedUserInfo?.copy(
            quickLinks = quickLinks
        )
        Storage.put(CommonDataKeys.USER_PROFILE, Gson().toJson(cachedUserInfo))
    }

    fun changeWidgetUserInfoFromStorage(widgets: List<String>) {
        cachedUserInfo = cachedUserInfo?.copy(
            widgets = widgets
        )
        Storage.put(CommonDataKeys.USER_PROFILE, Gson().toJson(cachedUserInfo))
    }

    fun clearUserInfoCache() {
        cachedUserInfo = null
        Storage.put(CommonDataKeys.USER_PROFILE, "")
    }

    fun getTemplateTab(): List<TemplateTransactionTabDMO> {
        val utilsMenu = findPermissionsByCode(getPermissionRes(), MenuCode.M_UTILITY())
        val templateMenu = findPermissionsByCode(
            utilsMenu,
            MenuCode.M_UTILITY_LUP_TPL()
        )?.map { it.code } ?: return emptyList()

        return listOfNotNull(
            MenuCode.M_UTILITY_LUP_TPL_PMT() to TemplateTransactionTabDMO(
                titleResourceID = R.string.chuyen_tien,
                routeID = TransactionTabRouteID.PaymentTab
            ),
            MenuCode.M_UTILITY_LUP_TPL_BILL() to TemplateTransactionTabDMO(
                titleResourceID = R.string.thanh_toan_hoa_don_va_nap_tien,
                routeID = TransactionTabRouteID.CnrTab
            )
        ).mapNotNull { (code, tab) -> tab.takeIf { templateMenu.contains(code) } }
    }

    fun changeAvatarUserInfoFromStorage(profileImage: String?) {
        if (profileImage != null) {
            cachedUserInfo = cachedUserInfo?.let {
                it.copy(user = it.user?.copy(profileImage = profileImage))
            }
            Storage.put(CommonDataKeys.USER_PROFILE, Gson().toJson(cachedUserInfo))
        }
    }

    fun saveUserInfoToStorage(userInfoResponseDMO: GetUserInfoResponseDMO) {
        cachedUserInfo = null
        Storage.put(CommonDataKeys.USER_PROFILE, Gson().toJson(userInfoResponseDMO))
    }
}