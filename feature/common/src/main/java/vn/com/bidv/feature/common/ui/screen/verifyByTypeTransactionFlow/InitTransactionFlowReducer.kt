package vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InitVerifyTransactionResponse

class InitTransactionFlowReducer :
    Reducer<InitTransactionFlowReducer.InitTransactionFlowViewState, InitTransactionFlowReducer.InitTransactionFlowViewEvent, InitTransactionFlowReducer.InitTransactionFlowViewEffect> {

    @Immutable
    data class InitTransactionFlowViewState(
        val isInitVerify: Boolean = false,
        val transKey: String? = null,
    ) : ViewState

    @Immutable
    sealed class InitTransactionFlowViewEvent : ViewEvent {
        data class DoInitVerifyTransaction(val txnIds: List<String>,val type: String, val confirm: Boolean = false) : InitTransactionFlowViewEvent()
        data class DoInitVerifyCreateTransaction(val dataString: String,val type: String, val confirm: Boolean = false) : InitTransactionFlowViewEvent()
        data class DoInitVerifyTransactionSuccessEvent(val data: InitVerifyTransactionResponse?) : InitTransactionFlowViewEvent()
        data class DoInitVerifyTransactionFailEvent(val errorMessage: String?): InitTransactionFlowViewEvent()
        data class DoHandleInitVerifyTransactionWarningEvent(val errorMessage: String?, val statusCode: String?): InitTransactionFlowViewEvent()
        data class DoInitVerifyTransactionWarningEventSuccess(val typeClick: ClickOptionTypePopupWarning, val errorMessage: String?): InitTransactionFlowViewEvent()
    }

    @Immutable
    sealed class InitTransactionFlowViewEffect : SideEffect {
        data class DoInitVerifyTransactionEffect(val txnIds: List<String>,val type: String, val confirm: Boolean = false) : InitTransactionFlowViewEffect()
        data class DoInitVerifyCreateTransactionEffect(val dataString: String,val type: String, val confirm: Boolean = false) : InitTransactionFlowViewEffect()
        data class DoInitVerifyTransactionSuccessEffect(val data: InitVerifyTransactionResponse?) : InitTransactionFlowViewEffect(), UIEffect
        data class DoInitVerifyTransactionSuccessFailEffect(val errorMessage: String?): InitTransactionFlowViewEffect(), UIEffect
        data class DoHandleInitVerifyTransactionSuccessWarningEffect(val errorMessage: String?, val statusCode: String?): InitTransactionFlowViewEffect()
        data class DoInitVerifyTransactionSuccessWarningEffect(val typeClick: ClickOptionTypePopupWarning, val errorMessage: String? ): InitTransactionFlowViewEffect(), UIEffect
    }

    override fun reduce(
        previousState: InitTransactionFlowViewState,
        event: InitTransactionFlowViewEvent,
    ): Pair<InitTransactionFlowViewState, InitTransactionFlowViewEffect?> {
        return when (event) {
            is InitTransactionFlowViewEvent.DoInitVerifyTransaction -> {
                previousState.copy(
                    isInitVerify = true
                ) to InitTransactionFlowViewEffect.DoInitVerifyTransactionEffect(event.txnIds, event.type, confirm = event.confirm)
            }

            is InitTransactionFlowViewEvent.DoInitVerifyTransactionSuccessEvent -> {
                previousState.copy(
                    isInitVerify = true,
                    transKey = event.data?.transKey,
                ) to InitTransactionFlowViewEffect.DoInitVerifyTransactionSuccessEffect(event.data)
            }

            is InitTransactionFlowViewEvent.DoInitVerifyTransactionFailEvent -> {
                previousState to InitTransactionFlowViewEffect.DoInitVerifyTransactionSuccessFailEffect(event.errorMessage)
            }

            is InitTransactionFlowViewEvent.DoHandleInitVerifyTransactionWarningEvent -> {
                previousState to InitTransactionFlowViewEffect.DoHandleInitVerifyTransactionSuccessWarningEffect(event.errorMessage, event.statusCode)
            }

            is InitTransactionFlowViewEvent.DoInitVerifyCreateTransaction -> {
                previousState.copy(
                    isInitVerify = true,
                ) to InitTransactionFlowViewEffect.DoInitVerifyCreateTransactionEffect(event.dataString, event.type, confirm = event.confirm)
            }

            is InitTransactionFlowViewEvent.DoInitVerifyTransactionWarningEventSuccess -> {
                previousState to InitTransactionFlowViewEffect.DoInitVerifyTransactionSuccessWarningEffect(event.typeClick, event.errorMessage)
            }
        }
    }
}
