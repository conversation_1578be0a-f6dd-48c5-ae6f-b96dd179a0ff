package vn.com.bidv.feature.common.domain.smartotp

import com.google.gson.Gson
import com.google.gson.JsonParser
import vn.com.bidv.feature.common.constants.Constants
import vn.com.bidv.feature.common.constants.SmartOTPError
import vn.com.bidv.feature.common.domain.data.UserActiveSmartOtpDMO
import vn.com.bidv.feature.common.domain.data.UserSmartOtpDMO
import vn.com.bidv.log.BLogUtil
import vn.com.bidv.sdkbase.domain.DomainResult

class SmartOTPUseCase(
    private val iSmartOTPUseCase: ISmartOTPUseCase
) {

    fun getListUserSmartOtpDMO(): List<UserSmartOtpDMO> {
        return iSmartOTPUseCase.getListUserSmartOtpDMO()
    }

    fun saveUserActiveSmartOtpDMO(newItem: UserActiveSmartOtpDMO) {
        iSmartOTPUseCase.saveUserActiveSmartOtpDMO(newItem)
    }

    fun checkUserActiveSmartOtp(userId: String): Boolean {
        return getListUserSmartOtpDMO().any { it.userId == userId }
    }

    fun checkPinBlocked(userId: String): Boolean {
        return iSmartOTPUseCase.checkPinBlocked(userId)
    }

    fun verifyPin(
        pin: String,
        userId: String,
    ): DomainResult<UserActiveSmartOtpDMO> {
        val isCheckPinBlocked = checkPinBlocked(userId)
        if (isCheckPinBlocked) {
            return DomainResult.Error(errorCode = SmartOTPError.SMART_OTP_LOCKED.name)
        }
        val validPin = iSmartOTPUseCase.verifyPin(pin, userId)
        if (validPin != null) {
            return DomainResult.Success(validPin)
        }

        val listUserSmartOtpDMO = getListUserSmartOtpDMO()
        val currentUser = listUserSmartOtpDMO.find { it.userId == userId }

        val currentNumberRetry = currentUser?.incorrectTime ?: 0

        val jsonString = Gson().toJson(currentUser)
        val jsonElement = try {
            JsonParser.parseString(jsonString)
        } catch (e: Exception) {
            BLogUtil.e("Error parsing JSON: ${e.message}")
            null
        }

        val (errorCode, user) = if (currentNumberRetry < Constants.MAX_NUMBER_RETRY_PIN_INPUT) {

            SmartOTPError.INPUT_PIN_INCORRECT.name to jsonElement
        } else {
//            blockPin(userId)
            SmartOTPError.SMART_OTP_LOCKED.name to jsonElement
        }

        return DomainResult.Error(errorCode = errorCode, data = user)
    }

    fun deleteUserSmartOtp(userId: String) {
        iSmartOTPUseCase.deleteUserSmartOtp(userId)
    }

    fun deleteAllUserSmartOtp() {
        iSmartOTPUseCase.deleteAllUserSmartOtp()
    }

}