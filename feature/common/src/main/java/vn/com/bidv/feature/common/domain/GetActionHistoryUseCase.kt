package vn.com.bidv.feature.common.domain

import vn.com.bidv.feature.common.data.FoReportRepository
import vn.com.bidv.feature.common.domain.data.DataListTxnLogDMO
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class GetActionHistoryUseCase @Inject constructor(
    private val foReportRepository: FoReportRepository
) {
    suspend fun invoke(txnId: String, txnCode: String): DomainResult<DataListTxnLogDMO> {
        val domain = foReportRepository.inquiryTxnLogs(txnId = txnId, txnCode = txnCode)
        return domain.convert(DataListTxnLogDMO::class.java)
    }
}