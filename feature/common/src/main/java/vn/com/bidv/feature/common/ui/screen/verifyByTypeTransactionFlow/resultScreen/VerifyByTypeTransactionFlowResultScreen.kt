package vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.resultScreen
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.common.ui.BaseMVIScreen
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.InitTransactionFlowViewModel
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.viewModelFlow.sharedVerifyTransactionViewModel
import vn.com.bidv.sdkbase.ui.DefaultNoRouteScreen

@Composable
fun VerifyByTypeTransactionFlowResultScreen(navController: NavHostController, resultData: String) {
	val viewModel: InitTransactionFlowViewModel = hiltViewModel<InitTransactionFlowViewModel>()
	val viewModelFlow = navController.sharedVerifyTransactionViewModel()
	BaseMVIScreen (
		viewModel = viewModel,
		renderContent = { _, _ ->
			VerifyByTypeTransactionFlowResultScreenContent(
				viewModel = viewModel,
				navController = navController,
				type = viewModelFlow.uiState.value.type,
				resultData = resultData,
				additionalData = viewModelFlow.uiState.value.additionalData
			)
		},
		handleSideEffect = {},
	)
}

@Composable
fun VerifyByTypeTransactionFlowResultScreenContent(
	viewModel: InitTransactionFlowViewModel,
	navController: NavHostController,
	type: String,
	resultData: String,
	additionalData: String?
) {
	Box(modifier = Modifier.fillMaxSize()) {
		viewModel.itemVerifyFlowScreenBuilder.find {
			it.type.code == (type)
		}?.content?.invoke(navController, resultData, additionalData) ?: DefaultNoRouteScreen()
	}
}

