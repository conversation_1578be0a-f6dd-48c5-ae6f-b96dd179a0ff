package vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.createdqr

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CompletableJob
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.common.extenstion.isNotNullOrEmpty
import vn.com.bidv.feature.common.domain.VerifyTransactionUseCase
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.createdqr.CreatedQrReducer.CreatedQrViewEffect
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.createdqr.CreatedQrReducer.CreatedQrViewEvent
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.createdqr.CreatedQrReducer.CreatedQrViewState
import vn.com.bidv.log.BLogUtil
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class CreatedQrViewModel @Inject constructor(
    private val verifyTransactionUseCase: VerifyTransactionUseCase
) : ViewModelIBankBase<CreatedQrViewState, CreatedQrViewEvent, CreatedQrViewEffect>(
    initialState = CreatedQrViewState(),
    reducer = CreatedQrReducer()
) {

    private var job: CompletableJob? = null
    private var scope: CoroutineScope? = null

    override fun handleEffect(
        sideEffect: CreatedQrViewEffect,
        onResult: (CreatedQrViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is CreatedQrViewEffect.StartReceiverOtp -> {
                startJobReceiverOtp(sideEffect.authId, onResult)
            }

            is CreatedQrViewEffect.OTPInputSuccess -> {
                cancelJobReceiverOtp()
                onResult(CreatedQrViewEvent.OnFinishQRFlow(sideEffect.otp))
            }

            is CreatedQrViewEffect.FinishQRFlow -> {
                //nothing
            }
        }
    }

    private fun startJobReceiverOtp(authId: String, onResult: (CreatedQrViewEvent) -> Unit) {
        if (job?.isActive == true) {
            return
        }
        job = SupervisorJob(viewModelScope.coroutineContext[Job])
        job?.let { job ->
            scope = CoroutineScope(Dispatchers.IO + job)
            scope?.launch {
                while (true) {
                    delay(5000)
                    when (val result = verifyTransactionUseCase.retrieveOtp(authId)) {
                        is DomainResult.Success -> {
                            if (result.data?.otp.isNotNullOrEmpty()) {
                                onResult(CreatedQrViewEvent.OnOTPGetFromService(result.data?.otp.orEmpty()))
                            }
                        }

                        is DomainResult.Error -> {
                            // do thing
                            BLogUtil.d(result.errorCode)
                        }
                    }
                }
            }
        }
    }

    private fun cancelJobReceiverOtp() {
        if (job?.isActive == true && job?.isCompleted == false) {
            job?.cancel()
        }
    }
}
