package vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.verifypin

import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.feature.common.constants.SmartOTPError
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.feature.common.domain.VerifyTransactionUseCase
import vn.com.bidv.feature.common.domain.data.UserSmartOtpDMO
import vn.com.bidv.feature.common.domain.smartotp.SmartOTPUseCase
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.verifypin.VerifyPinReducer.VerifyPinViewEffect
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.verifypin.VerifyPinReducer.VerifyPinViewEvent
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.verifypin.VerifyPinReducer.VerifyPinViewState
import vn.com.bidv.log.BLogUtil
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class VerifyPinViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val smartOTPUseCase: SmartOTPUseCase,
    private val verifyTransactionUseCase: VerifyTransactionUseCase,
    private val userInfoUseCase: UserInfoUseCase
) : ViewModelIBankBase<VerifyPinViewState, VerifyPinViewEvent, VerifyPinViewEffect>(
    initialState = VerifyPinViewState(),
    reducer = VerifyPinReducer()
) {
    override fun handleEffect(
        sideEffect: VerifyPinViewEffect,
        onResult: (VerifyPinViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is VerifyPinViewEffect.VerifyPin -> {
                callDomain(
                    showLoadingIndicator = false,
                    onSuccess = {
                        if (sideEffect.isShareDataVerifyPinSuccess) {
                            shareDataVerifyPinTransaction {
                                verifyTransactionUseCase.shareDataVerifyPin(it.data)
                            }
                        }
                        onResult(VerifyPinViewEvent.OnVerifyPinSuccess(userActiveSmartOtpDMO = it.data))
                    },
                    onFail = {
                        val validError = SmartOTPError.fromString(it?.errorCode)
                        val user = it?.data?.let { data ->
                            runCatching { Gson().fromJson(data, UserSmartOtpDMO::class.java) }
                                .onFailure { e -> BLogUtil.e("Error parsing UserSmartOtpDMO: ${e.message}") }
                                .getOrNull()
                        }
                        if (validError == SmartOTPError.SMART_OTP_LOCKED) {
                            viewModelScope.launch(dispatcher) {
                                if (localRepository.isLoginSuccess()) {
                                    verifyTransactionUseCase.selfLock()
                                } else {
                                    verifyTransactionUseCase.lock(sideEffect.userId)
                                }
                            }
                        }
                        onResult(VerifyPinViewEvent.OnVerifyPinFail(validError, incorrectTime = user?.incorrectTime))
                    },
                    listErrorCodeListen = listOf(
                        SmartOTPError.INPUT_PIN_INCORRECT.name,
                        SmartOTPError.SMART_OTP_LOCKED.name
                    )
                ) {
                    smartOTPUseCase.verifyPin(
                        pin = sideEffect.pinInput,
                        userId = sideEffect.userId,
                    )
                }
            }

            is VerifyPinViewEffect.CheckHasSmartOtp -> {
                val isHasSmartOtp = smartOTPUseCase.checkUserActiveSmartOtp(sideEffect.userId)
                if (!isHasSmartOtp && localRepository.isLoginSuccess()) {
                    viewModelScope.launch(dispatcher) {
                        verifyTransactionUseCase.deleteUserSmartOtp(sideEffect.userId.toLong())
                    }
                    smartOTPUseCase.deleteUserSmartOtp(sideEffect.userId)
                }
                onResult(VerifyPinViewEvent.OnCheckHasSmartOtpSuccess(isHasSmartOtp))
            }

            is VerifyPinViewEffect.InitTransaction -> {
                val isLoginSuccess = localRepository.isLoginSuccess()
                val isUserRoleAdmin = userInfoUseCase.isAdminRole()
                onResult(VerifyPinViewEvent.OnInitTransactionSuccess(isLoginSuccess, isUserRoleAdmin))
            }

            is VerifyPinViewEffect.CheckBlockVerifyPin -> {

                val isCheckPinBlocked = smartOTPUseCase.checkPinBlocked(sideEffect.userId)
                if (isCheckPinBlocked) {
                    viewModelScope.launch(dispatcher) {
                        if (localRepository.isLoginSuccess()) {
                            verifyTransactionUseCase.selfLock()
                        } else {
                            verifyTransactionUseCase.lock(sideEffect.userId)
                        }
                    }
                }
                onResult(VerifyPinViewEvent.OnCheckBlockVerifyPinSuccess(isCheckPinBlocked))
            }

            is VerifyPinViewEffect.VerifyPinFail,
            is VerifyPinViewEffect.VerifyPinSuccess,
            is VerifyPinViewEffect.CheckBlockVerifyPinSuccess,

            is VerifyPinViewEffect.SmartOtpNotExists -> {
                // nothing
            }

        }
    }
    private fun shareDataVerifyPinTransaction(onShareData: suspend () -> Unit) {
        viewModelScope.launch(Dispatchers.Default) {
            onShareData.invoke()
        }
    }
}
