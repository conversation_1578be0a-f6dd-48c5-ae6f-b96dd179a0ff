package vn.com.bidv.feature.common.domain.smartotp

import androidx.core.text.isDigitsOnly
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import vn.com.bidv.common.sharePreference.Storage
import vn.com.bidv.feature.common.constants.Constants
import vn.com.bidv.feature.common.domain.data.UserActiveSmartOtpDMO
import vn.com.bidv.feature.common.domain.data.UserSmartOtpDMO
import vn.com.bidv.feature.common.domain.data.toUserSmartOtpDMO
import vn.com.bidv.log.BLogUtil
import vn.com.bidv.secure.secure.smartotp.ISmartOTPSecure
import javax.inject.Inject

class DefaultSmartOTPUseCase @Inject constructor(
    private val smartOTPSecure: ISmartOTPSecure
) : ISmartOTPUseCase {
    override fun getListUserSmartOtpDMO(): List<UserSmartOtpDMO> {
        return getListUserActiveSmartOtpDMOFromStorage().map { it.toUserSmartOtpDMO() }
    }

    override fun saveUserActiveSmartOtpDMO(newItem: UserActiveSmartOtpDMO) {
        val currentList = getListUserActiveSmartOtpDMOFromStorage().toMutableList()
        val index = currentList.indexOfFirst { it.u == newItem.u }
        if (index != -1) {
            currentList[index] = if (newItem.p.length == 6 && newItem.p.isDigitsOnly())
                // Save when reactive smart otp already existing in storage
                newItem.copy(
                    p = smartOTPSecure.encryptPin(newItem.p),
                    s = smartOTPSecure.encryptKey(newItem.p, newItem.s) ?: ""
                ) else newItem
        } else {
            currentList.add(
                newItem.copy(
                    p = smartOTPSecure.encryptPin(newItem.p),
                    s = smartOTPSecure.encryptKey(newItem.p, newItem.s) ?: ""
                )
            )
        }

        val updatedJson = Gson().toJson(currentList)
        Storage.put(SmartOTPKey.USER_ACTIVE_SMART_OTP, updatedJson)
    }

    override fun verifyPin(pin: String, userId: String): UserActiveSmartOtpDMO? {
        val userList = getListUserActiveSmartOtpDMOFromStorage()
        val user = userList.find { it.u == userId }?.also { foundUser ->
            saveUserActiveSmartOtpDMO(foundUser.copy(it = foundUser.it + 1))
        }

        val userResult = user?.takeIf { smartOTPSecure.verifyPin(pin, it.p)}?.copy(
            it = 0
        ).also { userValid ->
            userValid?.let { saveUserActiveSmartOtpDMO(it) }
        }
        return userResult?.copy(s= smartOTPSecure.getSecretKey(pin, userResult.s) ?: "")
    }

    override fun checkPinBlocked(userId: String): Boolean {
        val listSmartOtp = getListUserActiveSmartOtpDMOFromStorage()
        val currentUserSmartOtp = listSmartOtp.find { it.u == userId }
        return currentUserSmartOtp?.it == Constants.MAX_NUMBER_RETRY_PIN_INPUT
    }

    override fun deleteUserSmartOtp(userId: String) {
        val listSmartOtp = getListUserActiveSmartOtpDMOFromStorage()
        val updatedList = listSmartOtp.filter { it.u != userId }
        val updatedJson = Gson().toJson(updatedList)
        Storage.put(SmartOTPKey.USER_ACTIVE_SMART_OTP, updatedJson)
    }

    private fun getListUserActiveSmartOtpDMOFromStorage(): List<UserActiveSmartOtpDMO> {
        val json = Storage.get(SmartOTPKey.USER_ACTIVE_SMART_OTP) ?: ""
        return try {
            val type = object : TypeToken<List<UserActiveSmartOtpDMO>>() {}.type
            val listUser: List<UserActiveSmartOtpDMO> = Gson().fromJson(json, type)
            val listSmartSorted = listUser.sortedByDescending { it.ts.toLongOrNull() }
            BLogUtil.d("listUser: $listUser listSmartSorted: $listSmartSorted")
            listSmartSorted
        } catch (e: Exception) {
            emptyList()
        }
    }

    override fun deleteAllUserSmartOtp() {
        val gson = Gson()
        Storage.put(SmartOTPKey.USER_ACTIVE_SMART_OTP, gson.toJson(emptyList<UserActiveSmartOtpDMO>()))
    }

}
