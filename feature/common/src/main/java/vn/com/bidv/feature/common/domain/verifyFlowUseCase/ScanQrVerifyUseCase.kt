package vn.com.bidv.feature.common.domain.verifyFlowUseCase

import vn.com.bidv.feature.common.data.UtilitiesRepository
import vn.com.bidv.feature.common.domain.smartotp.SmartOTPUseCase
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InitVerifyTransactionResponse
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyTransaction
import vn.com.bidv.network.retrofit.NetworkResponse
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import vn.com.bidv.sdkbase.utils.mapTo
import javax.inject.Inject

class ScanQrVerifyUseCase @Inject constructor(
    private val verifyTransactionRepository: UtilitiesRepository,
    private val smartOTPUseCase: SmartOTPUseCase
) : VerifyByTypeTransactionUseCase {

    override suspend fun initTransaction(input: InputVerifyTransaction): DomainResult<InitVerifyTransactionResponse> {
        return DomainResult.Success(InitVerifyTransactionResponse())
    }

    override suspend fun verifyTransaction(
        initResponse: InitVerifyTransactionResponse,
        reqValue: String?,
    ): DomainResult<String> {
        val transAuth = initResponse.transAuth
        val userId = transAuth?.userId ?: transAuth?.additionalInfo?.userId
        val smToken = smartOTPUseCase.getListUserSmartOtpDMO()
            .find { it.userId == userId }?.smToken ?: ""
        val result = verifyTransactionRepository.pushOtp(
            transAuth?.additionalInfo?.authId ?: transAuth?.authId ?: "", reqValue ?: "", smToken
        )
        val domain = result.convert {
            this.mapTo(NetworkResponse::class.java).isSuccess().toString()
        }
        return domain
    }
}