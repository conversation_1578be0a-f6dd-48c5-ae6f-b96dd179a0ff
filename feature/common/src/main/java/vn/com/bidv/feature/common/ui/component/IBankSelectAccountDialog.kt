package vn.com.bidv.feature.common.ui.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.datadisplay.badge.IBankBadgeLabel
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelColor
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelSize
import vn.com.bidv.designsystem.component.feedback.bottomsheet.IBankSearchDialog
import vn.com.bidv.designsystem.component.feedback.bottomsheet.SearchDialogState
import vn.com.bidv.designsystem.theme.IBSpacing.spacingXs
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.feature.common.domain.data.AccountDMO
import vn.com.bidv.sdkbase.utils.VNCharacterUtil

@Composable
fun IBankSelectAccountDialog(
    title: String,
    accountSelected: AccountDMO?,
    listData: List<AccountDMO>?,
    onRequestDismiss: (selected: AccountDMO?) -> Unit
) {
    IBankSearchDialog(
        title = title,
        itemSelected = accountSelected,
        compareKey = { it.accountNo },
        showSearchBox = (listData?.size ?: 0) > 10,
        listData = listData ?: emptyList(),
        searchFilter = { item, key ->
            VNCharacterUtil.removeAccent(item.searchKey())
                .contains(VNCharacterUtil.removeAccent(key), ignoreCase = true)
        },
        state = SearchDialogState.CONTENT,
        onRequestDismiss = onRequestDismiss,
    ) { index, searchItem ->
        IBankAccountItemView(searchItem.isSelected, searchItem.data)
    }
}

@Composable
fun IBankAccountItemView(
    isSelected: Boolean = false, account: AccountDMO
) {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current
    Row(
        Modifier
            .fillMaxWidth()
            .background(
                color = if (isSelected) colorScheme.bgBrand_01Tertiary else Color.Transparent,
                shape = RoundedCornerShape(spacingXs)
            )
            .padding(spacingXs), verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            modifier = Modifier.size(32.dp),
            painter = painterResource(id = R.mipmap.bidv),
            contentDescription = ""
        )
        Spacer(modifier = Modifier.width(spacingXs))
        Column(
            modifier = Modifier.weight(1f), verticalArrangement = Arrangement.Center
        ) {
            Row {
                Text(
                    color = colorScheme.contentMainPrimary,
                    text = account.accountNo ?: "",
                    style = typography.titleTitle_m
                )

                if (account.default == true) {
                    Spacer(modifier = Modifier.width(spacingXs))
                    IBankBadgeLabel(
                        title = stringResource(id = vn.com.bidv.localization.R.string.mac_dinh),
                        badgeSize = LabelSize.SM,
                        badgeColor = LabelColor.BRAND,
                    )
                }
            }
            Text(
                color = colorScheme.contentMainTertiary,
                text = account.getAmount(),
                style = typography.bodyBody_s
            )
            Text(
                color = colorScheme.contentMainTertiary,
                text = account.accountName ?: "",
                style = typography.bodyBody_s
            )
        }
        Box(modifier = Modifier.size(32.dp)) {
            if (isSelected) {
                Icon(
                    modifier = Modifier.size(20.dp),
                    painter = painterResource(id = R.drawable.check),
                    contentDescription = ""
                )
            }
        }

    }
}