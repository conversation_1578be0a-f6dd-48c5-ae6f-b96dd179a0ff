package vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.viewModelFlow

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionFlowScreenBuilder
import vn.com.bidv.sdkbase.navigation.IBankMainRouting
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject



@HiltViewModel
class VerifyByTypeTransactionFlowViewModel @Inject constructor(
    val itemVerifyFlowScreenBuilder: Set<@JvmSuppressWildcards VerifyTransactionFlowScreenBuilder>,
) : ViewModelIBankBase<VerifyByTypeTransactionFlowReducer.VerifyByTypeTransactionFlowViewState, VerifyByTypeTransactionFlowReducer.VerifyByTypeTransactionFlowViewEvent, VerifyByTypeTransactionFlowReducer.VerifyByTypeTransactionFlowEffect>(
    initialState = VerifyByTypeTransactionFlowReducer.VerifyByTypeTransactionFlowViewState(),
    reducer = VerifyByTypeTransactionFlowReducer()
) {

    fun isAllowTransResponse() : Boolean {
        return itemVerifyFlowScreenBuilder.find { it.type.code == uiState.value.type }?.isAllowTransResponse == true
    }

    fun isShareDataWhenFail(): Boolean {
        return itemVerifyFlowScreenBuilder.find { it.type.code == uiState.value.type }?.isShareDataWhenFail == true
    }
}

@Composable
fun NavController.sharedVerifyTransactionViewModel(): VerifyByTypeTransactionFlowViewModel {
    val parentEntry = remember(this) {
        getBackStackEntry(IBankMainRouting.CommonRoute.VerifyByTypeTransactionRoute.routeWithArgument)
    }
    return hiltViewModel(parentEntry)
}
