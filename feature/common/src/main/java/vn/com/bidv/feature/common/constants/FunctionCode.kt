package vn.com.bidv.feature.common.constants

/**
 * Function Code is defined at https://docs.google.com/spreadsheets/d/1tkFuy6AhKOECLwKvt3uuJE4c9qSTEKAEp6gNNvrGFBw/edit?gid=493027531#gid=493027531
 */
enum class FunctionCode(val code: String) {
    CREATE_F("CREATE_F"), // Khởi tạo
    UPDATE("UPDATE"), // Cập nhật
    UPDATE_DETAIL("UPDATE_DETAIL"), // Cập nhật chi tiết
    DELETE("DELETE"), // Xóa
    SUBMIT("SUBMIT"), // Đ<PERSON>y duyệt
    VIEW("VIEW"), // Xem - maker
    VIEW_DETAIL("VIEW_DETAIL"), // Xem chi tiết - maker
    APPROVE("APPROVE"), // Phê duyệt
    REPORT("REPORT"), // Báo cáo - checker
    REPORT_DETAIL("REPORT_DETAIL"), // Báo cáo chi tiết - checker
    CREATE_NF("CREATE_NF"), // Đăng ký
    INQUIRY("INQUIRY"), // Vấn tin
    PROCEED_BA("PROCEED_BA"), // Xử lý mặc định
    PROCEED_AD("PROCEED_AD"); // Xử lý nâng cao

    companion object {
        fun from(code: String?): FunctionCode? {
            return entries.find { it.code.equals(code, ignoreCase = true) }
        }
    }
}