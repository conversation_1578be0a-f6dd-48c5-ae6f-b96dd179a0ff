package vn.com.bidv.feature.common.navigation

import androidx.compose.ui.window.DialogProperties
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.compose.dialog
import androidx.navigation.navArgument
import androidx.navigation.navigation
import androidx.navigation.toRoute
import com.google.gson.Gson
import kotlinx.serialization.Serializable
import vn.com.bidv.feature.common.constants.Constants.URL_TYPE
import vn.com.bidv.feature.common.domain.data.TransAuthDMO
import vn.com.bidv.feature.common.navigation.VerifyByType.supportBrowserScreen
import vn.com.bidv.feature.common.ui.screen.supportopenbrowser.SupportOpenBrowserScreen
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.InitTransactionFlowScreen
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyCreateTransaction
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyTransaction
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.resultScreen.VerifyByTypeTransactionFlowResultScreen
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.verifyScreen.VerifyTransactionFlowScreen
import vn.com.bidv.feature.common.ui.screen.verifyRemoteSigningTransactionFlow.RemoteSigningScreen
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.createdqr.CreatedQrScreen
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.model.ModelVerifyTransactionUI
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.model.VerifyMethodType
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.scanqr.ScanQrScreen
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.verifyotp.VerifyOtpScreen
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.verifypin.VerifyPinScreen
import vn.com.bidv.sdkbase.navigation.FeatureGraphBuilder
import vn.com.bidv.sdkbase.navigation.IBankMainRouting
import vn.com.bidv.sdkbase.utils.CustomSafeArgs
import javax.inject.Inject
import kotlin.reflect.typeOf

internal object VerifyByType {
    const val initScreenRoute = "init_screen_route"
    const val verifyByTypePinScreenRoute = "verify_by_type_pin_screen"
    const val verifyByTypeOTPScreenRoute = "verify_by_type_otp_screen"
    const val verifyByTypeResultScreen = "verify_by_type_result_screen"
    const val verifyScreenRoute = "verify_screen_route"
    const val scanQRScreenRoute = "scan_qr_screen_route"
    const val supportBrowserScreen = "support_browser_screen"
    const val remoteSigningRoute = "remote_signing_route"
}



@Serializable
sealed class VerifyTransactionRoute(val route: String) {
    data object InitTransactionScreenRoute : VerifyTransactionRoute(VerifyByType.initScreenRoute)

    @Serializable
    data class VerifyByTypePinScreenRoute(
        val transAuth: TransAuthDMO,
    ) : VerifyTransactionRoute(VerifyByType.verifyByTypePinScreenRoute)

    @Serializable
    data class VerifyByTypeOTPScreenRoute(
        val modelVerifyTransactionUI: ModelVerifyTransactionUI,
        val secretKey: String,
        val smToken: String,
    ) :
        VerifyTransactionRoute(VerifyByType.verifyByTypeOTPScreenRoute)

    @Serializable
    data class VerifyTransactionScreenRoute(
        val data: String,
    ) : VerifyTransactionRoute(VerifyByType.verifyScreenRoute)

    @Serializable
    data class VerifyByTypeResultScreenRoute(
        val resultData: String,
    ) :
        VerifyTransactionRoute(VerifyByType.verifyByTypeResultScreen)

    @Serializable
    data class VerifyByTypeResultDialogRoute(
        val resultData: String,
    ) :
        VerifyTransactionRoute(VerifyByType.verifyByTypeResultScreen)

    @Serializable
    data object ScanQRScreenRoute : VerifyTransactionRoute(VerifyByType.scanQRScreenRoute)

    @Serializable
    data class RemoteSigningScreenRoute(
        val transAuth: TransAuthDMO,
    ) : VerifyTransactionRoute(VerifyByType.remoteSigningRoute)
}

@Serializable
sealed class CommonRoute(val route: String) {
    @Serializable
    data object OpenBrowserRoute: CommonRoute(VerifyByType.supportBrowserScreen) {
        fun toRoute(): String {
            return "${supportBrowserScreen}?$URL_TYPE={$URL_TYPE}"
        }
    }
}



class CommonNavigation @Inject constructor() : FeatureGraphBuilder {

    override fun buildGraph(
        navGraphBuilder: NavGraphBuilder,
        navController: NavHostController,
        registeredRoutes: (args: List<String>) -> Unit
    ) {

        navGraphBuilder.navigation(
            startDestination = VerifyTransactionRoute.InitTransactionScreenRoute.route,
            route = IBankMainRouting.CommonRoute.VerifyByTypeTransactionRoute.routeWithArgument
        ) {
            dialog(
                dialogProperties = DialogProperties(
                    dismissOnClickOutside = false,
                    usePlatformDefaultWidth = false
                ),
                route = VerifyTransactionRoute.InitTransactionScreenRoute.route,
                arguments = listOf(
                    navArgument(IBankMainRouting.CommonRoute.TYPE) {
                        type = NavType.StringType
                        nullable = true
                    }, navArgument(IBankMainRouting.CommonRoute.DATA_OBJECT_INIT) {
                        type = NavType.StringType
                        nullable = true
                    }, navArgument(IBankMainRouting.CommonRoute.ADDITIONAL_DATA) {
                        type = NavType.StringType
                        nullable = true
                    }
                    )
            ) {
                val txnIdsJson = it.arguments?.getString(IBankMainRouting.CommonRoute.DATA_OBJECT_INIT) ?: ""
                val type = it.arguments?.getString(IBankMainRouting.CommonRoute.TYPE) ?: ""
                val additionalData = it.arguments?.getString(IBankMainRouting.CommonRoute.ADDITIONAL_DATA) ?: ""


                val inputVerifyTransaction = try {
                    Gson().fromJson(txnIdsJson, InputVerifyTransaction::class.java)
                } catch (ex: Exception) {
                    InputVerifyTransaction(txnIds = emptyList())
                }

                val inputVerifyCreateTransaction = try {
                    Gson().fromJson(txnIdsJson, InputVerifyCreateTransaction::class.java)
                } catch (ex: Exception) {
                    InputVerifyCreateTransaction()
                }

                InitTransactionFlowScreen(
                    navController = navController,
                    txnIds = inputVerifyTransaction,
                    dataCreated = inputVerifyCreateTransaction,
                    type = type,
                    additionalData = additionalData
                )

            }

            dialog<VerifyTransactionRoute.VerifyByTypePinScreenRoute>(
                typeMap = mapOf(
                    typeOf<TransAuthDMO>() to CustomSafeArgs.SerializableNavType(
                        TransAuthDMO.serializer()
                    ),
                    typeOf<String>() to CustomSafeArgs.StringType,
                ), dialogProperties = DialogProperties(
                    dismissOnClickOutside = false,
                    usePlatformDefaultWidth = false
                ),
            ) {
                val transAuth = it.toRoute<VerifyTransactionRoute.VerifyByTypePinScreenRoute>().transAuth
                val modelVerifyTransactionUI = transAuth.mapToModelVerifySmartOtpTransactionUI()
                if (modelVerifyTransactionUI.verifyMethodType == VerifyMethodType.SAME_DEVICE ||
                    modelVerifyTransactionUI.verifyMethodType == VerifyMethodType.SCAN_QR
                ) {
                    VerifyPinScreen(
                        navController,
                        modelVerifyTransactionUI,
                    )
                } else if (modelVerifyTransactionUI.verifyMethodType == VerifyMethodType.OTHER_DEVICE) {
                    CreatedQrScreen(
                        navController,
                        modelVerifyTransactionUI,
                    )
                }
            }

            dialog<VerifyTransactionRoute.VerifyByTypeOTPScreenRoute>(
                typeMap = mapOf(
                    typeOf<ModelVerifyTransactionUI>() to CustomSafeArgs.SerializableNavType(
                        ModelVerifyTransactionUI.serializer()
                    ),
                    typeOf<String>() to CustomSafeArgs.StringType,
                ), dialogProperties = DialogProperties(
                    dismissOnClickOutside = false,
                    usePlatformDefaultWidth = false
                )
            ) {
                val modelVerifyOTPTransactionUI =
                    it.toRoute<VerifyTransactionRoute.VerifyByTypeOTPScreenRoute>().modelVerifyTransactionUI
                val secretKey =
                    it.toRoute<VerifyTransactionRoute.VerifyByTypeOTPScreenRoute>().secretKey
                val smToken =
                    it.toRoute<VerifyTransactionRoute.VerifyByTypeOTPScreenRoute>().smToken
                VerifyOtpScreen(
                    navController,
                    modelVerifyOTPTransactionUI,
                    secretKey,
                    smToken
                )
            }


            dialog<VerifyTransactionRoute.VerifyTransactionScreenRoute>(
                typeMap = mapOf(
                    typeOf<String>() to CustomSafeArgs.StringType,
                ), dialogProperties = DialogProperties(
                    dismissOnClickOutside = false,
                    usePlatformDefaultWidth = false
                )
            ) {
                val data = it.toRoute<VerifyTransactionRoute.VerifyTransactionScreenRoute>().data

                VerifyTransactionFlowScreen(
                    navController = navController,
                    data = data,
                )
            }

            composable<VerifyTransactionRoute.VerifyByTypeResultScreenRoute> {
                val resultData = it.toRoute<VerifyTransactionRoute.VerifyByTypeResultScreenRoute>().resultData

                VerifyByTypeTransactionFlowResultScreen(
                    navController = navController,
                    resultData = resultData
                )
            }

            dialog<VerifyTransactionRoute.VerifyByTypeResultDialogRoute>(
                dialogProperties = DialogProperties(
                    dismissOnClickOutside = false,
                    usePlatformDefaultWidth = false
                )
            ) {
                val resultData = it.toRoute<VerifyTransactionRoute.VerifyByTypeResultScreenRoute>().resultData
                VerifyByTypeTransactionFlowResultScreen(
                    navController = navController,
                    resultData = resultData
                )
            }


            composable(
                route = VerifyTransactionRoute.ScanQRScreenRoute.route
            ) {
                ScanQrScreen(
                    navController
                )
            }

            composable<VerifyTransactionRoute.RemoteSigningScreenRoute>(
                typeMap = mapOf(
                    typeOf<TransAuthDMO>() to CustomSafeArgs.SerializableNavType(
                        TransAuthDMO.serializer()
                    ),
                    typeOf<String>() to CustomSafeArgs.StringType,
                )
            ) {
                val transAuth = it.toRoute<VerifyTransactionRoute.RemoteSigningScreenRoute>().transAuth
                RemoteSigningScreen(navController, transAuth)
            }

        }

        navGraphBuilder.navigation(
            startDestination = CommonRoute.OpenBrowserRoute.route,
            route = CommonRoute.OpenBrowserRoute.toRoute()
        ) {
            composable(
                route = CommonRoute.OpenBrowserRoute.route,
//                dialogProperties = DialogProperties(
//                    dismissOnBackPress = true,
//                    dismissOnClickOutside = false,
//                    usePlatformDefaultWidth = false
//                ),
                arguments = listOf(navArgument(URL_TYPE) {
                    type = NavType.StringType
                    nullable = true
                }
                )
            ) {
                SupportOpenBrowserScreen(
                    navController = navController,
                    urlType = it.arguments?.getString(URL_TYPE) ?: "",
                )
            }
        }

        registeredRoutes(listOf())
    }

} 
