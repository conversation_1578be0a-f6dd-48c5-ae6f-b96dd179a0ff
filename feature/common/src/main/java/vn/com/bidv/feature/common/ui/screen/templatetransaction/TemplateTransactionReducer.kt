package vn.com.bidv.feature.common.ui.screen.templatetransaction

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.common.domain.data.TemplateTransactionTabDMO

class TemplateTransactionReducer :
    Reducer<TemplateTransactionReducer.TemplateTransactionViewState, TemplateTransactionReducer.TemplateTransactionViewEvent, TemplateTransactionReducer.TemplateTransactionViewEffect> {

    @Immutable
    data class TemplateTransactionViewState(
        val tabLayoutIndex: Int = 0,
        val tabs: List<TemplateTransactionTabDMO> = listOf()
    ) : ViewState

    @Immutable
    sealed class TemplateTransactionViewEvent : ViewEvent {
        data class SelectedTab(val indexTab: Int) : TemplateTransactionViewEvent()
    }

    @Immutable
    sealed class TemplateTransactionViewEffect : SideEffect

    override fun reduce(
        previousState: TemplateTransactionViewState,
        event: TemplateTransactionViewEvent
    ): Pair<TemplateTransactionViewState, TemplateTransactionViewEffect?> {
        return when (event) {
            is TemplateTransactionViewEvent.SelectedTab -> {
                previousState.copy(
                    tabLayoutIndex = event.indexTab
                ) to null
            }

        }
    }
}

