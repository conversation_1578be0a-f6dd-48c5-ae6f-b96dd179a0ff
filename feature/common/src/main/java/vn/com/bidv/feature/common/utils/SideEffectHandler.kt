package vn.com.bidv.feature.common.utils;

import androidx.compose.runtime.Composable
import com.google.gson.Gson
import kotlinx.coroutines.flow.Flow
import vn.com.bidv.common.utils.CollectSideEffect
import vn.com.bidv.feature.common.constants.Constants
import vn.com.bidv.feature.common.data.VerifyOtpResult
import vn.com.bidv.sdkbase.data.ShareDataDTO
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase

@Composable
private inline fun <reified T> CollectSideEffect(
    flow: Flow<ShareDataDTO>,
    crossinline onResult: (T) -> Unit,
    crossinline onError: (T?) -> Unit = {}
) {
    CollectSideEffect(
        sideEffect = flow,
        onSideEffect = { sideEffect ->
            val result = Gson().fromJson(sideEffect.data, T::class.java)
            if (result != null) {
                onResult(result)
            } else {
                onError(sideEffect as T)
            }
        }
    )
}

@Composable
fun SubscribeShareDataVerifyOtp(
    vm: ViewModelIBankBase<*, *, *>,
    onSuccess: (VerifyOtpResult) -> Unit,
    onError: (VerifyOtpResult?) -> Unit
) {
    CollectSideEffect<VerifyOtpResult>(
        flow = vm.subscribeShareData(Constants.VERIFY_OTP),
        onResult = { result ->
            onSuccess(result)
        },
        onError = { errorCode ->
            onError(errorCode)
        }
    )
}
