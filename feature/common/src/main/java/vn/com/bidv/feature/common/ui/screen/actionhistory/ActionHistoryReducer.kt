package vn.com.bidv.feature.common.ui.screen.actionhistory

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.common.domain.data.TxnLogDMO

class ActionHistoryReducer :
    Reducer<ActionHistoryReducer.ActionHistoryState, ActionHistoryReducer.ActionHistoryEvent, ActionHistoryReducer.ActionHistoryEffect> {
    @Immutable
    data class ActionHistoryState(
        val actionLogs: List<TxnLogDMO>? = null,
        val errorMessage: String? = null
    ) : ViewState

    @Immutable
    sealed class ActionHistoryEvent : ViewEvent {
        data class GetActionLogs(val txnId: String, val txnCode: String) : ActionHistoryEvent()
        data class GetActionLogsSuccess(val actionLogs: List<TxnLogDMO>?) : ActionHistoryEvent()
        data class ShowError(val errorMessage: String?) : ActionHistoryEvent()
    }

    @Immutable
    sealed class ActionHistoryEffect : SideEffect {
        data class GetActionLogsSideEffect(val txnId: String, val txnCode: String) : ActionHistoryEffect()
    }

    override fun reduce(
        previousState: ActionHistoryState,
        event: ActionHistoryEvent
    ): Pair<ActionHistoryState, ActionHistoryEffect?> {
        return when (event) {
            is ActionHistoryEvent.GetActionLogs -> {
                previousState.copy(errorMessage = null) to ActionHistoryEffect.GetActionLogsSideEffect(
                    txnId = event.txnId,
                    txnCode = event.txnCode
                )
            }

            is ActionHistoryEvent.GetActionLogsSuccess -> {
                previousState.copy(actionLogs = event.actionLogs) to null
            }

            is ActionHistoryEvent.ShowError -> {
                previousState.copy(errorMessage = event.errorMessage) to null
            }
        }
    }
}