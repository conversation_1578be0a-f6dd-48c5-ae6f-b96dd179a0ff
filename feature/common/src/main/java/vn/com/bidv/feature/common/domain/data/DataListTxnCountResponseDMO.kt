package vn.com.bidv.feature.common.domain.data

import com.google.gson.annotations.SerializedName

data class DataListTxnCountResponseDMO(

    /* List data */
    @SerializedName("items")
    val items: List<TxnCountResponseDMO>? = null,

    /* Total size */
    @SerializedName("total")
    val total: Long? = null

)

data class TxnCountResponseDMO(

    @SerializedName("txnCountDtos")
    val txnCountDtos: List<TxnCountDMO>? = null,

    @SerializedName("code")
    val code: String? = null,

    @SerializedName("total")
    val total: Int? = null

)

data class TxnCountDMO(

    @SerializedName("txnCode")
    val txnCode: String? = null,

    @SerializedName("menuCode")
    val menuCode: String? = null,

    @SerializedName("count")
    val count: Int? = null,

    @SerializedName("status")
    val status: String? = null

) 