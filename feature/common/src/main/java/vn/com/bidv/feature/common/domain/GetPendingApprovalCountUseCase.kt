package vn.com.bidv.feature.common.domain

import vn.com.bidv.feature.common.data.FoReportRepository
import vn.com.bidv.feature.common.domain.data.DataListTxnCountResponseDMO
import vn.com.bidv.feature.common.domain.data.MenuDMO
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class GetPendingApprovalCountUseCase @Inject constructor(
    private val foReportRepository: FoReportRepository,
    private val getUserInfoUseCase: UserInfoUseCase,
) {
    suspend fun getPendingApprovalCount(): DomainResult<DataListTxnCountResponseDMO> {
        val domain = foReportRepository.getTxnCountPendingApprovalChecker()
        return domain.convert(DataListTxnCountResponseDMO::class.java)
    }

    suspend fun getMenuApprovalWithCount(): DomainResult<List<MenuDMO>> {
        val getApprovalMenu = getUserInfoUseCase.getApprovalMenus()
        when (val getApprovalCount = getPendingApprovalCount()) {
            is DomainResult.Success -> {
                val txnCountMap = getApprovalCount.data?.items
                    ?.flatMap { it.txnCountDtos ?: emptyList() }
                    ?.associate { it.menuCode to (it.count ?: 0) } ?: emptyMap()

                val updatedMenus = getApprovalMenu.map { menu ->
                    if (!menu.isHeader) {
                        menu.copy(count = txnCountMap[menu.code] ?: 0)
                    } else {
                        menu
                    }
                }
                return DomainResult.Success(updatedMenus)
            }

            is DomainResult.Error -> {
                return DomainResult.Error(getApprovalCount.errorMessage ?: "")
            }
        }
    }

    suspend fun getTxnCount(): DomainResult<DataListTxnCountResponseDMO> {
        val domain = foReportRepository.getTxnCount()
        return domain.convert(DataListTxnCountResponseDMO::class.java)
    }
}