package vn.com.bidv.feature.common.ui.screen.verifyRemoteSigningTransactionFlow

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.fromHtml
import androidx.compose.ui.text.style.TextAlign
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import kotlinx.coroutines.delay
import vn.com.bidv.designsystem.component.IBankLoaderIndicators
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.common.domain.data.TransAuthDMO
import vn.com.bidv.feature.common.navigation.NavigationHelper
import vn.com.bidv.feature.common.navigation.VerifyTransactionRoute
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionTypeConstant
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.viewModelFlow.VerifyByTypeTransactionFlowViewModel
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.viewModelFlow.sharedVerifyTransactionViewModel
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.ui.HandleBackAction

@Composable
fun RemoteSigningScreen(navController: NavHostController,transAuth: TransAuthDMO) {
    val viewModel: RemoteSigningViewModel = hiltViewModel()
    //TODO thay doi khi lay duoc thoi gian con lai
    var timeActiveRemoteSinging by remember { mutableLongStateOf(((120).toLong())) }
    val viewModelFlow = navController.sharedVerifyTransactionViewModel()
    var messageError by remember { mutableStateOf("") }
    var isShowPopUpError by remember { mutableStateOf(false) }

    BaseScreen (
        viewModel = viewModel,
        navController = navController,
        renderContent = { uiState, onEvent ->
            if (!uiState.isInitRemoteSigning) {
                messageError = stringResource(R.string.co_loi_xay_ra_quy_khach_vui_long_thu_lai)
                onEvent(RemoteSigningReducer.RemoteSigningViewEvent.DoInitRemoteSigning(transAuth.authId.orEmpty()))
            }

            LaunchedEffect(true) {
                while (timeActiveRemoteSinging > 0) {
                    delay(1000)
                    timeActiveRemoteSinging -= 1
                }
            }

            if (timeActiveRemoteSinging <= 0) {
                isShowPopUpError = true
                messageError = stringResource(R.string.het_thoi_gian_xu_ly)
            }

            if (isShowPopUpError) {
                IBankModalConfirm(modalConfirmType = ModalConfirmType.Error,
                    title = stringResource(R.string.loi),
                    supportingText = messageError,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(R.string.dong),
                        ),
                    ),
                    onDismissRequest = {
                        isShowPopUpError = false
                        NavigationHelper.closeVerifyByTypeTransaction(navController, viewModelFlow)
                    })
            }

            HandleBackAction{
                NavigationHelper.closeVerifyByTypeTransaction(navController, viewModelFlow)
            }

            RemoteSigningContent(
                viewModelFlow = viewModelFlow,
                transAuth = transAuth,
                timeActiveRemoteSinging = timeActiveRemoteSinging
            )
        },
        handleSideEffect = { sideEffect ->
            when (sideEffect) {
                is RemoteSigningReducer.RemoteSigningViewEffect.DoInitRemoteSigningSuccessEffect -> {
                    handleSideEffectSuccessSigning(
                        navController = navController,
                        sideEffect = sideEffect,
                        viewModelFlow = viewModelFlow,
                        messageError = messageError
                    )
                }
                else -> {
                    //Do nothing
                }
            }
        },
        backgroundColor = LocalColorScheme.current.bgMainPrimary,
        topAppBarType = TopAppBarType.Title,
        topAppBarConfig = TopAppBarConfig(
            titleTopAppBar = stringResource(R.string.xac_thuc_bang_chu_ky_so_tu_xa),
            onNavigationClick = {
                NavigationHelper.closeVerifyByTypeTransaction(navController, viewModelFlow)
            }
        ),
    )
}

private fun handleSideEffectSuccessSigning(
    navController: NavHostController,
    sideEffect: RemoteSigningReducer.RemoteSigningViewEffect.DoInitRemoteSigningSuccessEffect,
    viewModelFlow: VerifyByTypeTransactionFlowViewModel,
    messageError: String
) {
    if (sideEffect.data?.authValue != null) {
        navController.navigate(
            VerifyTransactionRoute.VerifyTransactionScreenRoute(
                data = sideEffect.data.authValue,
            )
        )
    } else {
        viewModelFlow.showPopupError(errorMessage = messageError)
        NavigationHelper.closeVerifyByTypeTransaction(navController, viewModelFlow)
    }
}

@Composable
private fun RemoteSigningContent(
    viewModelFlow: VerifyByTypeTransactionFlowViewModel,
    transAuth: TransAuthDMO,
    timeActiveRemoteSinging: Long
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current

        Column(
            modifier = Modifier.padding(horizontal = IBSpacing.spacingM),
            verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingM)
        ) {
            Text(
                stringResource(R.string.quy_khach_vui_long_xac_thuc_giao_dich_bang_chung_thu_so_co_thong_tin_nhu_sau),
                style = typography.bodyBody_l,
                color = colorScheme.contentMainTertiary,
                textAlign = TextAlign.Start
            )

            //TODO thay doi khi lay duoc thong tin
            InfoItemRemoteSigning(
                title = stringResource(R.string.so_serial),
                info = "asdaysd121312312312312312he121312"
            )

            InfoItemRemoteSigning(
                title = stringResource(R.string.nha_cung_cap),
                info = "VIETTEL"
            )

            Box(modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(IBCornerRadius.cornerRadiusL))
                .border(
                    width = IBBorderDivider.borderDividerS / 4,
                    color = colorScheme.borderMainPrimary,
                    shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL)
                )
                .background(color = colorScheme.bgMainSecondary)
            ) {
                Column(
                    modifier = Modifier.padding(IBSpacing.spacingM)
                ) {
                    IBankLoaderIndicators(
                        isParentFullSize = false,
                        backgroundColor = colorScheme.bgMainSecondary,
                        modifier = Modifier
                            .padding(vertical = IBSpacing.spacing2xl)
                            .fillMaxWidth()
                    )

                    Text(
                        stringResource(R.string.he_thong_dang_trong_qua_trinh_ket_noi_voi_nha_cung_cap_chu_ky_so_cua_ban_vui_long_thuc_hien_ky_tren_ung_dung_ky_so_da_duoc_cai_dat),
                        style = typography.bodyBody_m,
                        color = colorScheme.contentMainTertiary,
                        textAlign = TextAlign.Center
                    )

                }
            }

            Text(
                AnnotatedString.fromHtml(stringResource(R.string.cdatathoi_gian_thuc_hien_ky_so_trong_vong_bsb_giay, timeActiveRemoteSinging)),
                style = typography.bodyBody_m,
                color = colorScheme.contentMainTertiary,
                textAlign = TextAlign.Start
            )

        }
}

@Composable
private fun InfoItemRemoteSigning(
    title: String,
    info: String
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(IBSpacing.spacingXs)
    ) {
        Icon(
            painter = painterResource(id = vn.com.bidv.designsystem.R.drawable.gui_chung_tu_chu_ky_so),
            contentDescription = null,
            modifier = Modifier
                .size(IBSpacing.spacingS)
        )

        Text(
            "$title:",
            style = typography.labelLabel_l,
            color = colorScheme.contentMainTertiary,
            textAlign = TextAlign.Start
        )

        Text(
            info,
            style = typography.bodyBody_m,
            color = colorScheme.contentMainTertiary,
            textAlign = TextAlign.Start
        )
    }
}




