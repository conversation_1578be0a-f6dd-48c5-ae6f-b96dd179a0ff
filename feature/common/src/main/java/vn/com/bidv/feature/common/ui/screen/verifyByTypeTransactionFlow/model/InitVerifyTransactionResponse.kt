package vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model

import com.google.gson.annotations.SerializedName
import vn.com.bidv.feature.common.domain.data.TransAuthDMO

data class InitVerifyTransactionResponse (
    @SerializedName("requireAuth")
    val requireAuth: Boolean? = null,

    @SerializedName("transAuth")
    val transAuth: TransAuthDMO? = null,

    @SerializedName("transKey")
    val transKey: String? = null,

    @SerializedName("confirmUrl")
    val confirmUrl: String? = null
)