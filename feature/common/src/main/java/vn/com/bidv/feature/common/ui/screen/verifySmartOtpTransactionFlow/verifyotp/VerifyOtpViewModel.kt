package vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.verifyotp

import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.feature.common.domain.VerifyTransactionUseCase
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.verifyotp.VerifyOtpReducer.VerifyOtpViewEffect
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.verifyotp.VerifyOtpReducer.VerifyOtpViewEvent
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.verifyotp.VerifyOtpReducer.VerifyOtpViewState
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class VerifyOtpViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val verifyTransactionUseCase: VerifyTransactionUseCase
) : ViewModelIBankBase<VerifyOtpViewState, VerifyOtpViewEvent, VerifyOtpViewEffect>(
    initialState = VerifyOtpViewState(), reducer = VerifyOtpReducer()
) {
    override fun handleEffect(
        sideEffect: VerifyOtpViewEffect, onResult: (VerifyOtpViewEvent) -> Unit
    ) {
        when (sideEffect) {

            is VerifyOtpViewEffect.InitTransaction -> {
                onResult(VerifyOtpViewEvent.OnInitTransactionSuccess(verifyTransactionUseCase.isLoginSuccess()))
            }

            is VerifyOtpViewEffect.GetListOtpCode -> {
                callDomain(showLoadingIndicator = false, onSuccess = {
                    onResult(
                        VerifyOtpViewEvent.OnGetListOtpCodeSuccess(
                            it.data ?: emptyList()
                        )
                    )
                }) {
                    verifyTransactionUseCase.genListOTPCode(
                        authId = sideEffect.authId,
                        sideEffect.secretKey
                    )
                }
            }

            is VerifyOtpViewEffect.ConfirmVerifyOtpTransaction -> {
                onResult(VerifyOtpViewEvent.OnShareOtpTransactionSuccess(sideEffect.otp))
            }

            is VerifyOtpViewEffect.ShareOtpTransactionSuccess -> {
                //nothing
            }
        }
    }
}
