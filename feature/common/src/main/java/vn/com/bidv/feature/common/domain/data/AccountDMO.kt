package vn.com.bidv.feature.common.domain.data

import com.google.gson.annotations.SerializedName
import vn.com.bidv.sdkbase.utils.formatMoney

data class AccountDMO(
    @SerializedName("accountNo") val accountNo: String? = null,
    @SerializedName("accountName") val accountName: String? = null,
    @SerializedName("branchCode") val branchCode: String? = null,
    @SerializedName("currCode") val currCode: String? = null,
    @SerializedName("holdBalance") val holdBalance: java.math.BigDecimal? = null,
    @SerializedName("minBalance") val minBalance: java.math.BigDecimal? = null,
    @SerializedName("overdraftBalance") val overdraftBalance: java.math.BigDecimal? = null,
    @SerializedName("availableBalance") val availableBalance: java.math.BigDecimal? = null,
    @SerializedName("default") val default: Boolean? = null
) {
    fun getAmount(): String {
        return availableBalance?.toString().formatMoney(currCode)
    }

    fun searchKey(): String {
        return "$accountNo $accountName"
    }

    fun searchKeyForPaymentAccountDetail(): String {
        return "$accountNo $accountName $currCode"
    }
}