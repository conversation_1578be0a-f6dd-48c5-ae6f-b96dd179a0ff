package vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.createdqr

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import vn.com.bidv.common.extenstion.isNotNullOrEmpty
import vn.com.bidv.common.utils.unpack
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.component.feedback.otpview.IBankOtpItemInputList
import vn.com.bidv.designsystem.component.keyboard.NumpadKeyboard
import vn.com.bidv.designsystem.component.keyboard.NumpadType
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankActionBar
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.theme.Roboto
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.createdqr.CreatedQrReducer.CreatedQrViewEvent
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.common.navigation.NavigationHelper
import vn.com.bidv.feature.common.navigation.VerifyTransactionRoute
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionTypeConstant
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.viewModelFlow.VerifyByTypeTransactionFlowReducer
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.viewModelFlow.VerifyByTypeTransactionFlowViewModel
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.viewModelFlow.sharedVerifyTransactionViewModel
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.model.ModelVerifyTransactionUI
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.ui.HandleBackAction
import vn.com.bidv.sdkbase.utils.ImageUtils
import vn.com.bidv.localization.R as RLocalization
import vn.com.bidv.sdkbase.utils.QrUtils
import vn.com.bidv.designsystem.R as RDesignSystem

@Composable
fun CreatedQrScreen(
    navController: NavHostController, modelCreatedQRUI: ModelVerifyTransactionUI
) {
    val vm: CreatedQrViewModel = hiltViewModel()
    val (_, onEvent, _) = vm.unpack()
    val viewModelFlow = navController.sharedVerifyTransactionViewModel()
    BaseScreen(
        navController = navController,
        viewModel = vm,
        renderContent = { uiState, onEvent ->
            if (!uiState.isInitSuccess) {
                onEvent(CreatedQrViewEvent.OnInitTransaction(modelCreatedQRUI))
            } else {
                CreatedQrContent(
                    uiState = uiState,
                    onEvent = onEvent,
                    navController = navController,
                    viewModelFlow = viewModelFlow
                )
            }

            HandleBackAction {
                NavigationHelper.closeVerifyByTypeTransaction(navController, viewModelFlow)
            }
        },
        topAppBarType = TopAppBarType.Title,
        topAppBarConfig = TopAppBarConfig(
            showHomeIcon = false,
            titleTopAppBar = stringResource(RLocalization.string.xac_thuc_bang_smart_otp),
            onNavigationClick = {
                NavigationHelper.closeVerifyByTypeTransaction(navController, viewModelFlow)
            }
        ),
        handleSideEffect = { createdQrViewEffect ->
            when (createdQrViewEffect) {
                is CreatedQrReducer.CreatedQrViewEffect.FinishQRFlow -> {
                    if (createdQrViewEffect.otpVerify.isNotNullOrEmpty()) {
                        navController.navigate(
                            VerifyTransactionRoute.VerifyTransactionScreenRoute(
                                data = createdQrViewEffect.otpVerify,
                            )
                        )
                    } else {
                        NavigationHelper.closeVerifyByTypeTransaction(navController, viewModelFlow)
                    }
                }

                else -> {
                    //nothing
                }
            }
        },
    )
}

private fun handleClickPopUpRetry(
    navController: NavHostController,
    stateInit: VerifyByTypeTransactionFlowReducer.VerifyByTypeTransactionFlowViewState,
    viewModelFlow: VerifyByTypeTransactionFlowViewModel
) {
    NavigationHelper.closeVerifyByTypeTransaction(navController, viewModelFlow, false)

    if (stateInit.txnIds.txnIds.isNotEmpty()) {
        NavigationHelper.navigateToVerifyByTypeTransaction(
            navController,
            txnIds = stateInit.txnIds.txnIds,
            type = VerifyTransactionTypeConstant.getTypeConstantByCode(stateInit.type)
        )
    } else if (stateInit.dataCreated.dataString.isNotNullOrEmpty()) {
        NavigationHelper.navigateToVerifyByTypeCreateTransaction(
            navController,
            type = VerifyTransactionTypeConstant.getTypeConstantByCode(stateInit.type),
            dataString = stateInit.dataCreated.dataString.orEmpty()
        )
    }

}

@Composable
fun CreatedQrContent(
    uiState: CreatedQrReducer.CreatedQrViewState,
    onEvent: (CreatedQrViewEvent) -> Unit,
    navController: NavHostController,
    viewModelFlow: VerifyByTypeTransactionFlowViewModel
) {
    var ticketBitmap: Bitmap? by remember { mutableStateOf(null) }
    var isShowPopUpRetry by remember { mutableStateOf(false) }
    val uiScope = rememberCoroutineScope()

    onEvent(CreatedQrViewEvent.OnStartReceiverOtp)

    var timeActiveQR by remember { mutableLongStateOf((uiState.modelVerifyTransactionUI.timeEffectTxn - System.currentTimeMillis()) / 1000L) }
    LaunchedEffect(true) {
        while (timeActiveQR > 0 && !uiState.isFillOtp) {
            delay(1000)
            timeActiveQR -= 1
        }
    }

    if (timeActiveQR <= 0) {
        isShowPopUpRetry = true
    }

    var qrBitmap by remember { mutableStateOf<Bitmap?>(null) }
    LaunchedEffect(true) {
        val logoBitmap = BitmapFactory.decodeResource(navController.context.resources, RDesignSystem.drawable.logo_qr)
        qrBitmap =
            QrUtils.createQRCode(content = uiState.modelVerifyTransactionUI.qrCode ?: "", size = 600, logo = logoBitmap)
    }

    if (isShowPopUpRetry) {
        IBankModalConfirm(modalConfirmType = ModalConfirmType.Question,
            title = stringResource(R.string.loi),
            supportingText = stringResource(R.string.giao_dich_het_hieu_luc_vui_long_thuc_hien_lai),
            listDialogButtonInfo = listOf(
                DialogButtonInfo(
                    label = stringResource(R.string.thu_lai),
                    onClick = {
                        handleClickPopUpRetry(
                            navController,
                            viewModelFlow.uiState.value,
                            viewModelFlow
                        )
                        isShowPopUpRetry = false
                    },
                    isDismissRequest = false
                ),
            ),
            onDismissRequest = {
                isShowPopUpRetry = false
                NavigationHelper.closeVerifyByTypeTransaction(navController, viewModelFlow)
            })
    }

    Column(modifier = Modifier.fillMaxSize()) {
        var isShowKeyboard by remember { mutableStateOf(false) }
        val listState = rememberLazyListState()

        LazyColumn(modifier = Modifier.weight(1f), state = listState) {
            items(3) { index ->
                when (index) {
                    0 -> {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .wrapContentHeight()
                                .clickable(
                                    indication = null,
                                    interactionSource = remember { MutableInteractionSource() }) {
                                    isShowKeyboard = false
                                },
                        ) {

                            Column(
                                modifier = Modifier
                                    .padding(horizontal = IBSpacing.spacingM)
                            ) {
                                ModalLine(
                                    modifier = Modifier
                                        .fillMaxWidth(),
                                    leadingIcon = RDesignSystem.drawable.tien_ich,
                                    text = buildAnnotatedString {
                                        withStyle(style = SpanStyle(fontWeight = FontWeight.Bold)) {
                                            append(
                                                stringResource(RLocalization.string.buoc_1).plus(
                                                    " "
                                                )
                                            )
                                        }
                                        append(
                                            stringResource(RLocalization.string.mo_app_bidv_direct_tren_dien_thoai).plus(
                                                " "
                                            )
                                        )
                                        withStyle(
                                            style = SpanStyle(
                                                fontWeight = FontWeight.Bold,
                                                color = LocalColorScheme.current.bgNegativePrimary
                                            )
                                        ) {
                                            append(
                                                "(${
                                                    stringResource(RLocalization.string.khong).uppercase()
                                                        .plus(" ")
                                                }"
                                            )
                                        }
                                        append("${stringResource(RLocalization.string.dang_nhap).lowercase()})")
                                    },
                                )

                                Spacer(modifier = Modifier.padding(IBSpacing.spacingXs))

                                ModalLine(
                                    modifier = Modifier
                                        .fillMaxWidth(),
                                    leadingIcon = RDesignSystem.drawable.tien_ich,
                                    text = buildAnnotatedString {
                                        withStyle(
                                            style = SpanStyle(
                                                fontSize = 14.sp,
                                                textDecoration = TextDecoration.None,
                                                fontFamily = Roboto,
                                                fontStyle = FontStyle.Normal,
                                                letterSpacing = 0.sp,
                                                fontWeight = FontWeight.Bold
                                            )
                                        ) {
                                            append(
                                                stringResource(RLocalization.string.buoc_2).plus(
                                                    " "
                                                )
                                            )
                                        }
                                        append(stringResource(RLocalization.string.chon_chuc_nang_quet_ma))

                                    },
                                    endIcon = RDesignSystem.drawable.qr_code
                                )

                                Spacer(modifier = Modifier.padding(IBSpacing.spacingXs))

                                ModalLine(
                                    modifier = Modifier
                                        .fillMaxWidth(),
                                    leadingIcon = RDesignSystem.drawable.tien_ich,
                                    text = buildAnnotatedString {
                                        withStyle(
                                            style = SpanStyle(
                                                fontSize = 14.sp,
                                                textDecoration = TextDecoration.None,
                                                fontFamily = Roboto,
                                                fontStyle = FontStyle.Normal,
                                                letterSpacing = 0.sp,
                                                fontWeight = FontWeight.Bold
                                            )
                                        ) {
                                            append(
                                                stringResource(RLocalization.string.buoc_3).plus(
                                                    " "
                                                )
                                            )
                                        }
                                        append(stringResource(RLocalization.string.quet_ma_qr_ben_duoi_de_xac_thuc))

                                    },
                                )

                                Spacer(modifier = Modifier.padding(IBSpacing.spacingXs))

                                ModalLine(
                                    modifier = Modifier
                                        .fillMaxWidth(),
                                    leadingIcon = RDesignSystem.drawable.tien_ich,
                                    text = buildAnnotatedString {
                                        withStyle(
                                            style = SpanStyle(
                                                fontSize = 14.sp,
                                                textDecoration = TextDecoration.None,
                                                fontFamily = Roboto,
                                                fontStyle = FontStyle.Normal,
                                                letterSpacing = 0.sp,
                                                fontWeight = FontWeight.Bold
                                            )
                                        ) {
                                            append(
                                                stringResource(RLocalization.string.buoc_4).plus(
                                                    " "
                                                )
                                            )
                                        }
                                        append(stringResource(RLocalization.string.nhap_ma_pin_va_nhan_xac_nhan_o_man_xac_thuc_giao_dich))

                                    },
                                )

                                Spacer(Modifier.size(IBSpacing.spacingM))

                                QRImageCaptureContent(
                                    qrBitmap = qrBitmap,
                                    timeActiveQR = timeActiveQR
                                )

                                Spacer(modifier = Modifier.size(IBSpacing.spacingM))
                            }

                            ticketBitmap?.let {
                                ImageUtils.shareImageViaIntent(
                                    context = navController.context,
                                    bitmap = it,
                                    onStartShareImage = {
                                        // Do something
                                    },
                                    onShareImageDone = { _ ->
                                        ticketBitmap = null
                                    }
                                )
                            }
                        }
                    }

                    1 -> {
                        Column(
                            Modifier
                                .fillMaxWidth()
                                .clickable(indication = null,
                                    interactionSource = remember { MutableInteractionSource() }) {
                                    isShowKeyboard = false
                                }
                                .padding(
                                    start = IBSpacing.spacingM,
                                    end = IBSpacing.spacingM
                                ),
                            horizontalAlignment = Alignment.CenterHorizontally,
                        ) {
                            Text(
                                text = stringResource(RLocalization.string.ma_xac_thuc_smart_otp),
                                style = LocalTypography.current.bodyBody_m.copy(fontWeight = FontWeight.Bold),
                                color = LocalColorScheme.current.contentMainTertiary
                            )
                            Spacer(modifier = Modifier.size(IBSpacing.spacing2xs))
                            Text(
                                text = stringResource(RLocalization.string.trong_truong_hop_he_thong_ho_tro_dien_tu_dong_khong_thanh_cong_quy_khach_vui_long_nhap_thu_cong_ma_xac_thuc_smart_otp),
                                style = LocalTypography.current.bodyBody_m,
                                color = LocalColorScheme.current.contentMainTertiary,
                                textAlign = TextAlign.Center
                            )
                        }
                    }

                    2 -> {
                        Column {
                            IBankOtpItemInputList(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(
                                        top = IBSpacing.spacingM,
                                        start = IBSpacing.spacingM,
                                        end = IBSpacing.spacingM
                                    )
                                    .clickable(indication = null, interactionSource = remember {
                                        MutableInteractionSource()
                                    }) {
                                        isShowKeyboard = true
                                    },
                                otpText = uiState.otp,
                                otpItemNumber = 6,
                                isFocus = isShowKeyboard,
                                onOtpInputDone = {
                                    onEvent(CreatedQrViewEvent.OnOTPInputSuccess)
                                }
                            )
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(top = IBSpacing.spacingM)
                            ) {
                                if (isShowKeyboard) {
                                    NumpadKeyboard(
                                        modifier = Modifier.height(320.dp),
                                        type = NumpadType.SHUFFLED,
                                        onKeyClick = {
                                            onEvent(CreatedQrViewEvent.OnOTPChanged(it))
                                        })
                                    LaunchedEffect(true) {
                                        uiScope.launch {
                                            listState.animateScrollToItem(2)
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        if (!isShowKeyboard) {
            IBankActionBar(
                modifier = Modifier
                    .fillMaxWidth(),
                isVertical = false,
                buttonNegative = DialogButtonInfo(
                    label = stringResource(RLocalization.string.dong)
                ) {
                    NavigationHelper.closeVerifyByTypeTransaction(navController, viewModelFlow)
                },
                buttonPositive = DialogButtonInfo(
                    label = stringResource(RLocalization.string.chia_se_qr)
                ) {
                    uiScope.launch {
                        ticketBitmap = ImageUtils.captureComposableToBitmap(navController.context) {
                            QRImageCaptureContent(
                                qrBitmap = qrBitmap,
                                timeActiveQR = timeActiveQR
                            )
                        }
                    }
                }
            )
        }
    }
}

@Composable
private fun QRImageCaptureContent(
    qrBitmap: Bitmap?,
    timeActiveQR: Long,
) {
    Column(
        modifier = Modifier
            .testTagIBank("common_qr_image")
            .fillMaxWidth()
            .clip(
                shape = RoundedCornerShape(IBSpacing.spacingXs)
            )
            .background(LocalColorScheme.current.bgMainTertiary)
            .padding(IBSpacing.spacingM)
    ) {

        Box(
            modifier = Modifier
                .size(200.dp)
                .align(Alignment.CenterHorizontally)
        ) {
            qrBitmap?.let {
                Image(
                    bitmap = it.asImageBitmap(),
                    contentDescription = "QR Code",
                    contentScale = ContentScale.Fit,
                    modifier = Modifier
                        .defaultMinSize(200.dp, 200.dp)
                )
            }
        }

        Spacer(modifier = Modifier.size(IBSpacing.spacingS))

        val textTimeActive = buildAnnotatedString {
            append(
                stringResource(
                    RLocalization.string.ma_qr_se_het_hieu_luc_sau
                ).plus(" ")
            )
            withStyle(
                style = SpanStyle(
                    letterSpacing = 0.sp,
                    fontWeight = FontWeight.W400,
                    fontStyle = FontStyle.Normal,
                    fontSize = 14.sp,
                    textDecoration = TextDecoration.None,
                    fontFamily = Roboto,
                    color = LocalColorScheme.current.contentBrand_01Primary
                )
            ) {
                append("$timeActiveQR ")
            }
            append(stringResource(RLocalization.string.giay))
        }

        Text(
            modifier = Modifier
                .testTagIBank("common_qr_time_active")
                .wrapContentWidth()
                .align(Alignment.CenterHorizontally),
            text = textTimeActive,
            style = LocalTypography.current.captionCaption_m,
            color = LocalColorScheme.current.contentMainSecondary,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun ModalLine(
    modifier: Modifier,
    @DrawableRes leadingIcon: Int? = null,
    text: AnnotatedString,
    @DrawableRes endIcon: Int? = null
) {
    Row(modifier) {

        leadingIcon?.let {
            Icon(
                modifier = Modifier.size(IBSpacing.spacingM),
                painter = painterResource(leadingIcon),
                contentDescription = null,
                tint = Color.Unspecified
            )

            Spacer(modifier = Modifier.size(IBSpacing.spacingXs))
        }

        Text(
            modifier = Modifier.wrapContentSize(),
            text = text,
        )

        endIcon?.let {
            Spacer(modifier = Modifier.size(IBSpacing.spacingXs))

            Icon(
                modifier = Modifier.size(IBSpacing.spacingM),
                painter = painterResource(endIcon),
                contentDescription = null,
                tint = Color.Unspecified
            )
        }
    }
}

@Preview
@Composable
fun ModalLinePreview() {
    ModalLine(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White),
        leadingIcon = RDesignSystem.drawable.tien_ich,
        text = buildAnnotatedString {
            withStyle(style = SpanStyle(fontWeight = FontWeight.Bold)) {
                append("Bước 2: ")
            }
            append("Mở App BIDV iBank trên điện thoại \n")
            withStyle(
                style = SpanStyle(
                    fontWeight = FontWeight.Bold, color = LocalColorScheme.current.bgNegativePrimary
                )
            ) {
                append("KHÔNG ")
            }
            append("đăng nhập")
        },
        endIcon = RDesignSystem.drawable.qr_code
    )
}
