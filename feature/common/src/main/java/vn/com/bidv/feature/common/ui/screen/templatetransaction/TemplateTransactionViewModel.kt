package vn.com.bidv.feature.common.ui.screen.templatetransaction

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class TemplateTransactionViewModel @Inject constructor(
    val tabBuilders: Set<@JvmSuppressWildcards TemplateTransactionTabBuilder>,
    val userInfoUseCase: UserInfoUseCase
) :
    ViewModelIBankBase<TemplateTransactionReducer.TemplateTransactionViewState, TemplateTransactionReducer.TemplateTransactionViewEvent, TemplateTransactionReducer.TemplateTransactionViewEffect>(
        initialState = TemplateTransactionReducer.TemplateTransactionViewState(
            tabs = userInfoUseCase.getTemplateTab()
        ),
        reducer = TemplateTransactionReducer()
    ) {
    override fun handleEffect(
        sideEffect: TemplateTransactionReducer.TemplateTransactionViewEffect,
        onResult: (TemplateTransactionReducer.TemplateTransactionViewEvent) -> Unit
    ) {/*Do nothing*/ }
}