package vn.com.bidv.feature.common.domain.data

import com.google.gson.annotations.SerializedName
import kotlinx.serialization.Serializable

@Serializable
data class FXTransactionNavParams(
    @SerializedName(value = "fxTransactionID")
    val fxTransactionID: String? = null,
    @SerializedName(value = "agreementCode")
    val agreementCode: String? = null,
    @SerializedName(value = "fxTransactionAction")
    val fxTransactionAction: FXTransactionTypeAction? = null
)

enum class FXTransactionTypeAction(val value: String) {
    @SerializedName(value = "CREATED")
    CREATED("CREATED"), // Tạo mới
    @SerializedName(value = "UPDATE")
    UPDATE("UPDATE"), // Cập nhật
    @SerializedName(value = "COPY")
    COPY("COPY"), // Sao chép
    @SerializedName(value = "EDIT")
    EDIT("EDIT") // Chỉnh sửa
}
