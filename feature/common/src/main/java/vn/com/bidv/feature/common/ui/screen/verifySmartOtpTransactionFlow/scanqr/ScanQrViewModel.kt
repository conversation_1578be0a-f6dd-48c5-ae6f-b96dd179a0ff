package vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.scanqr

import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.launch
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.feature.common.constants.Constants
import vn.com.bidv.feature.common.di.AuthProvider
import vn.com.bidv.feature.common.domain.VerifyTransactionUseCase
import vn.com.bidv.feature.common.domain.data.QrCodeParseDMO
import vn.com.bidv.feature.common.ui.component.callDomainLogout
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.scanqr.ScanQrReducer.ScanQrViewEffect
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.scanqr.ScanQrReducer.ScanQrViewEvent
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.scanqr.ScanQrReducer.ScanQrViewState
import vn.com.bidv.localization.R.string
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class ScanQrViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val verifyTransactionUseCase: VerifyTransactionUseCase,
    private val authProvider: AuthProvider,
) : ViewModelIBankBase<ScanQrViewState, ScanQrViewEvent, ScanQrViewEffect>(
    initialState = ScanQrViewState(),
    reducer = ScanQrReducer()
) {
    override fun handleEffect(sideEffect: ScanQrViewEffect, onResult: (ScanQrViewEvent) -> Unit) {
        when (sideEffect) {

            is ScanQrViewEffect.InitScreen -> {
                onResult(ScanQrViewEvent.OnInitScreenSuccess(verifyTransactionUseCase.isLoginSuccess()))
            }

            is ScanQrViewEffect.ParseQr -> {
                callDomain(
                    isListenAllError = true,
                    onSuccess = {
                        onResult(ScanQrViewEvent.OnParseQrSuccess(it.data ?: QrCodeParseDMO(), localRepository.isLoginSuccess()))
                    },
                    onFail = {
                        onResult(
                            ScanQrViewEvent.OnParseQrError(
                                errorCode = it?.errorCode ?: Constants.VERIFY_OTP,
                                errorMessage = it?.errorMessage
                                    ?: resourceProvider.getString(string.co_loi_xay_ra_quy_khach_vui_long_thu_lai)
                            )
                        )
                    },
                    listErrorCodeListen = listOf(Constants.PARSE_QR_ERROR)
                ) {
                    verifyTransactionUseCase.parseQr(sideEffect.data)
                }
            }
            is ScanQrViewEffect.ShareDataCreateTransaction -> {
                viewModelScopes.launch(dispatcher) {
                    localRepository.pushDataToCreateTransaction(sideEffect.data)
                }
            }

            is ScanQrViewEffect.Logout -> {
                callDomainLogout(
                    authProvider = authProvider,
                    onResult = {
                        onResult(ScanQrViewEvent.OnLogoutSuccess)
                    }
                )
            }

            is ScanQrViewEffect.ParseQrSuccess,
            is ScanQrViewEffect.ParseQrError,
            is ScanQrViewEffect.LogoutSuccess,
            is ScanQrViewEffect.ScanQrError -> {
                // nothing
            }
        }
    }
}
