package vn.com.bidv.feature.common.domain.data

import com.google.gson.annotations.SerializedName
import kotlinx.serialization.Serializable

data class DataListTxnLogDMO(

    /* List data */
    @SerializedName("items")
    val items: List<TxnLogDMO>? = null,

    /* Total size */
    @SerializedName("total")
    val total: Long? = null

)

@Serializable
data class TxnLogDMO(
    @SerializedName("id")
    val id: Long? = null,

    @SerializedName("txnId")
    val txnId: String? = null,

    @SerializedName("txnCode")
    val txnCode: String? = null,

    @SerializedName("logType")
    val logType: String? = null,

    @SerializedName("logTypeDesc")
    val logTypeDesc: String? = null,

    @SerializedName("status")
    val status: String? = null,

    @SerializedName("statusDesc")
    val statusDesc: String? = null,

    @SerializedName("createdDate")
    val createdDate: String? = null,

    @SerializedName("createdBy")
    val createdBy: String? = null,

    @SerializedName("channel")
    val channel: String? = null,

    @SerializedName("appcode")
    val appcode: String? = null,

    @SerializedName("source")
    val source: String? = null,

    @SerializedName("logNote")
    val logNote: String? = null

) {

}