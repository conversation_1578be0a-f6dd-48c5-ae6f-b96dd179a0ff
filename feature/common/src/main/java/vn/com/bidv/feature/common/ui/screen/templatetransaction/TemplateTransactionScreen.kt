package vn.com.bidv.feature.common.ui.screen.templatetransaction

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.segmentcontrol.tab.IBankTabRow
import vn.com.bidv.designsystem.component.segmentcontrol.tab.TabItem
import vn.com.bidv.designsystem.component.segmentcontrol.tab.TabType
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.common.domain.data.TransactionTabRouteID
import vn.com.bidv.sdkbase.ui.DefaultNoRouteScreen
import vn.com.bidv.localization.R as RLocal

@Composable
fun TemplateTransactionScreen(
    navController: NavHostController,
    routeID: TransactionTabRouteID? = null
) {
    val templateTransactionViewModel: TemplateTransactionViewModel = hiltViewModel()

    BaseScreen(
        navController = navController,
        viewModel = templateTransactionViewModel,
        topAppBarConfig = TopAppBarConfig(titleTopAppBar = stringResource(RLocal.string.quan_ly_mau_giao_dich)),
        handleSideEffect = { }
    ) { uiState, onEvent ->
        LaunchedEffect(Unit) {
            routeID?.let {
                onEvent(
                    TemplateTransactionReducer.TemplateTransactionViewEvent.SelectedTab(
                        indexTab = if (routeID == TransactionTabRouteID.PaymentTab) 0 else 1
                    )
                )
            }
        }
        ListTemplateTransactionScreenContent(
            navController = navController,
            templateTransactionViewModel = templateTransactionViewModel,
            uiState = uiState,
            onEvent = onEvent,
        )
    }
}

@Composable
fun ListTemplateTransactionScreenContent(
    navController: NavHostController,
    templateTransactionViewModel: TemplateTransactionViewModel,
    uiState: TemplateTransactionReducer.TemplateTransactionViewState,
    onEvent: (TemplateTransactionReducer.TemplateTransactionViewEvent) -> Unit,
) {
    if (uiState.tabs.isEmpty()) return
    val lazyListStates =
        uiState.tabs.associate { tab ->
            tab.routeID to rememberLazyListState()
        }.toMap()
    val selectedRouteID = uiState.tabs[uiState.tabLayoutIndex].routeID
    val selectedTabState = lazyListStates[selectedRouteID] ?: rememberLazyListState()

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        //tab layout
        val listTab = uiState.tabs.map { TabItem(stringResource(it.titleResourceID)) }

        Column {
            IBankTabRow(
                listTab = listTab,
                tabType = TabType.UNDERLINE,
                fullWidth = true,
                indent = true,
                showBackground = true,
                selectedTabIndex = uiState.tabLayoutIndex
            ) { currentTab ->
                onEvent(
                    TemplateTransactionReducer.TemplateTransactionViewEvent.SelectedTab(
                        indexTab = listTab.indexOf(currentTab)
                    )
                )
            }
        }

        templateTransactionViewModel.tabBuilders
            .find {
                it.routeID == (selectedRouteID)
            }?.content?.invoke(navController, selectedTabState) ?: DefaultNoRouteScreen()
    }
}
