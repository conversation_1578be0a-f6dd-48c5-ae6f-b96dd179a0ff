package vn.com.bidv.feature.common.ui.component

import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.common.di.AuthProvider
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase

fun <UiState : ViewState, UiEvent : ViewEvent, IBankSideEffect : SideEffect>
        ViewModelIBankBase<UiState, UiEvent, IBankSideEffect>.callDomainLogout(authProvider: AuthProvider, onResult: () -> Unit) {
    callDomain(
        isListenAllError = true,
        isListenSessionExpired = true,
        onSuccess = {
            onResult.invoke()
        },
        onFail = {
            onResult.invoke()
        }
    ) {
        authProvider.logout()
    }
}