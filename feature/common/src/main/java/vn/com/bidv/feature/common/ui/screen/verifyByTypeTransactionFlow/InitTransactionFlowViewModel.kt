package vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow

import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineScope
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.InitTransactionFlowReducer.InitTransactionFlowViewEffect
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.InitTransactionFlowReducer.InitTransactionFlowViewEvent
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.InitTransactionFlowReducer.InitTransactionFlowViewState
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InitVerifyTransactionResponse
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyCreateTransaction
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyTransaction
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionFlowScreenBuilder
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

enum class StatusCodeWarning(val statusCode: String) {
    WARNING_CODE("3"),
    TSS0201("TSS0201"),
    TSS0202("TSS0202"),
    TSS0303("TSS0303"),
    TSS1103("TSS1103"),
    TSS0501("TSS0501");

    companion object {
        fun contains(code: String?): Boolean {
            return entries.any { it.statusCode == code }
        }
    }
}

enum class ClickOptionTypePopupWarning {
    ClosePopup,
    ActiveSmartOtp,
    ConvertSmartOtp,
    ReActiveSmartOtp,
    DoInitAgain,
}

@HiltViewModel
class InitTransactionFlowViewModel @Inject constructor(
    private val userInfoUseCase: UserInfoUseCase,
    val itemVerifyFlowScreenBuilder: Set<@JvmSuppressWildcards VerifyTransactionFlowScreenBuilder>,
) : ViewModelIBankBase<InitTransactionFlowViewState, InitTransactionFlowViewEvent, InitTransactionFlowViewEffect>(
    initialState = InitTransactionFlowViewState(),
    reducer = InitTransactionFlowReducer()
) {
    override fun handleEffect(
        sideEffect: InitTransactionFlowViewEffect,
        onResult: (InitTransactionFlowViewEvent) -> Unit
    ) {

        when (sideEffect) {
            is InitTransactionFlowViewEffect.DoInitVerifyTransactionEffect -> {
                val matchedUseCase =
                    itemVerifyFlowScreenBuilder.find { it.type.code == sideEffect.type }?.useCase
                if (matchedUseCase != null) {
                    handleCallDomain(
                        onResult = onResult
                    ) {
                        matchedUseCase.initTransaction(
                            input = InputVerifyTransaction(
                                sideEffect.txnIds,
                                confirm = sideEffect.confirm
                            )
                        )
                    }
                }
            }

            is InitTransactionFlowViewEffect.DoInitVerifyCreateTransactionEffect -> {
                val matchedUseCase =
                    itemVerifyFlowScreenBuilder.find { it.type.code == sideEffect.type }?.useCase
                if (matchedUseCase != null) {
                    handleCallDomain(
                        onResult = onResult
                    ) {
                        matchedUseCase.initCreateTransaction(
                            input = InputVerifyCreateTransaction(
                                sideEffect.dataString,
                            )
                        )
                    }
                }
            }

            is InitTransactionFlowViewEffect.DoHandleInitVerifyTransactionSuccessWarningEffect -> {
                var typeClick = when (sideEffect.statusCode) {
                    StatusCodeWarning.WARNING_CODE.statusCode -> {
                        ClickOptionTypePopupWarning.DoInitAgain
                    }

                    StatusCodeWarning.TSS0201.statusCode -> {
                        ClickOptionTypePopupWarning.ActiveSmartOtp
                    }

                    StatusCodeWarning.TSS0501.statusCode -> {
                        ClickOptionTypePopupWarning.ConvertSmartOtp
                    }

                    //TSS0202,TSS0303,TSS1103
                    StatusCodeWarning.TSS0202.statusCode, StatusCodeWarning.TSS0303.statusCode, StatusCodeWarning.TSS1103.statusCode -> {
                        if (userInfoUseCase.isAdminRole()) {
                            ClickOptionTypePopupWarning.ClosePopup
                        } else {
                            ClickOptionTypePopupWarning.ReActiveSmartOtp
                        }
                    }

                    else -> {
                        null
                    }
                }

                typeClick?.let {
                    onResult(
                        InitTransactionFlowViewEvent.DoInitVerifyTransactionWarningEventSuccess(
                            typeClick = it,
                            errorMessage = sideEffect.errorMessage
                        )
                    )
                }

            }

            else -> {
                // Handle other side effects if needed
            }
        }
    }

    private fun handleCallDomain(
        onResult: (InitTransactionFlowViewEvent) -> Unit,
        callBlock: suspend CoroutineScope.() -> DomainResult<InitVerifyTransactionResponse>,
    ) {
        callDomain(
            isListenAllError = true,
            onSuccess = { result ->
                onResult(
                    InitTransactionFlowViewEvent.DoInitVerifyTransactionSuccessEvent(
                        data = result.data,
                    )
                )
            },
            onFail = { error ->
                // Xử lý trường hợp Init show popup warning
                if (StatusCodeWarning.WARNING_CODE.statusCode == error?.status) {
                    onResult(
                        InitTransactionFlowViewEvent.DoHandleInitVerifyTransactionWarningEvent(
                            errorMessage = error?.errorMessage,
                            statusCode = StatusCodeWarning.WARNING_CODE.statusCode,
                        )
                    )
                } else if (StatusCodeWarning.contains(error?.errorCode)) {
                    onResult(
                        InitTransactionFlowViewEvent.DoHandleInitVerifyTransactionWarningEvent(
                            errorMessage = error?.errorMessage,
                            statusCode = error?.errorCode,
                        )
                    )
                } else {
                    onResult(
                        InitTransactionFlowViewEvent.DoInitVerifyTransactionFailEvent(
                            errorMessage = error?.errorMessage
                        )
                    )
                }
            }
        ) {
            callBlock()
        }
    }
}
