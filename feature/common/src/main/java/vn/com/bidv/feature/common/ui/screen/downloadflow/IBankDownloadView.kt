package vn.com.bidv.feature.common.ui.screen.downloadflow

import android.app.DownloadManager
import android.content.Context
import android.net.Uri
import android.net.http.SslError
import android.os.Environment
import android.webkit.MimeTypeMap
import android.webkit.SslErrorHandler
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.Toast
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import vn.com.bidv.localization.R
import vn.com.bidv.log.BLogUtil
import java.io.File

@Composable
internal fun IBankDownloadView(
    url: String?,
    fileName: String? = null,
    onDownloadError: (String) -> Unit,
    onStartDownload: () -> Unit
) {
    BLogUtil.d("IBankDownloadView URL: $url")
    val context = LocalContext.current
    val downloadingMessage = stringResource(id = R.string.bat_dau_tai_du_lieu)
    val errorMessage = stringResource(R.string.co_loi_xay_ra_vui_long_thu_lai)
    val subFolderName = "IBank2"
    val hasErrorOccurred = remember { mutableStateOf(false) }

    val webView = WebView(context).apply {

        webViewClient = createWebViewClient(
            onDownloadError = onDownloadError,
            errorMessage = errorMessage,
            hasErrorOccurred = hasErrorOccurred
        )

        setDownloadListener { downloadUrl, _, contentDisposition, _, _ ->
            try {
                val finalFileName = getFileName(
                    fileName = fileName,
                    contentDisposition = contentDisposition,
                    downloadUrl = downloadUrl
                )
                BLogUtil.d("IBankDownloadView file name: $finalFileName")
                val downloadsDir =
                    Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
                val subFolder = File(downloadsDir, subFolderName)
                if (!subFolder.exists()) {
                    subFolder.mkdirs()
                }

                val destinationFile = File(subFolder, finalFileName)
                val mimeType = getMimeTypeFromFileName(finalFileName)

                val request = DownloadManager.Request(Uri.parse(downloadUrl))
                    .setAllowedNetworkTypes(DownloadManager.Request.NETWORK_WIFI or DownloadManager.Request.NETWORK_MOBILE)
                    .setTitle(finalFileName).setDescription("Please wait")
                    .setAllowedOverMetered(true).setAllowedOverRoaming(true)
                    .setMimeType(mimeType)
                    .setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)
                    .setDestinationUri(Uri.fromFile(destinationFile))

                val downloadManager =
                    context.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
                downloadManager.enqueue(request)
                Toast.makeText(context, downloadingMessage, Toast.LENGTH_SHORT).show()
                onStartDownload()
            } catch (e: Exception) {
                if (!hasErrorOccurred.value) {
                    hasErrorOccurred.value = true
                    onDownloadError(errorMessage)
                }
                BLogUtil.e("IBankDownloadView download file error ${e.message}")
            }
        }
    }
    webView.loadUrl(url ?: "")
}

private fun createWebViewClient(
    onDownloadError: (String) -> Unit,
    errorMessage: String,
    hasErrorOccurred: MutableState<Boolean>
) = object : WebViewClient() {
    override fun onReceivedError(
        view: WebView?, request: WebResourceRequest?, error: WebResourceError?
    ) {
        handleError(onDownloadError, errorMessage, hasErrorOccurred)
        BLogUtil.e("IBankDownloadView onReceivedError: ${error?.description}")
    }

    override fun onReceivedHttpError(
        view: WebView?, request: WebResourceRequest?, response: WebResourceResponse?
    ) {
        handleError(onDownloadError, errorMessage, hasErrorOccurred)
        BLogUtil.e("IBankDownloadView onReceivedHttpError: ${response?.statusCode} ${response?.reasonPhrase}")
    }

    override fun onReceivedSslError(view: WebView?, handler: SslErrorHandler?, error: SslError?) {
        handleError(onDownloadError, errorMessage, hasErrorOccurred)
        BLogUtil.e("IBankDownloadView onReceivedHttpError: ${error?.primaryError}")
    }
}

private fun handleError(
    onDownloadError: (String) -> Unit,
    errorMessage: String,
    hasErrorOccurred: MutableState<Boolean>
) {
    if (!hasErrorOccurred.value) {
        hasErrorOccurred.value = true
        onDownloadError(errorMessage)
    }
}

private fun getFileName(
    fileName: String?,
    contentDisposition: String?,
    downloadUrl: String
): String {
    return fileName?.takeIf { it.isNotBlank() } ?: contentDisposition?.let {
        Regex("filename=\"?(.*?)(\"|\$)").find(it)?.groupValues?.get(1)
    } ?: Uri.parse(downloadUrl).lastPathSegment ?: "downloaded_file"
}

private fun getMimeTypeFromFileName(fileName: String): String? {
    return when (val ext = fileName.substringAfterLast('.', "").lowercase()) {
        "pdf" -> "application/pdf"
        else -> {
            val map = MimeTypeMap.getSingleton()
            map.getMimeTypeFromExtension(ext)
        }
    }
}