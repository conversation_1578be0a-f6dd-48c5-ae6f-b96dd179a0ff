package vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.model

import kotlinx.serialization.Serializable

@Serializable
data class ModelVerifyTransactionUI(
    val authTxnType: AuthTxnType? = null,
    val tranId: String = "",
    val totalTxn: String? = null,
    val totalAmount: String? = null,
    val totalAmountText: String? = null,
    val currCode: String? = null,
    val txnType: String? = null,
    val createdBy: String? = null,
    val timeActiveOtp: Int = 30,
    val timeEffectTxn: Long = 0,
    val authId: String = "",
    val verifyMethodType: VerifyMethodType? = null,
    val qrCode: String? = null,
    val userId: String = "",
    val isGenOtp: Boolean = true,
)

enum class AuthTxnType(val value: String) {
    FINANCIAL_TRANSACTION("FINANCIAL_TRANSACTION"),
    NON_FINANCIAL_TRANSACTION("NON_FINANCIAL_TRANSACTION"),
}

@Serializable
enum class VerifyMethodType(val value: String) {
    SAME_DEVICE("SAME_DEVICE"),
    OTHER_DEVICE("OTHER_DEVICE"),
    SCAN_QR("SCAN_QR");

    companion object {
        fun getVerifyMethodType(value: String): VerifyMethodType {
            return entries.firstOrNull { it.value == value } ?: SAME_DEVICE
        }
    }
}
