package vn.com.bidv.feature.common.domain.data

import com.google.gson.annotations.SerializedName
import kotlinx.serialization.Serializable

@Serializable
data class UserActiveSmartOtpDMO(
    @SerializedName("p")
    val p: String,
    @SerializedName("s")
    val s: String,
    @SerializedName("st")
    val st: String,
    @SerializedName("u")
    val u: String,
    @SerializedName("un")
    val un: String,
    @SerializedName("c")
    val c: String,
    @SerializedName("ts")
    val ts: String,
    @SerializedName("it")
    val it: Int = 0,
)

fun UserActiveSmartOtpDMO.toUserSmartOtpDMO(): UserSmartOtpDMO {
    return UserSmartOtpDMO(
        userId = this.u,
        cifName = this.c,
        userName = this.un,
        smToken = this.st,
        incorrectTime = this.it,
    )
}
