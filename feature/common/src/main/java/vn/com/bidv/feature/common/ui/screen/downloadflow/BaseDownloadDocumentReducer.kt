package vn.com.bidv.feature.common.ui.screen.downloadflow

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState

class BaseDownloadDocumentReducer<T> :
    Reducer<BaseDownloadDocumentReducer.BaseDownloadState<T>, BaseDownloadDocumentReducer.BaseDownloadEvent<T>, BaseDownloadDocumentReducer.BaseDownloadEffect<T>> {
    @Immutable
    data class BaseDownloadState<T>(
        val requestDto: T? = null,
        val documentUrl: String? = null,
        val errorMessage: String = "",
    ) : ViewState

    @Immutable
    sealed class BaseDownloadEvent<out T> : ViewEvent {
        data class GetDocumentUrl<T>(val requestDto: T?) : BaseDownloadEvent<T>()
        data class GetDocumentUrlSuccess(val documentUrl: String?) : BaseDownloadEvent<Nothing>()
        data class GetDocumentUrlFailed(val errorMessage: String) : BaseDownloadEvent<Nothing>()
    }

    @Immutable
    sealed class BaseDownloadEffect<out T> : SideEffect {
        data class FetDocumentUrl<T>(val requestDto: T?) : BaseDownloadEffect<T>()
    }

    override fun reduce(
        previousState: BaseDownloadState<T>,
        event: BaseDownloadEvent<T>
    ): Pair<BaseDownloadState<T>, BaseDownloadEffect<T>?> {
        return when (event) {
            is BaseDownloadEvent.GetDocumentUrl -> {
                previousState.copy(requestDto = event.requestDto) to BaseDownloadEffect.FetDocumentUrl(
                    requestDto = event.requestDto
                )
            }

            is BaseDownloadEvent.GetDocumentUrlSuccess -> {
                previousState.copy(documentUrl = event.documentUrl) to null
            }

            is BaseDownloadEvent.GetDocumentUrlFailed -> {
                previousState.copy(errorMessage = event.errorMessage) to null
            }
        }
    }
}