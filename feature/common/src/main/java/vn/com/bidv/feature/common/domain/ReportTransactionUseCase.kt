package vn.com.bidv.feature.common.domain

import vn.com.bidv.feature.common.data.FoReportRepository
import vn.com.bidv.feature.common.data.foreport.model.FundTransferBaseCriteriaRequestDto
import vn.com.bidv.feature.common.domain.data.DataListTxnFundTransferDMO
import vn.com.bidv.feature.common.domain.data.FundTransferBaseCriteriaRequestDMO
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import vn.com.bidv.sdkbase.utils.toSafeBigDecimal
import javax.inject.Inject

class ReportTransactionUseCase @Inject constructor(
    private val foReportRepository: FoReportRepository
) {

    suspend fun exportReport(fundTransferMakerCriteriaRequestDMO: FundTransferBaseCriteriaRequestDMO): DomainResult<String>  {
        val result = foReportRepository.exportReport(
            FundTransferBaseCriteriaRequestDto(
                startDate = fundTransferMakerCriteriaRequestDMO.startDate,
                endDate = fundTransferMakerCriteriaRequestDMO.endDate,
                startEffDate = fundTransferMakerCriteriaRequestDMO.startEffDate,
                endEffDate = fundTransferMakerCriteriaRequestDMO.endEffDate,
                benBankCode = fundTransferMakerCriteriaRequestDMO.benBankCode,
                txnType = fundTransferMakerCriteriaRequestDMO.txnType,
                amountMax = fundTransferMakerCriteriaRequestDMO.amountMax,
                amountMin = fundTransferMakerCriteriaRequestDMO.amountMin,
                status = fundTransferMakerCriteriaRequestDMO.status,
                channel = fundTransferMakerCriteriaRequestDMO.channel,
                keyword = fundTransferMakerCriteriaRequestDMO.keyword,
                page = fundTransferMakerCriteriaRequestDMO.page,
            )
        )
        return result.convert {
            url ?: ""
        }
    }

}