package vn.com.bidv.feature.common.ui.screen.supportopenbrowser

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.designsystem.component.feedback.snackbar.IBankSnackBarInfo
import vn.com.bidv.feature.common.domain.SupportOpenBrowserUseCase
import vn.com.bidv.feature.common.ui.screen.supportopenbrowser.SupportOpenBrowserReducer.SupportOpenBrowserViewEffect
import vn.com.bidv.feature.common.ui.screen.supportopenbrowser.SupportOpenBrowserReducer.SupportOpenBrowserViewEvent
import vn.com.bidv.feature.common.ui.screen.supportopenbrowser.SupportOpenBrowserReducer.SupportOpenBrowserViewState
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class SupportOpenBrowserViewModel @Inject constructor(
    private val supportOpenBrowserUseCase: SupportOpenBrowserUseCase,
) : ViewModelIBankBase<SupportOpenBrowserViewState, SupportOpenBrowserViewEvent, SupportOpenBrowserViewEffect>(
    initialState = SupportOpenBrowserViewState(),
    reducer = SupportOpenBrowserReducer()
) {
    override fun handleEffect(
        sideEffect: SupportOpenBrowserViewEffect,
        onResult: (SupportOpenBrowserViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is SupportOpenBrowserViewEffect.InitData -> {
                onResult(
                    SupportOpenBrowserViewEvent.InitDataSuccess(
                        url = supportOpenBrowserUseCase.getSupportUrl(sideEffect.urlType) ?: ""
                    )
                )
            }

            SupportOpenBrowserViewEffect.ShowToast -> {
                showSnackBar(
                   IBankSnackBarInfo(
                       message = resourceProvider.getString(
                           R.string.khong_tim_thay_ung_dung_trinh_duyet_nao_de_mo_url
                       ),
                       primaryButtonText = resourceProvider.getString(R.string.dong)
                   )
                )
            }
        }
    }
}
