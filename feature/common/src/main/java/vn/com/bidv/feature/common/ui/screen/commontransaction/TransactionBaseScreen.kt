package vn.com.bidv.feature.common.ui.screen.commontransaction

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.datadisplay.badge.IBankBadgeLabel
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelColor
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelSize
import vn.com.bidv.designsystem.component.dataentry.IBankInputDropdownBase
import vn.com.bidv.designsystem.component.dataentry.RemoveVietnameseAccentFilter
import vn.com.bidv.designsystem.component.feedback.bottomsheet.IBankSearchDialog
import vn.com.bidv.designsystem.component.feedback.bottomsheet.SearchDialogState
import vn.com.bidv.designsystem.component.feedback.bottomsheet.SearchItem
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.common.domain.data.MenuDMO
import vn.com.bidv.feature.common.domain.data.TransactionRouteID
import vn.com.bidv.sdkbase.utils.VNCharacterUtil
import vn.com.bidv.localization.R as RLocal

@Composable
fun TransactionBaseScreen(
    navController: NavHostController,
    viewModel: TransactionBaseViewModel,
    actionbarTitle: String,
    routeId: TransactionRouteID?
) {
    BaseScreen(
        viewModel = viewModel,
        renderContent = { uiState, onEvent ->
            LaunchedEffect(Unit) {
                viewModel.sendEvent(
                    TransactionBaseReducer.TransactionBaseEvent.GetData(
                        routeId = uiState.currentRoute ?: routeId
                    )
                )
            }
            TransactionMenuContent(
                viewModel = viewModel,
                navHostController = navController,
                uiState = uiState,
                onEvent = onEvent,
                routeId = routeId
            )
        },
        topAppBarConfig = TopAppBarConfig(titleTopAppBar = actionbarTitle),
        handleSideEffect = {},
        navController = navController
    )
}

@Composable
fun TransactionMenuContent(
    viewModel: TransactionBaseViewModel,
    navHostController: NavHostController,
    uiState: TransactionBaseReducer.TransactionBaseState,
    onEvent: (TransactionBaseReducer.TransactionBaseEvent) -> Unit,
    routeId: TransactionRouteID?
) {
    val isApprovalMode = viewModel is TransactionApprovalViewModel

    val dropdownList = uiState.menuData?.map {
        it.route.setTitle(stringResource(it.route.titleRes))
        it
    }

    val lazyListStates = remember(dropdownList) {
        dropdownList?.associate { index -> index.route to LazyListState() }
    }

    val selectedItem = uiState.currentRoute?.let { currentRouteId ->
        dropdownList?.find { menu -> menu.route == currentRouteId }
    }

    val selectedItemState = lazyListStates?.get(selectedItem?.route) ?: rememberLazyListState()

    var expanded by remember { mutableStateOf(false) }
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        Box(
            modifier = Modifier.padding(
                vertical = IBSpacing.spacingXs, horizontal = IBSpacing.spacingM
            )
        ) {
            IBankInputDropdownBase(
                modifier = Modifier.padding(IBSpacing.spacing2xs),
                onClickEnd = {
                    expanded = true
                    viewModel.sendEvent(
                        TransactionBaseReducer.TransactionBaseEvent.GetData(
                            routeId = uiState.currentRoute ?: routeId
                        )
                    )
                },
                labelText = stringResource(id = RLocal.string.loai_giao_dich),
                listItems = listOf(selectedItem?.route?.titleRes?.let { stringResource(it) } ?: ""),
                iconEnd = R.drawable.arrow_bottom_outline,
            )
        }
        viewModel.itemBuilders.find {
            it.routeID == (selectedItem?.route)
        }?.content?.invoke(navHostController, selectedItemState)
    }
    if (expanded) {
        IBankSearchDialog(
            title = stringResource(RLocal.string.loai_giao_dich),
            searchPlaceholder = stringResource(RLocal.string.tim_kiem),
            listData = dropdownList,
            itemSelected = selectedItem,
            showSearchBox = (dropdownList?.count { !it.isHeader } ?: 0) > 10 || isApprovalMode,
            state = if (uiState.reloadMenu) SearchDialogState.LOADING else SearchDialogState.CONTENT,
            onRequestDismiss = {
                expanded = false
                if (it != null && !it.isHeader) {
                    onEvent(
                        TransactionBaseReducer.TransactionBaseEvent.SelectedItemIndex(it)
                    )
                }
            },
            listSearchFilterText = listOf(RemoveVietnameseAccentFilter()),
            searchFilter = { item, query ->
                if (isApprovalMode || !item.isHeader) {
                    VNCharacterUtil.removeAccent(item.route.title).contains(
                        VNCharacterUtil.removeAccent(query), ignoreCase = true
                    )
                } else false
            },
            compareKey = { it },
            itemContent = { _, item ->
                MenuBottomSheetItem(item)
            },
            isClickable = { !it.isHeader })
    }
}

@Composable
fun MenuBottomSheetItem(item: SearchItem<MenuDMO>) {
    val colorScheme = LocalColorScheme.current
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .background(
                color = if (item.data.isHeader) colorScheme.bgMainPrimary else if (item.isSelected) colorScheme.bgBrand_01Tertiary else colorScheme.bgMainTertiary,
                shape = if (item.data.isHeader) RectangleShape else RoundedCornerShape(
                    IBCornerRadius.cornerRadiusM
                )
            )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(
                    bottom = IBSpacing.spacingS,
                    top = IBSpacing.spacingS,
                    end = IBSpacing.spacingS,
                    start = IBSpacing.spacingXs
                ), verticalAlignment = Alignment.CenterVertically
        ) {
            Box(Modifier.weight(1f)) {
                MenuBottomSheetItemBody(item)
            }
            if (item.isSelected) {
                Image(
                    painter = painterResource(id = R.drawable.check),
                    contentDescription = null,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
    }
}

@Composable
fun MenuBottomSheetItemBody(item: SearchItem<MenuDMO>) {
    val colorScheme = LocalColorScheme.current
    Row(
        modifier = Modifier
            .padding(
                horizontal = if (item.data.isHeader) IBSpacing.spacingXs else 0.dp,
                vertical = IBSpacing.spacing2xs
            )
    ) {
        if (item.data.isHeader) {
            Text(
                text = item.data.route.title,
                style = LocalTypography.current.titleTitle_s,
                color = LocalColorScheme.current.contentMainPrimary,
            )
        } else {
            Text(
                text = item.data.route.title,
                style = LocalTypography.current.bodyBody_m,
                color = if (item.isSelected) colorScheme.contentMainPrimary else colorScheme.contentMainSecondary
            )
            if (item.data.count > 0) {
                Spacer(Modifier.width(IBSpacing.spacingXs))
                IBankBadgeLabel(
                    title = item.data.count.toString(),
                    badgeSize = LabelSize.SM,
                    badgeColor = LabelColor.LIGHT_GRAY
                )
            }
        }
    }
}
