package vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.verifypin

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import kotlinx.coroutines.delay
import vn.com.bidv.common.utils.unpack
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.component.feedback.otpview.IBankOtpItemInputList
import vn.com.bidv.designsystem.component.keyboard.NumpadKeyboard
import vn.com.bidv.designsystem.component.keyboard.NumpadType
import vn.com.bidv.designsystem.component.navigation.button.IBankLinkButton
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.common.constants.Constants
import vn.com.bidv.feature.common.constants.SmartOTPError
import vn.com.bidv.feature.common.navigation.NavigationHelper
import vn.com.bidv.feature.common.navigation.VerifyTransactionRoute
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.viewModelFlow.VerifyByTypeTransactionFlowViewModel
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.viewModelFlow.sharedVerifyTransactionViewModel
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.model.ModelVerifyTransactionUI
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.model.VerifyMethodType
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.verifypin.VerifyPinReducer.VerifyPinViewEvent
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.verifypin.VerifyPinReducer.VerifyPinViewState
import vn.com.bidv.sdkbase.ui.HandleBackAction
import vn.com.bidv.designsystem.R as RDesignsystem
import vn.com.bidv.localization.R as RLocalization

@Composable
fun VerifyPinScreen(
    navController: NavHostController,
    modelVerifyTransactionUI: ModelVerifyTransactionUI,
) {
    val vm: VerifyPinViewModel = hiltViewModel()
    val (_uiState, onEvent, _) = vm.unpack()
    var isShowSmartOTPBlocked by remember { mutableStateOf(false) }
    var isShowSmartOTPNotExisted by remember { mutableStateOf(false) }
    val viewModelFlow = navController.sharedVerifyTransactionViewModel()
    HandleBackAction {
        NavigationHelper.closeVerifyByTypeTransaction(navController, viewModelFlow)
    }
    BaseScreen(
        navController = navController,
        viewModel = vm,
        renderContent = { uiState, onEvent ->

            LaunchedEffect(true) {
                if (!uiState.isInitSuccess) {
                    onEvent(VerifyPinViewEvent.OnInitTransaction(modelVerifyTransactionUI))
                }
            }

            VerifyPinContent(uiState, onEvent, navController, viewModelFlow)

            if (isShowSmartOTPBlocked) {
                val messageError = getOtpBlockedMessage(uiState)
                IBankModalConfirm(
                    modalConfirmType = ModalConfirmType.Error,
                    title = stringResource(RLocalization.string.thong_bao),
                    supportingText = "",
                    annotatedSupportingText = messageError,
                    listDialogButtonInfo = dialogButtonInfoList(uiState, onEvent, navController, viewModelFlow),
                    onDismissRequest = {
                        isShowSmartOTPBlocked = false
                        NavigationHelper.closeVerifyByTypeTransaction(navController, viewModelFlow)
                    }
                )
            }

            if (isShowSmartOTPNotExisted) {
                IBankModalConfirm(
                    modalConfirmType = ModalConfirmType.Error,
                    title = stringResource(RLocalization.string.loi),
                    supportingText = stringResource(RLocalization.string.khong_ton_tai_cai_dat_smart_otp_cua_nguoi_dung_tren_thiet_bi_vui_long_kiem_tra_lai),
                    listDialogButtonInfo = dialogButtonInfoList(uiState, onEvent, navController, viewModelFlow),
                    onDismissRequest = {
                        isShowSmartOTPNotExisted = false
                        NavigationHelper.closeVerifyByTypeTransaction(navController, viewModelFlow)
                    }
                )
            }

        },
        handleSideEffect = { verifyPinSideEffect ->
            when (verifyPinSideEffect) {
                is VerifyPinReducer.VerifyPinViewEffect.VerifyPinFail -> {
                    isShowSmartOTPBlocked = true
                }

                is VerifyPinReducer.VerifyPinViewEffect.VerifyPinSuccess -> {
                    if (modelVerifyTransactionUI.isGenOtp) {
                        //change flow
                        navController.navigate(
                            VerifyTransactionRoute.VerifyByTypeOTPScreenRoute(
                                modelVerifyTransactionUI,
                                verifyPinSideEffect.userActiveSmartOtpDMO?.s ?: "",
                                verifyPinSideEffect.userActiveSmartOtpDMO?.st ?: "",
                            )
                        )
                    } else {
                        NavigationHelper.closeVerifyByTypeTransaction(navController, viewModelFlow)
                    }
                }

                is VerifyPinReducer.VerifyPinViewEffect.SmartOtpNotExists -> {
                    isShowSmartOTPNotExisted = true
                }

                else -> {
                    // nothing
                }
            }
        },
        topAppBarType = TopAppBarType.Title,
        topAppBarConfig = TopAppBarConfig(
            titleTopAppBar = stringResource(RLocalization.string.nhap_pin_smart_otp),
            onNavigationClick = { NavigationHelper.closeVerifyByTypeTransaction(navController, viewModelFlow) },
            onHomeClick = if (!_uiState.isLoginSuccess) {
                {
                    vn.com.bidv.sdkbase.navigation.NavigationHelper.navigationToLogin(navController)
                }
            } else null
        )
    )
}

@Composable
private fun VerifyPinContent(
    uiState: VerifyPinViewState,
    onEvent: (VerifyPinViewEvent) -> Unit,
    navController: NavHostController,
    viewModelFlow: VerifyByTypeTransactionFlowViewModel
) {

    if (!uiState.isInitSuccess) return

    var isShowPIN by remember { mutableStateOf(false) }
    var isShowForgotPinPopup by remember { mutableStateOf(false) }
    var isShowTimeExpiredPopup by remember { mutableStateOf(false) }

    var timeActiveTxn by remember { mutableLongStateOf((uiState.modelTransaction.timeEffectTxn - System.currentTimeMillis()) / 1000L) }
    LaunchedEffect(true) {
        while (timeActiveTxn > 0) {
            delay(1000)
            timeActiveTxn -= 1
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    start = IBSpacing.spacingM,
                    end = IBSpacing.spacingM,
                    top = IBSpacing.spacingM
                )
        ) {
            Row(modifier = Modifier.fillMaxWidth()) {
                Text(
                    modifier = Modifier
                        .weight(1f)
                        .align(Alignment.CenterVertically),
                    text = stringResource(RLocalization.string.nhap_ma_pin),
                    style = LocalTypography.current.titleTitle_s,
                    color = LocalColorScheme.current.contentMainPrimary
                )
                Icon(
                    painter = painterResource(if (isShowPIN) R.drawable.eyes_closed_outline else RDesignsystem.drawable.eyes_open_outline),
                    contentDescription = "Show password",
                    modifier = Modifier
                        .size(IBSpacing.spacing2xl)
                        .clickable(
                            onClick = {
                                isShowPIN = !isShowPIN
                            },
                            indication = null,
                            interactionSource = remember { MutableInteractionSource() }
                        ),
                    tint = Color.Unspecified
                )
            }

            IBankOtpItemInputList(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = IBSpacing.spacingM),
                otpText = if (isShowPIN) uiState.inputPin else "*".repeat(uiState.inputPin.length),
                otpItemNumber = 6,
                isFocus = true,
                onOtpInputDone = {
                    if (timeActiveTxn > 0 || uiState.modelTransaction.verifyMethodType in listOf(
                            VerifyMethodType.SCAN_QR,
                            VerifyMethodType.OTHER_DEVICE
                        )
                    ) {
                        onEvent(VerifyPinViewEvent.OnVerifyPin)
                    } else {
                        if (uiState.modelTransaction.verifyMethodType == VerifyMethodType.SAME_DEVICE) {
                            isShowTimeExpiredPopup = true
                        }
                    }
                }
            )

            if (uiState.validError != null) {
                val subText = if (uiState.validError == SmartOTPError.INPUT_PIN_INCORRECT) {
                    stringResource(
                        uiState.validError.res,
                        Constants.MAX_NUMBER_RETRY_PIN_INPUT - uiState.numberRetryEnterPin
                    )
                } else {
                    ""
                }
                Text(
                    modifier = Modifier
                        .padding(top = IBSpacing.spacingXs)
                        .align(Alignment.CenterHorizontally),
                    text = subText,
                    style = LocalTypography.current.captionCaption_m,
                    color = LocalColorScheme.current.bgNegativePrimary
                )
            }

            IBankLinkButton(
                modifier = Modifier
                    .wrapContentSize()
                    .align(Alignment.CenterHorizontally),
                text = stringResource(RLocalization.string.quen_ma_pin)
            ) {
                isShowForgotPinPopup = true
            }
        }
        NumpadKeyboard(
            modifier = Modifier.align(Alignment.BottomCenter),
            type = NumpadType.SHUFFLED
        ) { key ->
            onEvent(VerifyPinViewEvent.OnPinChanged(key))
        }
    }
    if (isShowForgotPinPopup) {

        val messageWarning = getForgotPinMessage(uiState)

        IBankModalConfirm(
            modalConfirmType = ModalConfirmType.Info,
            title = stringResource(RLocalization.string.quen_ma_pin),
            supportingText = "",
            annotatedSupportingText = messageWarning,
            listDialogButtonInfo = dialogButtonInfoList(uiState, onEvent, navController, viewModelFlow),
            onDismissRequest = {
                isShowForgotPinPopup = false
//                onEvent(VerifyPinViewEvent.OnSharePopVerifyTransaction)
            }
        )
    }

    if (isShowTimeExpiredPopup) {
        IBankModalConfirm(
            title = stringResource(RLocalization.string.giao_dich_het_hieu_luc),
            supportingText = stringResource(RLocalization.string.giao_dich_het_hieu_luc_vui_long_thuc_hien_lai),
            modalConfirmType = ModalConfirmType.Error,
            listDialogButtonInfo = listOf(
                DialogButtonInfo(
                    label = stringResource(RLocalization.string.quay_lai),
                    onClick = {
                        NavigationHelper.closeVerifyByTypeTransaction(navController, viewModelFlow)
                    }
                )
            ),
            onDismissRequest = {
                isShowTimeExpiredPopup = false
            }
        )
    }
}

@Composable
private fun dialogButtonInfoList(
    uiState: VerifyPinViewState,
    onEvent: (VerifyPinViewEvent) -> Unit,
    navController: NavHostController,
    viewModelFlow: VerifyByTypeTransactionFlowViewModel
) = listOf(
    if (uiState.isLoginSuccess && !uiState.isUserRoleAdmin) {
        DialogButtonInfo(
            label = stringResource(RLocalization.string.kich_hoat_lai_smart_otp),
            onClick = {
                NavigationHelper.closeVerifyByTypeTransaction(navController,viewModelFlow)
                NavigationHelper.navigateToReActiveSmartOtpScreen(navController)
            }
        )
    } else {
        DialogButtonInfo(
            label = stringResource(RLocalization.string.close),
            onClick = {}
        )
    }
)

@Composable
fun getForgotPinMessage(uiState: VerifyPinViewState): AnnotatedString {
    return if (uiState.isLoginSuccess) {
        if (!uiState.isUserRoleAdmin) {
            buildAnnotatedString {
                append(stringResource(RLocalization.string.quy_khach_vui_long_kich_hoat_lai_smart_otp_va_cai_dat_ma_pin_moi_tren_thiet_bi))
            }
        } else {
            buildAnnotatedString {
                append(stringResource(RLocalization.string.quy_khach_vui_long_den_chi_nhanh_bidv_de_duoc_ho_tro))
            }
        }
    } else {
        buildAnnotatedString {
            withStyle(style = LocalTypography.current.bodyBody_m.toSpanStyle()) {
                append(stringResource(RLocalization.string.quy_khach_vui_long_thuc_hien_nhu_sau_dang_nhap_app_chon_cai_dat_cai_dat_thong_tin_bao_mat_kich_hoat_lai_smart_otp))
            }
            append("\n")
            withStyle(
                style = LocalTypography.current.bodyBody_m.copy(
                    fontWeight = FontWeight.Bold,
                    fontStyle = FontStyle.Italic
                ).toSpanStyle()
            ) {
                append(stringResource(RLocalization.string.luu_y).plus(" "))
            }
            withStyle(style = LocalTypography.current.bodyBody_m.toSpanStyle()) {
                append(stringResource(RLocalization.string.neu_la_nguoi_dung_quan_tri_vien_vui_long_den_chi_nhanh_bidv_de_duoc_ho_tro))
            }
        }
    }
}

@Composable
private fun getOtpBlockedMessage(uiState: VerifyPinViewState): AnnotatedString {
    return if (uiState.isLoginSuccess) {
        buildAnnotatedString {
            if (uiState.isUserRoleAdmin) {
                append(stringResource(RLocalization.string.smart_otp_da_bi_khoa_do_sai_pin_qua_so_lan_cho_phep_quy_khach_vui_long_den_chi_nhanh_bidv_de_duoc_ho_tro))
            } else {
                append(stringResource(RLocalization.string.smart_otp_da_bi_khoa_do_nhap_sai_pin_qua_so_lan_cho_phep_quy_khach_vui_long_den_chi_nhanh_bidv_de_duoc_ho_tro_hoac_yeu_cau_kich_hoat_lai_smart_otp))
            }
        }
    } else {
        buildAnnotatedString {
            withStyle(style = LocalTypography.current.bodyBody_m.toSpanStyle()) {
                append(stringResource(RLocalization.string.smart_otp_da_bi_khoa_do_sai_pin_qua_so_lan_quy_dinh_vui_long_den_chi_nhanh_bidv_hoac_su_dung_tinh_nang_kich_hoat_lai_smart_otp_tren_app))
            }
            append("\n")
            withStyle(
                style = LocalTypography.current.bodyBody_m.copy(
                    fontWeight = FontWeight.Bold,
                    fontStyle = FontStyle.Italic
                ).toSpanStyle()
            ) {
                append(stringResource(RLocalization.string.luu_y).plus(" "))
            }
            withStyle(style = LocalTypography.current.bodyBody_m.toSpanStyle()) {
                append(stringResource(RLocalization.string.neu_la_nguoi_dung_quan_tri_vien_vui_long_den_chi_nhanh_bidv_de_duoc_ho_tro))
            }
        }
    }
}
