package vn.com.bidv.feature.common.constants

import androidx.annotation.StringRes
import vn.com.bidv.localization.R as RLocalization

enum class SmartOTPError(@StringRes val res: Int) {
    INPUT_PIN_INCORRECT(RLocalization.string.ma_pin_khong_chinh_xac_quy_khach_con_s_lan_nhap_lai),
    SMART_OTP_LOCKED(RLocalization.string.smart_otp_tam_ngung_hoat_dong_do_nhap_sai_ma_pin_qua_so_lan_cho_phep_quy_khach_vui_long_toi_chi_nhanh_bidv_de_duoc_ho_tro_hoac_yeu_cau_kich_hoat_lai_smart_otp),
    SCAN_QR_ERROR(RLocalization.string.co_loi_xay_ra_trong_qua_trinh_xu_ly_vui_long_thu_lai),
    SMART_OTP_02(RLocalization.string.co_loi_xay_ra_trong_qua_trinh_xu_ly_vui_long_thu_lai),
    SMART_OTP_09(RLocalization.string.co_loi_xay_ra_trong_qua_trinh_xu_ly_vui_long_thu_lai);


    companion object {
        fun fromString(string: String?): SmartOTPError {
            return entries.find { it.name == string } ?: INPUT_PIN_INCORRECT
        }
    }
}