package vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.viewModelFlow

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InitVerifyTransactionResponse
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyCreateTransaction
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyTransaction
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionTypeConstant

class VerifyByTypeTransactionFlowReducer :
    Reducer<VerifyByTypeTransactionFlowReducer.VerifyByTypeTransactionFlowViewState, VerifyByTypeTransactionFlowReducer.VerifyByTypeTransactionFlowViewEvent, VerifyByTypeTransactionFlowReducer.VerifyByTypeTransactionFlowEffect> {

    @Immutable
    data class VerifyByTypeTransactionFlowViewState(
        val dataOutputInit: InitVerifyTransactionResponse? = null,
        val txnIds: InputVerifyTransaction = InputVerifyTransaction(),
        val dataCreated: InputVerifyCreateTransaction = InputVerifyCreateTransaction(),
        val type: String = VerifyTransactionTypeConstant.DEFAULT.code,
        val additionalData: String? = null
    ) : ViewState

    @Immutable
    sealed class VerifyByTypeTransactionFlowViewEvent : ViewEvent {
        data class SaveDataInit(val txnIds: InputVerifyTransaction, val dataCreated: InputVerifyCreateTransaction, val type: String,val additionalData: String? ): VerifyByTypeTransactionFlowViewEvent()
        data class SaveTransKey(val dataOutputInit: InitVerifyTransactionResponse?): VerifyByTypeTransactionFlowViewEvent()

    }

    @Immutable
    sealed class VerifyByTypeTransactionFlowEffect : SideEffect {

    }

    override fun reduce(
        previousState: VerifyByTypeTransactionFlowViewState,
        event: VerifyByTypeTransactionFlowViewEvent,
    ): Pair<VerifyByTypeTransactionFlowViewState, VerifyByTypeTransactionFlowEffect?> {
        return when (event) {
            is VerifyByTypeTransactionFlowViewEvent.SaveDataInit -> {
                previousState.copy(
                    txnIds = event.txnIds,
                    dataCreated = event.dataCreated,
                    type = event.type,
                    additionalData = event.additionalData
                ) to null
            }

            is VerifyByTypeTransactionFlowViewEvent.SaveTransKey -> {
                previousState.copy(
                    dataOutputInit = event.dataOutputInit
                ) to null
            }
        }
    }
}
