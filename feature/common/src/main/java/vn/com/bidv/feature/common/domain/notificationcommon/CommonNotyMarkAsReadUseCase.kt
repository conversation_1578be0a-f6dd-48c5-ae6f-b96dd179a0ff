package vn.com.bidv.feature.common.domain.notificationcommon

import vn.com.bidv.feature.common.data.notify.NotifyCommonRepository
import vn.com.bidv.feature.common.data.utilitiesnotify.model.ResultString
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class CommonNotyMarkAsReadUseCase @Inject constructor(
    private val repository: NotifyCommonRepository
) {

    fun getListNotyId(tabNotiType: TabNotiType, notifyList: List<NotifyStatementDMO>): List<Long> {
        return notifyList
            .filter { tabNotiType != TabNotiType.SYSTEM_NOTIFICATION || !it.isItemTransInSystemTab() }
            .map { it.id }.mapNotNull { it }
    }

    fun getListNotyTransId(tabNotiType: TabNotiType, notifyList: List<NotifyStatementDMO>): List<Long> {
        return notifyList
            .filter { tabNotiType == TabNotiType.SYSTEM_NOTIFICATION && it.isItemTransInSystemTab() }
            .map { it.id }.mapNotNull { it }
    }

    suspend fun markRead(
        tabNotiType: TabNotiType,
        notifyList: List<NotifyStatementDMO>
    ): DomainResult<Unit> {
        val notifyIdList = getListNotyId(tabNotiType, notifyList)
        val transNotifyIdList = getListNotyTransId(tabNotiType, notifyList)
        val networkResult: NetworkResult<ResultString> =
            repository.markAsRead(
                type = tabNotiType.value,
                notifyIdList = notifyIdList,
                isMarkAll = false,
                listTransId = transNotifyIdList
            )
        return networkResult.convert { }
    }

    suspend fun markNotyItemRead(
        notifyId: Long?,
        tabNotiType: TabNotiType,
        displayTab: String?
    ): DomainResult<Unit> {
        return markRead(
            tabNotiType,
            listOf(
                NotifyStatementDMO(
                    id = notifyId,
                    tabNotiType = tabNotiType,
                    displayTab = displayTab
                )
            )
        )
    }


}

