package vn.com.bidv.feature.common.ui.screen.downloadflow

import android.Manifest
import android.os.Build
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.navigation.NavController
import vn.com.bidv.common.extenstion.isNotNullOrEmpty
import vn.com.bidv.common.ui.BaseMVIScreen
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.PermissionUtils

@Composable
fun <T> BaseDownloadDocumentScreen(
    navController: NavController,
    requestDto: T?,
    fileName: String? = null,
    viewModel: BaseDownloadDocumentViewModel<T>
) {
    var permissionGranted by remember { mutableStateOf(false) }
    if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.P) {
        PermissionUtils.checkPermissionAndDoSomeThing(navController.context,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            onPermissionGranted = {
                permissionGranted = true
            },
            onPermissionDenied = {
                permissionGranted = false
                navController.popBackStack()
            })
    } else {
        permissionGranted = true
    }
    LaunchedEffect(permissionGranted) {
        if (permissionGranted) {
            viewModel.sendEvent(
                BaseDownloadDocumentReducer.BaseDownloadEvent.GetDocumentUrl(requestDto = requestDto)
            )
        }
    }
    BaseMVIScreen(
        viewModel = viewModel,
        renderContent = { uiState, onEvent ->

            if (uiState.errorMessage.isNotNullOrEmpty()) {
                IBankModalConfirm(
                    modalConfirmType = ModalConfirmType.Error,
                    title = navController.context.getString(R.string.loi),
                    supportingText = uiState.errorMessage,
                    listDialogButtonInfo =
                        listOf(
                            DialogButtonInfo(
                                label = navController.context.getString(R.string.dong),
                            ),
                        ),
                    onDismissRequest = {
                        navController.popBackStack()
                    },
                )
            }

            IBankDownloadView(
                url = uiState.documentUrl,
                fileName = fileName,
                onDownloadError = { errorMess ->
                    onEvent(
                        BaseDownloadDocumentReducer.BaseDownloadEvent.GetDocumentUrlFailed(
                            errorMessage = errorMess
                        )
                    )
                },
                onStartDownload = {
                    navController.popBackStack()
                }
            )
        }, handleSideEffect = {
        })
}