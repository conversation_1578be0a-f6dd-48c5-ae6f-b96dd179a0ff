package vn.com.bidv.feature.common.ui.screen.commontransaction

import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import vn.com.bidv.feature.common.domain.data.TransactionRouteID

interface TransactionBaseBuilder {
    val routeID: TransactionRouteID
    val content: @Composable (
        navController: NavHostController,
        listReportState: LazyListState
    ) -> Unit
}

class TransactionReportBuilderDefault(
    override val routeID: TransactionRouteID = TransactionRouteID.CnrReport,
    override val content: @Composable ((
        navController: NavHostController,
        listReportState: LazyListState
    ) -> Unit
    ) = { _, _ -> }
) : TransactionBaseBuilder
