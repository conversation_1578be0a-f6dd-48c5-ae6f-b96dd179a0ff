package vn.com.bidv.feature.common.di

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import dagger.multibindings.IntoSet
import retrofit2.Retrofit
import vn.com.bidv.feature.common.data.CommonRepository
import vn.com.bidv.feature.common.data.FoReportRepository
import vn.com.bidv.feature.common.data.MasterDataRepository
import vn.com.bidv.feature.common.data.UtilitiesRepository
import vn.com.bidv.feature.common.data.foreport.apis.FoReportApi
import vn.com.bidv.feature.common.data.masterdata.apis.MasterDataApi
import vn.com.bidv.feature.common.data.utilities.apis.UtilitiesApi
import vn.com.bidv.feature.common.data.utilitiesnotify.apis.UtilitiesNotifyStatementsApi
import vn.com.bidv.feature.common.domain.smartotp.DefaultSmartOTPUseCase
import vn.com.bidv.feature.common.domain.smartotp.ISmartOTPUseCase
import vn.com.bidv.feature.common.domain.smartotp.SmartOTPUseCase
import vn.com.bidv.feature.common.domain.verifyFlowUseCase.ScanQrVerifyUseCase
import vn.com.bidv.feature.common.navigation.ActionHistoryNavigation
import vn.com.bidv.feature.common.navigation.CommonNavigation
import vn.com.bidv.feature.common.navigation.ScanQrVerifyFlowScreenBuilder
import vn.com.bidv.feature.common.navigation.TemplateTransactionNavigation
import vn.com.bidv.feature.common.navigation.TransactionApprovalNavigation
import vn.com.bidv.feature.common.navigation.TransactionReportNavigation
import vn.com.bidv.feature.common.ui.screen.commontransaction.TransactionBaseBuilder
import vn.com.bidv.feature.common.ui.screen.commontransaction.TransactionReportBuilderDefault
import vn.com.bidv.feature.common.ui.screen.templatetransaction.TemplateTransactionTabBuilder
import vn.com.bidv.feature.common.ui.screen.templatetransaction.TemplateTransactionTabBuilderDefault
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionFlowScreenBuilder
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionFlowScreenBuilderDefault
import vn.com.bidv.network.NetworkConfig
import vn.com.bidv.sdkbase.navigation.FeatureGraphBuilder
import vn.com.bidv.secure.secure.smartotp.ISmartOTPSecure
import java.util.Optional
import javax.inject.Singleton
import kotlin.jvm.optionals.getOrNull

@Module
@InstallIn(SingletonComponent::class)
class CommonModule {
    @Provides
    @Singleton
    fun provideCommonRepository(): CommonRepository {
        return CommonRepository()
    }

    @Provides
    @Singleton
    fun provideMasterDataRepository(service: MasterDataApi): MasterDataRepository {
        return MasterDataRepository(service)
    }

    @Provides
    @Singleton
    fun provideMasterDataApi(retrofit: Retrofit): MasterDataApi {
        return retrofit.create(MasterDataApi::class.java)
    }

    @Provides
    @Singleton
    fun provideAccountServiceApi(retrofit: Retrofit): FoReportApi {
        return retrofit.create(FoReportApi::class.java)
    }

    @Provides
    @Singleton
    fun provideFoReportRepository(serviceApi: FoReportApi): FoReportRepository {
        return FoReportRepository(serviceApi)
    }

    @Singleton
    @Provides
    @IntoSet
    fun provideCommonFeatureGraphBuilder(): FeatureGraphBuilder {
        return CommonNavigation()
    }

    @Singleton
    @Provides
    @IntoSet
    fun provideActionHistoryFeatureGraphBuilder(): FeatureGraphBuilder {
        return ActionHistoryNavigation()
    }

    @Singleton
    @Provides
    @IntoSet
    fun provideTransactionReportFeatureGraphBuilder(): FeatureGraphBuilder {
        return TransactionReportNavigation()
    }

    @Singleton
    @Provides
    @IntoSet
    fun provideTransactionApprovalFeatureGraphBuilder(): FeatureGraphBuilder {
        return TransactionApprovalNavigation()
    }

    @Singleton
    @Provides
    @IntoSet
    fun provideTemplateTransactionFeatureGraphBuilder(): FeatureGraphBuilder {
        return TemplateTransactionNavigation()
    }

    @Provides
    @Singleton
    fun provideUtilitiesApi(retrofit: Retrofit): UtilitiesApi {
        return retrofit.create(UtilitiesApi::class.java)
    }

    @Provides
    @Singleton
    fun provideVerifyUtilitiesRepository(service: UtilitiesApi, networkConfig: NetworkConfig): UtilitiesRepository {
        return UtilitiesRepository(service, networkConfig)
    }

    @Provides
    @Singleton
    fun provideSmartOTPUseCase(
        iSmartOTPUseCase: Optional<ISmartOTPUseCase>,
        smartOTPSecure: ISmartOTPSecure
    ): SmartOTPUseCase {
        var iSmartOTPUseCaseImp = iSmartOTPUseCase.getOrNull()
        if (iSmartOTPUseCaseImp == null) {
            iSmartOTPUseCaseImp = DefaultSmartOTPUseCase(smartOTPSecure)
        }
        return SmartOTPUseCase(iSmartOTPUseCaseImp)
    }

    @Provides
    @IntoSet
    fun provideTemplateTransactionTabBuilder(): TemplateTransactionTabBuilder {
        return TemplateTransactionTabBuilderDefault()
    }

    @Provides
    @IntoSet
    fun provideTransactionReportBuilder(): TransactionBaseBuilder {
        return TransactionReportBuilderDefault()
    }

    @Singleton
    @Provides
    fun provideNotificationsService(retrofit: Retrofit): UtilitiesNotifyStatementsApi {
        return retrofit.create(UtilitiesNotifyStatementsApi::class.java)
    }

    @Singleton
    @Provides
    @IntoSet
    fun providerVerifyTransactionFlowScreenBuilderDefault(): VerifyTransactionFlowScreenBuilder {
        return VerifyTransactionFlowScreenBuilderDefault()
    }

    @Singleton
    @Provides
    @IntoSet
    fun providerScanQrVerifyFlowScreenBuilder(
        useCase: ScanQrVerifyUseCase
    ): VerifyTransactionFlowScreenBuilder {
        return ScanQrVerifyFlowScreenBuilder(useCase)
    }
} 
