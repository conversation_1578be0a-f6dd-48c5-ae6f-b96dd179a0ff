package vn.com.bidv.feature.common.ui.screen.commontransaction

import kotlinx.coroutines.Job
import vn.com.bidv.feature.common.domain.data.MenuDMO
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase

abstract class TransactionBaseViewModel(
    val reducer: TransactionBaseReducer,
    val itemBuilders: Set<TransactionBaseBuilder>
) : ViewModelIBankBase<TransactionBaseReducer.TransactionBaseState, TransactionBaseReducer.TransactionBaseEvent, TransactionBaseReducer.TransactionBaseEffect>(
    initialState = TransactionBaseReducer.TransactionBaseState(),
    reducer = reducer
) {
    override fun handleEffect(
        sideEffect: TransactionBaseReducer.TransactionBaseEffect,
        onResult: (TransactionBaseReducer.TransactionBaseEvent) -> Unit
    ) {
        when (sideEffect) {
            is TransactionBaseReducer.TransactionBaseEffect.FetData -> {
                fetchData(
                    onLoadSuccess = { data ->
                        onResult(
                            TransactionBaseReducer.TransactionBaseEvent.GetDataSuccess(menuData = data)
                        )
                    },
                    onLoadFail = { errorMessage ->
                        onResult(
                            TransactionBaseReducer.TransactionBaseEvent.GetDataFail(
                                errorMessage ?: ""
                            )
                        )
                    }
                )
            }
        }
    }

    protected abstract fun fetchData(
        onLoadSuccess: (data: List<MenuDMO>?) -> Unit,
        onLoadFail: (String?) -> Unit
    ): Job
}