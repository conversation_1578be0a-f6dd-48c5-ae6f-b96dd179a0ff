package vn.com.bidv.feature.common.domain.notificationcommon

import vn.com.bidv.localization.R

enum class TabNotiType(val value: String, val redirectId: String, val resourceID: Int = 0) {
    BALANCE_NOTIFICATION("BAL_NOTI", "bal", R.string.bien_dong),
    TRANSACTION_NOTIFICATION("TRANS_NOTI", "trans", R.string.giao_dich),
    PROMOTION_NOTIFICATION("PROMO_NOTI", "promo", R.string.uu_dai),
    SYSTEM_NOTIFICATION("SYS_NOTI", "sys", R.string.he_thong);

    companion object {
        fun safeValueOf(value: String?): TabNotiType? {
            return entries.find { it.value == value }
        }

        fun fromRedirectId(redirectId: String?): TabNotiType? {
            return entries.find { it.redirectId == redirectId }
        }
    }
}

enum class ReadStatus(val value: String) {
    UNREAD("UNREAD"),
    READ("READ")
}

