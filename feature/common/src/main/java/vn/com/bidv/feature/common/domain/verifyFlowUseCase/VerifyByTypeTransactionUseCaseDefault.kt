package vn.com.bidv.feature.common.domain.verifyFlowUseCase

import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InitVerifyTransactionResponse
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyCreateTransaction
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyTransaction
import vn.com.bidv.sdkbase.domain.DomainResult
import javax.inject.Inject

class VerifyByTypeTransactionUseCaseDefault @Inject constructor() : VerifyByTypeTransactionUseCase {
    override suspend fun initTransaction(input: InputVerifyTransaction): DomainResult<InitVerifyTransactionResponse> {
        return DomainResult.Error(errorCode = "")
    }

    override suspend fun initCreateTransaction(input: InputVerifyCreateTransaction): DomainResult<InitVerifyTransactionResponse> {
        return DomainResult.Error(errorCode = "")
    }

    override suspend fun verifyTransaction(
        initResponse: InitVerifyTransactionResponse,
        reqValue: String?
    ): DomainResult<String> {
        return DomainResult.Error(errorCode = "")
    }
}