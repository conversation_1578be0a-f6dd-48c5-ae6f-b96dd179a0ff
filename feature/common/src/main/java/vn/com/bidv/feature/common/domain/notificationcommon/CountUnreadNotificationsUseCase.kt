package vn.com.bidv.feature.common.domain.notificationcommon

import vn.com.bidv.feature.common.data.notify.NotificationUnReadRepository
import vn.com.bidv.feature.common.data.notify.NotifyCommonRepository
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class CountUnreadNotificationsUseCase @Inject constructor(
    private val repository: NotifyCommonRepository,
    private val notificationUnReadRepository: NotificationUnReadRepository,
) {
    suspend fun invoke(): DomainResult<ModelNotifyStatementCountDMO> {
        val result = repository.countUnread()
        val modelNotifyStatementCountDMO = result.convert(ModelNotifyStatementCountDMO::class.java)
        modelNotifyStatementCountDMO.getSafeData()?.let {
            notificationUnReadRepository.updateNotyUnreadCountState(it)
        }
        return modelNotifyStatementCountDMO
    }
}