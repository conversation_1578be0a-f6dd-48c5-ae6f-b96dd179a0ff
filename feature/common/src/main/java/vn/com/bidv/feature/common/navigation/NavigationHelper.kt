package vn.com.bidv.feature.common.navigation

import androidx.navigation.NavHostController
import com.google.gson.Gson
import vn.com.bidv.feature.common.domain.data.FXTransactionNavParams
import vn.com.bidv.feature.common.domain.data.ModelRequestSmartOtp
import vn.com.bidv.feature.common.domain.data.SmartOtpDMO
import vn.com.bidv.feature.common.domain.data.SmartOtpStatus
import vn.com.bidv.feature.common.domain.data.TransAuthDMO
import vn.com.bidv.feature.common.domain.data.UserActiveSmartOtpDMO
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyCreateTransaction
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyTransaction
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionTypeConstant
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.viewModelFlow.VerifyByTypeTransactionFlowViewModel
import vn.com.bidv.sdkbase.navigation.IBankMainRouting
import vn.com.bidv.sdkbase.utils.exts.navigateWithArgument

object NavigationHelper {

    fun navigateToScanQRScreen(navController: NavHostController) {
        navController.navigate(VerifyTransactionRoute.ScanQRScreenRoute.route)
    }

    fun navigateToReActiveSmartOtpScreen(navigation: NavHostController, isPopToHome: Boolean = false) {
        navigation.navigateWithArgument(
            route = IBankMainRouting.AuthRoutes.ReActiveSmartOtpRoute.route,
            listData = listOf(
                Pair(IBankMainRouting.AuthRoutes.ARG_TRANS_AUTH, isPopToHome.toString())
            )
        )
    }

    fun navigateToCreateTransactionScreen(navController: NavHostController, transactionData: String) {
        navController.navigateWithArgument(
            route = IBankMainRouting.TransferRoute.InitTransferRoute.route,
            listData = listOf(
                Pair(IBankMainRouting.TransferRoute.ARG_DATA_QR, transactionData)
            )
        )
    }

    fun navigateToVerifyTransaction(
        navController: NavHostController,
        data: TransAuthDMO
    ) {
        navController.navigateWithArgument(
            IBankMainRouting.CommonRoute.VerifyTransactionRoute.route,
            listOf(
                Pair(
                    IBankMainRouting.CommonRoute.ARG_TRANS_AUTH,
                    Gson().toJson(data)
                )
            )
        )
    }

    fun navigateToActiveSmartOtp(
        navController: NavHostController,
        smartOtp: SmartOtpDMO? = null,
        userActiveSmartOtpDMO: UserActiveSmartOtpDMO? = null,
        status: SmartOtpStatus = SmartOtpStatus.ACTIVE
    ) {
        val model = ModelRequestSmartOtp(
            smartOtp = smartOtp,
            userActiveSmartOtpDMO = userActiveSmartOtpDMO,
            status = status
        )
        navController.navigateWithArgument(
            IBankMainRouting.AuthRoutes.ActiveSmartOtpRoute.route,
            listOf(
                Pair(
                    IBankMainRouting.AuthRoutes.ARG_TRANS_AUTH,
                    Gson().toJson(model)
                )
            )
        )
    }

    fun navigateToConvertSmartOtp(
        navController: NavHostController,
    ) {
        navController.navigate(IBankMainRouting.AuthRoutes.ConvertActiveSmartOtpRoute.route)
    }

    /**
     * Hàm đóng luồng init và verify ở luồng chung
     * cách lấy ra viewModel trong màn kết quả : val viewModelFlow = navController.sharedVerifyTransactionViewModel()
     */
    fun closeVerifyByTypeTransaction(navController: NavHostController, viewModel: VerifyByTypeTransactionFlowViewModel, isSendKey: Boolean = true) {
        if (viewModel.isShareDataWhenFail() && isSendKey) {
            val keyFail = VerifyTransactionTypeConstant.getKeyFailByCode(viewModel.uiState.value.type)
            keyFail?.let {
                viewModel.requestReloadData(it)
            }
        }

        navController.popBackStack(
            route = IBankMainRouting.CommonRoute.VerifyByTypeTransactionRoute.routeWithArgument,
            true
        )
    }

    fun navigateToVerifyByTypeTransaction(
        navController: NavHostController,
        txnIds: List<String>,
        type: VerifyTransactionTypeConstant,
        additionalData: String? = null
    ) {
        navController.navigateWithArgument(
            IBankMainRouting.CommonRoute.VerifyByTypeTransactionRoute.route,
            listOf(
                Pair(
                    IBankMainRouting.CommonRoute.DATA_OBJECT_INIT,
                    Gson().toJson(InputVerifyTransaction(txnIds = txnIds))
                ),
                Pair(
                    IBankMainRouting.CommonRoute.TYPE,
                    type.code
                ),
                Pair(
                    IBankMainRouting.CommonRoute.ADDITIONAL_DATA,
                    additionalData
                )
            )
        )

    }

    /**
     * Hàm này thực hiện init và verify cho màn tạo có thể truyền luôn object vào.
     * Hàm này có hai trường hợp nếu type == CHANGE_PIN || SCAN_QR thì sẽ truyền vào dataString là json của object InitVerifyTransactionResponse còn trường hợp khác truyền vào json của input api init bình thường
     * @param dataString là input api Init.
     */
    fun navigateToVerifyByTypeCreateTransaction(
        navController: NavHostController,
        dataString: String,
        type: VerifyTransactionTypeConstant,
        additionalData: String? = null
    ) {
        navController.navigateWithArgument(
            IBankMainRouting.CommonRoute.VerifyByTypeTransactionRoute.route,
            listOf(
                Pair(
                    IBankMainRouting.CommonRoute.DATA_OBJECT_INIT,
                    Gson().toJson(InputVerifyCreateTransaction(dataString = dataString))
                ),
                Pair(
                    IBankMainRouting.CommonRoute.TYPE,
                    type.code
                ),
                Pair(
                    IBankMainRouting.CommonRoute.ADDITIONAL_DATA,
                    additionalData
                )
            )
        )

    }

    fun navigateToActionHistoryScreen(
        navController: NavHostController,
        txnId: String,
        txnCode: String
    ) {
        navController.navigate(
            route = IBankMainRouting.CommonRoute.CommonActionHistoryRoute.routeWithArgument
                .replace("{${IBankMainRouting.CommonRoute.TXN_ID}}", txnId
                ).replace("{${IBankMainRouting.CommonRoute.TXN_CODE}}", txnCode)
        )
    }

    /**
     * This function navigates to the Sell Foreign Exchange screen.
     * @param navController: The navigation controller used to handle navigation between screens.
     * @param preInitData: This parameter contains initialization data for the Sell Foreign Exchange screen. It includes:
     * @fxTransactionID: The ID of the transaction.
     * @agreementCode: The agreement code.
     * @fxTransactionAction: The type of action for the transaction (e.g., CREATED, UPDATE, COPY, EDIT).
     */
    fun navigateToSellForeignExchangeScreen(
        navController: NavHostController,
        preInitData: FXTransactionNavParams? = null
    ) {
        navController.navigateWithArgument(
            IBankMainRouting.ForeignExchangeRoute.SellForeignExchangeRoute.route,
            listOf(
                Pair(
                    IBankMainRouting.ForeignExchangeRoute.PRE_INIT_DATA,
                    Gson().toJson(preInitData)
                ),
            )
        )
    }

    /**
     * This function navigates to the Buy Foreign Exchange screen.
     * @param navController: The navigation controller used to handle navigation between screens.
     * @param preInitData: This parameter contains initialization data for the Buy Foreign Exchange screen. It includes:
     * @fxTransactionID: The ID of the transaction.
     * @agreementCode: The agreement code.
     * @fxTransactionAction: The type of action for the transaction (e.g., CREATED, UPDATE, COPY, EDIT).
     */
    fun navigateToBuyForeignExchangeScreen(
        navController: NavHostController,
        preInitData: FXTransactionNavParams? = null
    ) {
        navController.navigateWithArgument(
            IBankMainRouting.ForeignExchangeRoute.BuyForeignExchangeRoute.route,
            listOf(
                Pair(
                    IBankMainRouting.ForeignExchangeRoute.PRE_INIT_DATA,
                    Gson().toJson(preInitData)
                ),
            )
        )
    }
}