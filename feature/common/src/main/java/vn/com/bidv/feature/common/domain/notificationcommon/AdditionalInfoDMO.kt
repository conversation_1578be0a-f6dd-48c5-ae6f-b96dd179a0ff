package vn.com.bidv.feature.common.domain.notificationcommon

import com.google.gson.annotations.SerializedName

data class AdditionalInfoDMO(

    @SerializedName("balTab") val balTab: NotyAdditionalInfo?,
    @SerializedName("transTab") val transTab: NotyAdditionalInfo?,
    @SerializedName("sysTab") val sysTab: NotyAdditionalInfo?,
    @SerializedName("promoTab") val promoTab: NotyAdditionalInfo?
)

data class NotyAdditionalInfo(

    @SerializedName("redirect") val redirect: RedirectDetailDMO?,
    @SerializedName("button") val button: List<ButtonInfo>?
)

data class RedirectDetailDMO(

    @SerializedName("redirectId") val redirectId: String,
    @SerializedName("params") val params: Map<String, String>?
)

data class ButtonInfo(
    @SerializedName("buttonName") val buttonName: String?,
    @SerializedName("buttonValue") val buttonValue: String?,
    @SerializedName("buttonOrder") val buttonOrder: Int?
)

