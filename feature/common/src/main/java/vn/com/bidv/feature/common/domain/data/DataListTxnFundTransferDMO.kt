package vn.com.bidv.feature.common.domain.data
import com.google.gson.annotations.SerializedName

data class DataListTxnFundTransferDMO(
    @SerializedName("items") val items: List<TxnFundTransferDMO>? = null,
    @SerializedName("total") val total: Long? = null
)

data class TxnFundTransferDMO(
    @SerializedName("id") val id: String? = null,
    @SerializedName("debitAccNo") val debitAccNo: String? = null,
    @SerializedName("debitAccName") val debitAccName: String? = null,
    @SerializedName("status") val status: String? = null,
    @SerializedName("amount") val amount: java.math.BigDecimal? = null,
    @SerializedName("ccy") val ccy: String? = null,
    @SerializedName("benAccNo") val benAccNo: String? = null,
    @SerializedName("benCardNo") val benCardNo: String? = null,
    @SerializedName("benName") val benName: String? = null,
    @SerializedName("createdDate") val createdDate: String? = null,
    @SerializedName("createdBy") val createdBy: String? = null,
    @SerializedName("benBankCode") val benBankCode: String? = null,
    @SerializedName("benBankNameShort") val benBankNameShort: String? = null,
    @SerializedName("remark") val remark: String? = null,
    @SerializedName("raNote") val raNote: String? = null,
    @SerializedName("effDate") val effDate: String? = null,
    @SerializedName("pmtMethod") val pmtMethod: String? = null,
    @SerializedName("txnType") val txnType: String? = null,
    @SerializedName("channel") val channel: String? = null,
    @SerializedName("orgId") val orgId: String? = null,
    @SerializedName("batchNo") val batchNo: String? = null,
    @SerializedName("benId") val benId: String? = null,
    @SerializedName("benIssueDate") val benIssueDate: String? = null,
    @SerializedName("benIssueAddr") val benIssueAddr: String? = null,
    @SerializedName("feeID") val feeID: String? = null,
    @SerializedName("feeTotal") val feeTotal: String? = null,
    @SerializedName("benBrcd") val benBrcd: String? = null,
    @SerializedName("txnDesc") val txnDesc: String? = null,
    @SerializedName("approvalUsers") val approvalUsers: String? = null,
    @SerializedName("coreRef") val coreRef: String? = null,
    @SerializedName("pmtRef") val pmtRef: String? = null,
    @SerializedName("feeMethod") val feeMethod: String? = null,
    @SerializedName("approvedDate") val approvedDate: String? = null,
    @SerializedName("feeAccNo") val feeAccNo: java.math.BigDecimal? = null,
    @SerializedName("priority") val priority: String? = null
)