@echo off
setlocal

rem copy the commit-msg, post-rewrite hook to the parent git repository
copy .\config\git\commit-msg .\.git\hooks\
copy .\config\git\post-rewrite .\.git\hooks\

rem copy the commit-msg hook to the submodules
set "SOURCE_FILE=.\config\git\commit-msg"
set "BASE_DIR=.\.git\modules"

rem Loop through each subdirectory in the base directory
for /d %%i in ("%BASE_DIR%\*") do (
    rem Check if the hooks directory exists
    if exist "%%i\hooks" (
        rem Copy the source file to the hooks directory
        echo Copying %SOURCE_FILE% to %%i\hooks
        copy /Y "%SOURCE_FILE%" "%%i\hooks\"
    )
)

set "BASE_DIR=.\.git\modules\core"

rem Loop through each subdirectory in the base directory
for /d %%i in ("%BASE_DIR%\*") do (
    rem Check if the hooks directory exists
    if exist "%%i\hooks" (
        rem Copy the source file to the hooks directory
        echo Copying %SOURCE_FILE% to %%i\hooks
        copy /Y "%SOURCE_FILE%" "%%i\hooks\"
    )
)

set "BASE_DIR=.\.git\modules\feature"

rem Loop through each subdirectory in the base directory
for /d %%i in ("%BASE_DIR%\*") do (
    rem Check if the hooks directory exists
    if exist "%%i\hooks" (
        rem Copy the source file to the hooks directory
        echo Copying %SOURCE_FILE% to %%i\hooks
        copy /Y "%SOURCE_FILE%" "%%i\hooks\"
    )
)

endlocal