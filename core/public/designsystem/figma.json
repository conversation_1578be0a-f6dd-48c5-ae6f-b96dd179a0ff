{"gradient": {"background gradient": {"color-grd-card-nh": {"description": "", "type": "custom-gradient", "value": {"gradientType": "linear", "rotation": 134.82612473237015, "stops": [{"position": 0, "color": "#70797bff"}, {"position": 1, "color": "#3d494bff"}]}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:e2446ecaa0d1d9d221a9cf29b38b8b61eb44aa9a,", "exportKey": "gradient"}}}, "color-grd-card-primary": {"description": "", "type": "custom-gradient", "value": {"gradientType": "linear", "rotation": 318.1555572598695, "stops": [{"position": 0.01, "color": "#005c59ff"}, {"position": 0.33, "color": "#006b68ff"}, {"position": 1, "color": "#37<PERSON><PERSON>f"}]}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:db97312e7e5e23a081f86759da617ac80204fc15,", "exportKey": "gradient"}}}, "bg topnav": {"description": "", "type": "custom-gradient", "value": {"gradientType": "linear", "rotation": 131.*************, "stops": [{"position": 0, "color": "#262a29ff"}, {"position": 1, "color": "#31403cff"}]}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:9d97910e85d67c8c9057113a06ba7bdde0bf172d,", "exportKey": "gradient"}}}, "card": {"primary": {"description": "", "type": "custom-gradient", "value": {"gradientType": "linear", "rotation": 125.51876091709443, "stops": [{"position": 0, "color": "#006b68ff"}, {"position": 1, "color": "#003332ff"}]}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:1ca64c723973f6d0c11359251e4e1e2e0d11d63a,", "exportKey": "gradient"}}}, "secondary": {"description": "", "type": "custom-gradient", "value": {"gradientType": "linear", "rotation": 318.1555572598695, "stops": [{"position": 0, "color": "#005c59ff"}, {"position": 1, "color": "#37<PERSON><PERSON>f"}]}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:7db476643e3aee220799c9ee53d40a0ae9488e2c,", "exportKey": "gradient"}}}, "tertiary": {"description": "", "type": "custom-gradient", "value": {"gradientType": "linear", "rotation": 125.51876091709443, "stops": [{"position": 0, "color": "#4f5d5fff"}, {"position": 1, "color": "#3d494bff"}]}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:a891685b9375440d1b6d43e02316541a15eac77b,", "exportKey": "gradient"}}}, "quarternary": {"description": "", "type": "custom-gradient", "value": {"gradientType": "linear", "rotation": 131.*************, "stops": [{"position": 0, "color": "#454c4cff"}, {"position": 1, "color": "#183d3cff"}]}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:21a94ede609bb34a3963d5596a5eb72d666ce0ed,", "exportKey": "gradient"}}}}, "primary": {"left-right": {"description": "", "type": "custom-gradient", "value": {"gradientType": "linear", "rotation": 287.78164283728586, "stops": [{"position": 0, "color": "#f3f6f6ff"}, {"position": 1, "color": "#ffffff00"}]}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:9444f2f777cb61e44ac4781c99f4d54f28813f68,", "exportKey": "gradient"}}}, "right-left": {"description": "", "type": "custom-gradient", "value": {"gradientType": "linear", "rotation": 287.78164283728586, "stops": [{"position": 0, "color": "#ffffff00"}, {"position": 1, "color": "#f3f6f6ff"}]}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:7b14d737cdf3fbefcf4d7dc9c135ab827a7744b8,", "exportKey": "gradient"}}}, "top-bot": {"description": "", "type": "custom-gradient", "value": {"gradientType": "linear", "rotation": 180, "stops": [{"position": 0, "color": "#f3f6f6ff"}, {"position": 1, "color": "#ffffff00"}]}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:c218808e240e1ae1576353484148c77ce3b4664d,", "exportKey": "gradient"}}}, "bot-top": {"description": "", "type": "custom-gradient", "value": {"gradientType": "linear", "rotation": 180, "stops": [{"position": 0, "color": "#ffffff00"}, {"position": 1, "color": "#f3f6f6ff"}]}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:e1a2a9251407d0d22814556314e19752525fc4a6,", "exportKey": "gradient"}}}}, "secondary": {"left-right": {"description": "", "type": "custom-gradient", "value": {"gradientType": "linear", "rotation": 287.78164283728586, "stops": [{"position": 0, "color": "#f9fbfbff"}, {"position": 1, "color": "#ffffff00"}]}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:1d2696e24df6eb2d91b4526c10fe066d7af21f52,", "exportKey": "gradient"}}}, "right-left": {"description": "", "type": "custom-gradient", "value": {"gradientType": "linear", "rotation": 287.78164283728586, "stops": [{"position": 0, "color": "#ffffff00"}, {"position": 1, "color": "#f9fbfbff"}]}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:78baaa819f0772d49a1c88d64b855634ef8a3d83,", "exportKey": "gradient"}}}, "top-bot": {"description": "", "type": "custom-gradient", "value": {"gradientType": "linear", "rotation": 180, "stops": [{"position": 0, "color": "#f9fbfbff"}, {"position": 1, "color": "#ffffff00"}]}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:c93a5b72f3ef52d79101000ccaa32942ad37f9de,", "exportKey": "gradient"}}}, "bot-top": {"description": "", "type": "custom-gradient", "value": {"gradientType": "linear", "rotation": 180, "stops": [{"position": 0, "color": "#ffffff00"}, {"position": 1, "color": "#f9fbfbff"}]}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:04cc428cb07725d50125502e9514f7563513fc9e,", "exportKey": "gradient"}}}}, "tertiary": {"left-right": {"description": "", "type": "custom-gradient", "value": {"gradientType": "linear", "rotation": 287.78164283728586, "stops": [{"position": 0, "color": "#ffffffff"}, {"position": 1, "color": "#ffffff00"}]}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:98a8447e964339523ab500893ff0b2ee482023c6,", "exportKey": "gradient"}}}, "right-left": {"description": "", "type": "custom-gradient", "value": {"gradientType": "linear", "rotation": 287.78164283728586, "stops": [{"position": 0, "color": "#ffffff00"}, {"position": 1, "color": "#ffffffff"}]}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:1aa61df2de9acb5685a021a9c6f2252fcc848c3d,", "exportKey": "gradient"}}}, "top-bot": {"description": "", "type": "custom-gradient", "value": {"gradientType": "linear", "rotation": 180, "stops": [{"position": 0, "color": "#ffffffff"}, {"position": 1, "color": "#ffffff00"}]}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:953dbc1841a8ab6963ecbea423cca0249269a79e,", "exportKey": "gradient"}}}, "bot-top": {"description": "", "type": "custom-gradient", "value": {"gradientType": "linear", "rotation": 180, "stops": [{"position": 0, "color": "#ffffff00"}, {"position": 1, "color": "#ffffffff"}]}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:7c1ccdb106aa767d67ba893418ad73e51ff84d45,", "exportKey": "gradient"}}}}}}, "grid": {"mobile": {"0": {"type": "custom-grid", "value": {"pattern": "columns", "gutterSize": 16, "alignment": "stretch", "count": 4, "offset": 16}}, "1": {"type": "custom-grid", "value": {"pattern": "rows", "sectionSize": 107, "gutterSize": 20, "alignment": "min", "count": 1, "offset": 0}}, "2": {"type": "custom-grid", "value": {"pattern": "rows", "sectionSize": 32, "gutterSize": 20, "alignment": "max", "count": 1, "offset": 0}}, "description": null, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:bf648de83b3d07ac25cd39d0b27a78f282189c19,", "exportKey": "grid"}}}, "desktop expand": {"0": {"type": "custom-grid", "value": {"pattern": "columns", "sectionSize": 300, "gutterSize": 40, "alignment": "min", "count": 1, "offset": 0}}, "1": {"type": "custom-grid", "value": {"pattern": "rows", "sectionSize": 64, "gutterSize": 20, "alignment": "min", "count": 1, "offset": 0}}, "description": null, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:f6b03042e97e4f31b4562c371f64e5ab50a4c6c8,", "exportKey": "grid"}}}, "desktop colapse": {"0": {"type": "custom-grid", "value": {"pattern": "columns", "gutterSize": 16, "alignment": "stretch", "count": 12, "offset": 24}}, "1": {"type": "custom-grid", "value": {"pattern": "rows", "sectionSize": 64, "gutterSize": 20, "alignment": "min", "count": 1, "offset": 0}}, "description": null, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:30140bc9b4364fbfc42dba64f718ad62d35bb973,", "exportKey": "grid"}}}}, "font": {"display": {"display s": {"type": "custom-fontStyle", "value": {"fontSize": 32, "textDecoration": "none", "fontFamily": "Roboto", "fontWeight": 700, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 44, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:03883032facca80e64ec861d244693bcc054613f,", "exportKey": "font"}}}}, "headline": {"headline m": {"type": "custom-fontStyle", "value": {"fontSize": 28, "textDecoration": "none", "fontFamily": "Roboto", "fontWeight": 700, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 36, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:6492ddd2b597dc429d321b40e6642da48af88379,", "exportKey": "font"}}}, "headline s": {"type": "custom-fontStyle", "value": {"fontSize": 24, "textDecoration": "none", "fontFamily": "Roboto", "fontWeight": 700, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 32, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:af5d882b453afd4eac63a0796f484d046341778e,", "exportKey": "font"}}}}, "title": {"title l": {"type": "custom-fontStyle", "value": {"fontSize": 20, "textDecoration": "none", "fontFamily": "Roboto", "fontWeight": 500, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 28, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:87e3f8ee52a100765dbc70692ce9f988fe9c35a0,", "exportKey": "font"}}}, "title m": {"type": "custom-fontStyle", "value": {"fontSize": 16, "textDecoration": "none", "fontFamily": "Roboto", "fontWeight": 500, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 24, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:5fc734fd1899c2c65e2407e673bfba0424781557,", "exportKey": "font"}}}, "title s": {"type": "custom-fontStyle", "value": {"fontSize": 14, "textDecoration": "none", "fontFamily": "Roboto", "fontWeight": 500, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 20, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:a89f898fc96ba99f25a6f064459b04764198722e,", "exportKey": "font"}}}}, "label": {"label xl": {"type": "custom-fontStyle", "value": {"fontSize": 16, "textDecoration": "none", "fontFamily": "Roboto", "fontWeight": 500, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 24, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:2f586c8f5d70063bf1deed31b4ab704cf5fed3a8,", "exportKey": "font"}}}, "label l": {"type": "custom-fontStyle", "value": {"fontSize": 14, "textDecoration": "none", "fontFamily": "Roboto", "fontWeight": 500, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 20, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:3291d2a88bab33dc29e4b19f81dba3cdd5872469,", "exportKey": "font"}}}, "label m": {"type": "custom-fontStyle", "value": {"fontSize": 12, "textDecoration": "none", "fontFamily": "Roboto", "fontWeight": 500, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 16, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:dd650390022bb46670b384f8dd00d725b83f98cd,", "exportKey": "font"}}}, "label s": {"type": "custom-fontStyle", "value": {"fontSize": 11, "textDecoration": "none", "fontFamily": "Roboto", "fontWeight": 500, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 16, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:b4217fa6534143c882237be901c306714119633a,", "exportKey": "font"}}}}, "body": {"body l": {"type": "custom-fontStyle", "value": {"fontSize": 16, "textDecoration": "none", "fontFamily": "Roboto", "fontWeight": 400, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 24, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:a6c7bb05d5d785a59c9d1b525e58fe5ee3ed4d2b,", "exportKey": "font"}}}, "body m": {"type": "custom-fontStyle", "value": {"fontSize": 14, "textDecoration": "none", "fontFamily": "Roboto", "fontWeight": 400, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 20, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:af9eb857a7df26bffee91eb7066ffab5369ccac4,", "exportKey": "font"}}}, "body s": {"type": "custom-fontStyle", "value": {"fontSize": 12, "textDecoration": "none", "fontFamily": "Roboto", "fontWeight": 400, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 16, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:3f7af446f48d11932e5b34dfb28ac4b25a97ceef,", "exportKey": "font"}}}}, "caption": {"caption m": {"type": "custom-fontStyle", "value": {"fontSize": 12, "textDecoration": "none", "fontFamily": "Roboto", "fontWeight": 400, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 18, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:16c0f18b8a0221bd349b78e8fdbf76bbe2e2ce4d,", "exportKey": "font"}}}, "caption s": {"type": "custom-fontStyle", "value": {"fontSize": 10, "textDecoration": "none", "fontFamily": "Roboto", "fontWeight": 400, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 14, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:837ea20d8ba99e4dafbbc678ccb0905adf163c8a,", "exportKey": "font"}}}}}, "effect": {"shadows": {"shadow-s": {"0": {"type": "custom-shadow", "value": {"shadowType": "dropShadow", "radius": 2, "color": "#0000000a", "offsetX": 0, "offsetY": 0, "spread": 0}}, "1": {"type": "custom-shadow", "value": {"shadowType": "dropShadow", "radius": 8, "color": "#0000001a", "offsetX": 0, "offsetY": 2, "spread": -2}}, "description": null, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:7a4faf3735119155672df33290dd2821f732a58c,", "exportKey": "effect"}}}, "shadow-m": {"0": {"type": "custom-shadow", "value": {"shadowType": "dropShadow", "radius": 4, "color": "#0000000a", "offsetX": 0, "offsetY": 0, "spread": 0}}, "1": {"type": "custom-shadow", "value": {"shadowType": "dropShadow", "radius": 12, "color": "#0000001a", "offsetX": 0, "offsetY": 4, "spread": -2}}, "description": null, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:2cc1a3f438fbf8ecf5a209d9644684c7b2c9d5ac,", "exportKey": "effect"}}}, "shadow-l": {"0": {"type": "custom-shadow", "value": {"shadowType": "dropShadow", "radius": 8, "color": "#0000000a", "offsetX": 0, "offsetY": 0, "spread": 0}}, "1": {"type": "custom-shadow", "value": {"shadowType": "dropShadow", "radius": 16, "color": "#0000001a", "offsetX": 0, "offsetY": 8, "spread": -2}}, "description": null, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:24e4472feb3c2d92eaa51b9b586b472edf7a1220,", "exportKey": "effect"}}}, "shadow-xl": {"0": {"type": "custom-shadow", "value": {"shadowType": "dropShadow", "radius": 2, "color": "#0000000a", "offsetX": 0, "offsetY": 0, "spread": 0}}, "1": {"type": "custom-shadow", "value": {"shadowType": "dropShadow", "radius": 24, "color": "#0000001a", "offsetX": 0, "offsetY": 12, "spread": -4}}, "description": null, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:9d622aeb6aec8cf22f4e7d2f270e40e20feae1ab,", "exportKey": "effect"}}}, "shadow-2xl": {"0": {"type": "custom-shadow", "value": {"shadowType": "dropShadow", "radius": 4, "color": "#0000000a", "offsetX": 0, "offsetY": 0, "spread": 0}}, "1": {"type": "custom-shadow", "value": {"shadowType": "dropShadow", "radius": 48, "color": "#0000001a", "offsetX": 0, "offsetY": 24, "spread": -6}}, "description": null, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:aa7e9cec41ae1401fe8a7c5ccf4efd71a9cc96de,", "exportKey": "effect"}}}, "shadow-3xl": {"0": {"type": "custom-shadow", "value": {"shadowType": "dropShadow", "radius": 8, "color": "#0000000a", "offsetX": 0, "offsetY": 0, "spread": 0}}, "1": {"type": "custom-shadow", "value": {"shadowType": "dropShadow", "radius": 64, "color": "#0000001a", "offsetX": 0, "offsetY": 32, "spread": -8}}, "description": null, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:db7788c7023e815351466776fb1244368ae6691a,", "exportKey": "effect"}}}}, "focus rings": {"brand-primary": {"description": null, "type": "custom-shadow", "value": {"shadowType": "dropShadow", "radius": 0, "color": "#ddf2f2ff", "offsetX": 0, "offsetY": 0, "spread": 4}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:cf4deeed93384431f4b5f1e13a2f08523a12aa0d,", "exportKey": "effect"}}}, "brand-secondary": {"0": {"type": "custom-shadow", "value": {"shadowType": "dropShadow", "radius": 0, "color": "#ddf2f2ff", "offsetX": 0, "offsetY": 0, "spread": 4}}, "1": {"type": "custom-shadow", "value": {"shadowType": "dropShadow", "radius": 2, "color": "#0000001a", "offsetX": 0, "offsetY": 1, "spread": 0}}, "description": null, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:dedd2adc2d121794c581f2c5737a485f1ac8fd27,", "exportKey": "effect"}}}, "error": {"0": {"type": "custom-shadow", "value": {"shadowType": "dropShadow", "radius": 0, "color": "#fdf3f3ff", "offsetX": 0, "offsetY": 0, "spread": 4}}, "1": {"type": "custom-shadow", "value": {"shadowType": "dropShadow", "radius": 2, "color": "#0000001a", "offsetX": 0, "offsetY": 1, "spread": 0}}, "description": null, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:f2c4e853b49bf3fb539d48356ff72a2f2cd4d0cf,", "exportKey": "effect"}}}, "gray": {"0": {"type": "custom-shadow", "value": {"shadowType": "dropShadow", "radius": 0, "color": "#e6e9eaff", "offsetX": 0, "offsetY": 0, "spread": 4}}, "1": {"type": "custom-shadow", "value": {"shadowType": "dropShadow", "radius": 2, "color": "#0000001a", "offsetX": 0, "offsetY": 1, "spread": 0}}, "description": null, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:5478fdebabfc3eb28de136da59a69055e7282ffd,", "exportKey": "effect"}}}}}, "0.primitives": {"spacing": {"0": {"type": "dimension", "value": 0, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:12:1028", "exportKey": "variables"}}}, "2": {"type": "dimension", "value": 2, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:12:1029", "exportKey": "variables"}}}, "4": {"type": "dimension", "value": 4, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:12:1030", "exportKey": "variables"}}}, "8": {"type": "dimension", "value": 8, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:12:1032", "exportKey": "variables"}}}, "12": {"type": "dimension", "value": 12, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:12:1033", "exportKey": "variables"}}}, "16": {"type": "dimension", "value": 16, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:12:1034", "exportKey": "variables"}}}, "20": {"type": "dimension", "value": 20, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:12:1035", "exportKey": "variables"}}}, "24": {"type": "dimension", "value": 24, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:12:1036", "exportKey": "variables"}}}, "32": {"type": "dimension", "value": 32, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:12:1037", "exportKey": "variables"}}}, "40": {"type": "dimension", "value": 40, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:12:1038", "exportKey": "variables"}}}, "48": {"type": "dimension", "value": 48, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:12:1039", "exportKey": "variables"}}}}, "color": {"base": {"white": {"type": "color", "value": "#ffffffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1060", "exportKey": "variables"}}}, "black": {"type": "color", "value": "#000000ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1061", "exportKey": "variables"}}}}, "neutral": {"50": {"type": "color", "value": "#f9fbfbff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1064", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#f3f6f6ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1065", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#e6e9eaff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1066", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#d3d9daff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1067", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#9<PERSON><PERSON><PERSON>", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1068", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#70797bff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1069", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#4f5d5fff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1070", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#3d494bff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1071", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#202828ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1072", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#171f21ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1073", "exportKey": "variables"}}}}, "brand_primary": {"25": {"type": "color", "value": "#f0f9f9ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:910:17199", "exportKey": "variables"}}}, "50": {"type": "color", "value": "#ddf2f2ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1100", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#b8e5e5ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1101", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#9adad8ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1102", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#6ecac8ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1103", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#37<PERSON><PERSON>f", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1104", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#006b68ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1105", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#005c59ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1106", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#004240ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1107", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#003332ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1108", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#002424ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1109", "exportKey": "variables"}}}}, "red": {"50": {"type": "color", "value": "#fdf3f3ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1112", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#fbe5e5ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1113", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#f8d1cfff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1114", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#f5aeadff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1115", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#eb7f7eff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1116", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#df5654ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1117", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#cb3937ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1118", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#a62c2aff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1119", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#8d2926ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1120", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#762626ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1121", "exportKey": "variables"}}}}, "orange": {"50": {"type": "color", "value": "#fffaebff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1124", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#fef0c7ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1125", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#fedf89ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1126", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#fec84bff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1127", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#fdb022ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1128", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#f79009ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1129", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#dc6803ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1130", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#b54708ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1131", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#93370dff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1132", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#7a2e0eff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1133", "exportKey": "variables"}}}}, "green": {"50": {"type": "color", "value": "#ecfdf3ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1136", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#dcfae6ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1137", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#abefc6ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1138", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#75e0a7ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1139", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#47cd89ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1140", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#17b26aff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1141", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#079455ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1142", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#067647ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1143", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#085d3aff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1144", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#074d31ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1145", "exportKey": "variables"}}}}, "blue": {"50": {"type": "color", "value": "#f0f9ffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1292", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#e0f2feff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1293", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#b9e6feff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1294", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#7cd4fdff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1295", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#36bffaff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1296", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#0ba5ecff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1297", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#0086c9ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1298", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#026aa2ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1299", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#065986ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1300", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#0b4a6fff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1301", "exportKey": "variables"}}}}, "brand_secondary": {"50": {"type": "color", "value": "#fff7e4ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1508", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#ffeec8ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1509", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#ffe7b1ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1510", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#ffdc8fff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1511", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#ffcd5fff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1512", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#ffb71bff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1513", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#c28a15ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1514", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#8b640eff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1515", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#66490aff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1516", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#382805ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:12:1517", "exportKey": "variables"}}}}, "neutral_blue": {"25": {"type": "color", "value": "#e8e8e8ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:3051:13", "exportKey": "variables"}}}, "50": {"type": "color", "value": "#daddddff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:3051:14", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#c5c8c8ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:3051:15", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#8a8e94ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:3051:16", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#6b7075ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:3051:17", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#4a4f54ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:3051:18", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#373b3eff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:3051:19", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#2c2e31ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:3051:20", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#26292bff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:3051:21", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#242629ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:3051:22", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#1f2123ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:3062:30752", "exportKey": "variables"}}}, "950": {"type": "color", "value": "#0e0f10ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:3263:129", "exportKey": "variables"}}}}, "grayscale": {"50": {"type": "color", "value": "#f7f7f7ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4002:3319", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#f2f2f2ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4002:3329", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#ebebebff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4002:3330", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#ddddddff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4002:3331", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#b0b0b0ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4002:3332", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#929292ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4002:3333", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#8c8c8cff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4002:3334", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#6a6a6aff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4002:3335", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#3f3f3fff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4002:3336", "exportKey": "variables"}}}, "850": {"type": "color", "value": "#222222ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4002:3337", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#1a1a1aff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4002:3338", "exportKey": "variables"}}}, "950": {"type": "color", "value": "#111111ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4002:3339", "exportKey": "variables"}}}}, "opacity": {"white": {"0": {"type": "color", "value": "#ffffff00", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4002:3353", "exportKey": "variables"}}}, "4": {"type": "color", "value": "#ffffff0a", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4002:3354", "exportKey": "variables"}}}, "5": {"type": "color", "value": "#ffffff0d", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4002:3341", "exportKey": "variables"}}}, "7": {"type": "color", "value": "#ffffff12", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4002:3355", "exportKey": "variables"}}}, "10": {"type": "color", "value": "#ffffff1a", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4002:3356", "exportKey": "variables"}}}, "20": {"type": "color", "value": "#ffffff33", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4002:3357", "exportKey": "variables"}}}, "30": {"type": "color", "value": "#ffffff4d", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4002:3358", "exportKey": "variables"}}}, "40": {"type": "color", "value": "#ffffff66", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4003:3359", "exportKey": "variables"}}}, "50": {"type": "color", "value": "#ffffff80", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4003:3360", "exportKey": "variables"}}}, "60": {"type": "color", "value": "#ffffff99", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4003:3361", "exportKey": "variables"}}}, "70": {"type": "color", "value": "#ffffffb3", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4003:3362", "exportKey": "variables"}}}, "80": {"type": "color", "value": "#ffffffcc", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4003:3363", "exportKey": "variables"}}}, "90": {"type": "color", "value": "#ffffffe6", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4003:3364", "exportKey": "variables"}}}}, "black": {"0": {"type": "color", "value": "#00000000", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4004:3378", "exportKey": "variables"}}}, "3": {"type": "color", "value": "#00000008", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4004:3379", "exportKey": "variables"}}}, "5": {"type": "color", "value": "#0000000d", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4004:3380", "exportKey": "variables"}}}, "8": {"type": "color", "value": "#00000014", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4004:3381", "exportKey": "variables"}}}, "10": {"type": "color", "value": "#0000001a", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4004:3382", "exportKey": "variables"}}}, "20": {"type": "color", "value": "#00000033", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4004:3383", "exportKey": "variables"}}}, "30": {"type": "color", "value": "#0000004d", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4004:3384", "exportKey": "variables"}}}, "40": {"type": "color", "value": "#00000066", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4004:3385", "exportKey": "variables"}}}, "50": {"type": "color", "value": "#00000080", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4004:3386", "exportKey": "variables"}}}, "60": {"type": "color", "value": "#00000099", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4004:3387", "exportKey": "variables"}}}, "70": {"type": "color", "value": "#000000b3", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4004:3388", "exportKey": "variables"}}}, "80": {"type": "color", "value": "#000000cc", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4004:3389", "exportKey": "variables"}}}, "90": {"type": "color", "value": "#000000e6", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Primitives", "scopes": [], "variableId": "VariableID:4004:3390", "exportKey": "variables"}}}}}}, "typography": {"font size": {"10": {"type": "dimension", "value": 10, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:827:1052", "exportKey": "variables"}}}, "11": {"type": "dimension", "value": 11, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:827:1055", "exportKey": "variables"}}}, "12": {"type": "dimension", "value": 12, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:827:1053", "exportKey": "variables"}}}, "14": {"type": "dimension", "value": 14, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:827:1054", "exportKey": "variables"}}}, "16": {"type": "dimension", "value": 16, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:827:1051", "exportKey": "variables"}}}, "20": {"type": "dimension", "value": 20, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:511:14483", "exportKey": "variables"}}}, "24": {"type": "dimension", "value": 24, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:511:14484", "exportKey": "variables"}}}, "28": {"type": "dimension", "value": 28, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:511:16457", "exportKey": "variables"}}}, "32": {"type": "dimension", "value": 32, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:511:14485", "exportKey": "variables"}}}}, "font-family": {"roboto": {"type": "string", "value": "Roboto", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:511:16464", "exportKey": "variables"}}}}, "font-weight": {"regular": {"type": "dimension", "value": 400, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:827:1057", "exportKey": "variables"}}}, "medium": {"type": "dimension", "value": 500, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:827:1058", "exportKey": "variables"}}}, "bold": {"type": "dimension", "value": 700, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:827:1059", "exportKey": "variables"}}}}, "line height": {"14": {"type": "dimension", "value": 14, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:910:17182", "exportKey": "variables"}}}, "16": {"type": "dimension", "value": 16, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:910:17183", "exportKey": "variables"}}}, "18": {"type": "dimension", "value": 18, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:910:17184", "exportKey": "variables"}}}, "20": {"type": "dimension", "value": 20, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:910:17185", "exportKey": "variables"}}}, "24": {"type": "dimension", "value": 24, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:910:17186", "exportKey": "variables"}}}, "28": {"type": "dimension", "value": 28, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:910:17187", "exportKey": "variables"}}}, "32": {"type": "dimension", "value": 32, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:910:17188", "exportKey": "variables"}}}, "36": {"type": "dimension", "value": 36, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:910:17189", "exportKey": "variables"}}}, "44": {"type": "dimension", "value": 44, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:910:17196", "exportKey": "variables"}}}}}, "border & divider": {"1": {"type": "dimension", "value": 1, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:833:3297", "exportKey": "variables"}}}, "2": {"type": "dimension", "value": 2, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:511:16410", "exportKey": "variables"}}}, "3": {"type": "dimension", "value": 3, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:511:16411", "exportKey": "variables"}}}, "0,5": {"type": "dimension", "value": 0.5, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:910:17201", "exportKey": "variables"}}}}, "corner-radius": {"0": {"type": "dimension", "value": 0, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:511:16459", "exportKey": "variables"}}}, "2": {"type": "dimension", "value": 2, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:812:15539", "exportKey": "variables"}}}, "4": {"type": "dimension", "value": 4, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:511:16460", "exportKey": "variables"}}}, "8": {"type": "dimension", "value": 8, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:511:16461", "exportKey": "variables"}}}, "9999": {"type": "dimension", "value": 9999, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:906:17180", "exportKey": "variables"}}}}, "breakpoints": {"375": {"type": "dimension", "value": 375, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:511:16475", "exportKey": "variables"}}}, "768": {"type": "dimension", "value": 768, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:511:16474", "exportKey": "variables"}}}, "1440": {"type": "dimension", "value": 1440, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "0.Primitives", "scopes": [], "variableId": "VariableID:511:16473", "exportKey": "variables"}}}}}, "3. spacing": {"none": {"type": "dimension", "value": "{0.primitives.default.spacing.0}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "3. Spacing", "scopes": ["GAP"], "variableId": "VariableID:12:6464", "exportKey": "variables"}}}, "3xs": {"type": "dimension", "value": "{0.primitives.default.spacing.2}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "3. Spacing", "scopes": ["GAP"], "variableId": "VariableID:12:6465", "exportKey": "variables"}}}, "2xs": {"type": "dimension", "value": "{0.primitives.default.spacing.4}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "3. Spacing", "scopes": ["GAP"], "variableId": "VariableID:12:6466", "exportKey": "variables"}}}, "xs": {"type": "dimension", "value": "{0.primitives.default.spacing.8}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "3. Spacing", "scopes": ["GAP"], "variableId": "VariableID:12:6468", "exportKey": "variables"}}}, "s": {"type": "dimension", "value": "{0.primitives.default.spacing.12}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "3. Spacing", "scopes": ["GAP"], "variableId": "VariableID:12:6469", "exportKey": "variables"}}}, "m": {"type": "dimension", "value": "{0.primitives.default.spacing.16}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "3. Spacing", "scopes": ["GAP"], "variableId": "VariableID:12:6470", "exportKey": "variables"}}}, "l": {"type": "dimension", "value": "{0.primitives.default.spacing.20}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "3. Spacing", "scopes": ["GAP"], "variableId": "VariableID:12:6471", "exportKey": "variables"}}}, "2xl": {"type": "dimension", "value": "{0.primitives.default.spacing.24}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "3. Spacing", "scopes": ["GAP"], "variableId": "VariableID:12:6472", "exportKey": "variables"}}}, "3xl": {"type": "dimension", "value": "{0.primitives.default.spacing.32}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "3. Spacing", "scopes": ["GAP"], "variableId": "VariableID:12:6473", "exportKey": "variables"}}}, "4xl": {"type": "dimension", "value": "{0.primitives.default.spacing.40}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "3. Spacing", "scopes": ["GAP"], "variableId": "VariableID:12:6474", "exportKey": "variables"}}}, "5xl": {"type": "dimension", "value": "{0.primitives.default.spacing.48}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "3. Spacing", "scopes": ["GAP"], "variableId": "VariableID:12:6475", "exportKey": "variables"}}}}, "2. typography": {"body": {"s": {"font size": {"type": "dimension", "value": "{0.primitives.default.typography.font size.12}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_SIZE"], "variableId": "VariableID:21:6402", "exportKey": "variables"}}}, "line height": {"type": "dimension", "value": "{0.primitives.default.typography.line height.16}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["LINE_HEIGHT"], "variableId": "VariableID:510:14471", "exportKey": "variables"}}}, "font family": {"type": "string", "value": "{0.primitives.default.typography.font-family.roboto}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_FAMILY"], "variableId": "VariableID:820:998", "exportKey": "variables"}}}, "font weight": {"type": "dimension", "value": "{0.primitives.default.typography.font-weight.regular}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:827:1166", "exportKey": "variables"}}}}, "m": {"font size": {"type": "dimension", "value": "{0.primitives.default.typography.font size.14}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_SIZE"], "variableId": "VariableID:511:16415", "exportKey": "variables"}}}, "line height": {"type": "dimension", "value": "{0.primitives.default.typography.line height.20}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["LINE_HEIGHT"], "variableId": "VariableID:511:16416", "exportKey": "variables"}}}, "font family": {"type": "string", "value": "{0.primitives.default.typography.font-family.roboto}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_FAMILY"], "variableId": "VariableID:820:999", "exportKey": "variables"}}}, "font weight": {"type": "dimension", "value": "{0.primitives.default.typography.font-weight.regular}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:827:1164", "exportKey": "variables"}}}}, "l": {"font size": {"type": "dimension", "value": "{0.primitives.default.typography.font size.16}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_SIZE"], "variableId": "VariableID:511:16418", "exportKey": "variables"}}}, "line height": {"type": "dimension", "value": "{0.primitives.default.typography.line height.24}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["LINE_HEIGHT"], "variableId": "VariableID:511:16419", "exportKey": "variables"}}}, "font family": {"type": "string", "value": "{0.primitives.default.typography.font-family.roboto}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_FAMILY"], "variableId": "VariableID:820:1000", "exportKey": "variables"}}}, "font weight": {"type": "dimension", "value": "{0.primitives.default.typography.font-weight.regular}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:827:1163", "exportKey": "variables"}}}}}, "caption": {"m": {"font size": {"type": "dimension", "value": "{0.primitives.default.typography.font size.12}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_SIZE"], "variableId": "VariableID:511:16421", "exportKey": "variables"}}}, "line height": {"type": "dimension", "value": "{0.primitives.default.typography.line height.18}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["LINE_HEIGHT"], "variableId": "VariableID:511:16422", "exportKey": "variables"}}}, "font family": {"type": "string", "value": "{0.primitives.default.typography.font-family.roboto}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_FAMILY"], "variableId": "VariableID:820:997", "exportKey": "variables"}}}, "font weight": {"type": "dimension", "value": "{0.primitives.default.typography.font-weight.regular}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:827:1171", "exportKey": "variables"}}}}, "s": {"font family": {"type": "string", "value": "{0.primitives.default.typography.font-family.roboto}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_FAMILY"], "variableId": "VariableID:827:1047", "exportKey": "variables"}}}, "font size": {"type": "dimension", "value": "{0.primitives.default.typography.font size.10}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_SIZE"], "variableId": "VariableID:827:1048", "exportKey": "variables"}}}, "line height": {"type": "dimension", "value": "{0.primitives.default.typography.line height.14}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["LINE_HEIGHT"], "variableId": "VariableID:827:1049", "exportKey": "variables"}}}, "font weight": {"type": "dimension", "value": "{0.primitives.default.typography.font-weight.regular}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:827:1172", "exportKey": "variables"}}}}}, "label": {"xl": {"font size": {"type": "dimension", "value": "{0.primitives.default.typography.font size.16}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_SIZE"], "variableId": "VariableID:511:16433", "exportKey": "variables"}}}, "line height": {"type": "dimension", "value": "{0.primitives.default.typography.line height.24}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["LINE_HEIGHT"], "variableId": "VariableID:511:16434", "exportKey": "variables"}}}, "font family": {"type": "string", "value": "{0.primitives.default.typography.font-family.roboto}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_FAMILY"], "variableId": "VariableID:820:995", "exportKey": "variables"}}}, "font weight": {"type": "dimension", "value": "{0.primitives.default.typography.font-weight.medium}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:827:1169", "exportKey": "variables"}}}}, "l": {"font size": {"type": "dimension", "value": "{0.primitives.default.typography.font size.14}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_SIZE"], "variableId": "VariableID:511:16436", "exportKey": "variables"}}}, "line height": {"type": "dimension", "value": "{0.primitives.default.typography.line height.20}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["LINE_HEIGHT"], "variableId": "VariableID:511:16437", "exportKey": "variables"}}}, "font family": {"type": "string", "value": "{0.primitives.default.typography.font-family.roboto}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_FAMILY"], "variableId": "VariableID:820:996", "exportKey": "variables"}}}, "font weight": {"type": "dimension", "value": "{0.primitives.default.typography.font-weight.medium}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:827:1170", "exportKey": "variables"}}}}, "m": {"font family": {"type": "string", "value": "{0.primitives.default.typography.font-family.roboto}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_FAMILY"], "variableId": "VariableID:827:1173", "exportKey": "variables"}}}, "font size": {"type": "dimension", "value": "{0.primitives.default.typography.font size.12}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_SIZE"], "variableId": "VariableID:827:1174", "exportKey": "variables"}}}, "line height": {"type": "dimension", "value": "{0.primitives.default.typography.line height.16}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["LINE_HEIGHT"], "variableId": "VariableID:827:1175", "exportKey": "variables"}}}, "font weight": {"type": "dimension", "value": "{0.primitives.default.typography.font-weight.medium}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:827:1177", "exportKey": "variables"}}}}, "s": {"font family": {"type": "string", "value": "{0.primitives.default.typography.font-family.roboto}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_FAMILY"], "variableId": "VariableID:827:1178", "exportKey": "variables"}}}, "font size": {"type": "dimension", "value": "{0.primitives.default.typography.font size.11}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_SIZE"], "variableId": "VariableID:827:1179", "exportKey": "variables"}}}, "line height": {"type": "dimension", "value": "{0.primitives.default.typography.line height.16}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["LINE_HEIGHT"], "variableId": "VariableID:827:1180", "exportKey": "variables"}}}, "font weight": {"type": "dimension", "value": "{0.primitives.default.typography.font-weight.medium}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:827:1182", "exportKey": "variables"}}}}}, "title": {"m": {"font size": {"type": "dimension", "value": "{0.primitives.default.typography.font size.16}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_SIZE"], "variableId": "VariableID:511:16439", "exportKey": "variables"}}}, "line height": {"type": "dimension", "value": "{0.primitives.default.typography.line height.24}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["LINE_HEIGHT"], "variableId": "VariableID:511:16440", "exportKey": "variables"}}}, "font family": {"type": "string", "value": "{0.primitives.default.typography.font-family.roboto}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_FAMILY"], "variableId": "VariableID:820:1001", "exportKey": "variables"}}}, "font weight": {"type": "dimension", "value": "{0.primitives.default.typography.font-weight.medium}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:827:1162", "exportKey": "variables"}}}}, "l": {"font size": {"type": "dimension", "value": "{0.primitives.default.typography.font size.20}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_SIZE"], "variableId": "VariableID:511:16445", "exportKey": "variables"}}}, "line height": {"type": "dimension", "value": "{0.primitives.default.typography.line height.28}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["LINE_HEIGHT"], "variableId": "VariableID:511:16446", "exportKey": "variables"}}}, "font family": {"type": "string", "value": "{0.primitives.default.typography.font-family.roboto}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_FAMILY"], "variableId": "VariableID:820:1002", "exportKey": "variables"}}}, "font weight": {"type": "dimension", "value": "{0.primitives.default.typography.font-weight.medium}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:827:1161", "exportKey": "variables"}}}}, "s": {"font family": {"type": "string", "value": "{0.primitives.default.typography.font-family.roboto}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_FAMILY"], "variableId": "VariableID:833:3273", "exportKey": "variables"}}}, "font size": {"type": "dimension", "value": "{0.primitives.default.typography.font size.14}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_SIZE"], "variableId": "VariableID:833:3274", "exportKey": "variables"}}}, "line height": {"type": "dimension", "value": "{0.primitives.default.typography.line height.20}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["LINE_HEIGHT"], "variableId": "VariableID:833:3275", "exportKey": "variables"}}}, "font weight": {"type": "dimension", "value": "{0.primitives.default.typography.font-weight.medium}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:833:3277", "exportKey": "variables"}}}}}, "dislay": {"s": {"font size": {"type": "dimension", "value": "{0.primitives.default.typography.font size.32}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_SIZE"], "variableId": "VariableID:511:16448", "exportKey": "variables"}}}, "line height": {"type": "dimension", "value": "{0.primitives.default.typography.line height.44}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["LINE_HEIGHT"], "variableId": "VariableID:511:16449", "exportKey": "variables"}}}, "font family": {"type": "string", "value": "{0.primitives.default.typography.font-family.roboto}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_FAMILY"], "variableId": "VariableID:820:1005", "exportKey": "variables"}}}, "font weight": {"type": "dimension", "value": "{0.primitives.default.typography.font-weight.bold}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:827:1157", "exportKey": "variables"}}}}}, "headline": {"m": {"font size": {"type": "dimension", "value": "{0.primitives.default.typography.font size.28}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_SIZE"], "variableId": "VariableID:511:16451", "exportKey": "variables"}}}, "line height": {"type": "dimension", "value": "{0.primitives.default.typography.line height.36}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["LINE_HEIGHT"], "variableId": "VariableID:511:16452", "exportKey": "variables"}}}, "font family": {"type": "string", "value": "{0.primitives.default.typography.font-family.roboto}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_FAMILY"], "variableId": "VariableID:820:1004", "exportKey": "variables"}}}, "font weight": {"type": "dimension", "value": "{0.primitives.default.typography.font-weight.bold}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:827:1158", "exportKey": "variables"}}}}, "s": {"font size": {"type": "dimension", "value": "{0.primitives.default.typography.font size.24}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_SIZE"], "variableId": "VariableID:511:16454", "exportKey": "variables"}}}, "line height": {"type": "dimension", "value": "{0.primitives.default.typography.line height.32}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["LINE_HEIGHT"], "variableId": "VariableID:511:16455", "exportKey": "variables"}}}, "font family": {"type": "string", "value": "{0.primitives.default.typography.font-family.roboto}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_FAMILY"], "variableId": "VariableID:820:1003", "exportKey": "variables"}}}, "font weight": {"type": "dimension", "value": "{0.primitives.default.typography.font-weight.bold}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "<PERSON><PERSON><PERSON>", "collection": "2. <PERSON><PERSON><PERSON>", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:827:1159", "exportKey": "variables"}}}}}}, "4. radius": {"none": {"type": "dimension", "value": "{0.primitives.mode 1.corner-radius.0}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Mode 1", "collection": "4. <PERSON><PERSON>", "scopes": ["CORNER_RADIUS"], "variableId": "VariableID:27:803", "exportKey": "variables"}}}, "l": {"type": "dimension", "value": "{0.primitives.mode 1.corner-radius.8}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Mode 1", "collection": "4. <PERSON><PERSON>", "scopes": ["CORNER_RADIUS"], "variableId": "VariableID:27:804", "exportKey": "variables"}}}, "m": {"type": "dimension", "value": "{0.primitives.mode 1.corner-radius.4}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Mode 1", "collection": "4. <PERSON><PERSON>", "scopes": ["CORNER_RADIUS"], "variableId": "VariableID:164:39492", "exportKey": "variables"}}}, "s": {"type": "dimension", "value": "{0.primitives.mode 1.corner-radius.2}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Mode 1", "collection": "4. <PERSON><PERSON>", "scopes": ["CORNER_RADIUS"], "variableId": "VariableID:164:86049", "exportKey": "variables"}}}, "round": {"type": "dimension", "value": "{0.primitives.mode 1.corner-radius.9999}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Mode 1", "collection": "4. <PERSON><PERSON>", "scopes": ["CORNER_RADIUS"], "variableId": "VariableID:906:17181", "exportKey": "variables"}}}}, "1. color theme": {"bg": {"main": {"tertiary": {"type": "color", "value": "{0.primitives.value.color.base.white}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13662", "exportKey": "variables"}}}, "secondary": {"type": "color", "value": "{0.primitives.value.color.neutral.50}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13663", "exportKey": "variables"}}}, "primary": {"type": "color", "value": "{0.primitives.value.color.neutral.100}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13667", "exportKey": "variables"}}}, "secondary-hover": {"type": "color", "value": "{0.primitives.value.color.base.white}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13793", "exportKey": "variables"}}}, "secondary-press": {"type": "color", "value": "{0.primitives.value.color.neutral.100}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13794", "exportKey": "variables"}}}, "tertiary-hover": {"type": "color", "value": "{0.primitives.value.color.neutral.100}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13796", "exportKey": "variables"}}}, "tertiary-press": {"type": "color", "value": "{0.primitives.value.color.neutral.200}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13797", "exportKey": "variables"}}}, "primary-hover": {"type": "color", "value": "{0.primitives.value.color.neutral.50}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:515:13", "exportKey": "variables"}}}, "primary-press": {"type": "color", "value": "{0.primitives.value.color.neutral.200}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:515:14", "exportKey": "variables"}}}, "quaternary-hover": {"type": "color", "value": "{0.primitives.value.color.neutral.200}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1441:12600", "exportKey": "variables"}}}, "quaternary-press": {"type": "color", "value": "{0.primitives.value.color.neutral.300}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1441:12601", "exportKey": "variables"}}}}, "disable": {"primary": {"type": "color", "value": "{0.primitives.value.color.neutral.100}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13710", "exportKey": "variables"}}}}, "positive": {"primary": {"type": "color", "value": "{0.primitives.value.color.green.600}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13735", "exportKey": "variables"}}}, "secondary": {"type": "color", "value": "{0.primitives.value.color.green.100}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13736", "exportKey": "variables"}}}, "primary-hover": {"type": "color", "value": "{0.primitives.value.color.green.500}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13799", "exportKey": "variables"}}}, "primary-press": {"type": "color", "value": "{0.primitives.value.color.green.700}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13800", "exportKey": "variables"}}}, "secondary-hover": {"type": "color", "value": "{0.primitives.value.color.green.50}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13802", "exportKey": "variables"}}}, "secondary-press": {"type": "color", "value": "{0.primitives.value.color.green.200}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13803", "exportKey": "variables"}}}, "tertiary": {"type": "color", "value": "{0.primitives.value.color.green.50}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1252:1630", "exportKey": "variables"}}}}, "negative": {"primary": {"type": "color", "value": "{0.primitives.value.color.red.700}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13737", "exportKey": "variables"}}}, "secondary": {"type": "color", "value": "{0.primitives.value.color.red.100}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13738", "exportKey": "variables"}}}, "primary-hover": {"type": "color", "value": "{0.primitives.value.color.red.600}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13810", "exportKey": "variables"}}}, "primary-press": {"type": "color", "value": "{0.primitives.value.color.red.800}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13811", "exportKey": "variables"}}}, "secondary-hover": {"type": "color", "value": "{0.primitives.value.color.red.50}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13813", "exportKey": "variables"}}}, "secondary-press": {"type": "color", "value": "{0.primitives.value.color.red.200}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13814", "exportKey": "variables"}}}, "tertiary": {"type": "color", "value": "{0.primitives.value.color.red.50}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1252:1631", "exportKey": "variables"}}}}, "warning": {"primary": {"type": "color", "value": "{0.primitives.value.color.orange.500}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13739", "exportKey": "variables"}}}, "secondary": {"type": "color", "value": "{0.primitives.value.color.orange.100}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13740", "exportKey": "variables"}}}, "primary-hover": {"type": "color", "value": "{0.primitives.value.color.orange.400}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13816", "exportKey": "variables"}}}, "primary-press": {"type": "color", "value": "{0.primitives.value.color.orange.600}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13817", "exportKey": "variables"}}}, "secondary-hover": {"type": "color", "value": "{0.primitives.value.color.orange.50}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13819", "exportKey": "variables"}}}, "secondary-press": {"type": "color", "value": "{0.primitives.value.color.orange.200}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13820", "exportKey": "variables"}}}, "tertiary": {"type": "color", "value": "{0.primitives.value.color.orange.50}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1252:1628", "exportKey": "variables"}}}}, "info": {"primary": {"type": "color", "value": "{0.primitives.value.color.blue.500}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13741", "exportKey": "variables"}}}, "secondary": {"type": "color", "value": "{0.primitives.value.color.blue.100}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13742", "exportKey": "variables"}}}, "primary-hover": {"type": "color", "value": "{0.primitives.value.color.blue.400}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13822", "exportKey": "variables"}}}, "primary-press": {"type": "color", "value": "{0.primitives.value.color.blue.600}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13823", "exportKey": "variables"}}}, "secondary-hover": {"type": "color", "value": "{0.primitives.value.color.blue.50}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13825", "exportKey": "variables"}}}, "secondary-press": {"type": "color", "value": "{0.primitives.value.color.blue.200}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13826", "exportKey": "variables"}}}, "tertiary": {"type": "color", "value": "{0.primitives.value.color.blue.50}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1252:1632", "exportKey": "variables"}}}}, "brand-01": {"primary": {"type": "color", "value": "{0.primitives.value.color.brand_primary.500}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13783", "exportKey": "variables"}}}, "primary-hover": {"type": "color", "value": "{0.primitives.value.color.brand_primary.400}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13786", "exportKey": "variables"}}}, "primary-press": {"type": "color", "value": "{0.primitives.value.color.brand_primary.600}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13788", "exportKey": "variables"}}}, "secondary-hover": {"type": "color", "value": "{0.primitives.value.color.brand_primary.100}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13790", "exportKey": "variables"}}}, "secondary-press": {"type": "color", "value": "{0.primitives.value.color.brand_primary.300}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13791", "exportKey": "variables"}}}, "tertiary-hover": {"type": "color", "value": "{0.primitives.value.color.brand_primary.25}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:93:8688", "exportKey": "variables"}}}, "tertiary-press": {"type": "color", "value": "{0.primitives.value.color.brand_primary.100}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:93:8689", "exportKey": "variables"}}}, "secondary": {"type": "color", "value": "{0.primitives.value.color.brand_primary.200}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:405:36358", "exportKey": "variables"}}}, "tertiary": {"type": "color", "value": "{0.primitives.value.color.brand_primary.50}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:910:17198", "exportKey": "variables"}}}, "primary_subtle": {"type": "color", "value": "{0.primitives.value.color.brand_primary.300}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1844:1665", "exportKey": "variables"}}}}, "overlay": {"type": "color", "value": "#00000080", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:519:2", "exportKey": "variables"}}}, "solid": {"primary": {"type": "color", "value": "{0.primitives.value.color.neutral.700}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1086:2247", "exportKey": "variables"}}}, "primary-hover": {"type": "color", "value": "{0.primitives.value.color.neutral.600}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1086:2248", "exportKey": "variables"}}}, "primary-press": {"type": "color", "value": "{0.primitives.value.color.neutral.800}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1086:2249", "exportKey": "variables"}}}}, "non-opaque": {"none": {"description": "", "type": "color", "value": "{0.primitives.value.color.opacity.black.0}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Colors", "scopes": ["FRAME_FILL", "SHAPE_FILL"], "variableId": "VariableID:4010:3430", "exportKey": "variables"}}}, "default": {"description": "", "type": "color", "value": "{0.primitives.value.color.opacity.black.3}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Colors", "scopes": ["FRAME_FILL", "SHAPE_FILL"], "variableId": "VariableID:4010:5908", "exportKey": "variables"}}}, "hovered": {"description": "", "type": "color", "value": "{0.primitives.value.color.opacity.black.5}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Colors", "scopes": ["FRAME_FILL", "SHAPE_FILL"], "variableId": "VariableID:4010:5909", "exportKey": "variables"}}}, "pressed": {"description": "", "type": "color", "value": "{0.primitives.value.color.opacity.black.8}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Colors", "scopes": ["FRAME_FILL", "SHAPE_FILL"], "variableId": "VariableID:4010:5910", "exportKey": "variables"}}}, "focus": {"description": "", "type": "color", "value": "{0.primitives.value.color.opacity.black.3}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Colors", "scopes": ["FRAME_FILL", "SHAPE_FILL"], "variableId": "VariableID:4010:5911", "exportKey": "variables"}}}, "inverse-default": {"description": "", "type": "color", "value": "{0.primitives.value.color.opacity.white.4}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Colors", "scopes": ["FRAME_FILL", "SHAPE_FILL"], "variableId": "VariableID:4010:5912", "exportKey": "variables"}}}, "inverse-hovered": {"description": "", "type": "color", "value": "{0.primitives.value.color.opacity.white.7}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Colors", "scopes": ["FRAME_FILL", "SHAPE_FILL"], "variableId": "VariableID:4010:5913", "exportKey": "variables"}}}, "inverse-pressed": {"description": "", "type": "color", "value": "{0.primitives.value.color.base.black}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Colors", "scopes": ["FRAME_FILL", "SHAPE_FILL"], "variableId": "VariableID:4010:5914", "exportKey": "variables"}}}, "inverse-focus": {"description": "", "type": "color", "value": "{0.primitives.value.color.opacity.white.4}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Colors", "scopes": ["FRAME_FILL", "SHAPE_FILL"], "variableId": "VariableID:4010:5915", "exportKey": "variables"}}}}}, "border": {"main": {"primary": {"type": "color", "value": "{0.primitives.value.color.neutral.300}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13711", "exportKey": "variables"}}}, "secondary": {"type": "color", "value": "{0.primitives.value.color.neutral.200}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13712", "exportKey": "variables"}}}, "tertiary": {"type": "color", "value": "{0.primitives.value.color.neutral.100}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13713", "exportKey": "variables"}}}, "primary-hover": {"type": "color", "value": "{0.primitives.value.color.neutral.200}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13762", "exportKey": "variables"}}}, "primary-press": {"type": "color", "value": "{0.primitives.value.color.neutral.400}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13847", "exportKey": "variables"}}}, "tertiary-hover": {"type": "color", "value": "{0.primitives.value.color.neutral.50}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1374:12519", "exportKey": "variables"}}}}, "disable": {"primary": {"type": "color", "value": "{0.primitives.value.color.neutral.300}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13714", "exportKey": "variables"}}}}, "positive": {"primary": {"type": "color", "value": "{0.primitives.value.color.green.300}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13743", "exportKey": "variables"}}}, "primary-hover": {"type": "color", "value": "{0.primitives.value.color.green.200}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13830", "exportKey": "variables"}}}, "primary-press": {"type": "color", "value": "{0.primitives.value.color.green.400}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13831", "exportKey": "variables"}}}}, "negative": {"primary": {"type": "color", "value": "{0.primitives.value.color.red.300}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13744", "exportKey": "variables"}}}, "primary-hover": {"type": "color", "value": "{0.primitives.value.color.red.200}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13833", "exportKey": "variables"}}}, "primary-press": {"type": "color", "value": "{0.primitives.value.color.red.400}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13834", "exportKey": "variables"}}}}, "warning": {"primary": {"type": "color", "value": "{0.primitives.value.color.orange.300}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13745", "exportKey": "variables"}}}, "primary-hover": {"type": "color", "value": "{0.primitives.value.color.orange.200}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13836", "exportKey": "variables"}}}, "primary-press": {"type": "color", "value": "{0.primitives.value.color.orange.400}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13837", "exportKey": "variables"}}}}, "info": {"primary": {"type": "color", "value": "{0.primitives.value.color.blue.300}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13746", "exportKey": "variables"}}}, "primary-hover": {"type": "color", "value": "{0.primitives.value.color.blue.200}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13839", "exportKey": "variables"}}}, "primary-press": {"type": "color", "value": "{0.primitives.value.color.blue.400}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13840", "exportKey": "variables"}}}}, "brand": {"tertiary": {"type": "color", "value": "{0.primitives.value.color.brand_primary.300}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13755", "exportKey": "variables"}}}, "tertiary-hover": {"type": "color", "value": "{0.primitives.value.color.brand_primary.200}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13763", "exportKey": "variables"}}}, "tertiary-press": {"type": "color", "value": "{0.primitives.value.color.brand_primary.400}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13828", "exportKey": "variables"}}}, "primary-hover": {"type": "color", "value": "{0.primitives.value.color.brand_primary.400}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13861", "exportKey": "variables"}}}, "primary-press": {"type": "color", "value": "{0.primitives.value.color.brand_primary.600}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13862", "exportKey": "variables"}}}, "primary": {"type": "color", "value": "{0.primitives.value.color.brand_primary.500}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13864", "exportKey": "variables"}}}, "quaternary-hover": {"type": "color", "value": "{0.primitives.value.color.brand_primary.50}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:164:44542", "exportKey": "variables"}}}, "quaternary-press": {"type": "color", "value": "{0.primitives.value.color.brand_primary.200}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:164:44543", "exportKey": "variables"}}}, "quaternary": {"type": "color", "value": "{0.primitives.value.color.brand_primary.100}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:164:45313", "exportKey": "variables"}}}, "secondary": {"type": "color", "value": "{0.primitives.value.color.brand_primary.400}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1098:2100", "exportKey": "variables"}}}}, "solid": {"primary": {"type": "color", "value": "{0.primitives.value.color.neutral.700}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:2566:28", "exportKey": "variables"}}}, "secondary": {"type": "color", "value": "{0.primitives.value.color.neutral.400}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:2590:1801", "exportKey": "variables"}}}, "secondary-hover": {"type": "color", "value": "{0.primitives.value.color.neutral.500}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:2590:1802", "exportKey": "variables"}}}, "secondary-press": {"type": "color", "value": "{0.primitives.value.color.neutral.600}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:2590:1803", "exportKey": "variables"}}}}}, "content": {"main": {"primary": {"type": "color", "value": "{0.primitives.value.color.neutral.900}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13747", "exportKey": "variables"}}}, "secondary": {"type": "color", "value": "{0.primitives.value.color.neutral.700}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13748", "exportKey": "variables"}}}, "tertiary": {"type": "color", "value": "{0.primitives.value.color.neutral.600}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13750", "exportKey": "variables"}}}}, "positive": {"primary": {"type": "color", "value": "{0.primitives.value.color.green.700}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13749", "exportKey": "variables"}}}, "secondary": {"type": "color", "value": "{0.primitives.value.color.green.600}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:124:464", "exportKey": "variables"}}}}, "negative": {"primary": {"type": "color", "value": "{0.primitives.value.color.red.700}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13751", "exportKey": "variables"}}}, "secondary": {"type": "color", "value": "{0.primitives.value.color.red.600}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:124:465", "exportKey": "variables"}}}}, "warning": {"primary": {"type": "color", "value": "{0.primitives.value.color.orange.700}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13752", "exportKey": "variables"}}}, "secondary": {"type": "color", "value": "{0.primitives.value.color.orange.600}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:124:466", "exportKey": "variables"}}}}, "info": {"primary": {"type": "color", "value": "{0.primitives.value.color.blue.700}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13753", "exportKey": "variables"}}}, "secondary": {"type": "color", "value": "{0.primitives.value.color.blue.600}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:124:467", "exportKey": "variables"}}}}, "disable": {"primary": {"type": "color", "value": "{0.primitives.value.color.neutral.400}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13754", "exportKey": "variables"}}}, "secondary": {"type": "color", "value": "{0.primitives.value.color.neutral.50}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:118:6834", "exportKey": "variables"}}}}, "on-special": {"primary": {"type": "color", "value": "{0.primitives.value.color.base.white}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13756", "exportKey": "variables"}}}, "secondary": {"type": "color", "value": "{0.primitives.value.color.neutral.300}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:367:30982", "exportKey": "variables"}}}}, "brand-01": {"primary": {"type": "color", "value": "{0.primitives.value.color.brand_primary.600}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:84:13860", "exportKey": "variables"}}}, "tertiary": {"type": "color", "value": "{0.primitives.value.color.brand_primary.300}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:946:994", "exportKey": "variables"}}}, "secondary": {"type": "color", "value": "{0.primitives.value.color.brand_primary.400}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1098:2021", "exportKey": "variables"}}}}, "placeholder": {"type": "color", "value": "{0.primitives.value.color.neutral.400}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:93:8234", "exportKey": "variables"}}}, "brand-02": {"primary": {"type": "color", "value": "{0.primitives.value.color.brand_secondary.400}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:272:24820", "exportKey": "variables"}}}, "secondary": {"type": "color", "value": "{0.primitives.value.color.brand_secondary.50}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:272:24821", "exportKey": "variables"}}}}}, "effects": {"shadow": {"primary": {"type": "color", "value": "#0000001a", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:93:4880", "exportKey": "variables"}}}, "secondary": {"type": "color", "value": "#0000000a", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:93:4881", "exportKey": "variables"}}}}, "focus-ring": {"brand": {"type": "color", "value": "{0.primitives.value.color.brand_primary.50}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:93:7493", "exportKey": "variables"}}}, "error": {"type": "color", "value": "{0.primitives.value.color.red.50}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:93:7494", "exportKey": "variables"}}}, "gray": {"type": "color", "value": "{0.primitives.value.color.neutral.200}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Value", "collection": "1. Color Theme", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:496:404", "exportKey": "variables"}}}}}}, "5. border & divider": {"s": {"type": "dimension", "value": "{0.primitives.mode 1.border & divider.1}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Mode 1", "collection": "5. Border & Divider", "scopes": ["WIDTH_HEIGHT", "STROKE_FLOAT"], "variableId": "VariableID:511:16466", "exportKey": "variables"}}}, "m": {"type": "dimension", "value": "{0.primitives.mode 1.border & divider.2}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Mode 1", "collection": "5. Border & Divider", "scopes": ["WIDTH_HEIGHT", "STROKE_FLOAT"], "variableId": "VariableID:511:16468", "exportKey": "variables"}}}, "l": {"type": "dimension", "value": "{0.primitives.mode 1.border & divider.3}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Mode 1", "collection": "5. Border & Divider", "scopes": ["WIDTH_HEIGHT", "STROKE_FLOAT"], "variableId": "VariableID:833:3299", "exportKey": "variables"}}}, "xs": {"type": "dimension", "value": "{0.primitives.mode 1.border & divider.0,5}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Mode 1", "collection": "5. Border & Divider", "scopes": ["WIDTH_HEIGHT", "STROKE_FLOAT"], "variableId": "VariableID:910:17200", "exportKey": "variables"}}}}, "6. breakpoints": {"desktop": {"type": "dimension", "value": "{0.primitives.mode 1.breakpoints.1440}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Mode 1", "collection": "6. Breakpoints", "scopes": ["WIDTH_HEIGHT"], "variableId": "VariableID:511:16470", "exportKey": "variables"}}}, "tablet": {"type": "dimension", "value": "{0.primitives.mode 1.breakpoints.768}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Mode 1", "collection": "6. Breakpoints", "scopes": ["WIDTH_HEIGHT"], "variableId": "VariableID:511:16471", "exportKey": "variables"}}}, "mobile": {"type": "dimension", "value": "{0.primitives.mode 1.breakpoints.375}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"mode": "Mode 1", "collection": "6. Breakpoints", "scopes": ["WIDTH_HEIGHT"], "variableId": "VariableID:511:16472", "exportKey": "variables"}}}}, "typography": {"display": {"display s": {"fontSize": {"type": "dimension", "value": 32}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Roboto"}, "fontWeight": {"type": "number", "value": 700}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 44}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}}, "headline": {"headline m": {"fontSize": {"type": "dimension", "value": 28}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Roboto"}, "fontWeight": {"type": "number", "value": 700}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 36}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "headline s": {"fontSize": {"type": "dimension", "value": 24}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Roboto"}, "fontWeight": {"type": "number", "value": 700}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 32}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}}, "title": {"title l": {"fontSize": {"type": "dimension", "value": 20}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Roboto"}, "fontWeight": {"type": "number", "value": 500}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 28}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "title m": {"fontSize": {"type": "dimension", "value": 16}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Roboto"}, "fontWeight": {"type": "number", "value": 500}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 24}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "title s": {"fontSize": {"type": "dimension", "value": 14}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Roboto"}, "fontWeight": {"type": "number", "value": 500}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 20}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}}, "label": {"label xl": {"fontSize": {"type": "dimension", "value": 16}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Roboto"}, "fontWeight": {"type": "number", "value": 500}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 24}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "label l": {"fontSize": {"type": "dimension", "value": 14}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Roboto"}, "fontWeight": {"type": "number", "value": 500}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 20}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "label m": {"fontSize": {"type": "dimension", "value": 12}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Roboto"}, "fontWeight": {"type": "number", "value": 500}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 16}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "label s": {"fontSize": {"type": "dimension", "value": 11}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Roboto"}, "fontWeight": {"type": "number", "value": 500}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 16}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}}, "body": {"body l": {"fontSize": {"type": "dimension", "value": 16}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Roboto"}, "fontWeight": {"type": "number", "value": 400}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 24}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "body m": {"fontSize": {"type": "dimension", "value": 14}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Roboto"}, "fontWeight": {"type": "number", "value": 400}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 20}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "body s": {"fontSize": {"type": "dimension", "value": 12}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Roboto"}, "fontWeight": {"type": "number", "value": 400}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 16}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}}, "caption": {"caption m": {"fontSize": {"type": "dimension", "value": 12}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Roboto"}, "fontWeight": {"type": "number", "value": 400}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 18}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "caption s": {"fontSize": {"type": "dimension", "value": 10}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Roboto"}, "fontWeight": {"type": "number", "value": 400}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 14}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}}}}