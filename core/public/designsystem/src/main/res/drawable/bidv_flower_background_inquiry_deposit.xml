<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="343dp"
    android:height="167dp"
    android:viewportWidth="343"
    android:viewportHeight="167">
  <path
      android:pathData="M386.57,19.04C384.06,14.77 380.5,11.52 376.85,8.25C365.13,-2.16 350.42,-9.54 335.42,-12.9C334.3,-13.14 333.58,-14.16 333.69,-15.26C335.28,-29.5 333.55,-44.06 328.34,-57.66C326.56,-56.86 324.31,-55.68 321.61,-53.97L321.41,-53.84C326.67,-39.41 327.76,-23.96 324.84,-9.29C324.58,-8 325.48,-6.76 326.81,-6.56C347.17,-3.7 366.46,6.65 379.94,22.43C368.22,32.37 354.27,39.74 339.32,43.38C338.26,43.63 337.53,44.59 337.59,45.69C338.51,63.28 334.41,80.96 326.03,96.46C377.04,114.49 415.55,63.23 386.6,19.07L386.57,19.04Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="359.19"
          android:startY="-56.97"
          android:endX="358.59"
          android:endY="97.54"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M326.52,-70C308.78,-62.37 293.16,-48.77 283.47,-32.53C282.89,-31.57 281.69,-31.15 280.66,-31.62C267.74,-37.43 253.04,-40.35 238.42,-39.72C238.59,-37.79 238.95,-35.26 239.65,-32.14V-31.98C255.21,-32.47 270.36,-28.73 283.61,-21.43C284.78,-20.8 286.26,-21.24 286.84,-22.45C295.75,-40.71 311.82,-55.6 331.03,-63.5L332.12,-60.89C337.55,-47.51 339.69,-33 338.72,-18.76C338.63,-17.69 339.33,-16.73 340.39,-16.42C354,-12.79 366.64,-6.24 377.56,2.74H377.59C380.34,5.08 383.04,7.5 385.58,10.12C389.56,5.19 392.54,-1.06 394.21,-6.79C409.8,-48.11 366.7,-87.01 326.52,-70.03V-70Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="249.42"
          android:startY="-69.36"
          android:endX="396.41"
          android:endY="-18.04"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M314.5,81.81L314.64,81.53C313.97,81.09 313.33,80.65 312.69,80.18C300.66,71.51 291.5,60.33 285.38,47.39C284.82,46.21 283.34,45.71 282.17,46.35C264.05,55.85 242.19,58.79 222,53.78C225.6,38.89 232.45,24.87 242.38,13.2C243.08,12.37 243.11,11.16 242.41,10.34C231.25,-3.32 224.06,-19.98 221.64,-37.54V-37.71C216.49,-36.38 211.14,-33.82 206.86,-30.99C157.71,1 185.92,66.78 242.11,63.94C255.05,63.94 267.53,61.32 278.95,56.48C280,56.04 281.26,56.42 281.81,57.42C288.8,69.61 298.94,80.51 311.11,88.5C312.11,86.85 313.3,84.64 314.53,81.81H314.5Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="284.88"
          android:startY="105.52"
          android:endX="196.48"
          android:endY="-19.91"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M367.82,16.37L367.68,16.26C355.43,25.68 340.92,31.43 325.91,33.27C324.58,33.44 323.66,34.65 323.91,35.97C327.42,55.91 323.63,77.52 312.41,94.86L310.07,93.38C297.65,85.53 287.49,75.32 279.83,63.17C279.24,62.27 278.1,61.88 277.07,62.27C260.53,68.54 242.16,70.19 224.67,67.06C222.08,116.56 280.08,142.13 314.22,104.47C329.09,88.56 334.46,63.37 332.62,42.17C332.54,41.07 333.35,39.99 334.43,39.74C348.38,36.91 362.03,30.69 373.5,21.63C372.16,20.2 370.3,18.41 367.82,16.37Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="381.91"
          android:startY="51.58"
          android:endX="233.94"
          android:endY="99.19"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M315.15,-79.99C315.15,-79.99 315.15,-79.99 315.13,-80.02C280.46,-117.6 219.84,-87.95 226.25,-38.5C226.25,-38.5 226.25,-38.5 226.25,-38.47C228.56,-20.58 236.66,-2.96 248.55,10.34C249.3,11.2 249.27,12.46 248.55,13.29C239.03,23.67 231.65,36.61 227.78,50.57C229.67,51.01 232.18,51.45 235.3,51.75H235.57C239.89,37.02 248.19,23.89 259.3,13.65C260.27,12.74 260.27,11.22 259.33,10.31C258.05,9.08 256.79,7.81 255.59,6.46C243.01,-7.03 234.99,-25.42 233.54,-44.06C248.86,-45.33 264.73,-43.12 278.87,-37.43C279.9,-37.01 281.04,-37.37 281.66,-38.28C291.32,-52.95 305.07,-64.87 321.2,-72.61C319.5,-75.2 317.41,-77.7 315.18,-79.99H315.15Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="203.06"
          android:startY="31.63"
          android:endX="294.8"
          android:endY="-94.5"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
