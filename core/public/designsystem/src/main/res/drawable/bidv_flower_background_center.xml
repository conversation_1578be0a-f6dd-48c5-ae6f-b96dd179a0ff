<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="192dp"
    android:height="92dp"
    android:viewportWidth="192"
    android:viewportHeight="92">
  <path
      android:pathData="M240.51,35.81C237.62,30.89 233.51,27.14 229.3,23.37C215.79,11.37 198.83,2.86 181.53,-1.02C180.24,-1.3 179.41,-2.48 179.54,-3.75C181.37,-20.16 179.37,-36.95 173.37,-52.64C171.32,-51.72 168.71,-50.35 165.6,-48.38L165.38,-48.23C171.44,-31.59 172.7,-13.78 169.32,3.14C169.04,4.63 170.06,6.06 171.6,6.29C195.08,9.59 217.33,21.52 232.87,39.72C219.35,51.18 203.26,59.69 186.02,63.88C184.8,64.16 183.97,65.27 184.03,66.54C185.09,86.83 180.37,107.21 170.71,125.09C229.53,145.88 273.93,86.77 240.54,35.84L240.51,35.81Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient
          android:startX="208.94"
          android:startY="-51.84"
          android:endX="208.25"
          android:endY="126.33"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M171.27,-66.86C150.81,-58.07 132.8,-42.39 121.63,-23.66C120.95,-22.54 119.57,-22.07 118.38,-22.61C103.49,-29.31 86.53,-32.67 69.67,-31.94C69.87,-29.72 70.28,-26.8 71.09,-23.21V-23.02C89.04,-23.59 106.5,-19.27 121.79,-10.86C123.14,-10.13 124.84,-10.64 125.51,-12.04C135.79,-33.08 154.31,-50.26 176.47,-59.37L177.72,-56.36C183.98,-40.93 186.45,-24.2 185.33,-7.78C185.23,-6.54 186.04,-5.43 187.26,-5.08C202.96,-0.89 217.54,6.66 230.12,17.01H230.15C233.33,19.71 236.45,22.51 239.37,25.52C243.96,19.84 247.4,12.63 249.32,6.03C267.3,-41.63 217.6,-86.49 171.27,-66.9V-66.86Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient
          android:startX="82.35"
          android:startY="-66.13"
          android:endX="251.86"
          android:endY="-6.95"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M157.41,108.19L157.57,107.88C156.8,107.37 156.06,106.86 155.32,106.32C141.45,96.32 130.89,83.43 123.82,68.51C123.18,67.14 121.48,66.57 120.13,67.3C99.23,78.25 74.02,81.65 50.74,75.87C54.88,58.7 62.78,42.54 74.25,29.08C75.05,28.12 75.08,26.73 74.28,25.78C61.4,10.03 53.12,-9.18 50.33,-29.43V-29.63C44.39,-28.1 38.22,-25.15 33.28,-21.88C-23.4,15.01 9.13,90.86 73.93,87.59C88.86,87.59 103.24,84.57 116.4,78.98C117.63,78.48 119.07,78.92 119.71,80.06C127.77,94.13 139.46,106.7 153.49,115.91C154.65,114 156.03,111.46 157.44,108.19H157.41Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient
          android:startX="123.25"
          android:startY="135.54"
          android:endX="21.31"
          android:endY="-9.1"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M218.89,32.73L218.73,32.61C204.6,43.46 187.87,50.1 170.57,52.23C169.02,52.42 167.96,53.81 168.25,55.34C172.3,78.32 167.93,103.25 154.99,123.25L152.29,121.53C137.98,112.49 126.25,100.71 117.43,86.71C116.75,85.66 115.43,85.21 114.25,85.66C95.17,92.9 73.98,94.8 53.82,91.18C50.83,148.26 117.71,177.76 157.08,134.33C174.23,115.98 180.42,86.93 178.3,62.48C178.21,61.21 179.14,59.97 180.39,59.69C196.48,56.42 212.21,49.24 225.44,38.8C223.9,37.15 221.75,35.08 218.89,32.73Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient
          android:startX="235.14"
          android:startY="73.34"
          android:endX="64.51"
          android:endY="128.23"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M158.16,-78.38C158.16,-78.38 158.16,-78.38 158.13,-78.42C118.15,-121.75 48.25,-87.56 55.64,-30.54C55.64,-30.54 55.64,-30.54 55.64,-30.51C58.3,-9.87 67.64,10.45 81.36,25.78C82.22,26.77 82.19,28.23 81.36,29.18C70.37,41.15 61.87,56.07 57.4,72.17C59.59,72.67 62.48,73.18 66.07,73.53H66.39C71.37,56.55 80.94,41.4 93.75,29.59C94.87,28.54 94.87,26.8 93.78,25.75C92.3,24.32 90.86,22.86 89.48,21.31C74.97,5.75 65.72,-15.46 64.05,-36.95C81.71,-38.41 100.01,-35.87 116.32,-29.3C117.51,-28.83 118.83,-29.24 119.53,-30.29C130.67,-47.21 146.54,-60.95 165.13,-69.88C163.17,-72.86 160.76,-75.75 158.19,-78.38H158.16Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient
          android:startX="28.9"
          android:startY="50.33"
          android:endX="134.68"
          android:endY="-95.12"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
