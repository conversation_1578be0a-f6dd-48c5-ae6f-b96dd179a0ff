<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="311dp"
    android:height="68dp"
    android:viewportWidth="311"
    android:viewportHeight="68">
  <path
      android:pathData="M354.57,14.04C352.06,9.77 348.5,6.52 344.85,3.25C333.13,-7.16 318.42,-14.54 303.42,-17.9C302.3,-18.14 301.58,-19.16 301.69,-20.26C303.28,-34.5 301.55,-49.06 296.34,-62.66C294.56,-61.86 292.31,-60.68 289.61,-58.97L289.41,-58.84C294.67,-44.41 295.76,-28.96 292.84,-14.29C292.58,-13 293.48,-11.76 294.81,-11.56C315.17,-8.7 334.46,1.65 347.94,17.43C336.22,27.36 322.27,34.74 307.32,38.38C306.26,38.63 305.53,39.59 305.59,40.69C306.51,58.28 302.41,75.96 294.03,91.46C345.04,109.49 383.55,58.23 354.6,14.07L354.57,14.04Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="327.19"
          android:startY="-61.97"
          android:endX="326.59"
          android:endY="92.54"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M294.52,-75C276.78,-67.37 261.16,-53.77 251.47,-37.53C250.89,-36.57 249.69,-36.15 248.66,-36.62C235.74,-42.43 221.04,-45.35 206.42,-44.72C206.59,-42.79 206.95,-40.26 207.65,-37.14V-36.98C223.21,-37.47 238.36,-33.73 251.61,-26.43C252.78,-25.8 254.26,-26.24 254.84,-27.45C263.75,-45.71 279.82,-60.6 299.03,-68.5L300.12,-65.89C305.55,-52.51 307.69,-38 306.72,-23.76C306.63,-22.69 307.33,-21.73 308.39,-21.42C322,-17.79 334.64,-11.24 345.56,-2.26H345.59C348.34,0.08 351.04,2.5 353.58,5.12C357.56,0.19 360.54,-6.06 362.21,-11.79C377.8,-53.11 334.7,-92.01 294.52,-75.03V-75Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="217.42"
          android:startY="-74.36"
          android:endX="364.41"
          android:endY="-23.04"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M282.5,76.81L282.64,76.53C281.97,76.09 281.33,75.65 280.69,75.18C268.66,66.51 259.5,55.33 253.38,42.39C252.82,41.21 251.34,40.71 250.18,41.35C232.05,50.85 210.19,53.79 190,48.78C193.6,33.89 200.45,19.87 210.38,8.2C211.08,7.37 211.11,6.16 210.41,5.34C199.25,-8.32 192.06,-24.98 189.64,-42.54V-42.71C184.49,-41.38 179.14,-38.82 174.86,-35.99C125.71,-4 153.92,61.78 210.11,58.94C223.05,58.94 235.53,56.32 246.95,51.48C248,51.04 249.26,51.42 249.81,52.42C256.8,64.61 266.94,75.51 279.11,83.5C280.11,81.85 281.3,79.64 282.53,76.81H282.5Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="252.88"
          android:startY="100.52"
          android:endX="164.48"
          android:endY="-24.91"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M335.82,11.37L335.68,11.26C323.43,20.68 308.92,26.43 293.91,28.27C292.58,28.44 291.66,29.65 291.91,30.97C295.42,50.91 291.63,72.52 280.41,89.86L278.07,88.38C265.65,80.53 255.49,70.32 247.83,58.17C247.24,57.27 246.1,56.88 245.07,57.27C228.53,63.54 210.16,65.19 192.67,62.06C190.08,111.56 248.08,137.13 282.22,99.47C297.09,83.56 302.46,58.37 300.62,37.17C300.54,36.07 301.35,34.99 302.43,34.74C316.38,31.91 330.03,25.69 341.5,16.63C340.16,15.2 338.3,13.41 335.82,11.37Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="349.91"
          android:startY="46.58"
          android:endX="201.94"
          android:endY="94.19"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M283.15,-84.99C283.15,-84.99 283.15,-84.99 283.13,-85.02C248.46,-122.6 187.84,-92.95 194.25,-43.5C194.25,-43.5 194.25,-43.5 194.25,-43.47C196.56,-25.58 204.66,-7.96 216.55,5.34C217.3,6.2 217.27,7.46 216.55,8.29C207.03,18.67 199.65,31.61 195.78,45.57C197.67,46.01 200.18,46.45 203.3,46.75H203.57C207.89,32.02 216.19,18.89 227.3,8.65C228.27,7.74 228.27,6.22 227.33,5.31C226.04,4.08 224.79,2.81 223.59,1.46C211.01,-12.03 202.99,-30.42 201.54,-49.06C216.86,-50.33 232.73,-48.12 246.87,-42.43C247.9,-42.01 249.04,-42.37 249.66,-43.28C259.32,-57.95 273.07,-69.87 289.2,-77.61C287.5,-80.2 285.41,-82.7 283.18,-84.99H283.15Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="171.06"
          android:startY="26.63"
          android:endX="262.8"
          android:endY="-99.5"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
