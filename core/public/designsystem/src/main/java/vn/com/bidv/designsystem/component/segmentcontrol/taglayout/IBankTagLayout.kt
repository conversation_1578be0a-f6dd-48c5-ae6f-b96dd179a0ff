package vn.com.bidv.designsystem.component.segmentcontrol.taglayout

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.toMutableStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.datadisplay.badge.IBankBadgeLabel
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelColor
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelSize
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelType
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBColorScheme
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.IBTypography
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.log.BLogUtil

enum class IBankTagLayoutMode {
    SINGLE_ROW, MULTIPLE_ROWS
}

enum class IBankTagSelectionMode {
    NONE, SINGLE, MULTIPLE, SINGLE_AT_LEAST_ONE_SELECT
}

enum class TagState {
    DEFAULT, SELECTED
}

data class IBankTagStateStyle(
    val textColor: Color,
    val typography: TextStyle,
    val borderWidth: Dp,
    val borderColor: Color,
    val backgroundBrush: Brush,
)

sealed class IBankTagCornerStyle(val cornerRadius: Dp) {
    data object Rounded : IBankTagCornerStyle(IBCornerRadius.cornerRadiusM)
    data object Pill : IBankTagCornerStyle(IBCornerRadius.cornerRadiusRound)
}

data class TagItemData<T>(
    val data: T,
    val label: String,
    val uiState: TagState = TagState.DEFAULT,
    val badgeNumber: Int? = null,
    val hasCloseButton: Boolean = false,
    val iconPainter: Painter? = null,
    val iconVector: ImageVector? = null
)

sealed class IBankTagSize(
    val iconSize: Dp,
    val closeButtonSize: Dp,
    val tagModifier: Modifier,
    val defaultStateStyle: IBankTagStateStyle,
    val selectedStateStyle: IBankTagStateStyle,
    val pressedStateStyle: IBankTagStateStyle,
) {
    class M(scheme: IBColorScheme, typography: IBTypography) : IBankTagSize(
        iconSize = IBSpacing.spacingM,
        closeButtonSize = IBSpacing.spacingL,
        tagModifier = Modifier.padding(
            top = IBSpacing.spacing2xs,
            bottom = IBSpacing.spacing2xs,
            start = IBSpacing.spacingXs,
            end = IBSpacing.spacing2xs
        ),
        defaultStateStyle = IBankTagStateStyle(
            textColor = scheme.contentMainSecondary,
            typography = typography.bodyBody_m,
            borderWidth = IBBorderDivider.borderDividerS,
            borderColor = scheme.borderMainPrimary,
            backgroundBrush = SolidColor(value = scheme.bgMainTertiary),
        ),
        selectedStateStyle = IBankTagStateStyle(
            textColor = scheme.contentMainSecondary,
            typography = typography.bodyBody_m,
            borderWidth = IBBorderDivider.borderDividerS,
            borderColor = scheme.borderBrandSecondary,
            backgroundBrush = SolidColor(value = scheme.bgMainTertiary),
        ),
        pressedStateStyle = IBankTagStateStyle(
            textColor = scheme.contentMainSecondary,
            typography = typography.bodyBody_m,
            borderWidth = IBBorderDivider.borderDividerS,
            borderColor = scheme.borderMainPrimary,
            backgroundBrush = SolidColor(value = scheme.bgMainTertiary_press)
        )

    )

    class SM(scheme: IBColorScheme, typography: IBTypography) :
        IBankTagSize(
            iconSize = IBSpacing.spacingS,
            closeButtonSize = IBSpacing.spacingM,
            tagModifier = Modifier.padding(
                top = IBSpacing.spacing3xs,
                bottom = IBSpacing.spacing3xs,
                start = IBSpacing.spacing2xs,
                end = IBSpacing.spacing3xs
            ),
            defaultStateStyle = IBankTagStateStyle(
                textColor = scheme.contentMainSecondary,
                typography = typography.bodyBody_s,
                borderWidth = IBBorderDivider.borderDividerS,
                borderColor = scheme.borderMainPrimary,
                backgroundBrush = SolidColor(value = scheme.bgMainTertiary),
            ),
            selectedStateStyle = IBankTagStateStyle(
                textColor = scheme.contentMainSecondary,
                typography = typography.bodyBody_s,
                borderWidth = IBBorderDivider.borderDividerS,
                borderColor = scheme.borderBrandSecondary,
                backgroundBrush = SolidColor(value = scheme.bgMainTertiary)
            ),
            pressedStateStyle = IBankTagStateStyle(
                textColor = scheme.contentMainSecondary,
                typography = typography.bodyBody_s,
                borderWidth = IBBorderDivider.borderDividerS,
                borderColor = scheme.borderMainPrimary,
                backgroundBrush = SolidColor(value = scheme.bgMainTertiary_press)
            ),
        )
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun <T> IBankTagLayout(
    modifier: Modifier = Modifier.fillMaxWidth(),
    tags: List<TagItemData<T>>,
    size: IBankTagSize = IBankTagSize.M(LocalColorScheme.current, LocalTypography.current),
    cornerStyle: IBankTagCornerStyle = IBankTagCornerStyle.Rounded,
    selectionMode: IBankTagSelectionMode = IBankTagSelectionMode.NONE,
    layoutMode: IBankTagLayoutMode = IBankTagLayoutMode.SINGLE_ROW,
    defaultStateStyle: IBankTagStateStyle = size.defaultStateStyle,
    selectedStateStyle: IBankTagStateStyle = size.selectedStateStyle,
    pressedStateStyle: IBankTagStateStyle = size.pressedStateStyle,
    tagItemModifier: Modifier = size.tagModifier,
    onTagStateChanged: (TagItemData<T>, TagState, List<TagItemData<T>>) -> Unit = { _, _, _ -> },
    onTagRemoved: (TagItemData<T>) -> Unit = {}
) {
    val tagStates = remember(tags) { tags.toMutableStateList() }
    fun handleTagStateChange(index: Int, tag: TagItemData<T>, newState: TagState) {
        if (selectionMode == IBankTagSelectionMode.NONE) {
            return
        }
        if (selectionMode == IBankTagSelectionMode.SINGLE_AT_LEAST_ONE_SELECT) {
            if (tagStates.count { it.uiState == TagState.SELECTED } == 1 && tag.uiState == TagState.SELECTED)
                return
        }
        if ((selectionMode == IBankTagSelectionMode.SINGLE || selectionMode == IBankTagSelectionMode.SINGLE_AT_LEAST_ONE_SELECT) && newState == TagState.SELECTED) {
            tagStates.forEachIndexed { idx, _ ->
                tagStates[idx] = tagStates[idx].copy(uiState = TagState.DEFAULT)
            }
        }
        tagStates[index] = tag.copy(uiState = newState)
        onTagStateChanged(tagStates[index], newState, tagStates)
    }

    val renderTagItem: @Composable (TagItemData<T>, Int) -> Unit = { tag, index ->
        IBankTagItem(
            modifier = tagItemModifier.semantics {
                contentDescription = "tg_$index"
            },
            tag = tag,
            size = size,
            cornerStyle = cornerStyle,
            defaultStateStyle = defaultStateStyle,
            selectedStateStyle = selectedStateStyle,
            pressedStateStyle = pressedStateStyle,
            selectable = selectionMode != IBankTagSelectionMode.NONE,
            onClick = {
                val newState = when (tag.uiState) {
                    TagState.DEFAULT -> TagState.SELECTED
                    TagState.SELECTED -> TagState.DEFAULT
                }
                handleTagStateChange(index, tag, newState)
            },
            onRemove = {
                tagStates.removeAt(index)
                onTagRemoved(tag)
            }
        )
    }

    when (layoutMode) {
        IBankTagLayoutMode.SINGLE_ROW -> LazyRow(
            modifier = modifier,
            horizontalArrangement = Arrangement.spacedBy(IBSpacing.spacingXs),
        ) {
            itemsIndexed(tagStates) { index, tag -> renderTagItem(tag, index) }
        }

        IBankTagLayoutMode.MULTIPLE_ROWS -> FlowRow(
            horizontalArrangement = Arrangement.spacedBy(IBSpacing.spacingXs),
            verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingXs),
            modifier = modifier,
        ) {
            tagStates.forEachIndexed { index, tag -> renderTagItem(tag, index) }
        }
    }
}

@Composable
fun <T> IBankTagItem(
    modifier: Modifier = Modifier,
    textModifier: Modifier = Modifier,
    tag: TagItemData<T>,
    cornerStyle: IBankTagCornerStyle,
    size: IBankTagSize,
    defaultStateStyle: IBankTagStateStyle = size.defaultStateStyle,
    selectedStateStyle: IBankTagStateStyle = size.selectedStateStyle,
    pressedStateStyle: IBankTagStateStyle = size.pressedStateStyle,
    selectable: Boolean,
    onClick: (() -> Unit) = {},
    onRemove: () -> Unit = {}
) {
    val scheme = LocalColorScheme.current
    val shape = RoundedCornerShape(cornerStyle.cornerRadius)
    var style = defaultStateStyle
    val state = tag.uiState

    val stateModifier = if (selectable) {
        val interactionSource = remember { MutableInteractionSource() }
        val isPressed by interactionSource.collectIsPressedAsState()
        style = if (isPressed) pressedStateStyle else {
            when (state) {
                TagState.SELECTED -> selectedStateStyle
                else -> defaultStateStyle
            }
        }
        Modifier
            .clickable(
                interactionSource = interactionSource,
                indication = null
            ) { onClick() }
            .background(brush = style.backgroundBrush, shape = shape)
            .border(width = style.borderWidth, color = style.borderColor, shape = shape)
    } else {
        Modifier
            .background(brush = style.backgroundBrush, shape = shape)
            .border(width = style.borderWidth, color = style.borderColor, shape = shape)
    }
    Row(
        modifier = Modifier
            .then(stateModifier)
            .then(modifier),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        TagLeadingIcon(tag = tag, size = size)
        Text(
            modifier = textModifier,
            text = tag.label,
            color = style.textColor,
            style = style.typography,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
        )

        TagBadge(tag = tag, size = size, state = state)
        TagCloseIcon(tag = tag, scheme = scheme, size = size, onRemove = onRemove)
    }
}

@Composable
private fun <T> TagCloseIcon(
    tag: TagItemData<T>,
    scheme: IBColorScheme,
    size: IBankTagSize,
    onRemove: () -> Unit
) {
    if (tag.hasCloseButton) {
        Icon(imageVector = ImageVector.vectorResource(id = R.drawable.close),
            contentDescription = "Remove",
            tint = scheme.contentMainSecondary,
            modifier = Modifier
                .size(size.closeButtonSize)
                .clickable { onRemove() }
                .padding(
                    start = IBSpacing.spacing3xs,
                    top = IBSpacing.spacing3xs,
                    bottom = IBSpacing.spacing3xs
                ))
    } else {
        Spacer(modifier = Modifier.width(IBSpacing.spacing3xs))
    }
}

@Composable
private fun <T> TagBadge(
    tag: TagItemData<T>,
    size: IBankTagSize,
    state: TagState
) {
    tag.badgeNumber?.let { badge ->
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier.padding(
                start = IBSpacing.spacing2xs,
                top = IBSpacing.spacing3xs,
                bottom = IBSpacing.spacing3xs
            )
        ) {
            val badgeSize = when (size) {
                is IBankTagSize.M -> LabelSize.M
                is IBankTagSize.SM -> LabelSize.SM
            }
            IBankBadgeLabel(
                title = badge.toString(),
                badgeType = LabelType.PILL,
                badgeSize = badgeSize,
                badgeColor = if (state == TagState.SELECTED) LabelColor.BRAND else LabelColor.LIGHT_GRAY
            )
        }
    }
}

@Composable
private fun <T> TagLeadingIcon(
    tag: TagItemData<T>,
    size: IBankTagSize
) {
    tag.iconPainter?.let {
        Row {
            Image(
                painter = it,
                contentDescription = null,
                modifier = Modifier.size(size.iconSize),
            )
            Spacer(modifier = Modifier.width(IBSpacing.spacing3xs))
        }
    } ?: tag.iconVector?.let {
        Row {
            Image(
                imageVector = it,
                contentDescription = null,
                modifier = Modifier.size(size.iconSize),
            )
            Spacer(modifier = Modifier.width(IBSpacing.spacing3xs))
        }
    }
}

@Composable
private fun <T> TagSection(
    tags: List<TagItemData<T>>,
    size: IBankTagSize,
    cornerStyle: IBankTagCornerStyle,
    selectionMode: IBankTagSelectionMode = IBankTagSelectionMode.SINGLE,
) {
    IBankTagLayout(tags = tags,
        size = size,
        cornerStyle = cornerStyle,
        selectionMode = selectionMode,
        onTagStateChanged = { tag, newState, _ ->
            BLogUtil.d("Tag ${tag.data} - ${tag.label} changed to $newState")
        },
        onTagRemoved = { removedTag ->
            BLogUtil.d("Removed tag: ${removedTag.data} - ${removedTag.label}")
        })
}

@Composable
@Preview(
    showBackground = true, widthDp = 600
)
fun IBankTagLayoutPreview() {
    val defaultTags = listOf(
        TagItemData(
            data = 1,
            label = "Label",
            iconVector = ImageVector.vectorResource(id = R.drawable.information_circle),
            badgeNumber = null
        ), TagItemData(
            data = 2,
            label = "Label",
            iconVector = ImageVector.vectorResource(id = R.drawable.information_circle),
            badgeNumber = null,
            hasCloseButton = true
        ), TagItemData(
            data = 3,
            label = "Label",
            badgeNumber = 5,
        )
    )

    val selectedTags = defaultTags.map { it.copy(uiState = TagState.SELECTED) }

    Column(
        modifier = Modifier
            .background(color = Color.White)
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(20.dp)
    ) {
        listOf(IBankTagCornerStyle.Rounded, IBankTagCornerStyle.Pill).forEach { cornerStyle ->
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Column(
                    modifier = Modifier.weight(1f),
                    verticalArrangement = Arrangement.spacedBy(20.dp)
                ) {
                    TagSection(
                        tags = defaultTags,
                        size = IBankTagSize.SM(LocalColorScheme.current, LocalTypography.current),
                        cornerStyle = cornerStyle,
                        selectionMode = IBankTagSelectionMode.MULTIPLE
                    )
                    TagSection(
                        tags = selectedTags,
                        size = IBankTagSize.SM(LocalColorScheme.current, LocalTypography.current),
                        cornerStyle = cornerStyle,
                        selectionMode = IBankTagSelectionMode.MULTIPLE
                    )
                }
                Column(
                    modifier = Modifier.weight(1f),
                    verticalArrangement = Arrangement.spacedBy(20.dp)
                ) {
                    TagSection(
                        tags = defaultTags,
                        size = IBankTagSize.M(LocalColorScheme.current, LocalTypography.current),
                        cornerStyle = cornerStyle,
                    )
                    TagSection(
                        tags = selectedTags,
                        size = IBankTagSize.M(LocalColorScheme.current, LocalTypography.current),
                        cornerStyle = cornerStyle,
                        selectionMode = IBankTagSelectionMode.MULTIPLE
                    )
                }
            }
        }
    }
}