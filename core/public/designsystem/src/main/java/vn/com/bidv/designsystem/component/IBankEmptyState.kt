package vn.com.bidv.designsystem.component

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.navigation.button.IBankNormalButton
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

@Composable
fun IBankEmptyState(
    modifier: Modifier = Modifier,
    emptyStateType: EmptyStateType = EmptyStateType.EmptyBox,
    title: String? = null,
    supportingText: String? = null,
    textButton: String? = null,
    leadingIconButton: ImageVector? = null,
    backgroundColor: Color = LocalColorScheme.current.bgMainTertiary,
    verticalArrangement: Arrangement.Vertical = Arrangement.Center,
    onClickButton: () -> Unit = { }
) {

    val typography = LocalTypography.current

    Surface(
        modifier = modifier.testTagIBank("IBankEmptyState_$title"),
        color = backgroundColor
    ) {
        Column(
            modifier = Modifier.padding(IBSpacing.spacingM),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = verticalArrangement
        ) {
            Icon(
                modifier = Modifier,
                painter = painterResource(emptyStateType.icon),
                contentDescription = null,
                tint = Color.Unspecified
            )

            title?.let {
                Spacer(modifier = Modifier.height(IBSpacing.spacingXs))
                Text(
                    text = title,
                    style = typography.titleTitle_m,
                    textAlign = TextAlign.Center
                )
            }

            supportingText?.let {
                Spacer(modifier = Modifier.height(IBSpacing.spacing2xs))
                Text(
                    text = supportingText,
                    style = typography.bodyBody_m,
                    textAlign = TextAlign.Center
                )
            }

            textButton?.let {
                Spacer(modifier = Modifier.height(IBSpacing.spacingXs))
                IBankNormalButton(
                    leadingIcon = leadingIconButton,
                    text = textButton,
                    onClick = onClickButton
                )
            }
        }
    }
}

enum class EmptyStateType(val icon: Int) {
    EmptyBox(R.drawable.empty_box_64),
    SearchNoResult(R.drawable.search_no_results_64),
    Lock(R.drawable.lock)
}

@Preview
@Composable
fun TestEmptyView() {
    IBankEmptyState(
        modifier = Modifier
            .width(324.dp)
            .wrapContentHeight(),
        emptyStateType = EmptyStateType.SearchNoResult,
        title = "Title",
        supportingText = "Không có dữ liệu",
        textButton = "Button CTA"
    )
}
