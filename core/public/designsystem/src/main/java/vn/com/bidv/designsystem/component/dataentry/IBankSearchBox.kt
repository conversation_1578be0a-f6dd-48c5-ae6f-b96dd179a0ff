package vn.com.bidv.designsystem.component.dataentry

import android.app.Activity
import android.graphics.Rect
import android.view.ViewTreeObserver
import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusState
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextMeasurer
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay
import timber.log.Timber
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import kotlin.math.min

/**
 * A composable function that provides a customizable search input field with optional filters.
 *
 * @param modifier A [Modifier] to apply styling and layout configurations to the input field.
 * @param textValue A [TextFieldValue] representing the current text value of the input field. Defaults to an empty [TextFieldValue].
 * @param filters An optional list of [IBankFilterTextField], representing filters to be displayed with the search input. Defaults to null.
 * @param onTextChange A callback triggered when the text value changes, providing the new [TextFieldValue].
 * @param onRequestChange A callback triggered when the user requests a change (e.g., by pressing search), providing the entered query as a [String].
 * @param onClickClear A callback triggered when the clear action is performed (e.g., clearing the text field). Defaults to a no-op.
 */

@Composable
fun IBankInputSearchBase(
    modifier: Modifier = Modifier,
    placeHolderText: String = "",
    textValue: String = "",
    filters: List<IBankFilterTextField>? = null,
    maxLength: Int = Int.MAX_VALUE,
    keyboardOption: KeyboardOptions = KeyboardOptions.Default,
    onSubmitText: (String) -> Unit = {},
    onTextChange: (String) -> Unit,
    onRequestChange: (String) -> Unit,
    onClickClear: () -> Unit = {},
    onFocusChange: (isFocused: Boolean) -> Unit = {}
) {
    val colorScheme = LocalColorScheme.current
    var inputState by remember { mutableStateOf(IBFrameState.DEFAULT(colorScheme) as IBFrameState) }

    Box(modifier = modifier.width(IntrinsicSize.Min).testTagIBank("IBankInputSearchBase_$placeHolderText")) {
        SearchLayoutBody(
            placeHolderText = placeHolderText,
            textValue = textValue,
            state = inputState,
            filters = filters,
            maxLength = maxLength,
            keyboardOption = keyboardOption,
            onSubmitText = onSubmitText,
            onTextChange = {
                onTextChange(it)
            },
            onStateChange = {
                inputState = if (it.isFocused) {
                    IBFrameState.FOCUS(colorScheme)
                } else {
                    IBFrameState.DEFAULT(colorScheme)
                }
                onFocusChange(it.isFocused)
            },
            onRequestChange = {
                onRequestChange(it)
            },
        )
    }
}

@Composable
fun SearchLayoutBody(
    placeHolderText: String,
    textValue: String = "",
    state: IBFrameState = IBFrameState.DEFAULT(LocalColorScheme.current),
    filters: List<IBankFilterTextField>? = null,
    keyboardOption: KeyboardOptions = KeyboardOptions.Default,
    debounceDelay: Long = 500L, // Delay in milliseconds
    maxLength: Int = Int.MAX_VALUE,
    onSubmitText: (String) -> Unit = {},
    onTextChange: (String) -> Unit,
    onStateChange: (FocusState) -> Unit,
    onRequestChange: (String) -> Unit,
) = IBankInputFrameBase(state = state, bodyContent = {

    val focusManager = LocalFocusManager.current
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current
    var inputValue by remember { mutableStateOf(TextFieldValue(textValue)) }
    var debouncedValue by remember { mutableStateOf("") }
    var textFieldWidth by remember { mutableIntStateOf(0) }
    val scrollState = rememberScrollState()
    var rawInput by remember { mutableStateOf(textValue) }
    val textMeasurer = rememberTextMeasurer()
    val textStyle = typography.bodyBody_m.copy(color = colorScheme.contentMainTertiary)

    val isKeyboardVisible = rememberKeyboardVisibility()

    // listen on keyboard visibility changes
    LaunchedEffect(isKeyboardVisible) {
        if (!isKeyboardVisible) {
            focusManager.clearFocus()
        }
    }
    LaunchedEffect(textValue) {
        if (inputValue.text != textValue) {
            inputValue = TextFieldValue(textValue)
        }
    }

    LaunchedEffect(inputValue.text) {
        onTextChange(inputValue.text)
        delay(debounceDelay)
        if (inputValue.text != debouncedValue) {
            debouncedValue = inputValue.text
            onRequestChange(debouncedValue)
        }
    }

    // Scroll to the cursor position when it changes
    LaunchedEffect(rawInput) {
        val cursorOffset = calculateScrollOffset(
            textFieldValue = inputValue,
            textStyle = typography.bodyBody_m,
            textMeasurer = textMeasurer,
            currentScrollOffset = scrollState.value, // Lấy giá trị scroll hiện tại
            textFieldWidth = textFieldWidth
        )
        scrollState.animateScrollTo(cursorOffset)
    }

    Row(
        Modifier
            .height(IBSpacing.spacing4xl)
            .padding(IBSpacing.spacingXs),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painterResource(id = R.drawable.search),
            contentDescription = "",
            colorFilter = ColorFilter.tint(color = colorScheme.contentBrand_01Primary),
            modifier = Modifier.size(20.dp)
        )

        Box(modifier = Modifier
            .weight(1f)
            .padding(start = IBSpacing.spacingXs)
            .onGloballyPositioned { coordinates ->
                textFieldWidth = coordinates.size.width // Lấy chiều rộng thực tế của View
            }) {
            BasicTextField(
                modifier = Modifier
                    .fillMaxWidth()
                    .horizontalScroll(scrollState)
                    .onFocusChanged { onStateChange(it) },
                value = inputValue,
                keyboardOptions = keyboardOption,
                textStyle = textStyle,
                onValueChange = {
                    rawInput = it.text
                    var filteredText = it.text
                    filters?.forEach { filter ->
                        filteredText = filter.apply(filteredText)
                    }
                    val newInput = filteredText.substring(0, min(filteredText.length, maxLength))
                    val selection = if (it.text == newInput) {
                        it.selection
                    } else {
                        val indexToEnd = filteredText.length - (it.text.length - it.selection.end)
                        TextRange(
                            start = indexToEnd,
                            end = if (indexToEnd == filteredText.length) filteredText.length else it.selection.end
                        )
                    }
                    inputValue = TextFieldValue(
                        newInput,
                        selection = selection
                    )
                },
                enabled = state !is IBFrameState.DISABLE,
                singleLine = true,
                decorationBox = { innerTextField ->
                    if (inputValue.text.isEmpty() && state !is IBFrameState.FOCUS) {
                        Text(
                            text = placeHolderText,
                            style = LocalTypography.current.bodyBody_m,
                            color = LocalColorScheme.current.contentPlaceholder
                        )
                    }
                    innerTextField()
                },
                keyboardActions = KeyboardActions(
                    onAny = {
                        focusManager.clearFocus()
                        onSubmitText(inputValue.text)
                    }
                )
            )
        }

        if (state is IBFrameState.FOCUS && inputValue.text.isNotEmpty()) {
            Spacer(modifier = Modifier.width(IBSpacing.spacingXs))
            Image(
                painterResource(id = R.drawable.circle_close_outline),
                contentDescription = "",
                colorFilter = ColorFilter.tint(color = colorScheme.contentMainTertiary),
                modifier = Modifier
                    .size(IBSpacing.spacingL)
                    .clickable { inputValue = TextFieldValue("") }
            )
        }
    }
})

private fun calculateScrollOffset(
    textFieldValue: TextFieldValue,
    textStyle: TextStyle,
    textMeasurer: TextMeasurer,
    currentScrollOffset: Int,
    textFieldWidth: Int
): Int {
    val text = textFieldValue.text
    if (text.isEmpty()) return 0
    val cursorIndex = textFieldValue.selection.start // Vị trí con trỏ
    // Đo width của text đến vị trí con trỏ
    val measuredWidth = textMeasurer.measure(
        text = text.substring(0, cursorIndex),
        style = textStyle
    ).size.width

    // Vị trí con trỏ so với hiện tại
    val cursorPosition = measuredWidth - currentScrollOffset
    return when {
        cursorPosition < 0 -> measuredWidth - textFieldWidth / 2// Scroll để con trỏ không bị khuất trái
        cursorPosition > textFieldWidth -> measuredWidth - textFieldWidth + 4 // Scroll để con trỏ không bị khuất phải, 4 là width của con trỏ
        else -> currentScrollOffset // Không scroll nếu con trỏ nằm trong tầm nhìn
    }
}

@Preview
@Composable
fun PreviewSearch() {

    var inputValue1 by remember { mutableStateOf("") }
    val colorScheme = LocalColorScheme.current

    Column {
        IBankInputSearchBase(
            modifier = Modifier.height(40.dp),
            placeHolderText = "Search", textValue = inputValue1,
            onTextChange = {
                inputValue1 = it
            }, onRequestChange = {
                Timber.tag("XXX_SearchBox_Request: $it").d("XXX_SearchBox_Request: $it")
            }, onClickClear = {
                inputValue1 = ""
            })
        Spacer(Modifier.height(16.dp))
        Row(
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically,
        ) {

            IBankInputSearchBase(
                placeHolderText = "Search",
                modifier = Modifier.weight(1f),
                textValue = inputValue1,
                onTextChange = {
                    inputValue1 = it
                }, onRequestChange = {
                    Timber.tag("XXX_SearchBox_Request: $it").d("XXX_SearchBox_Request: $it")
                }, onClickClear = {
                    inputValue1 = ""
                })

            Spacer(Modifier.width(IBSpacing.spacingXs))
            Box(
                Modifier
                    .border(
                        width = IBBorderDivider.borderDividerS,
                        color = colorScheme.borderMainPrimary,
                        shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL)
                    )
                    .padding(IBSpacing.spacingXs)
            ) {
                Image(
                    painter = painterResource(
                        id = R.drawable.nav_settings_outline
                    ),
                    contentDescription = "",
                    modifier = Modifier
                        .size(IBSpacing.spacingL)
                        .align(Alignment.Center)
                )
            }
        }
        Spacer(Modifier.height(16.dp))
    }
}

@Composable
private fun rememberKeyboardVisibility(): Boolean {
    val context = LocalContext.current
    val activity = context as? Activity
    val rootView = remember { activity?.window?.decorView }
    val keyboardVisible = remember { mutableStateOf(false) }

    DisposableEffect(rootView) {
        val listener = ViewTreeObserver.OnGlobalLayoutListener {
            val rect = Rect()
            rootView?.getWindowVisibleDisplayFrame(rect)
            val screenHeight = rootView?.height ?: 0
            val keyboardHeight = screenHeight - rect.bottom

            keyboardVisible.value =
                keyboardHeight > screenHeight * 0.15 // Adjust threshold if needed
        }

        rootView?.viewTreeObserver?.addOnGlobalLayoutListener(listener)

        onDispose {
            rootView?.viewTreeObserver?.removeOnGlobalLayoutListener(listener)
        }
    }

    return keyboardVisible.value
}
