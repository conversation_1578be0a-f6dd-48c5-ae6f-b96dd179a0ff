package vn.com.bidv.designsystem.component.dataentry

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

@Composable
fun IBankInputHelpText(
    modifier: Modifier = Modifier,
    title: String,
    subText: String,
    textStyle: TextStyle = LocalTypography.current.captionCaption_m,
    isMoneyTransfer: Boolean = false,
    isErrorState: Boolean = false
) {

    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current

    Row(modifier = modifier) {
        if (isMoneyTransfer) {
            Box(Modifier.fillMaxWidth()) {
                Row(
                    Modifier.align(Alignment.CenterEnd),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painterResource(id = R.drawable.information_circle),
                        contentDescription = ""
                    )
                    Box {
                        Text(
                            text = subText,
                            style = typography.captionCaption_m,
                            color = colorScheme.contentBrand_01Primary
                        )
                    }
                }
            }
        } else {
            Text(
                text = title, style = textStyle,
                color = if (isErrorState) colorScheme.contentNegativePrimary else colorScheme.contentMainTertiary,
            )
            Box(Modifier.fillMaxWidth()) {
                Text(
                    text = subText,
                    style = textStyle,
                    color = if (isErrorState) colorScheme.contentNegativePrimary else colorScheme.contentMainTertiary,
                    modifier = Modifier.align(Alignment.CenterEnd)
                )
            }
        }
    }
}

@Preview
@Composable
fun PreviewHelpText() {
    IBankInputHelpText(title = "This is a hint text to help user.", subText = "VND")
}