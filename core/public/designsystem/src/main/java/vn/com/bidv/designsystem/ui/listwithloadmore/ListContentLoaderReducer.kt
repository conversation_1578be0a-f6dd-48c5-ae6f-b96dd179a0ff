package vn.com.bidv.designsystem.ui.listwithloadmore

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState

class ListContentLoaderReducer<T> :
    Reducer<ListContentLoaderReducer.ListContentLoaderViewState<T>, ListContentLoaderReducer.ListContentLoaderViewEvent<T>, ListContentLoaderReducer.ListContentLoaderViewEffect> {

    @Immutable
    data class ScreenContent<T>(
        val items: MutableList<T> = mutableListOf(),
        val isLastPage: Boolean = false,
        val pageSize: Int = 20,
        val errorMessage: String? = null
    )

    @Immutable
    sealed class ListContentLoaderViewState<T> : ViewState {
        data class Loading<T>(val content: ScreenContent<T> = ScreenContent()) : ListContentLoaderViewState<T>()
        data class ShowContent<T>(val content: ScreenContent<T> = ScreenContent()) : ListContentLoaderViewState<T>()
    }

    @Immutable
    sealed class ListContentLoaderViewEvent<T> : ViewEvent {
        data class GetDataSuccess<T>(val items: List<T>, val isLastPage: Boolean = false) : ListContentLoaderViewEvent<T>()
        data class GetDataFail<T>(val errorMessage: String? = null) : ListContentLoaderViewEvent<T>()
        class LoadMoreData<T> : ListContentLoaderViewEvent<T>()
        class RefreshData<T> : ListContentLoaderViewEvent<T>()
    }

    @Immutable
    sealed class ListContentLoaderViewEffect : SideEffect {
        data class GetData(val pageIndex: Int, val pageSize: Int) : ListContentLoaderViewEffect()
    }

    override fun reduce(
        previousState: ListContentLoaderViewState<T>,
        event: ListContentLoaderViewEvent<T>
    ): Pair<ListContentLoaderViewState<T>, ListContentLoaderViewEffect?> {
        return when (previousState) {
            is ListContentLoaderViewState.Loading -> {
                if (event is ListContentLoaderViewEvent.GetDataFail) {
                    return ListContentLoaderViewState.ShowContent(
                        content = previousState.content.copy(
                            errorMessage = event.errorMessage
                        )
                    ) to null
                }
                if (event is ListContentLoaderViewEvent.GetDataSuccess) {
                    return ListContentLoaderViewState.ShowContent(
                        content = previousState.content.copy(
                            items = previousState.content.items.also { it.addAll(event.items) },
                            isLastPage = event.isLastPage)
                    ) to null
                }
                previousState to null
            }

            is ListContentLoaderViewState.ShowContent -> {
                if (event is ListContentLoaderViewEvent.RefreshData) {
                    return ListContentLoaderViewState.Loading(content = previousState.content.copy(
                        items = mutableListOf(),
                        isLastPage = false,
                        errorMessage = null
                    )) to ListContentLoaderViewEffect.GetData(
                        pageIndex = 1,
                        pageSize = previousState.content.pageSize
                    )
                }
                if (event is ListContentLoaderViewEvent.LoadMoreData) {
                    return ListContentLoaderViewState.Loading(content = previousState.content) to ListContentLoaderViewEffect.GetData(
                        pageIndex = previousState.content.items.size / previousState.content.pageSize + 1,
                        pageSize = previousState.content.pageSize
                    )
                }
                previousState to null
            }
        }
    }
}
    