package vn.com.bidv.designsystem.component.feedback.snackbar

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material3.Snackbar
import androidx.compose.material3.SnackbarDuration
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import kotlinx.coroutines.delay
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

class IBankSnackBarInfo(
    val message: String,
    val primaryButtonText: String? = null,
    val primaryButtonAction: () -> Unit = { },
    val secondaryButtonText: String? = null,
    val secondaryButtonAction: () -> Unit = { },
)

@Composable
fun IBankSnackBar(
    modifier: Modifier = Modifier,
    message: String,
    onDismiss: () -> Unit = {},
    isVisible: Boolean = true,
    snackbarDuration: SnackbarDuration = SnackbarDuration.Indefinite,
    primaryButtonText: String? = null,
    primaryButtonAction: () -> Unit = { },
    secondaryButtonText: String? = null,
    secondaryButtonAction: () -> Unit = { },
) {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current
    val durationMillis = when (snackbarDuration) {
        SnackbarDuration.Indefinite -> Long.MAX_VALUE
        SnackbarDuration.Long -> 10000L
        SnackbarDuration.Short -> 4000L
    }

    if (isVisible) {
        Snackbar(
            modifier = modifier,
            containerColor = colorScheme.bgSolidPrimary,
            action = { // show one button
                if (primaryButtonText.isNullOrEmpty() || secondaryButtonText.isNullOrEmpty()) {
                    ActionButtons(
                        primaryButtonText = primaryButtonText,
                        primaryButtonAction = {
                            onDismiss()
                            primaryButtonAction()
                        },
                        secondaryButtonText = secondaryButtonText,
                        secondaryButtonAction = {
                            onDismiss()
                            secondaryButtonAction()
                        }
                    )
                }
            }
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth(),
                horizontalAlignment = Alignment.Start
            ) {
                Text(
                    modifier = Modifier
                        .wrapContentHeight()
                        .testTagIBank("IBankSnackBar_$message"),
                    style = typography.bodyBody_m,
                    text = message
                )

                if (primaryButtonText != null && secondaryButtonText != null) { //show two buttons
                    Spacer(modifier = Modifier.height(IBSpacing.spacingXs))
                    ActionButtons(
                        primaryButtonText = primaryButtonText,
                        primaryButtonAction = {
                            onDismiss()
                            primaryButtonAction()
                        },
                        secondaryButtonText = secondaryButtonText,
                        secondaryButtonAction = {
                            onDismiss()
                            secondaryButtonAction()
                        }
                    )
                }
            }
        }
        LaunchedEffect(message) {
            delay(durationMillis)
            onDismiss()
        }
    }
}

@Composable
fun ActionButtons(
    primaryButtonText: String? = null,
    primaryButtonAction: () -> Unit = { },
    secondaryButtonText: String? = null,
    secondaryButtonAction: () -> Unit = { },
) {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current

    Row(
        modifier = if (primaryButtonText.isNullOrEmpty() || secondaryButtonText.isNullOrEmpty())
            Modifier.wrapContentSize() else Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.End
    ) {
        primaryButtonText?.let {
            TextButton(
                onClick = { primaryButtonAction.invoke() }
            ) {
                Text(
                    modifier = Modifier.testTagIBank("IBankSnackBar_button_$it"),
                    style = typography.labelLabel_l,
                    color = colorScheme.contentBrand_01Tertiary,
                    text = it
                )
            }
        }
        secondaryButtonText?.let {
            TextButton(onClick = { secondaryButtonAction.invoke() }
            ) {
                Text(
                    modifier = Modifier.testTagIBank("IBankSnackBar_button_$it"),
                    style = typography.labelLabel_l,
                    color = colorScheme.contentBrand_01Tertiary,
                    text = it
                )
            }
        }
    }
}

@Composable
@Preview
fun Test() {
    Column(modifier = Modifier.fillMaxWidth()) {
        IBankSnackBar(
            message = "Bạn đang dùng phiên bản cũ. Cập nhật để trải nghiệm tốt hơn.",
            secondaryButtonText = "Retry"
        )

        Spacer(modifier = Modifier.height(IBSpacing.spacingXs))

        IBankSnackBar(
            message = "Bạn đang dùng phiên bản cũ. Cập nhật để trải nghiệm tốt hơn.",
            primaryButtonText = "Retry",
            secondaryButtonText = "Close"
        )
    }
}