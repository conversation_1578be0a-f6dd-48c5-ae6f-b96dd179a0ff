package vn.com.bidv.designsystem.component.dataentry

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBColorScheme
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBShadow
import vn.com.bidv.designsystem.theme.IBShadow.dropShadow
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme

@Composable
fun IBankInputFrameBase(
    modifier: Modifier = Modifier,
    state: IBFrameState = IBFrameState.DEFAULT(LocalColorScheme.current),
    radiusValue: Dp = IBCornerRadius.cornerRadiusL,
    borderWidthValue: Dp = IBBorderDivider.borderDividerS,
    bodyContent: @Composable BoxScope.() -> Unit? = {},
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .dropShadow(
                config = state.colorShadowUnder
            )
            .dropShadow(
                config = state.colorShadowUpper
            )
    ) {
        Box(
            modifier
                .clip(RoundedCornerShape(radiusValue))
                .background(state.colorBackground)
                .border(
                    width = borderWidthValue,
                    color = state.colorBorder,
                    shape = RoundedCornerShape(radiusValue)
                )
                .fillMaxWidth()
        ) {
            bodyContent()
        }
    }
}

@Preview
@Composable
fun PreviewEditTextBox() {
    val colorScheme = LocalColorScheme.current
    Box(
        modifier = Modifier.padding(
            horizontal = IBSpacing.spacingM, vertical = IBSpacing.spacingS
        )
    ) {
        IBankInputFrameBase(state = IBFrameState.FOCUS(colorScheme))
    }
}

sealed class IBFrameState(
    val colorBorder: Color,
    val colorShadowUpper: IBShadow.ShadowConfig,
    val colorShadowUnder: IBShadow.ShadowConfig,
    val colorBackground: Color,
) {
    class DEFAULT(colorScheme: IBColorScheme) : IBFrameState(
        colorBorder = colorScheme.borderMainPrimary, colorShadowUnder = IBShadow.ShadowConfig(
            color = Color.Transparent, blur = 0.dp, offsetX = 0.dp, offsetY = 0.dp, spread = 0.dp
        ), colorShadowUpper = IBShadow.ShadowConfig(
            color = Color.Transparent, blur = 0.dp, offsetX = 0.dp, offsetY = 0.dp, spread = 0.dp
        ), colorBackground = colorScheme.bgMainTertiary
    )

    class FOCUS(colorScheme: IBColorScheme) : IBFrameState(
        colorBorder = colorScheme.bgBrand_01Primary_hover,
        colorShadowUnder = IBShadow.focusringsBrandsecondary0.copy(
            shape = RoundedCornerShape(
                IBCornerRadius.cornerRadiusL
            )
        ),
        colorShadowUpper = IBShadow.focusringsBrandsecondary1.copy(
            shape = RoundedCornerShape(
                IBCornerRadius.cornerRadiusL
            )
        ),
        colorBackground = colorScheme.bgMainTertiary
    )

    class ERROR(colorScheme: IBColorScheme) : IBFrameState(
        colorBorder = colorScheme.contentNegativePrimary,

        colorShadowUnder = IBShadow.focusringsError0.copy(
            shape = RoundedCornerShape(
                IBCornerRadius.cornerRadiusL
            )
        ),

        colorShadowUpper = IBShadow.focusringsError1.copy(
            shape = RoundedCornerShape(
                IBCornerRadius.cornerRadiusL
            )
        ), colorBackground = colorScheme.bgMainTertiary
    )

    class DISABLE(colorScheme: IBColorScheme) : IBFrameState(
        colorBorder = colorScheme.borderMainPrimary,
        colorShadowUnder = IBShadow.ShadowConfig(
            color = Color.Transparent, blur = 0.dp, offsetX = 0.dp, offsetY = 0.dp, spread = 0.dp
        ),
        colorShadowUpper = IBShadow.ShadowConfig(
            color = Color.Transparent, blur = 0.dp, offsetX = 0.dp, offsetY = 0.dp, spread = 0.dp
        ),
        colorBackground = colorScheme.bgDisablePrimary,
    )
}
