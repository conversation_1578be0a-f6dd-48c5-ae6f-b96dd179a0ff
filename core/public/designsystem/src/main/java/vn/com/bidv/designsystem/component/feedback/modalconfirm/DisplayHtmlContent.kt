package vn.com.bidv.designsystem.component.feedback.modalconfirm

import android.content.Context
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.FilterQuality
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.painter.BitmapPainter
import androidx.compose.ui.graphics.painter.ColorPainter
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.graphics.scale
import coil3.BitmapImage
import coil3.Image
import coil3.ImageLoader
import coil3.compose.ImagePainter
import coil3.request.ImageRequest
import coil3.request.SuccessResult
import com.mohamedrejeb.richeditor.annotation.ExperimentalRichTextApi
import com.mohamedrejeb.richeditor.model.ImageData
import com.mohamedrejeb.richeditor.model.rememberRichTextState
import com.mohamedrejeb.richeditor.ui.material3.RichText
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import vn.com.bidv.designsystem.theme.IBSpacing
import com.mohamedrejeb.richeditor.model.ImageLoader as RichEditorImageLoader

data class WebViewSettings(
    val webViewClient: WebViewClient = WebViewClient(),
    val javaScriptEnabled: Boolean = false,
    val domStorageEnabled: Boolean = false,
)

@Composable
fun DisplayHtmlContent(
    htmlContent: String,
    webViewSettings: WebViewSettings? = null,
) {
    AndroidView(
        factory = { context ->
            WebView(context).apply {
                webViewSettings?.let {
                    webViewClient = webViewSettings.webViewClient
                    // Configure WebView settings
                    settings.javaScriptEnabled = webViewSettings.javaScriptEnabled
                    settings.domStorageEnabled = webViewSettings.domStorageEnabled
                } ?: run {
                    webViewClient = WebViewClient()
                }
            }
        },
        update = {
            it.loadDataWithBaseURL(null, htmlContent, "text/html", "UTF-8", null)
        }
    )
}

@Composable
private fun loadImagePainter(
    url: String,
    widthImg: Int? = null,
): Painter {
    var painter by remember { mutableStateOf<Painter?>(null) }
    val context = LocalContext.current

    LaunchedEffect(url) {
        painter = loadImagePainterSuspend(url, context, widthImg)
    }

    return painter ?: ColorPainter(Color.Transparent)
}

private suspend fun loadImagePainterSuspend(
    url: String,
    context: Context,
    widthImg: Int? = null,
): Painter? {
    if (!isHTTPUrlValid(url)) return null
    val imageLoader = ImageLoader(context)
    val request = ImageRequest.Builder(context).memoryCacheKey(url).data(url).build()

    return withContext(Dispatchers.IO) {
        val result = imageLoader.execute(request)
        if (result is SuccessResult) {
            result.image.asPainterWithResize(widthImg)
        } else {
            null
        }
    }
}

fun Image.asPainterWithResize(
    widthImg: Int? = null,
    filterQuality: FilterQuality = FilterQuality.Low,
): Painter {
    return when (this) {
        is BitmapImage -> {
            val newBitmap = if (widthImg != null && widthImg != this.width) {
                bitmap.scale(widthImg, this.height * widthImg / this.width)
            } else {
                bitmap
            }
            BitmapPainter(
                image = newBitmap.asImageBitmap(),
                filterQuality = filterQuality,
            )
        }

        else -> ImagePainter(this)
    }
}

private fun isHTTPUrlValid(url: String): Boolean {
    val regex = Regex("^https?://([\\w-]+\\.)+[\\w-]+(/[\\w-./?%&=]*)?\$")
    return regex.matches(url)
}

class HtmlImageLoader(private val widthImg: Int? = null) : RichEditorImageLoader {
    @OptIn(ExperimentalRichTextApi::class)
    @Composable
    override fun load(model: Any): ImageData? {

        return if (model is String) {
            return ImageData(
                painter = loadImagePainter(model, widthImg),
                contentDescription = null,
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = IBSpacing.spacingXs, bottom = IBSpacing.spacingXs)
            )
        } else {
            null
        }
    }

}

@Composable
fun DisplayHtmlUseRichText(
    htmlContent: String?,
    textStyle: TextStyle,
    color: Color = Color.Unspecified,
    widthImg: Int? = null,
) {
    htmlContent?.let {
        val richTextState = rememberRichTextState()
        LaunchedEffect(Unit) {
            val cleaned = htmlContent
                .moveNbspIntoPreviousSpan()
                .replace("&nbsp;", " ")

            richTextState.setHtml(cleaned)
        }
        RichText(
            state = richTextState,
            fontSize = textStyle.fontSize,
            fontStyle = textStyle.fontStyle,
            fontWeight = textStyle.fontWeight,
            fontFamily = textStyle.fontFamily,
            letterSpacing = textStyle.letterSpacing,
            lineHeight = textStyle.lineHeight,
            textAlign = TextAlign.Left,
            color = color,
            imageLoader = HtmlImageLoader(widthImg)
        )
    }
}

private fun String.moveNbspIntoPreviousSpan(): String {
    return this.replace(
        Regex("""</span>\s*(?:&nbsp;|\u00A0)\s*<span"""),
        "&#160;</span><span"
    )
}
