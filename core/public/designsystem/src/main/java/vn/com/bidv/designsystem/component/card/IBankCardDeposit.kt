package vn.com.bidv.designsystem.component.card

import IBGradient
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.IBSpacing.spacingM
import vn.com.bidv.designsystem.theme.IBSpacing.spacingS
import vn.com.bidv.designsystem.theme.IBSpacing.spacingXs
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

data class TextField(
    val value: String?,
    val color: Color? = null,
)

@Composable
fun IBankCardDeposit(
    modifier: Modifier = Modifier.fillMaxWidth(),
    icon: (@Composable () -> Unit)? = null,
    title: TextField,
    subTitle: TextField? = null,
    content: TextField,
    subContent: TextField,
    bgBrush: Brush = IBGradient.color_grd_card_primary,
    isShowLogo: Boolean = true,
    onClick: () -> Unit = {},
) {

    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current
    var iconHeight by remember { mutableIntStateOf(0) }
    val paddingVertical = if (isShowLogo) {
        spacingS
    } else {
        spacingM
    }
    val spacingCenter = if (isShowLogo) {
        spacingXs
    } else {
        spacingM
    }
    val heightPadding = if (isShowLogo) {
        2 * with(LocalDensity.current) { spacingS.roundToPx() }
    } else {
        2 * with(LocalDensity.current) { spacingM.roundToPx() }
    }

    Box(modifier = modifier
        .clip(RoundedCornerShape(IBCornerRadius.cornerRadiusL))
        .wrapContentHeight()
        .background(brush = bgBrush)
        .clickable { onClick() }) {
        Icon(
            imageVector = ImageVector.vectorResource(id = R.drawable.bidv_flower_card_deposit),
            modifier = Modifier
                .align(Alignment.CenterEnd)
                .alpha(if (isShowLogo) 1f else 0f)
                .height(with(LocalDensity.current) { (iconHeight).toDp() }),
            contentDescription = "Account Icon",
            tint = Color.Unspecified
        )

        Column(
            modifier = Modifier
                .fillMaxWidth()
//                .height(with(LocalDensity.current) { iconHeight.toDp() })
                .wrapContentHeight()
                .padding(
                    start = spacingM,
                    top = paddingVertical,
                    bottom = paddingVertical,
                    end = spacingM
                )
                .onGloballyPositioned { coordinates ->
                    iconHeight = coordinates.size.height + heightPadding
                },
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (icon != null) {
                    Box(modifier = Modifier.size(32.dp)) {
                        icon()
                    }
                    Spacer(modifier = Modifier.width(spacingXs))
                }
                Column {
                    Text(
                        color = title.color ?: colorSchema.contentOn_specialPrimary,
                        text = title.value ?: "",
                        style = typography.titleTitle_m
                    )
                    subTitle?.run {
                        Text(
                            color = subTitle.color ?: colorSchema.contentOn_specialSecondary,
                            text = subTitle.value ?: "",
                            style = typography.bodyBody_m,
                        )
                    }
                }
                Spacer(modifier = Modifier.weight(1f))
            }

            Spacer(modifier = Modifier.height(spacingCenter))

            Column(
                Modifier
                    .fillMaxWidth()
            ) {
                Text(
                    color = content.color ?: colorSchema.contentOn_specialPrimary,
                    text = content.value ?: "",
                    style = typography.bodyBody_m,
                )
                Spacer(modifier = Modifier.width(IBSpacing.spacing3xs))
                Text(
                    color = subContent.color ?: colorSchema.contentOn_specialPrimary,
                    text = subContent.value ?: "",
                    style = typography.titleTitle_m,
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ShowPreviewIBankCardDeposit() {
    var firstCardHeight by remember { mutableIntStateOf(0) }
    val spacing = with(LocalDensity.current) { ((spacingXs + spacingS) * 2).roundToPx() }
    Box {
        Column {

            Box(Modifier.wrapContentHeight()) {
                IBankCardDeposit(
                    Modifier
                        .wrapContentHeight()
                        .onGloballyPositioned { coordinates ->
                            firstCardHeight = coordinates.size.height
                        },
                    title = TextField("***********"),
                    subTitle = TextField("CÔNG TY TNHH HOA BINH"),
                    icon = {
                        Image(
                            painter = painterResource(id = R.mipmap.bidv),
                            contentDescription = "bidv"
                        )
                    },
                    content = TextField("Số tiền rút"),
                    subContent = TextField("40,000,000 VND"),
                )
            }

            Spacer(modifier = Modifier.height(spacingXs))

            val brushWhite = Brush.linearGradient(
                colors = listOf(
                    LocalColorScheme.current.bgMainTertiary,
                    LocalColorScheme.current.bgMainTertiary
                ),
                start = Offset(0f, Float.POSITIVE_INFINITY),
                end = Offset(0f, Float.POSITIVE_INFINITY)
            )
            IBankCardDeposit(
                title = TextField("***********", LocalColorScheme.current.contentMainPrimary),
                subTitle = TextField(
                    "CÔNG TY TNHH HOA BINH",
                    LocalColorScheme.current.contentMainTertiary
                ),
                icon = {
                    Image(
                        painter = painterResource(id = R.mipmap.bidv),
                        contentDescription = "bidv"
                    )
                },
                content = TextField("Số tiền rút", LocalColorScheme.current.contentMainTertiary),
                subContent = TextField(
                    "40,000,000 VND",
                    LocalColorScheme.current.contentMainPrimary
                ),
                bgBrush = brushWhite,
                isShowLogo = false
            )
        }

        Surface(
            modifier = Modifier
                .wrapContentSize()
                .align(Alignment.TopCenter)
                .offset {
                    IntOffset(
                        x = 0,
                        y = (firstCardHeight / 2) + spacing
                    )
                },
            shape = CircleShape
        ) {
            Box(
                modifier = Modifier
                    .size(32.dp)
                    .clip(CircleShape)
                    .background(LocalColorScheme.current.bgMainTertiary),
                contentAlignment = Alignment.Center
            ) {
                Box(
                    modifier = Modifier
                        .size(24.dp)
                        .clip(CircleShape)
                        .background(LocalColorScheme.current.bgMainPrimary),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        modifier = Modifier.padding(4.dp),
                        painter = painterResource(R.drawable.arrow2_bottom),
                        contentDescription = "",
                        tint = LocalColorScheme.current.contentMainPrimary,
                    )
                }
            }
        }
    }
}