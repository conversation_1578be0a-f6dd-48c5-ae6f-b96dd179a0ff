package vn.com.bidv.designsystem.component.dataentry

import androidx.annotation.DrawableRes
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBColorScheme
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

@Composable
fun IBankDropDown(
    modifier: Modifier = Modifier,
    labelText: String,
    state: IBFrameState = IBFrameState.DEFAULT(LocalColorScheme.current),
    hintTextStart: String? = "",
    hintTextEnd: String? = "",
    @DrawableRes iconEnd: Int = R.drawable.arrow_bottom,
    onClickEnd: () -> Unit = {},
) {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current
    val hintTextColor = when (state) {
        is IBFrameState.ERROR -> IBHintTextColor.ERROR(colorScheme).hintTextColor
        else -> IBHintTextColor.DEFAULT(colorScheme).hintTextColor
    }

    Column {
        DropdownBody(
            modifier = modifier,
            labelText = labelText,
            state = state,
            iconEnd = iconEnd,
            onClickEnd = onClickEnd
        )
        if (!hintTextStart.isNullOrEmpty() || !hintTextEnd.isNullOrEmpty()) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(
                        top = IBSpacing.spacing2xs, start = IBSpacing.spacing2xs
                    ), horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = hintTextStart ?: "",
                    style = typography.captionCaption_m,
                    color = hintTextColor,
                )
                Text(
                    modifier = Modifier.padding(end = IBSpacing.spacing2xs),
                    text = hintTextEnd ?: "",
                    style = typography.captionCaption_m,
                    color = hintTextColor,
                )
            }
        }
    }
}

@Composable
fun DropdownBody(
    modifier: Modifier = Modifier,
    labelText: String,
    iconEnd: Int,
    state: IBFrameState = IBFrameState.DEFAULT(LocalColorScheme.current),
    onClickEnd: () -> Unit = {},
) = IBankInputFrameBase(state = state) {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current

    val style = when (state) {
        is IBFrameState.DEFAULT -> Style.DEFAULT(colorScheme)
        is IBFrameExtendState.FILLED -> Style.FILLED(colorScheme)
        is IBFrameState.FOCUS -> Style.FOCUS(colorScheme)
        is IBFrameState.DISABLE -> Style.DISABLE(colorScheme)
        is IBFrameState.ERROR -> Style.ERROR(colorScheme)
        is IBFrameExtendState.PRESSED -> Style.PRESSED(colorScheme)
        is IBFrameExtendState.FIXED -> Style.DEFAULT(colorScheme)
    }
    val labelStyle = typography.bodyBody_l
    val labelColor = style.labelColor
    val iconEndTint = style.iconEndTint

    Row(modifier = modifier
        .fillMaxWidth()
        .background(state.colorBackground)
        .wrapContentHeight()
        .clickable { onClickEnd() }
        .padding(
            start = IBSpacing.spacingM,
            end = IBSpacing.spacingM,
            top = IBSpacing.spacingM,
            bottom = IBSpacing.spacingM
        ), verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Box(modifier = Modifier.weight(1f), contentAlignment = Alignment.CenterStart) {
            val testTag = if (state is IBFrameState.DEFAULT) "IBankDropDown_$labelText" else "IBankDropDown_text_value"
            Text(
                labelText,
                style = labelStyle,
                color = labelColor,
                textAlign = TextAlign.Start,
                modifier = Modifier.testTagIBank(testTag),
            )
        }
        Icon(
            modifier = Modifier.testTagIBank("IBankDropDown_iconEnd").size(IBSpacing.spacingM),
            tint = iconEndTint,
            imageVector = ImageVector.vectorResource(id = iconEnd),
            contentDescription = null,
        )
    }


}

sealed class Style(
    val labelColor: Color,
    val iconEndTint: Color,
) {
    class DEFAULT(colorScheme: IBColorScheme) : Style(
        labelColor = colorScheme.contentPlaceholder,
        iconEndTint = colorScheme.contentMainSecondary,
    )

    class FILLED(colorScheme: IBColorScheme) : Style(
        labelColor = colorScheme.contentMainPrimary,
        iconEndTint = colorScheme.contentMainPrimary,
    )

    class FOCUS(colorScheme: IBColorScheme) : Style(
        labelColor = colorScheme.contentMainPrimary,
        iconEndTint = colorScheme.contentMainSecondary,
    )

    class ERROR(colorScheme: IBColorScheme) : Style(
        labelColor = colorScheme.contentPlaceholder,
        iconEndTint = colorScheme.contentMainSecondary,
    )

    class PRESSED(colorScheme: IBColorScheme) : Style(
        labelColor = colorScheme.contentMainPrimary,
        iconEndTint = colorScheme.contentMainSecondary,
    )

    class DISABLE(colorScheme: IBColorScheme) : Style(
        labelColor = colorScheme.contentPlaceholder,
        iconEndTint = colorScheme.contentDisablePrimary,
    )
}

@Preview
@Composable
fun PreviewDropDown() {
    val colorScheme = LocalColorScheme.current
    Spacer(Modifier.height(16.dp))
    Box(
        modifier = Modifier.padding(
            horizontal = IBSpacing.spacingM, vertical = IBSpacing.spacingS
        )
    ) {
        IBankDropDown(
            labelText = "Label",
            state = IBFrameState.DEFAULT(colorScheme),
            hintTextStart = "Hint text start",
            hintTextEnd = "Hint text end",
            onClickEnd = {}
        )
    }
}

