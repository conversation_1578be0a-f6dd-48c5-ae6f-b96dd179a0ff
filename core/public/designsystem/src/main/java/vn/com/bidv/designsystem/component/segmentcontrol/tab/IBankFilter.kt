package vn.com.bidv.designsystem.component.segmentcontrol.tab

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.FilterChip
import androidx.compose.material3.FilterChipDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import vn.com.bidv.designsystem.component.datadisplay.badge.IBankBadgeLabel
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelColor
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelSize
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelType
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

/**
 * A composable function that represents a filter chip with a text label and
 * an optional badge to display a number. The chip can toggle between selected and unselected states,
 * and allows customization based on its selection state.
 *
 * @param modifier Modifier applied to the `IBankFilter` for custom styling.
 * @param text The text displayed on the filter chip.
 * @param badgeNumber Optional text displayed as a badge on the chip, typically used to show a count.
 * @param isSelected Indicates whether the filter chip is currently selected.
 * @param onSelectedChange Callback function invoked when the selection state of the chip changes.
 */
@Composable
fun IBankFilter(
    modifier: Modifier = Modifier,
    text: String,
    badgeNumber: String? = null,
    isSelected: Boolean = false,
    onSelectedChange: (Boolean) -> Unit
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current

    val interactionSource = remember { MutableInteractionSource() }

    val isPressed = interactionSource.collectIsPressedAsState().value
    var titleColor by remember { mutableStateOf(colorScheme.contentMainSecondary) }
    var textStyle by remember { mutableStateOf(typography.bodyBody_m) }

    var selectedContainerColor by remember { mutableStateOf(Color.Transparent) }
    var containerColor by remember { mutableStateOf(Color.Transparent) }

    if (isSelected) {
        textStyle = typography.titleTitle_s
        if (isPressed) {
            selectedContainerColor = colorScheme.bgBrand_01Tertiary_press
        } else {
            selectedContainerColor = colorScheme.bgMainTertiary
        }
        titleColor = colorScheme.contentBrand_01Primary
    } else {
        if (isPressed) {
            containerColor = colorScheme.bgMainPrimary_press
        } else {
            containerColor = colorScheme.bgMainSecondary
            selectedContainerColor = colorScheme.bgMainSecondary
        }
        titleColor = colorScheme.contentMainSecondary
    }

    FilterChip(
        interactionSource = interactionSource,
        modifier = modifier,
        shape = RoundedCornerShape(IBCornerRadius.cornerRadiusRound),
        selected = isSelected,
        onClick = { onSelectedChange(!isSelected) },
        label = {
            Text(
                text = text,
                color = titleColor,
                style = textStyle,
            )
        },
        trailingIcon = {
            if (!badgeNumber.isNullOrBlank()) {
                if (isSelected) {
                    IBankBadgeLabel(
                        modifier = Modifier,
                        badgeColor = LabelColor.BRAND,
                        badgeSize = LabelSize.SM,
                        badgeType = LabelType.PILL,
                        title = badgeNumber
                    )
                } else {
                    IBankBadgeLabel(
                        modifier = Modifier,
                        badgeColor = LabelColor.LIGHT_GRAY,
                        badgeSize = LabelSize.SM,
                        badgeType = LabelType.PILL,
                        title = badgeNumber
                    )
                }
            }
        },
        colors = FilterChipDefaults.filterChipColors(
            containerColor = containerColor, selectedContainerColor = selectedContainerColor
        ),
        border = if (isSelected) BorderStroke(
            width = IBBorderDivider.borderDividerS, color = colorScheme.borderBrandTertiary_press
        ) else null
    )
}

