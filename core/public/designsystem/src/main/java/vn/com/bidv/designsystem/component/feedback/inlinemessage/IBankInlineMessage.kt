package vn.com.bidv.designsystem.component.feedback.inlinemessage

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.theme.IBColorScheme
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

@Composable
fun InlineMessage(
    modifier: Modifier = Modifier,
    message: String,
    showLeading: Boolean? = true,
    title: String? = null,
    status: InlineMessageStatus = InlineMessageStatus.Brand(LocalColorScheme.current)
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .background(
                color = status.backgroundColor,
                shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL)
            )
            .padding(IBSpacing.spacingS)
            .wrapContentHeight()
    ) {
        Row(
            modifier = Modifier.fillMaxWidth()
        ) {
            if (showLeading == true) {
                Icon(
                    imageVector = ImageVector.vectorResource(id = status.leadingIconId),
                    contentDescription = null,
                    tint = status.leadingIconColor,
                    modifier = Modifier
                        .size(status.leadingIconSize)
                        .align(alignment = Alignment.Top)
                )
                Spacer(modifier = Modifier.width(IBSpacing.spacingXs))
            }
            Column(
                verticalArrangement = Arrangement.aligned(alignment = Alignment.Top),
                horizontalAlignment = Alignment.Start
            ) {
                if (title?.isNotEmpty() == true) {
                    Text(
                        modifier = Modifier.padding(bottom = IBSpacing.spacing2xs),
                        text = title ?: "",
                        style = LocalTypography.current.titleTitle_m,
                        color = LocalColorScheme.current.contentMainPrimary,
                    )
                }
                Text(
                    text = message,
                    style = LocalTypography.current.bodyBody_m,
                    color = LocalColorScheme.current.contentMainSecondary
                )
            }
        }
    }
}

sealed class InlineMessageStatus(
    val backgroundColor: Color,
    val leadingIconColor: Color,
    val leadingIconSize: Dp,
    val leadingIconId: Int
) {

    class Success(colorScheme: IBColorScheme) : InlineMessageStatus(
        backgroundColor = colorScheme.bgPositiveTertiary,
        leadingIconColor = colorScheme.contentPositivePrimary,
        leadingIconSize = IBSpacing.spacing2xl,
        leadingIconId = R.drawable.success_outline
    )

    class Brand(colorScheme: IBColorScheme) : InlineMessageStatus(
        backgroundColor = colorScheme.bgBrand_01Tertiary,
        leadingIconColor = colorScheme.contentBrand_01Primary,
        leadingIconSize = IBSpacing.spacing2xl,
        leadingIconId = R.drawable.information_circle_outline
    )

    class Error(colorScheme: IBColorScheme) : InlineMessageStatus(
        backgroundColor = colorScheme.bgNegativeTertiary,
        leadingIconColor = colorScheme.contentNegativeSecondary,
        leadingIconSize = IBSpacing.spacing2xl,
        leadingIconId = R.drawable.error_outline
    )

    class Info(colorScheme: IBColorScheme) : InlineMessageStatus(
        backgroundColor = colorScheme.bgInfoTertiary,
        leadingIconColor = colorScheme.contentInfoSecondary,
        leadingIconSize = IBSpacing.spacing2xl,
        leadingIconId = R.drawable.information_circle_outline
    )

    class Warning(colorScheme: IBColorScheme) : InlineMessageStatus(
        backgroundColor = colorScheme.bgWarningTertiary,
        leadingIconColor = colorScheme.contentWarningPrimary,
        leadingIconSize = IBSpacing.spacing2xl,
        leadingIconId = R.drawable.warning
    )

    class Gray(colorScheme: IBColorScheme) : InlineMessageStatus(
        backgroundColor = colorScheme.bgNon_opaqueDefault,
        leadingIconColor = colorScheme.contentMainPrimary,
        leadingIconSize = IBSpacing.spacing2xl,
        leadingIconId = R.drawable.information_circle_outline
    )
}

@Preview
@Composable
fun InlineMessageExample() {
    Column(Modifier.background(Color.White)) {
        InlineMessage(
            message = "Trường hợp quên mật khẩu, chúng tôi sẽ yêu cầu Quý khách trả lời câu hỏi bảo mật để xác minh danh tính",
            title = "Thông báo",
            status = InlineMessageStatus.Brand(LocalColorScheme.current)
        )
        Spacer(modifier = Modifier.height(16.dp))
        InlineMessage(
            message = "Trường hợp quên mật khẩu, chúng tôi sẽ yêu cầu Quý khách trả lời câu hỏi bảo mật để xác minh danh tính",
            title = "Thông báo",
            showLeading = false,
            status = InlineMessageStatus.Success(LocalColorScheme.current)
        )
        Spacer(modifier = Modifier.height(16.dp))
        InlineMessage(
            message = "Trường hợp quên mật khẩu, chúng tôi sẽ yêu cầu Quý khách trả lời câu hỏi bảo mật để xác minh danh tính",
            title = "Thông báo",
            status = InlineMessageStatus.Error(LocalColorScheme.current)
        )
        Spacer(modifier = Modifier.height(16.dp))
        InlineMessage(
            message = "Trường hợp quên mật khẩu, chúng tôi sẽ yêu cầu Quý khách trả lời câu hỏi bảo mật để xác minh danh tính",
            title = "Thông báo",
            status = InlineMessageStatus.Info(LocalColorScheme.current)
        )
        Spacer(modifier = Modifier.height(16.dp))
        InlineMessage(
            message = "Trường hợp quên mật khẩu, chúng tôi sẽ yêu cầu Quý khách trả lời câu hỏi bảo mật để xác minh danh tính",
            title = "Thông báo",
            status = InlineMessageStatus.Warning(LocalColorScheme.current)
        )
        Spacer(modifier = Modifier.height(16.dp))
        InlineMessage(
            message = "Trường hợp quên mật khẩu, chúng tôi sẽ yêu cầu Quý khách trả lời câu hỏi bảo mật để xác minh danh tính",
            title = "Thông báo",
            status = InlineMessageStatus.Gray(LocalColorScheme.current)
        )
    }
}