package vn.com.bidv.designsystem.component.dataentry

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import vn.com.bidv.designsystem.component.datadisplay.badge.IBankBadgeLabel
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelColor
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelSize
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelType
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

enum class InformationType {
    DefaultInformation, HighlightInformation, StatusInformation
}

data class IBankBadgeConfig(
    val title: String,
    val badgeSize: LabelSize = LabelSize.SM,
    val badgeColor: LabelColor = LabelColor.INFO,
    val badgeType: LabelType = LabelType.ROUNDED
)

@Composable
fun IBankInformation(
    modifier: Modifier = Modifier,
    informationType: InformationType = InformationType.DefaultInformation,
    label: String,
    dataValue: String,
    supportingText: String? = null,
    badgeConfig: IBankBadgeConfig? = null
) {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current

    Box(
        modifier = modifier
    ) {
        Row(
            verticalAlignment = Alignment.Top,
            modifier = Modifier.padding(vertical = IBSpacing.spacingXs),
            horizontalArrangement = Arrangement.spacedBy(IBSpacing.spacingXs)
        ) {
            BoxWithConstraints {
                val maxWidthLimit = maxWidth * (2f / 3f)
                Text(
                    modifier = Modifier
                        .widthIn(max = maxWidthLimit)
                        .testTagIBank("IBankInformation_$label"),
                    style = typography.bodyBody_m,
                    color = colorScheme.contentMainTertiary,
                    text = label
                )
            }

            Column(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                horizontalAlignment = Alignment.End,
                verticalArrangement = Arrangement.Center
            ) {
                if (informationType != InformationType.StatusInformation) {
                    if (dataValue != "") {
                        Text(
                            modifier = Modifier.testTagIBank("IBankInformation_${label}_dataValue"),
                            style = typography.titleTitle_s,
                            color = if (informationType == InformationType.HighlightInformation)
                                colorScheme.contentBrand_01Primary else colorScheme.contentMainPrimary,
                            text = dataValue,
                            textAlign = TextAlign.End,
                        )
                    }

                    supportingText?.let {
                        Spacer(modifier = Modifier.height(IBSpacing.spacing2xs))
                        Text(
                            modifier = Modifier.testTagIBank("IBankInformation_${label}_supportingText"),
                            style = typography.captionCaption_m,
                            color = colorScheme.contentMainTertiary,
                            text = supportingText,
                            textAlign = TextAlign.End
                        )
                    }
                }

                badgeConfig?.let {
                    if (informationType != InformationType.StatusInformation) Spacer(
                        modifier = Modifier.height(
                            IBSpacing.spacing2xs
                        )
                    )
                    IBankBadgeLabel(
                        modifier = Modifier,
                        title = badgeConfig.title,
                        badgeSize = badgeConfig.badgeSize,
                        badgeColor = badgeConfig.badgeColor,
                        badgeType = badgeConfig.badgeType
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun Preview() {
    IBankInformation(
        modifier = Modifier.padding(horizontal = IBSpacing.spacingM),
        label = "Số tiền giao dịch aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        dataValue = "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
    )
}
