package vn.com.bidv.designsystem.component.dataentry

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

/**
 * A base composable function for rendering a customizable input field with various features.
 *
 * @param required Indicates if the field is mandatory (default: false).
 * @param isTextArea Specifies if the field should behave as a multi-line text area (default: false).
 * @param text The current text string without selection and composition in TextFieldValue. Only use text when textValue is null
 * @param textValue The current text value of the input field (default: empty TextFieldValue).
 * @param maxLengthText An optional maximum length for the input text.
 * @param placeholderText Placeholder text displayed when the input field is empty (default: empty string).
 * @param filters A list of custom text filters to apply to the input.
 * @param inputType Configures the keyboard options for the input field (default: text keyboard).
 * @param textStyle The text style to apply to the input field (default: bodyBody_m from LocalTypography).
 * @param showIconHelp Specifies if a help icon should be displayed (default: false).
 * @param iconEnd A composable for displaying a custom trailing icon.
 * @param customTail A composable for adding a custom tail element to the input field.
 * @param state The visual state of the input field, including color and style (default: IBFrameState.DEFAULT).
 * @param helpTextLeft Text to display on the left side of the help section (default: empty string).
 * @param helpTextRight Text to display on the right side of the help section (default: empty string).
 * @param maxLineText An optional maximum line for the input text (default: 3 lines).
 * @param onClickHelp Callback invoked when the help icon is clicked (default: no-op).
 * @param onClickClear Callback invoked when the clear button is clicked (default: no-op).
 * @param onValueChange Callback invoked whenever the input text changes, providing the updated value.
 */

@Composable
fun IBankInputFieldBase(
    required: Boolean? = false,
    isTextArea: Boolean = false,
    text: String = "",
    textValue: TextFieldValue? = null,
    maxLengthText: Int? = null,
    isShowMaxLength: Boolean = false,
    placeholderText: String = "",
    placeholderColor: Color? = null,
    filters: List<IBankFilterTextField>? = null,
    inputType: KeyboardOptions? = KeyboardOptions(keyboardType = KeyboardType.Text),
    textStyle: TextStyle = LocalTypography.current.bodyBody_l,
    showIconHelp: Boolean = false,
    iconEnd: @Composable (() -> Unit)? = null,
    customTail: @Composable (() -> Unit)? = null,
    state: IBFrameState = IBFrameState.DEFAULT(LocalColorScheme.current),
    helpTextLeft: String = "",
    helpTextRight: String = "",
    maxLineText: Int = 3,
    onFocusChange: (Boolean) -> Unit = {},
    visualTransformation: VisualTransformation = VisualTransformation.None,
    isAutoFocus: Boolean = false,
    onClickHelp: () -> Unit? = {},
    onClickClear: () -> Unit = {},
    onSubmitText: (TextFieldValue) -> Unit = {},
    onValueChange: (TextFieldValue) -> Unit,
) {
    Column {
        IBankInputFieldBody(
            isTextArea = isTextArea,
            text = text,
            textValue = textValue,
            maxLengthText = maxLengthText,
            isShowMaxLength = isShowMaxLength,
            required = required,
            placeholderText = placeholderText,
            placeholderColor = placeholderColor,
            filters = filters,
            inputType = inputType,
            textStyle = textStyle,
            showIconHelp = showIconHelp,
            iconEnd = iconEnd,
            customTail = customTail,
            state = state,
            visualTransformation = visualTransformation,
            onClickHelp = onClickHelp,
            onSubmitText = onSubmitText,
            isAutoFocus = isAutoFocus,
            maxLineText = maxLineText,
            onValueChange = {
                onValueChange(it)
            },
            onClickClear = onClickClear,
            onFocusChange = onFocusChange,
        )

        if (helpTextLeft.isNotEmpty() || helpTextRight.isNotEmpty()) {
            IBankInputHelpText(
                modifier = Modifier.padding(top = IBSpacing.spacingXs),
                title = helpTextLeft,
                subText = helpTextRight,
                textStyle = LocalTypography.current.bodyBody_m,
                isErrorState = (state is IBFrameState.ERROR)
            )
        }
    }
}

@Composable
private fun IBankInputFieldBody(
    required: Boolean? = false,
    text: String = "",
    textValue: TextFieldValue? = null,
    isTextArea: Boolean = false,
    maxLengthText: Int? = null,
    isShowMaxLength: Boolean = false,
    placeholderText: String = "",
    placeholderColor: Color? = null,
    filters: List<IBankFilterTextField>? = null,
    inputType: KeyboardOptions? = KeyboardOptions(keyboardType = KeyboardType.Text),
    textStyle: TextStyle,
    showIconHelp: Boolean = false,
    iconEnd: @Composable (() -> Unit)? = null,
    customTail: @Composable (() -> Unit)? = null,
    state: IBFrameState = IBFrameState.DEFAULT(LocalColorScheme.current),
    visualTransformation: VisualTransformation = VisualTransformation.None,
    isAutoFocus: Boolean = false,
    maxLineText: Int,
    onFocusChange: (Boolean) -> Unit,
    onClickHelp: () -> Unit? = {},
    onClickClear: () -> Unit = {},
    onSubmitText: (TextFieldValue) -> Unit = {},
    onValueChange: (TextFieldValue) -> Unit,
) {
    val focusManager = LocalFocusManager.current
    var isFocus by remember { mutableStateOf(false) }
    var textFieldValue by remember {
        if (textValue != null) {
            mutableStateOf(textValue)
        } else {
            mutableStateOf(TextFieldValue(text, selection = TextRange(text.length)))
        }
    }
    SideEffect {
        if (textValue != null) {
            val newStringValue = if (textFieldValue.text == textValue.text) textValue.text
            else applyFilterText(textValue.text, filters)
            textFieldValue = textValue.copy(newStringValue)
        } else {
            val newStringValue = if (textFieldValue.text == text) text
            else applyFilterText(text, filters)
            textFieldValue = textFieldValue.copy(newStringValue)
        }
    }
    IBankInputFrameBase(state = if (isFocus) {
        IBFrameState.FOCUS(LocalColorScheme.current)
    } else {
        state
    }, bodyContent = {
        val focusRequester = remember { FocusRequester() }
        val typography = LocalTypography.current
        val colorScheme = LocalColorScheme.current
        var inputCounter = textFieldValue.text.length

        LaunchedEffect(Unit) {
            if (isAutoFocus) {
                focusRequester.requestFocus()
            }
        }

        Column(modifier = Modifier.testTagIBank("IBankInputField_$placeholderText")
            .focusRequester(focusRequester)
            .onFocusChanged {
                if (isFocus != it.hasFocus) {
                    isFocus = it.hasFocus
                    onFocusChange(isFocus)
                }
            }
            .clickable { focusRequester.requestFocus() }
            .height(IntrinsicSize.Min)
            .padding(
                vertical = IBSpacing.spacingXs, horizontal = IBSpacing.spacingM
            )) {
            val isVisible = isFocus || inputCounter > 0
            val basicTextModifier = Modifier.fillMaxWidth()
            if (isTextArea) {
                basicTextModifier.horizontalScroll(state = rememberScrollState())
            }
            Row(verticalAlignment = Alignment.CenterVertically) {
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .height(IntrinsicSize.Min)
                ) {

                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .alpha(if (isVisible) 1f else 0f)
                    ) {
                        Row {
                            Text(
                                placeholderText,
                                style = typography.captionCaption_m,
                                color = placeholderColor ?: colorScheme.contentMainTertiary,
                                modifier = Modifier
                            )
                            if (required == true) {
                                DotRequire()
                            }
                        }
                        Spacer(modifier = Modifier.height(2.dp))
                        BasicTextField(
                            modifier = basicTextModifier,
                            value = textFieldValue,
                            keyboardOptions = inputType?.copy(imeAction = ImeAction.Done)
                                ?: KeyboardOptions(keyboardType = KeyboardType.Text).copy(
                                    imeAction = ImeAction.Done
                                ),
                            keyboardActions = KeyboardActions(onDone = {
                                focusManager.clearFocus()
                                onSubmitText(textFieldValue)
                            }),
                            textStyle = textStyle,
                            onValueChange = {
                                if (textValue == null && it.text == textFieldValue.text) {
                                    textFieldValue = it
                                    return@BasicTextField
                                }
                                var newStringValue = applyFilterText(it.text, filters)
                                if (textFieldValue.text.length == maxLengthText && newStringValue.length > maxLengthText) {
                                    return@BasicTextField
                                }

                                if (maxLengthText != null && newStringValue.length > maxLengthText) {
                                    newStringValue = newStringValue.substring(0, maxLengthText)
                                    newStringValue = applyFilterText(newStringValue, filters)
                                }

                                inputCounter = newStringValue.length
                                val currentCursor = textFieldValue.selection.start
                                textFieldValue = if (currentCursor == textFieldValue.text.length) {
                                    it.copy(
                                        newStringValue,
                                        TextRange(newStringValue.length)
                                    )
                                } else {
                                    it.copy(newStringValue)
                                }
                                onValueChange(textFieldValue)
                            },
                            enabled = state !is IBFrameState.DISABLE,
                            maxLines = maxLineText,
                            cursorBrush = SolidColor(colorScheme.contentBrand_01Primary),
                            singleLine = !isTextArea,
                            visualTransformation = visualTransformation
                        )
                        if (maxLengthText != null && isShowMaxLength) {
                            Box {
                                TextCounter(
                                    counter = inputCounter,
                                    max = maxLengthText,
                                    modifier = Modifier.align(Alignment.BottomEnd).testTagIBank("IBankInputFieldBase_TextCounter_${placeholderText}")
                                )
                            }
                        }
                    }
                    Row(
                        modifier = Modifier
                            .fillMaxSize()
                            .alpha(if (isFocus || textFieldValue.text.isNotEmpty()) 0f else 1f),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        Row {
                            Text(
                                placeholderText,
                                style = typography.bodyBody_l,
                                color = placeholderColor ?: colorScheme.contentPlaceholder,
                                modifier = Modifier
                            )
                            if (required == true) {
                                DotRequire()
                            }
                        }
                        if (maxLengthText != null && isShowMaxLength && !isFocus && textFieldValue.text.isEmpty()) {
                            Box(
                                Modifier
                                    .fillMaxWidth()
                                    .align(Alignment.CenterVertically)
                            ) {
                                TextCounter(
                                    counter = inputCounter,
                                    max = maxLengthText,
                                    modifier = Modifier.align(Alignment.CenterEnd)
                                )
                            }

                        }
                    }
                }

                Row(verticalAlignment = Alignment.CenterVertically) {
                    if (isFocus && textFieldValue.text.isNotEmpty()) {
                        Image(painterResource(id = R.drawable.circle_close_outline),
                            contentDescription = "",
                            Modifier
                                .size(IBSpacing.spacingL)
                                .clickable {
                                    inputCounter = 0
                                    onClickClear()
                                })
                        Spacer(modifier = Modifier.width(2.dp))
                    }

                    if (!isShowMaxLength) {
                        if (showIconHelp) {
                            Image(painter = painterResource(id = R.drawable.question),
                                contentDescription = "",
                                colorFilter = ColorFilter.tint(color = colorScheme.contentMainTertiary),
                                modifier = Modifier
                                    .padding(start = 4.dp)
                                    .size(16.dp)
                                    .clickable {
                                        onClickHelp()
                                    })
                        }

                        iconEnd?.let { it() }

                        if (customTail != null) {
                            Box {
                                Row(
                                    Modifier.align(Alignment.BottomEnd),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    VerticalDivider(
                                        Modifier
                                            .padding(horizontal = IBSpacing.spacingS)
                                            .width(IBSpacing.spacing3xs),
                                        color = colorScheme.borderMainPrimary
                                    )
                                    customTail()
                                }
                            }
                        }
                    }

                }

            }

        }

    })
}

fun applyFilterText(text: String?, filters: List<IBankFilterTextField>? = null): String {
    if (text.isNullOrBlank()) return ""
    var filteredText = text

    filters?.forEach { filter ->
        filteredText = filter.apply(filteredText ?: "")
    }
    return filteredText ?: ""
}

@Composable
private fun TextCounter(modifier: Modifier = Modifier, max: Int, counter: Int) {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current

    Box(Modifier.fillMaxWidth()) {
        val str = "$counter/$max"
        Text(
            str,
            style = typography.captionCaption_m,
            color = colorScheme.contentMainTertiary,
            modifier = modifier,
            textAlign = TextAlign.Center,
        )
    }
}

@Composable
private fun DotRequire() {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current

    Text(
        "*",
        style = typography.captionCaption_m,
        color = colorScheme.contentNegativePrimary,
        modifier = Modifier.padding(start = IBSpacing.spacing2xs)
    )
}

@Preview
@Composable
fun PreviewInputFieldBase() {
    // Code mẫu sử dụng Component
    val colorScheme = LocalColorScheme.current
    Spacer(Modifier.height(16.dp))

    var inputValue1 by remember { mutableStateOf(TextFieldValue("")) }

    Column {

        Box(Modifier.padding(horizontal = 12.dp)) {

            val lstFilter = listOf(
                SpecialCharacterFilter(),
                RemoveVietnameseAccentFilter(),
                NumberFilter(),
            )

            IBankInputFieldBase(textValue = inputValue1,
                placeholderText = "Nhập tên tài khoản",
                required = true,
                filters = lstFilter,
                inputType = KeyboardOptions(keyboardType = KeyboardType.Password),
                maxLengthText = 200,
                isTextArea = true,
                state = IBFrameState.DEFAULT(colorScheme),
                onValueChange = {
                    inputValue1 = it
                },
                onClickClear = {
                    inputValue1 = TextFieldValue("")
                })
        }
        Spacer(Modifier.height(16.dp))
        Box(Modifier.padding(horizontal = 12.dp)) {

            var currencyCode by remember { mutableStateOf("VND") }
            val pos by remember { mutableStateOf(0) }

            var inputValue2 by remember { mutableStateOf("") }

            IBankInputMoney(
                textValue = "",
                placeholderText = pos.toString(),
                required = false,
                currencyCode = currencyCode,
                maxLengthText = 20,
                state = IBFrameState.DEFAULT(colorScheme),
                onAmountChange = {
                    inputValue2 = it
                },
                onSelectCurrency = {
                    currencyCode = if (currencyCode == "VND") {
                        "USD"
                    } else {
                        "VND"
                    }
                },
                onClickClear = {
                    inputValue2 = ""
                })

        }
        Spacer(Modifier.height(16.dp))

    }
    // Code mẫu sử dụng Component
}
