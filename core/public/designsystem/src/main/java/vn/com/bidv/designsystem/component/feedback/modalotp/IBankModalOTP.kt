package vn.com.bidv.designsystem.component.feedback.modalotp

import android.os.CountDownTimer
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.dataentry.IBankInputHelpText
import vn.com.bidv.designsystem.component.feedback.otpview.IBankOtpItemInputList
import vn.com.bidv.designsystem.component.keyboard.KeyInput
import vn.com.bidv.designsystem.component.keyboard.NumpadKeyboard
import vn.com.bidv.designsystem.component.navigation.button.IBankLinkButton
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.localization.R as RLocalization

data class IBankModalOTPConfig(
    val supportingText: String,
    val timeoutResendOTP: String,
    val timeoutEffectOTP: String,
    val onRetrySendOTP: () -> Unit,
    val onVerifyOTP: (String) -> Unit,
)

@Composable
fun IBankModalOTP(
    modifier: Modifier,
    title: String = stringResource(RLocalization.string.thong_bao),
    config: IBankModalOTPConfig,
    dialogProperties: DialogProperties = DialogProperties(
        dismissOnBackPress = false,
        dismissOnClickOutside = false,
        usePlatformDefaultWidth = false
    ),
    onDismissRequest: () -> Unit = { },
) {
    Dialog(
        onDismissRequest = {
            onDismissRequest()
        },
        properties = dialogProperties
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .border(8.dp, Color.Transparent, shape = RoundedCornerShape(8.dp))
                .clip(shape = RoundedCornerShape(8.dp)),
        ) {
            IBankModalOTPContent(
                modifier = modifier,
                title = title,
                config = config,
                onDismissRequest = onDismissRequest
            )
        }
    }

}

@Composable
private fun IBankModalOTPContent(
    modifier: Modifier,
    title: String,
    config: IBankModalOTPConfig,
    onDismissRequest: () -> Unit = { },
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current
    val timeoutResendOTP = (config.timeoutResendOTP).toIntOrNull() ?: 0
    val timeoutEffectOTP = (config.timeoutEffectOTP).toIntOrNull() ?: 0

    var timeoutResendOTPObs by remember { mutableIntStateOf(timeoutResendOTP) }
    var timeoutEffectOTPObs by remember { mutableIntStateOf(timeoutEffectOTP) }
    var reLoadNewOtp by remember { mutableIntStateOf(0) }
    var otpTextObs by remember { mutableStateOf("") }

    val countDownTimerResendOTP = object : CountDownTimer(timeoutResendOTP * 1000L, 1000) {
        override fun onTick(millisUntilFinished: Long) {
            timeoutResendOTPObs = (millisUntilFinished / 1000L).toInt() + 1
        }

        override fun onFinish() {
            timeoutResendOTPObs = 0
        }
    }

    val countDownTimerEffectOTP = object : CountDownTimer(timeoutEffectOTP * 1000L, 1000) {
        override fun onTick(millisUntilFinished: Long) {
            timeoutEffectOTPObs = (millisUntilFinished / 1000L).toInt() + 1
        }

        override fun onFinish() {
            timeoutEffectOTPObs = 0
        }
    }

    DisposableEffect(key1 = reLoadNewOtp) {
        countDownTimerResendOTP.start()
        onDispose {
            countDownTimerResendOTP.cancel()
        }
    }

    DisposableEffect(key1 = reLoadNewOtp) {
        countDownTimerEffectOTP.start()
        onDispose {
            countDownTimerEffectOTP.cancel()
        }
    }

    Column (modifier.fillMaxSize()) {
        Box(
            modifier = Modifier.weight(1f),
            contentAlignment = Alignment.Center
        ) {
            Surface(
                shape = RoundedCornerShape(IBSpacing.spacingL),
                color = colorScheme.contentOn_specialPrimary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = IBSpacing.spacingL, end = IBSpacing.spacingL),
            ) {
                Box {
                    Column(
                        modifier = Modifier
                            .wrapContentSize()
                            .padding(
                                start = IBSpacing.spacingL,
                                top = IBSpacing.spacingM,
                                bottom = IBSpacing.spacingL,
                                end = IBSpacing.spacingM
                            ),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {

                        Spacer(modifier = Modifier.height(IBSpacing.spacingS))

                        Text(
                            text = title,
                            style = typography.titleTitle_l,
                            modifier = Modifier
                                .padding(horizontal = IBSpacing.spacing2xl),
                            textAlign = TextAlign.Center
                        )

                        Spacer(modifier = Modifier.height(IBSpacing.spacingXs))

                        Text(
                            text = config.supportingText,
                            style = typography.bodyBody_m,
                            textAlign = TextAlign.Center
                        )

                        Spacer(modifier = Modifier.height(IBSpacing.spacingM))

                        IBankOtpItemInputList(
                            modifier = Modifier.fillMaxWidth(),
                            otpItemNumber = 6,
                            otpText = otpTextObs,
                            isFocus = true,
                        ) {
                            config.onVerifyOTP(it)
                        }

                        if (timeoutEffectOTPObs <= 0) {
                            IBankInputHelpText(
                                modifier = Modifier
                                    .padding(top = IBSpacing.spacingXs)
                                    .align(Alignment.End),
                                title = "",
                                subText = stringResource(RLocalization.string.ma_otp_da_het_thoi_gian_hieu_luc),
                                isErrorState = true
                            )
                        }

                        Spacer(modifier = Modifier.height(IBSpacing.spacingS))

                        Row(
                            modifier = Modifier
                                .wrapContentSize()
                                .align(Alignment.CenterHorizontally)
                        ) {
                            CountdownProgressCircle(
                                modifier = Modifier
                                    .size(14.dp),
                                totalTime = timeoutResendOTP,
                                currentTime = timeoutResendOTPObs
                            )
                            Spacer(modifier = Modifier.size(3.dp))
                            Text(
                                text = "${timeoutResendOTPObs}s",
                                style = typography.bodyBody_m,
                                color = colorScheme.contentMainTertiary,
                                textAlign = TextAlign.Center
                            )
                        }

                        IBankLinkButton(
                            modifier = Modifier.wrapContentSize(),
                            isEnable = timeoutResendOTPObs <= 0,
                            text = stringResource(RLocalization.string.gui_lai_otp),
                            onClick = {
                                reLoadNewOtp++
                                config.onRetrySendOTP()
                            },
                        )

                    }
                    IconButton(
                        modifier = Modifier.align(Alignment.TopEnd),
                        onClick = onDismissRequest
                    ) {
                        Icon(
                            painter = painterResource(R.drawable.modal_confirm_close),
                            contentDescription = null
                        )
                    }
                }
            }
        }
        Box {
            NumpadKeyboard(modifier = Modifier.align(Alignment.BottomCenter)) { key ->
                when (key) {
                    is KeyInput.Delete -> {
                        if (otpTextObs.isNotEmpty()) {
                            otpTextObs = otpTextObs.dropLast(1)
                        }
                    }

                    is KeyInput.Number -> {
                        if (otpTextObs.length < 6) {
                            otpTextObs += key.value
                        }
                    }
                    else -> {
                        // no thing
                    }
                }
            }
        }
    }
}

@Preview
@Composable
fun testIBankModalOTP() {
    IBankModalOTP(
        modifier = Modifier,
        title = "Title",
        config = IBankModalOTPConfig(
            supportingText = "Quý khách vui lòng nhập mã OTP đã được gửi về số điện thoại 097*****75 để xác thực",
            timeoutResendOTP = "5",
            timeoutEffectOTP = "10",
            onRetrySendOTP = {},
            onVerifyOTP = {}
        )
    )
}

@Composable
fun CountdownProgressCircle(
    modifier: Modifier,
    totalTime: Int,
    currentTime: Int,
) {
    val animatedProgress by animateFloatAsState(
        targetValue = currentTime / totalTime.toFloat(),
        animationSpec = tween(durationMillis = 1000, easing = LinearEasing), label = "",
    )

    Canvas(modifier = modifier) {
        val radius = size.minDimension / 2
        val strokeWidth = 2.dp.toPx()

        drawCircle(
            color = Color(0xFFF3F6F6),
            radius = radius - strokeWidth / 2,
            style = Stroke(width = strokeWidth)
        )

        drawArc(
            color = Color(0xFF006B68),
            startAngle = -90f,
            sweepAngle = -360 * animatedProgress,
            useCenter = false,
            style = Stroke(width = strokeWidth),
            topLeft = Offset(strokeWidth / 2, strokeWidth / 2),
            size = Size(
                width = size.width - strokeWidth,
                height = size.height - strokeWidth
            )
        )
    }
}





