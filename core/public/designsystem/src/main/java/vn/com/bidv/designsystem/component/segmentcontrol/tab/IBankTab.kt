package vn.com.bidv.designsystem.component.segmentcontrol.tab

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Tab
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.component.datadisplay.badge.IBankBadgeLabel
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelColor
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelSize
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelType
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

/**
 * A composable function that represents an individual tab within a tab row.
 * This tab can display a title and an optional badge, and can be customized based on its
 * selected state and type.
 *
 * @param modifier Modifier applied to the `IBankTab` for custom styling.
 * @param title The title displayed on the tab.
 * @param badgeText Optional text displayed on a badge within the tab, if present.
 * @param isCurrent Determines whether the tab is currently selected.
 * @param tabType Specifies the style of the tab (e.g., `underline` or `button`).
 * @param isTabAlignLeft Tab aligns left when there is only one tab.
 * @param onClick Callback function invoked when the tab is clicked.
 */
@Composable
internal fun IBankTab(
    modifier: Modifier = Modifier,
    tabItem: TabItem,
    isCurrent: Boolean,
    tabType: TabType,
    tabWidth: MutableState<Dp>,
    isTabAlignLeft: Boolean,
    onClick: () -> Unit,
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current
    val density = LocalDensity.current
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()

    var titleColor by remember { mutableStateOf(colorScheme.contentMainSecondary) }
    var bgColor by remember { mutableStateOf(Color.Transparent) }

    when (tabType) {
        TabType.UNDERLINE -> {
            if (isCurrent) {
                bgColor = if (isPressed) {
                    colorScheme.bgBrand_01Tertiary_press
                } else {
                    Color.Transparent
                }
                titleColor = colorScheme.contentBrand_01Primary
            } else {
                if (isPressed) {
                    bgColor = colorScheme.bgMainPrimary_press
                    titleColor = colorScheme.contentMainSecondary
                } else {
                    bgColor = Color.Transparent
                    titleColor = colorScheme.contentMainTertiary
                }
            }
        }

        TabType.BUTTON -> {
            if (isCurrent) {
                bgColor = if (isPressed) {
                    colorScheme.bgMainTertiary_press
                } else {
                    colorScheme.bgMainTertiary
                }
                titleColor = colorScheme.contentMainPrimary
            } else {
                bgColor = if (isPressed) {
                    colorScheme.bgMainPrimary_press
                } else {
                    colorScheme.bgMainPrimary
                }
                titleColor = colorScheme.contentMainSecondary
            }
        }
    }
    val showBorder = tabType == TabType.BUTTON && isCurrent
    Tab(
        selected = isCurrent,
        onClick = onClick,
        modifier = modifier.testTagIBank("IBankTab_${tabItem.title}")
            .wrapContentWidth()
            .fillMaxHeight()
            .clip(
                if (tabType == TabType.UNDERLINE) {
                    RoundedCornerShape(
                        topStart = IBCornerRadius.cornerRadiusM,
                        topEnd = IBCornerRadius.cornerRadiusM
                    )
                } else RoundedCornerShape(IBCornerRadius.cornerRadiusM)
            )
            .border(
                shape = RoundedCornerShape(IBCornerRadius.cornerRadiusM),
                width = if (showBorder) IBBorderDivider.borderDividerS else 0.dp,
                color = if (showBorder) colorScheme.borderMainPrimary else Color.Transparent
            )
            .background(color = bgColor),
        interactionSource = interactionSource
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = if (isTabAlignLeft) Arrangement.spacedBy(
                space = IBSpacing.spacing2xs, Alignment.Start
            ) else Arrangement.spacedBy(
                space = IBSpacing.spacing2xs, Alignment.CenterHorizontally
            ),
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(
                    start = IBSpacing.spacingS,
                    end = IBSpacing.spacingS,
                    bottom = if (tabType == TabType.BUTTON) IBSpacing.spacingXs else IBSpacing.spacingS,
                    top = IBSpacing.spacingXs
                )
        ) {
            Row(
                modifier = Modifier
                    .wrapContentWidth()
                    .onSizeChanged {
                        tabWidth.value = with(density) { it.width.toDp() }
                    }
            ) {
                Text(
                    text = tabItem.title,
                    color = titleColor,
                    style = if (isCurrent) typography.titleTitle_s else typography.bodyBody_m,
                    textAlign = TextAlign.Center,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.wrapContentWidth()
                )
                if (!tabItem.badgeText.isNullOrEmpty()) {
                    if (tabType == TabType.UNDERLINE && isCurrent) {
                        IBankBadgeLabel(
                            modifier = Modifier,
                            badgeColor = LabelColor.BRAND,
                            badgeSize = LabelSize.SM,
                            badgeType = LabelType.ROUNDED,
                            title = tabItem.badgeText
                        )
                    } else {
                        IBankBadgeLabel(
                            modifier = Modifier,
                            badgeColor = LabelColor.LIGHT_GRAY,
                            badgeSize = LabelSize.SM,
                            badgeType = LabelType.ROUNDED,
                            title = tabItem.badgeText
                        )
                    }
                }
                if (tabItem.showBadgeNoti) {
                    Box(
                        modifier = Modifier
                            .size(8.dp)
                            .background(
                                color = LocalColorScheme.current.bgNegativePrimary,
                                shape = RoundedCornerShape(size = 9999.dp)
                            )
                    )
                }
            }
        }
    }
}