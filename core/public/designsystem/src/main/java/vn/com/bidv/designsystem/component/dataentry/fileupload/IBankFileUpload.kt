package vn.com.bidv.designsystem.component.dataentry.fileupload

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.navigation.button.IBankNormalButton
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonSize
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonType
import vn.com.bidv.designsystem.component.util.dashedBorder
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBColorScheme
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.localization.R as RLocal

/**
 * A composable function for rendering a reusable File Upload component.
 *
 * This component displays an icon at the top center and includes two buttons:
 * "Upload" and "Select Template File". It supports enabled and disabled states.
 *
 * @param isEnabled A Boolean indicating whether the component is enabled or disabled.
 * @param onUploadClick A callback invoked when the "Upload" button is clicked.
 * @param onSelectTemplateClick A callback invoked when the "Select Template File" button is clicked.
 */
@Composable
fun IBankFileUpload(
    modifier: Modifier = Modifier,
    isEnabled: Boolean,
    onUploadClick: () -> Unit,
    onSelectTemplateClick: () -> Unit,
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current
    Box(modifier = modifier
        .wrapContentSize()
        .dashedBorder(
            strokeWidth = IBBorderDivider.borderDividerS,
            color = colorScheme.borderMainPrimary,
            cornerRadiusDp = IBCornerRadius.cornerRadiusL
        )
        .let {
            if (isEnabled) it else it
                .alpha(0.5f)
                .clickable(enabled = false) {}
        }) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingS),
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(IBSpacing.spacingM)
        ) {
            Icon(
                painterResource(id = R.drawable.upload_file),
                contentDescription = "ic_arrow",
                tint = colorScheme.contentMainPrimary
            )
            Text(
                text = stringResource(id = RLocal.string.dinh_kem_tep_tin_toi_da_s),
                style = typography.captionCaption_m
            )
            Row(
                horizontalArrangement = Arrangement.Center, modifier = Modifier.fillMaxWidth()
            ) {
                IBankNormalButton(
                    modifier = Modifier.width(120.dp),
                    onClick = onUploadClick,
                    isEnable = isEnabled,
                    size = NormalButtonSize.SM(typography),
                    type = NormalButtonType.SECONDARYGRAY(colorScheme),
                    text = stringResource(id = RLocal.string.tai_mau)
                )
                Spacer(modifier = Modifier.width(IBSpacing.spacingXs))
                IBankNormalButton(
                    modifier = Modifier.width(120.dp),
                    onClick = onSelectTemplateClick,
                    isEnable = isEnabled,
                    size = NormalButtonSize.SM(typography),
                    type = NormalButtonType.PRIMARY(colorScheme),
                    text = stringResource(id = RLocal.string.chon_tep_tin)
                )
            }
        }
    }
}

data class File(val fileName: String, val fileSize: String, val fileType: String)

@Composable
fun getFileIcon(fileType: String): ImageVector {
    return when (fileType.lowercase()) {
        "doc" -> ImageVector.vectorResource(id = R.drawable.icon_file_type_doc)
        "docx" -> ImageVector.vectorResource(id = R.drawable.icon_file_type_docx)
        "xls" -> ImageVector.vectorResource(id = R.drawable.icon_file_type_xls)
        "xlsx" -> ImageVector.vectorResource(id = R.drawable.icon_file_type_xlsx)
        "pdf" -> ImageVector.vectorResource(id = R.drawable.icon_file_type_pdf)
        else -> {
            ImageVector.vectorResource(id = R.drawable.icon_file_type_doc)
        }
    }
}

/**
 * A composable function for displaying an individual file item in a file upload UI.
 *
 * This component shows the file's information along with options to remove or refresh
 * the file, depending on its upload state.
 *
 * @param modifier A [Modifier] to customize the appearance or layout of the file item.
 * @param file The [File] object representing the file to be displayed.
 * @param fileState The current state of the file upload, represented by [FileUploadState].
 * @param onRemoveClick A callback invoked when the "Remove" action is clicked. Defaults to an empty lambda.
 * @param onRefreshClick A callback invoked when the "Refresh" action is clicked. Defaults to an empty lambda.
 */
@Composable
fun FileItem(
    modifier: Modifier = Modifier,
    file: File,
    fileState: FileUploadState,
    onRemoveClick: () -> Unit = {},
    onRefreshClick: () -> Unit = {}
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .fillMaxWidth()
            .background(
                colorScheme.bgMainTertiary, RoundedCornerShape(IBCornerRadius.cornerRadiusL)
            )
            .border(
                width = fileState.borderStrokeWidth,
                color = fileState.borderColor,
                shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL)
            )
            .padding(IBSpacing.spacingL)
    ) {
        Image(
            imageVector = getFileIcon(file.fileType),
            contentDescription = "File Icon",
            modifier = Modifier
                .align(Alignment.CenterVertically)
                .size(24.dp)
        )

        Spacer(modifier = Modifier.width(IBSpacing.spacingXs))

        Column(
            modifier = Modifier
                .weight(1f)
                .align(Alignment.CenterVertically)
        ) {
            Text(
                text = file.fileName,
                style = typography.titleTitle_s,
                color = colorScheme.contentMainPrimary
            )
            Row(modifier = Modifier) {
                Text(
                    text = if (fileState is FileUploadState.Error) "" else file.fileSize + " - ",
                    style = typography.captionCaption_m,
                    color = colorScheme.contentMainTertiary
                )
                Text(
                    text = stringResource(id = fileState.textId),
                    style = typography.captionCaption_m,
                    color = colorScheme.contentMainTertiary
                )
            }
        }
        Row(modifier = Modifier) {
            if (fileState.stateSubIconId != null) {
                IBankNormalButton(
                    text = "",
                    modifier = Modifier,
                    size = NormalButtonSize.SM(typography),
                    type = NormalButtonType.TERTIARY(colorScheme),
                    leadingIcon = ImageVector.vectorResource(id = fileState.stateSubIconId),
                    onClick = onRefreshClick
                )
            }
            IBankNormalButton(
                text = "",
                modifier = Modifier,
                size = NormalButtonSize.SM(typography),
                type = if (fileState is FileUploadState.Error) NormalButtonType.DESTERTIARY(
                    colorScheme
                ) else NormalButtonType.TERTIARY(colorScheme),
                leadingIcon = ImageVector.vectorResource(id = fileState.stateIconId),
                onClick = onRemoveClick
            )
        }
    }
}

sealed class FileUploadState(
    val textId: Int,
    val borderStrokeWidth: Dp = IBBorderDivider.borderDividerS,
    val borderColor: Color,
    val stateIconId: Int = R.drawable.trash_outline,
    val stateSubIconId: Int? = null,
) {
    class InProgress(colorScheme: IBColorScheme) : FileUploadState(
        textId = RLocal.string.dang_tai_s,
        borderColor = colorScheme.borderMainPrimary,
        stateIconId = R.drawable.loading,
    )

    class Complete(colorScheme: IBColorScheme) : FileUploadState(
        textId = RLocal.string.hoan_tat,
        borderColor = colorScheme.borderMainPrimary,
    )

    class Error(colorScheme: IBColorScheme) : FileUploadState(
        textId = RLocal.string.co_loi_xay_ra_vui_long_thu_lai,
        borderStrokeWidth = IBBorderDivider.borderDividerM,
        borderColor = colorScheme.borderNegativePrimary,
        stateSubIconId = R.drawable.refresh_outline
    )
}

@Composable
@Preview(showBackground = true)
fun UploadPreview() {
    val colorScheme = LocalColorScheme.current
    val fileList = listOf(
        File("Bảng lương tháng 10.pdf", "1.2 MB", "pdf"),
        File("Bảng lương tháng 11.docx", "500 KB", "docx"),
        File("Bảng lương tháng 12.xls", "500 KB", "xls"),
        File("Bảng lương tháng 1.doc", "1.2 MB", "doc"),
        File("Bảng lương tháng 2.xlsx", "500 KB", "xlsx")
    )
    Column(
        modifier = Modifier
            .fillMaxHeight()
            .padding(10.dp)
    ) {
        IBankFileUpload(
            isEnabled = true,
            onUploadClick = {},
            onSelectTemplateClick = {}
        )
        Spacer(modifier = Modifier.height(IBSpacing.spacingS))
        LazyColumn {
            items(fileList.size) { index ->
                val file = fileList[index]
                val fileState: FileUploadState = if (file.fileType.contains("doc")) {
                    FileUploadState.Complete(colorScheme)
                } else if (file.fileType.contains("xls")) {
                    FileUploadState.Error(colorScheme)
                } else {
                    FileUploadState.InProgress(colorScheme)
                }
                FileItem(
                    modifier = Modifier.padding(bottom = IBSpacing.spacingXs),
                    file = file,
                    fileState = fileState,
                )
            }
        }
    }
}