package vn.com.bidv.designsystem.component.datadisplay.sectionheader

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.material3.Button
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.datadisplay.badge.IBankBadgeLabel
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelColor
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelSize
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelType
import vn.com.bidv.designsystem.component.dataentry.IBankSwitchWithText
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

sealed class LeadingType(val modifierLeadingType: Modifier) {
    data class Dash(
        val modifier: Modifier = Modifier
            .width(IBSpacing.spacingXs)
            .height(IBSpacing.spacing2xl)
    ) : LeadingType(modifier)

    data class Icon(
        val modifier: Modifier = Modifier
            .width(IBSpacing.spacing2xl)
            .height(IBSpacing.spacing2xl)
    ) : LeadingType(modifier)
}

@Composable
fun IBankSectionHeader(
    modifier: Modifier = Modifier,
    shEnableLeading: Boolean = true,
    shLeadingType: LeadingType,
    shLeadingIconId: Int? = null,
    shSectionTitle: String,
    thumbContent: (@Composable () -> Unit)? = null
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current

    val iconId = when {
        shEnableLeading && shLeadingType is LeadingType.Dash -> R.drawable.icon_dash_leading
        shEnableLeading -> shLeadingIconId
        else -> null
    }

    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(
                start = IBSpacing.spacingM,
                end = IBSpacing.spacingM,
                top = IBSpacing.spacingS,
                bottom = IBSpacing.spacingS
            ),
        horizontalArrangement = Arrangement.spacedBy(
            space = IBSpacing.spacingXs, Alignment.CenterHorizontally
        ),
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (shEnableLeading && iconId != null) {
            Image(
                painter = painterResource(id = iconId),
                contentDescription = "Leading Icon",
                modifier = shLeadingType.modifierLeadingType
            )
        }

        Text(
            color = colorScheme.contentMainPrimary,
            style = typography.titleTitle_m,
            text = shSectionTitle,
            modifier = Modifier.weight(1f)
        )

        thumbContent?.invoke()
    }
}

@Composable
@Preview(showBackground = true)
fun HeaderPreview() {
    Column {
        IBankSectionHeader(
            shEnableLeading = true,
            shLeadingType = LeadingType.Dash(),
            shLeadingIconId = R.drawable.icon_dash_leading,
            shSectionTitle = "Section Title",
            thumbContent = {
                Text(
                    text = "Optional Content",
                    style = LocalTypography.current.titleTitle_s,
                    color = LocalColorScheme.current.contentMainSecondary
                )
            }
        )
        IBankSectionHeader(
            shLeadingType = LeadingType.Dash(),
            shSectionTitle = "Section Title",
        )
        IBankSectionHeader(
            shEnableLeading = true,
            shLeadingType = LeadingType.Icon(),
            shLeadingIconId = R.drawable.icon_dash_leading,
            shSectionTitle = "Section Title",
            thumbContent = {
                IBankSwitchWithText(
                    title = "Switch Title",
                    checked = true,
                    onCheckedChange = {}
                )
            }
        )
        IBankSectionHeader(
            shEnableLeading = true,
            shLeadingType = LeadingType.Icon(),
            shLeadingIconId = R.drawable.icon_dash_leading,
            shSectionTitle = "Section Title Section Title Section Title",
            thumbContent = {
                Button(modifier = Modifier.wrapContentWidth(), onClick = {}) {
                    Text(text = "Button")
                }
            }
        )
        IBankSectionHeader(
            shEnableLeading = true,
            shLeadingType = LeadingType.Icon(),
            shLeadingIconId = R.drawable.icon_dash_leading,
            shSectionTitle = "Section Title",
            thumbContent = {
                IBankBadgeLabel(
                    modifier = Modifier,
                    title = "Badge",
                    badgeSize = LabelSize.SM,
                    badgeColor = LabelColor.BRAND,
                    badgeType = LabelType.ROUNDED
                )
            }
        )

        IBankSectionHeader(
            shEnableLeading = true,
            shLeadingType = LeadingType.Icon(),
            shLeadingIconId = R.drawable.icon_popup_info_64,
            shSectionTitle = "Section Title",
            thumbContent = {
                IconButton(
                    onClick = {},
                    modifier = Modifier
                        .width(IBSpacing.spacing3xl)
                        .height(IBSpacing.spacing3xl)
                        .padding(IBSpacing.spacingXs)
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.arrow_bottom),
                        contentDescription = "Collapse",
                        tint = LocalColorScheme.current.contentMainPrimary
                    )
                }
            }
        )
    }
}

