package vn.com.bidv.designsystem.component.feedback.bottomsheet

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.EmptyStateType
import vn.com.bidv.designsystem.component.IBankEmptyState
import vn.com.bidv.designsystem.component.IBankLoaderIndicators
import vn.com.bidv.designsystem.component.dataentry.AllowSpecificCharactersFilter
import vn.com.bidv.designsystem.component.dataentry.IBankFilterTextField
import vn.com.bidv.designsystem.component.dataentry.IBankInputSearchBase
import vn.com.bidv.designsystem.component.dataentry.RemoveVietnameseAccentFilter
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.localization.R as RLocalized

enum class MultiSearchDialogState {
    LOADING, CONTENT, ERROR
}

data class MultiSearchItem<T>(
    val data: T, var isSelected: Boolean = false
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun <T> IBankMultiChoiceSearchDialog(
    title: String,
    state: MultiSearchDialogState = MultiSearchDialogState.CONTENT,
    listData: List<T>? = null,
    itemSelected: List<T>? = null,
    searchFilter: (T, String) -> Boolean,
    showSearchBox: Boolean = false,
    listSearchFilterText: List<IBankFilterTextField> = listOf(
        RemoveVietnameseAccentFilter(), AllowSpecificCharactersFilter("., ")
    ),
    searchMaxLength: Int = 70,
    searchPlaceholder: String = stringResource(id = RLocalized.string.tim_kiem),
    onApply: (selected: List<T>?) -> Unit,
    applyView: @Composable (onClick: () -> Unit) -> Unit,
    isSelectedAllSupport: Boolean = false,
    selectedAllView: @Composable ((Boolean, () -> Unit) -> Unit)? = null,
    emptyView: @Composable () -> Unit = {
        IBankEmptyState(
            modifier = Modifier.fillMaxSize(),
            supportingText = stringResource(RLocalized.string.khong_co_du_lieu)
        )
    },
    searchEmptyView: @Composable () -> Unit = {
        IBankEmptyState(
            emptyStateType = EmptyStateType.SearchNoResult,
            modifier = Modifier.fillMaxSize(),
            supportingText = stringResource(RLocalized.string.khong_tim_thay_ket_qua)
        )
    },
    errorView: @Composable () -> Unit = {
        IBankEmptyState(
            modifier = Modifier.fillMaxSize(),
            textButton = stringResource(RLocalized.string.co_loi_xay_ra_vui_long_thu_lai)
        )
    },
    itemContent: @Composable (index: Int, item: MultiSearchItem<T>, onClick: () -> Unit) -> Unit
) {
    val originData = listData?.map {
        MultiSearchItem(it, itemSelected?.contains(it) == true)
    } ?: emptyList()
    val lazyListState = rememberLazyListState()
    IBankBottomSheet(title = title, onDismiss = { onApply.invoke(null) }) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(min = LocalConfiguration.current.screenHeightDp.dp / 3)
        ) {
            if (state == MultiSearchDialogState.LOADING) {
                LoadingView()
            } else if (state == MultiSearchDialogState.ERROR) {
                errorView()
            } else if (state == MultiSearchDialogState.CONTENT) {
                if (originData.isEmpty()) {
                    emptyView()
                } else {
                    ContentView(
                        lazyListState = lazyListState,
                        listData = originData,
                        searchPlaceholder = searchPlaceholder,
                        showSearchBox = showSearchBox,
                        searchMaxLength = searchMaxLength,
                        searchFilter = searchFilter,
                        listSearchFilterText = listSearchFilterText,
                        searchEmptyView = searchEmptyView,
                        itemContent = itemContent,
                        applyView = applyView,
                        isSelectedAllSupport = isSelectedAllSupport,
                        selectedAllView = selectedAllView,
                        onApply = onApply,
                    )
                }
            }
        }
    }
}

@Composable
private fun <T> ContentView(
    lazyListState: LazyListState,
    listData: List<MultiSearchItem<T>>,
    isSelectedAllSupport: Boolean = false,
    selectedAllView: @Composable ((Boolean, () -> Unit) -> Unit)? = null,
    searchPlaceholder: String,
    showSearchBox: Boolean,
    listSearchFilterText: List<IBankFilterTextField>,
    searchMaxLength: Int,
    searchFilter: (T, String) -> Boolean,
    onApply: (selected: List<T>?) -> Unit,
    searchEmptyView: @Composable () -> Unit,
    itemContent: @Composable (index: Int, item: MultiSearchItem<T>, onClick: () -> Unit) -> Unit,
    applyView: @Composable (onClick: () -> Unit) -> Unit,
) {
    var searchQuery by remember { mutableStateOf("") }
    var isSelectedAll by remember { mutableStateOf(false) }
    var originData by remember { mutableStateOf(listData) }
    var mDataSet by remember { mutableStateOf(originData) }

    fun updateData(newData: List<MultiSearchItem<T>>) {
        newData.forEach { item ->
            originData = originData.map { if (it.data == item.data) item else it }
        }
    }

    fun applyData() {
        val data = originData
            .filter { it.isSelected }
            .map { it.data }
        onApply.invoke(data)
    }

    LaunchedEffect(originData, searchQuery) {
        mDataSet = originData.filter { item -> searchFilter(item.data, searchQuery) }
        val listItemUncheck = mDataSet.filter { !it.isSelected }
        isSelectedAll = mDataSet.isNotEmpty() == true && listItemUncheck.isEmpty()
    }

    Column {
        if (showSearchBox) {
            IBankInputSearchBase(maxLength = searchMaxLength,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        horizontal = IBSpacing.spacingM, vertical = IBSpacing.spacingXs
                    ),
                filters = listSearchFilterText,
                placeHolderText = searchPlaceholder,
                textValue = searchQuery,
                onTextChange = { searchQuery = it },
                onRequestChange = { txtSearch ->
                    mDataSet = if (txtSearch.isBlank()) {
                        originData
                    } else {
                        originData.filter { item ->
                            searchFilter(item.data, txtSearch)
                        }
                    }
                },
                onClickClear = { searchQuery = "" })
        }
        if (isSelectedAllSupport) {
            Box(
                modifier = Modifier
                    .padding(horizontal = IBSpacing.spacingM)
            ) {
                selectedAllView?.invoke(isSelectedAll) {
                    isSelectedAll = !isSelectedAll
                    updateData(mDataSet.map { it.copy(isSelected = isSelectedAll) })
                }
            }

            Spacer(modifier = Modifier.height(IBSpacing.spacing2xs))
        }
        if (mDataSet.isEmpty()) {
            searchEmptyView()
        } else {
            LazyColumn(
                state = lazyListState,
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .weight(1f, fill = false)
                    .padding(horizontal = IBSpacing.spacingM),
                verticalArrangement = Arrangement.spacedBy(IBSpacing.spacing2xs),
            ) {
                items(mDataSet.size) { index ->
                    val searchItem = mDataSet[index]
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .wrapContentHeight()
                    ) {
                        itemContent(index, searchItem) {
                            updateData(listOf(searchItem.copy(isSelected = !searchItem.isSelected)))
                        }
                    }
                }

            }
        }
        Spacer(modifier = Modifier.height(IBSpacing.spacingXs))
        Box(
            modifier = Modifier
                .padding(horizontal = IBSpacing.spacingM)
        ) {
            applyView.invoke {
                applyData()
            }
        }
    }
}

@Composable
private fun LoadingView() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .heightIn(LocalConfiguration.current.screenHeightDp.dp / 3),
        contentAlignment = Alignment.Center
    ) {
        IBankLoaderIndicators(
            backgroundColor = Color.Transparent, isParentFullSize = false
        )
    }
}

@Composable
fun ItemMultiSelect(isSelected: Boolean, content: String, indeterminate: Boolean = false, onClick: () -> Unit) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick.invoke() }
            .background(
                if (isSelected) colorScheme.bgBrand_01Tertiary else colorScheme.contentOn_specialPrimary,
                shape = RoundedCornerShape(IBCornerRadius.cornerRadiusM)
            )
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically, modifier = Modifier
                .padding(
                    horizontal = IBSpacing.spacingXs,
                    vertical = IBSpacing.spacingS
                )
        ) {
            CheckBox(checked = isSelected, indeterminate = indeterminate)
            Spacer(modifier = Modifier.width(IBSpacing.spacingXs))
            Text(
                text = content,
                style = typography.bodyBody_l,
                color = colorScheme.contentMainPrimary,
                textAlign = TextAlign.Start,
                modifier = Modifier.weight(1f)
            )
        }
    }
}

@Composable
private fun CheckBox(
    modifier: Modifier = Modifier,
    enable: Boolean = true,
    indeterminate: Boolean = false,
    checked: Boolean = false,
) {
    val colorScheme = LocalColorScheme.current
    var colorBackgroundValue: Color
    var colorBorderValue: Color

    if (enable) {
        colorBorderValue = colorScheme.borderMainPrimary
        colorBackgroundValue = Color.Transparent
        if (checked) {
            colorBackgroundValue = colorScheme.bgBrand_01Primary
            colorBorderValue = colorScheme.bgBrand_01Primary
        }
    } else {
        colorBorderValue = colorScheme.borderDisablePrimary
        colorBackgroundValue = colorScheme.bgDisablePrimary
    }

    Box(
        modifier = modifier
            .clip(RoundedCornerShape(IBCornerRadius.cornerRadiusM))
            .background(colorBackgroundValue)
            .border(
                width = IBBorderDivider.borderDividerS,
                color = colorBorderValue,
                shape = RoundedCornerShape(IBCornerRadius.cornerRadiusM)
            )
            .width(IBSpacing.spacingM)
            .height(IBSpacing.spacingM)
    ) {
        if (checked) {
            Image(
                painter = if (indeterminate) painterResource(id = R.drawable.minus) else painterResource(
                    id = R.drawable.check
                ),
                colorFilter = ColorFilter.tint(colorScheme.borderMainPrimary),
                contentDescription = "",
                modifier = Modifier
                    .align(Alignment.Center)
                    .size(IBSpacing.spacingS)
            )
        }
    }
}

