package vn.com.bidv.designsystem.component.segmentcontrol.tab

import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.ScrollableTabRow
import androidx.compose.material3.TabPosition
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.debugInspectorInfo
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.times
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.localization.R
import vn.com.bidv.log.BLogUtil

enum class TabType { UNDERLINE, BUTTON }

data class TabItem(
    val title: String,
    val badgeText: String? = null,
    val showBadgeNoti: Boolean = false
)



/**
 * A composable function that displays a horizontal row of tabs with customizable appearance
 * and scrolling support. Users can select a tab from the `listTab`, with each tab's style determined by `tabType`.
 *
 * @param modifier Modifier applied to the `IBankTabRow` for custom styling.
 * @param listTab List of tabs displayed in the row, each represented by a `TabItem`. When there is only one tab, tab aligns left instead of center.
 * @param tabType Style of the tabs (`underline` or `button`), defining the visual appearance of the tabs.
 * @param fullWidth If `true`, the tab row will span the full width of the screen, enabling horizontal scrolling.
 * @param indent If `true`, adds spacing between tabs, starting from the second tab.
 * @param showBackground If `true`, displays a background behind the row of tabs.
 * @param onTabSelected Callback invoked when a tab is selected, providing the selected `TabItem`.
 * @param selectedTabIndex set default selected tab in the row
 */
@Composable
fun IBankTabRow(
    modifier: Modifier = Modifier,
    listTab: List<TabItem>,
    tabType: TabType = TabType.UNDERLINE,
    fullWidth: Boolean = false,
    indent: Boolean = false,
    showBackground: Boolean = false,
    selectedTabIndex: Int = 0,
    onTabSelected: (TabItem) -> Unit
) {
    val colorScheme = LocalColorScheme.current
    val (selectedTab, setSelectedTab) = remember(selectedTabIndex) { mutableIntStateOf(selectedTabIndex) }
    val isUnderline = tabType == TabType.UNDERLINE

    val density = LocalDensity.current
    val tabWidths = remember {
        val tabWidthStateList = mutableStateListOf<Dp>()
        repeat(listTab.size) {
            tabWidthStateList.add(0.dp)
        }
        tabWidthStateList
    }
    val dividerContent: @Composable () -> Unit = {
        if (isUnderline) {
            HorizontalDivider(
                color = colorScheme.borderSolidPrimary.copy(alpha = 0.2f)
            )
        }
    }

    val indicatorContent: @Composable (List<TabPosition>) -> Unit = { tabPositions ->
        BLogUtil.d("TabWidth ${tabWidths[selectedTab]}")
        if (isUnderline) {
            TabRowDefaults.Indicator(
                modifier = modifier
                    .customTabIndicatorOffset(
                        currentTabPosition = tabPositions[selectedTab],
                        tabWidth = tabWidths[selectedTab],
                        isTabAlignLeft = listTab.size == 1
                    )
                    .height(IBBorderDivider.borderDividerM),
                color = colorScheme.borderBrandPrimary,
            )
        }
    }
    val containerPadding = if (tabType == TabType.BUTTON && showBackground) {
        IBSpacing.spacingXs
    } else {
        0.dp
    }

    val containerBg = if (tabType == TabType.UNDERLINE && showBackground) {
        Color.Transparent
    } else {
        colorScheme.bgNon_opaqueDefault
    }

    if (fullWidth) {
        TabRow(divider = dividerContent,
            containerColor = containerBg,
            selectedTabIndex = selectedTab,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = containerPadding)
                .wrapContentHeight(),
            indicator = { tabPositions -> indicatorContent(tabPositions) }) {
            ListTab(
                modifier = Modifier.padding(vertical = containerPadding),
                listTab = listTab,
                tabWidths = tabWidths,
                selectedTab = selectedTab,
                tabType = tabType,
                onTabSelected = onTabSelected,
                setSelectedTab = setSelectedTab,
                density = density
            )
        }
    } else {
        ScrollableTabRow(divider = dividerContent,
            containerColor = containerBg,
            selectedTabIndex = selectedTab,
            edgePadding = if (indent) IBSpacing.spacingM else 0.dp,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = containerPadding)
                .wrapContentHeight(),
            indicator = { tabPositions -> indicatorContent(tabPositions) }) {
            ListTab(
                modifier = Modifier.padding(vertical = containerPadding),
                listTab = listTab,
                tabWidths = tabWidths,
                selectedTab = selectedTab,
                tabType = tabType,
                onTabSelected = onTabSelected,
                setSelectedTab = setSelectedTab,
                density = density
            )
        }
    }
}

@Composable
private fun ListTab(
    modifier: Modifier,
    listTab: List<TabItem>,
    tabWidths: MutableList<Dp>,
    selectedTab: Int,
    tabType: TabType,
    onTabSelected: (TabItem) -> Unit,
    setSelectedTab: (Int) -> Unit,
    density: Density
) {
    listTab.forEachIndexed { index, tabItem ->
        val tabWidth = remember { mutableStateOf(0.dp) }
        LaunchedEffect(tabWidth.value) {
            tabWidths[index] = tabWidth.value
        }
        Row(
            modifier = modifier
                .wrapContentWidth()
                .fillMaxHeight()
            , horizontalArrangement = Arrangement.spacedBy(IBSpacing.spacingS)
        ) {
            IBankTab(
                modifier = Modifier.semantics {
                    contentDescription = "tab_$index"
                },
                tabItem = tabItem,
                isCurrent = selectedTab == index,
                tabType = tabType,
                tabWidth = tabWidth,
                isTabAlignLeft = listTab.size == 1,
                onClick = {
                    onTabSelected(tabItem)
                    setSelectedTab(index)
                })
        }
    }
}

private fun Modifier.customTabIndicatorOffset(
    currentTabPosition: TabPosition,
    tabWidth: Dp,
    isTabAlignLeft: Boolean
): Modifier = composed(inspectorInfo = debugInspectorInfo {
    name = "customTabIndicatorOffset"
    value = currentTabPosition
}) {
    val padding = IBSpacing.spacing2xs
    val offsetValue =
        if (isTabAlignLeft) currentTabPosition.left + 4 * padding else (currentTabPosition.left + currentTabPosition.right - (tabWidth - padding)) / 2
    val currentTabWidth by animateDpAsState(
        targetValue = tabWidth - padding,
        animationSpec = tween(durationMillis = 250, easing = FastOutSlowInEasing),
        label = ""
    )
    val indicatorOffset by animateDpAsState(
        targetValue = offsetValue,
        animationSpec = tween(durationMillis = 250, easing = FastOutSlowInEasing),
        label = ""
    )
    fillMaxWidth()
        .wrapContentSize(Alignment.BottomStart)
        .width(currentTabWidth)
        .offset(x = indicatorOffset)
}

@Composable
@Preview
fun TabExample() {
    val tabs = listOf(
        TabItem(title = "Tất cả"),
        TabItem(title = "Tab 2", badgeText = "2"),
        TabItem(title = "Tab 3", showBadgeNoti = true),
        TabItem(title = "Tab 4"),
        TabItem(title = "Tab 5")
    )
    val selectedTab by remember { mutableStateOf(0) }
    Column(
        modifier = Modifier.wrapContentWidth()
    ) {
        val listTab = listOf(
            TabItem(title = stringResource(id = R.string.tat_ca)),
            TabItem(title = stringResource(id = R.string.tien_vao)),
            TabItem(title = stringResource(id = R.string.tien_ra)),
            TabItem(title = stringResource(id = R.string.tien_ra)),
            TabItem(title = stringResource(id = R.string.tien_ra)),
        )
        IBankTabRow(
            selectedTabIndex = selectedTab,
            listTab = listTab,
            tabType = TabType.UNDERLINE,
            showBackground = true
        ) {
        }
        IBankTabRow(
            listTab = tabs,
            tabType = TabType.UNDERLINE,
            fullWidth = true,
            indent = true,
            showBackground = true,
            selectedTabIndex = selectedTab
        ) { index ->
            println("Selected UNDERLINE Index: $index")
        }
        Spacer(modifier = Modifier.height(IBSpacing.spacingS))
        IBankTabRow(
            selectedTabIndex = selectedTab,
            listTab = tabs,
            tabType = TabType.UNDERLINE,
            showBackground = false
        ) { item ->
            println("Selected Tab: $item")
        }
        Spacer(modifier = Modifier.height(IBSpacing.spacingS))
        IBankTabRow(
            selectedTabIndex = selectedTab,
            listTab = tabs,
            tabType = TabType.BUTTON,
            showBackground = false
        ) { item ->
            println("Selected Tab: $item")
        }
        Spacer(modifier = Modifier.height(IBSpacing.spacingS))
        IBankTabRow(
            selectedTabIndex = selectedTab,
            listTab = tabs,
            tabType = TabType.BUTTON,
            showBackground = false
        ) { item ->
            println("Selected Tab: $item")
        }
        Spacer(modifier = Modifier.height(IBSpacing.spacingS))
        IBankTabRow(
            selectedTabIndex = selectedTab,
            listTab = listOf(
                TabItem("Giao dich cho xu ly", badgeText = "20"),
            ),
            tabType = TabType.UNDERLINE,
            fullWidth = true,
            indent = true,
            showBackground = true,
        ) { item ->
            println("Selected Tab: $item")
        }
    }
}

