package vn.com.bidv.designsystem.icon.ibankicons

import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Brush.Companion.linearGradient
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.icon.IBankIcons

public val IBankIcons.ImgNoData: ImageVector
    get() {
        if (_imgNoData != null) {
            return _imgNoData!!
        }
        _imgNoData = Builder(name = "ImgNoData", defaultWidth = 207.0.dp, defaultHeight = 207.0.dp,
                viewportWidth = 207.0f, viewportHeight = 207.0f).apply {
            group {
                path(fill = SolidColor(Color(0xFFE8E5E3)), stroke = null, fillAlpha = 0.84f,
                        strokeAlpha = 0.84f, strokeLineWidth = 0.0f, strokeLineCap = Butt,
                        strokeLineJoin = Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(60.701f, 166.374f)
                    curveTo(51.196f, 167.457f, 41.709f, 162.507f, 35.456f, 155.283f)
                    curveTo(29.202f, 148.06f, 25.758f, 138.825f, 23.465f, 129.558f)
                    curveTo(21.708f, 122.456f, 20.552f, 115.013f, 22.042f, 107.85f)
                    curveTo(23.531f, 100.688f, 28.093f, 93.82f, 34.948f, 91.223f)
                    curveTo(38.256f, 89.972f, 41.94f, 89.738f, 45.05f, 88.059f)
                    curveTo(53.7f, 83.391f, 53.352f, 71.273f, 55.457f, 61.687f)
                    curveTo(57.125f, 54.089f, 61.081f, 47.006f, 66.687f, 41.601f)
                    curveTo(72.287f, 36.196f, 80.679f, 32.414f, 87.858f, 35.437f)
                    curveTo(93.364f, 37.753f, 96.658f, 43.406f, 98.819f, 48.961f)
                    curveTo(108.902f, 74.85f, 102.447f, 104.02f, 94.041f, 130.505f)
                    curveTo(90.508f, 141.647f, 75.491f, 164.368f, 63.858f, 165.751f)
                }
                path(fill = SolidColor(Color(0xFFE8E5E3)), stroke = null, fillAlpha = 0.84f,
                        strokeAlpha = 0.84f, strokeLineWidth = 0.0f, strokeLineCap = Butt,
                        strokeLineJoin = Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(35.521f, 77.03f)
                    curveTo(35.756f, 74.372f, 38.11f, 72.412f, 40.77f, 72.647f)
                    curveTo(43.434f, 72.881f, 45.398f, 75.229f, 45.163f, 77.883f)
                    curveTo(44.928f, 80.54f, 42.574f, 82.5f, 39.915f, 82.266f)
                    curveTo(37.255f, 82.031f, 35.286f, 79.683f, 35.521f, 77.03f)
                    close()
                }
                path(fill = SolidColor(Color(0xFFE8E5E3)), stroke = null, fillAlpha = 0.84f,
                        strokeAlpha = 0.84f, strokeLineWidth = 0.0f, strokeLineCap = Butt,
                        strokeLineJoin = Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(88.281f, 20.226f)
                    curveTo(88.384f, 19.087f, 89.389f, 18.248f, 90.531f, 18.347f)
                    curveTo(91.673f, 18.45f, 92.514f, 19.453f, 92.415f, 20.592f)
                    curveTo(92.312f, 21.731f, 91.306f, 22.57f, 90.165f, 22.472f)
                    curveTo(89.018f, 22.373f, 88.177f, 21.365f, 88.281f, 20.226f)
                    close()
                }
                path(fill = SolidColor(Color(0xFFE8E5E3)), stroke = null, fillAlpha = 0.84f,
                        strokeAlpha = 0.84f, strokeLineWidth = 0.0f, strokeLineCap = Butt,
                        strokeLineJoin = Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(26.528f, 155.86f)
                    curveTo(26.435f, 156.938f, 27.229f, 157.889f, 28.309f, 157.983f)
                    curveTo(29.39f, 158.077f, 30.344f, 157.285f, 30.438f, 156.207f)
                    curveTo(30.532f, 155.129f, 29.738f, 154.177f, 28.657f, 154.083f)
                    curveTo(27.576f, 153.989f, 26.622f, 154.782f, 26.528f, 155.86f)
                    close()
                }
                path(fill = SolidColor(Color(0xFFE8E5E3)), stroke = null, fillAlpha = 0.84f,
                        strokeAlpha = 0.84f, strokeLineWidth = 0.0f, strokeLineCap = Butt,
                        strokeLineJoin = Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(38.717f, 169.241f)
                    curveTo(40.877f, 167.701f, 41.375f, 164.705f, 39.831f, 162.551f)
                    curveTo(38.286f, 160.397f, 35.284f, 159.899f, 33.125f, 161.44f)
                    curveTo(30.965f, 162.981f, 30.467f, 165.977f, 32.011f, 168.131f)
                    curveTo(33.556f, 170.285f, 36.558f, 170.782f, 38.717f, 169.241f)
                    close()
                }
                path(fill = SolidColor(Color(0xFFE8E5E3)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(92.025f, 196.126f)
                    curveTo(92.025f, 194.935f, 75.233f, 193.974f, 54.513f, 193.974f)
                    curveTo(33.797f, 193.974f, 17.0f, 194.94f, 17.0f, 196.126f)
                    curveTo(17.0f, 197.317f, 33.792f, 198.278f, 54.513f, 198.278f)
                    curveTo(75.233f, 198.278f, 92.025f, 197.317f, 92.025f, 196.126f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF1A2E35)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(71.681f, 19.518f)
                    curveTo(73.936f, 22.247f, 75.186f, 25.781f, 75.144f, 29.315f)
                    curveTo(75.12f, 31.336f, 74.749f, 33.562f, 75.928f, 35.208f)
                    curveTo(76.821f, 36.454f, 78.451f, 37.078f, 79.18f, 38.428f)
                    curveTo(80.054f, 40.054f, 79.307f, 42.107f, 79.847f, 43.87f)
                    curveTo(80.608f, 46.373f, 83.545f, 47.418f, 85.288f, 49.368f)
                    curveTo(86.796f, 51.061f, 87.355f, 53.54f, 86.749f, 55.725f)
                    curveTo(86.143f, 57.909f, 84.395f, 59.737f, 82.248f, 60.478f)
                    curveTo(81.097f, 60.876f, 79.776f, 60.961f, 78.686f, 60.417f)
                    curveTo(77.507f, 59.831f, 76.784f, 58.631f, 76.191f, 57.459f)
                    curveTo(75.031f, 55.153f, 64.868f, 42.675f, 64.295f, 40.158f)
                }
                path(fill = SolidColor(Color(0xFF1A2E35)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(64.286f, 16.575f)
                    curveTo(60.128f, 15.909f, 56.134f, 18.661f, 54.386f, 22.481f)
                    curveTo(52.633f, 26.301f, 52.586f, 30.642f, 52.46f, 34.842f)
                    curveTo(52.333f, 39.037f, 52.037f, 43.42f, 49.89f, 47.034f)
                    curveTo(48.527f, 49.326f, 46.417f, 51.337f, 46.084f, 53.981f)
                    curveTo(45.788f, 56.348f, 47.014f, 58.734f, 46.61f, 61.087f)
                    curveTo(46.253f, 63.136f, 44.693f, 64.917f, 44.754f, 66.994f)
                    curveTo(44.829f, 69.492f, 47.404f, 71.377f, 49.908f, 71.442f)
                    curveTo(52.413f, 71.508f, 54.752f, 70.2f, 56.716f, 68.648f)
                    curveTo(59.902f, 66.131f, 62.486f, 62.855f, 64.192f, 59.175f)
                }
                path(fill = SolidColor(Color(0xFFFFBF9D)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(57.449f, 26.465f)
                    curveTo(56.899f, 22.092f, 60.071f, 18.098f, 64.52f, 17.671f)
                    curveTo(68.716f, 17.273f, 72.442f, 20.39f, 73.072f, 24.544f)
                    curveTo(73.621f, 28.186f, 74.096f, 31.865f, 74.082f, 33.201f)
                    curveTo(74.03f, 37.228f, 71.667f, 38.798f, 70.708f, 39.267f)
                    curveTo(70.487f, 39.375f, 69.886f, 39.497f, 69.646f, 39.529f)
                    curveTo(69.632f, 39.529f, 69.623f, 39.548f, 69.623f, 39.562f)
                    lineTo(70.52f, 44.189f)
                    curveTo(70.929f, 47.217f, 69.233f, 49.922f, 66.198f, 50.362f)
                    curveTo(63.158f, 50.803f, 60.776f, 48.478f, 60.315f, 45.445f)
                    lineTo(57.449f, 26.465f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF1A2E35)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(72.245f, 26.311f)
                    curveTo(72.179f, 26.404f, 71.643f, 26.118f, 70.962f, 26.222f)
                    curveTo(70.276f, 26.311f, 69.83f, 26.737f, 69.74f, 26.662f)
                    curveTo(69.698f, 26.629f, 69.759f, 26.47f, 69.952f, 26.278f)
                    curveTo(70.144f, 26.09f, 70.487f, 25.879f, 70.915f, 25.823f)
                    curveTo(71.342f, 25.762f, 71.723f, 25.87f, 71.953f, 25.997f)
                    curveTo(72.188f, 26.128f, 72.277f, 26.268f, 72.245f, 26.311f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF1A2E35)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(66.169f, 27.782f)
                    curveTo(66.104f, 27.876f, 65.568f, 27.59f, 64.887f, 27.693f)
                    curveTo(64.201f, 27.782f, 63.754f, 28.209f, 63.665f, 28.134f)
                    curveTo(63.623f, 28.101f, 63.679f, 27.942f, 63.876f, 27.75f)
                    curveTo(64.069f, 27.562f, 64.412f, 27.351f, 64.84f, 27.295f)
                    curveTo(65.267f, 27.234f, 65.648f, 27.342f, 65.878f, 27.468f)
                    curveTo(66.108f, 27.6f, 66.202f, 27.74f, 66.169f, 27.782f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF1A2E35)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(68.66f, 31.439f)
                    curveTo(68.65f, 31.401f, 69.064f, 31.265f, 69.741f, 31.078f)
                    curveTo(69.914f, 31.036f, 70.074f, 30.975f, 70.084f, 30.853f)
                    curveTo(70.107f, 30.722f, 70.004f, 30.544f, 69.891f, 30.356f)
                    curveTo(69.665f, 29.958f, 69.43f, 29.54f, 69.181f, 29.104f)
                    curveTo(68.19f, 27.319f, 67.443f, 25.842f, 67.513f, 25.804f)
                    curveTo(67.584f, 25.767f, 68.448f, 27.178f, 69.44f, 28.964f)
                    curveTo(69.679f, 29.404f, 69.91f, 29.826f, 70.13f, 30.225f)
                    curveTo(70.224f, 30.412f, 70.37f, 30.623f, 70.323f, 30.895f)
                    curveTo(70.295f, 31.031f, 70.182f, 31.143f, 70.074f, 31.19f)
                    curveTo(69.966f, 31.242f, 69.867f, 31.256f, 69.783f, 31.275f)
                    curveTo(69.097f, 31.411f, 68.669f, 31.476f, 68.66f, 31.439f)
                    close()
                }
                path(fill = SolidColor(Color(0xFFFF9A6C)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(69.618f, 39.539f)
                    curveTo(69.618f, 39.539f, 67.01f, 40.139f, 63.421f, 38.639f)
                    curveTo(63.421f, 38.639f, 64.971f, 41.967f, 69.863f, 40.786f)
                    lineTo(69.618f, 39.539f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF1A2E35)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(65.822f, 25.997f)
                    curveTo(65.78f, 26.193f, 65.084f, 26.203f, 64.3f, 26.418f)
                    curveTo(63.506f, 26.615f, 62.895f, 26.943f, 62.763f, 26.793f)
                    curveTo(62.702f, 26.718f, 62.792f, 26.531f, 63.026f, 26.311f)
                    curveTo(63.261f, 26.095f, 63.647f, 25.865f, 64.121f, 25.739f)
                    curveTo(64.596f, 25.617f, 65.047f, 25.626f, 65.357f, 25.706f)
                    curveTo(65.672f, 25.781f, 65.841f, 25.903f, 65.822f, 25.997f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF1A2E35)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(71.686f, 24.801f)
                    curveTo(71.587f, 24.975f, 71.094f, 24.881f, 70.525f, 24.956f)
                    curveTo(69.957f, 25.012f, 69.496f, 25.214f, 69.36f, 25.069f)
                    curveTo(69.299f, 24.998f, 69.355f, 24.83f, 69.543f, 24.651f)
                    curveTo(69.727f, 24.473f, 70.055f, 24.305f, 70.445f, 24.262f)
                    curveTo(70.835f, 24.22f, 71.192f, 24.309f, 71.413f, 24.44f)
                    curveTo(71.639f, 24.567f, 71.728f, 24.717f, 71.686f, 24.801f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF1A2E35)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(64.685f, 28.664f)
                    curveTo(64.732f, 28.959f, 65.023f, 29.165f, 65.333f, 29.123f)
                    curveTo(65.643f, 29.081f, 65.859f, 28.804f, 65.812f, 28.509f)
                    curveTo(65.765f, 28.214f, 65.474f, 28.007f, 65.164f, 28.05f)
                    curveTo(64.854f, 28.092f, 64.638f, 28.368f, 64.685f, 28.664f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF1A2E35)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(70.736f, 27.356f)
                    curveTo(70.783f, 27.651f, 71.075f, 27.858f, 71.385f, 27.816f)
                    curveTo(71.695f, 27.773f, 71.911f, 27.497f, 71.864f, 27.201f)
                    curveTo(71.817f, 26.906f, 71.526f, 26.7f, 71.216f, 26.742f)
                    curveTo(70.906f, 26.789f, 70.689f, 27.061f, 70.736f, 27.356f)
                    close()
                }
                path(fill = SolidColor(Color(0xFFFF9A6C)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(69.735f, 32.765f)
                    curveTo(69.294f, 32.639f, 68.918f, 32.859f, 68.739f, 33.178f)
                    curveTo(68.561f, 33.497f, 68.603f, 33.932f, 68.862f, 34.19f)
                    curveTo(69.12f, 34.448f, 69.576f, 34.49f, 69.858f, 34.261f)
                    curveTo(70.05f, 34.106f, 70.144f, 33.857f, 70.173f, 33.609f)
                    curveTo(70.196f, 33.379f, 70.158f, 33.14f, 70.017f, 32.958f)
                    curveTo(69.877f, 32.779f, 69.618f, 32.681f, 69.407f, 32.775f)
                }
                path(fill = SolidColor(Color(0xFF1A2E35)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(72.541f, 21.285f)
                    curveTo(72.226f, 19.771f, 71.183f, 18.478f, 69.909f, 17.596f)
                    curveTo(68.829f, 16.846f, 67.551f, 16.35f, 66.23f, 16.312f)
                    curveTo(64.915f, 16.27f, 63.567f, 16.715f, 62.613f, 17.625f)
                    curveTo(61.908f, 18.295f, 61.551f, 19.247f, 61.518f, 20.217f)
                    lineTo(61.593f, 19.546f)
                    curveTo(61.396f, 20.095f, 61.396f, 20.695f, 61.603f, 21.234f)
                    curveTo(61.603f, 21.234f, 61.603f, 21.234f, 61.603f, 21.239f)
                    verticalLineTo(21.234f)
                    curveTo(61.715f, 21.529f, 61.847f, 21.82f, 61.964f, 22.115f)
                    curveTo(62.298f, 22.954f, 62.951f, 23.657f, 63.759f, 24.065f)
                    curveTo(64.13f, 24.253f, 64.544f, 24.375f, 64.957f, 24.356f)
                    curveTo(65.375f, 24.337f, 65.794f, 24.159f, 66.047f, 23.826f)
                    curveTo(66.409f, 23.353f, 66.367f, 22.701f, 66.306f, 22.111f)
                    curveTo(66.663f, 22.668f, 67.043f, 23.245f, 67.617f, 23.573f)
                    curveTo(68.19f, 23.901f, 69.021f, 23.892f, 69.425f, 23.367f)
                    curveTo(69.815f, 22.861f, 69.66f, 22.129f, 69.435f, 21.534f)
                    curveTo(69.801f, 22.092f, 70.328f, 22.546f, 70.934f, 22.828f)
                    curveTo(71.267f, 22.982f, 71.667f, 23.086f, 72.005f, 22.94f)
                    curveTo(72.291f, 22.814f, 72.489f, 22.528f, 72.559f, 22.223f)
                    curveTo(72.634f, 21.914f, 72.602f, 21.595f, 72.541f, 21.285f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF375A64)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(69.068f, 22.537f)
                    curveTo(69.026f, 22.537f, 69.031f, 21.97f, 68.848f, 21.084f)
                    curveTo(68.664f, 20.217f, 68.223f, 18.989f, 67.189f, 18.08f)
                    curveTo(66.682f, 17.629f, 66.094f, 17.32f, 65.526f, 17.184f)
                    curveTo(64.957f, 17.044f, 64.417f, 17.062f, 63.98f, 17.147f)
                    curveTo(63.097f, 17.325f, 62.669f, 17.742f, 62.641f, 17.7f)
                    curveTo(62.631f, 17.69f, 62.725f, 17.578f, 62.937f, 17.428f)
                    curveTo(63.148f, 17.278f, 63.486f, 17.095f, 63.942f, 16.978f)
                    curveTo(64.398f, 16.865f, 64.971f, 16.828f, 65.577f, 16.964f)
                    curveTo(66.188f, 17.1f, 66.813f, 17.423f, 67.349f, 17.897f)
                    curveTo(68.439f, 18.858f, 68.871f, 20.156f, 69.017f, 21.051f)
                    curveTo(69.092f, 21.506f, 69.106f, 21.881f, 69.101f, 22.139f)
                    curveTo(69.106f, 22.397f, 69.087f, 22.537f, 69.068f, 22.537f)
                    close()
                }
                path(fill = SolidColor(Color(0xFFFFBF9D)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(113.281f, 78.375f)
                    lineTo(94.825f, 71.076f)
                    curveTo(94.825f, 71.076f, 85.372f, 58.706f, 85.569f, 58.467f)
                    lineTo(78.93f, 65.719f)
                    lineTo(88.576f, 78.745f)
                    lineTo(110.931f, 83.967f)
                    lineTo(121.517f, 84.052f)
                    curveTo(120.695f, 79.988f, 113.281f, 78.375f, 113.281f, 78.375f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF00BBAE)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(73.052f, 43.083f)
                    lineTo(70.539f, 42.112f)
                    lineTo(59.681f, 43.486f)
                    verticalLineTo(45.398f)
                    lineTo(59.371f, 45.436f)
                    curveTo(59.371f, 45.436f, 55.997f, 46.045f, 52.718f, 48.014f)
                    curveTo(48.747f, 50.39f, 46.685f, 54.98f, 47.418f, 59.541f)
                    curveTo(47.859f, 62.278f, 48.715f, 65.611f, 50.364f, 67.533f)
                    curveTo(53.516f, 71.213f, 56.396f, 66.666f, 56.396f, 66.666f)
                    curveTo(56.396f, 66.666f, 58.375f, 71.17f, 58.262f, 73.299f)
                    curveTo(58.168f, 75.084f, 57.374f, 78.802f, 57.12f, 79.959f)
                    curveTo(57.078f, 80.156f, 57.205f, 80.344f, 57.402f, 80.381f)
                    lineTo(80.307f, 84.605f)
                    lineTo(80.42f, 79.017f)
                    lineTo(79.691f, 56.044f)
                    lineTo(78.681f, 47.119f)
                    curveTo(78.686f, 47.128f, 78.719f, 42.862f, 73.052f, 43.083f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF00BBAE)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(78.334f, 45.642f)
                    lineTo(87.068f, 58.233f)
                    lineTo(80.035f, 66.778f)
                    lineTo(78.334f, 45.642f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF1A2E35)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(80.209f, 72.136f)
                    curveTo(80.19f, 72.136f, 80.18f, 71.845f, 80.176f, 71.325f)
                    curveTo(80.166f, 70.762f, 80.162f, 70.012f, 80.147f, 69.113f)
                    curveTo(80.101f, 67.247f, 79.997f, 64.669f, 79.626f, 61.842f)
                    curveTo(79.264f, 59.015f, 78.715f, 56.494f, 78.292f, 54.675f)
                    curveTo(78.085f, 53.798f, 77.916f, 53.072f, 77.784f, 52.519f)
                    curveTo(77.667f, 52.008f, 77.606f, 51.726f, 77.624f, 51.722f)
                    curveTo(77.643f, 51.717f, 77.732f, 51.994f, 77.878f, 52.495f)
                    curveTo(78.029f, 52.997f, 78.23f, 53.728f, 78.465f, 54.633f)
                    curveTo(78.931f, 56.442f, 79.513f, 58.973f, 79.875f, 61.809f)
                    curveTo(80.251f, 64.645f, 80.326f, 67.238f, 80.326f, 69.108f)
                    curveTo(80.317f, 70.045f, 80.303f, 70.8f, 80.274f, 71.325f)
                    curveTo(80.246f, 71.845f, 80.223f, 72.136f, 80.209f, 72.136f)
                    close()
                }
                path(fill = SolidColor(Color(0xFFFAFAFA)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(58.717f, 52.992f)
                    curveTo(58.717f, 53.034f, 58.055f, 53.025f, 56.993f, 53.081f)
                    curveTo(55.931f, 53.137f, 54.465f, 53.283f, 52.873f, 53.611f)
                    curveTo(51.285f, 53.939f, 49.88f, 54.389f, 48.884f, 54.759f)
                    curveTo(47.883f, 55.13f, 47.281f, 55.397f, 47.263f, 55.364f)
                    curveTo(47.253f, 55.35f, 47.399f, 55.265f, 47.662f, 55.125f)
                    curveTo(47.925f, 54.984f, 48.32f, 54.801f, 48.813f, 54.595f)
                    curveTo(49.8f, 54.178f, 51.209f, 53.7f, 52.816f, 53.367f)
                    curveTo(54.423f, 53.039f, 55.908f, 52.917f, 56.979f, 52.908f)
                    curveTo(57.515f, 52.903f, 57.952f, 52.912f, 58.248f, 52.936f)
                    curveTo(58.553f, 52.954f, 58.717f, 52.978f, 58.717f, 52.992f)
                    close()
                }
                path(fill = SolidColor(Color(0xFFFAFAFA)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(78.945f, 58.758f)
                    curveTo(78.95f, 58.791f, 78.536f, 58.889f, 77.855f, 58.992f)
                    curveTo(77.174f, 59.095f, 76.229f, 59.199f, 75.181f, 59.231f)
                    curveTo(74.134f, 59.269f, 73.18f, 59.231f, 72.494f, 59.175f)
                    curveTo(71.808f, 59.119f, 71.39f, 59.049f, 71.39f, 59.016f)
                    curveTo(71.394f, 58.978f, 71.817f, 58.988f, 72.498f, 59.002f)
                    curveTo(73.185f, 59.016f, 74.124f, 59.02f, 75.167f, 58.988f)
                    curveTo(76.21f, 58.95f, 77.15f, 58.88f, 77.831f, 58.819f)
                    curveTo(78.517f, 58.758f, 78.94f, 58.72f, 78.945f, 58.758f)
                    close()
                }
                path(fill = SolidColor(Color(0xFFFAFAFA)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(82.957f, 53.058f)
                    curveTo(83.0f, 53.105f, 82.229f, 53.873f, 81.083f, 54.572f)
                    curveTo(79.941f, 55.27f, 78.902f, 55.608f, 78.879f, 55.547f)
                    curveTo(78.851f, 55.477f, 79.833f, 55.045f, 80.951f, 54.356f)
                    curveTo(82.074f, 53.676f, 82.906f, 53.001f, 82.957f, 53.058f)
                    close()
                }
                path(fill = SolidColor(Color(0xFFFAFAFA)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(79.424f, 47.222f)
                    curveTo(79.466f, 47.264f, 78.437f, 48.187f, 76.882f, 48.839f)
                    curveTo(75.331f, 49.5f, 73.95f, 49.598f, 73.945f, 49.537f)
                    curveTo(73.931f, 49.462f, 75.266f, 49.256f, 76.783f, 48.609f)
                    curveTo(78.306f, 47.976f, 79.382f, 47.161f, 79.424f, 47.222f)
                    close()
                }
                path(fill = SolidColor(Color(0xFFFAFAFA)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(79.903f, 68.503f)
                    curveTo(79.908f, 68.517f, 79.748f, 68.601f, 79.448f, 68.742f)
                    curveTo(79.152f, 68.883f, 78.71f, 69.065f, 78.156f, 69.267f)
                    curveTo(77.051f, 69.675f, 75.477f, 70.125f, 73.697f, 70.401f)
                    curveTo(71.916f, 70.673f, 70.276f, 70.711f, 69.102f, 70.655f)
                    curveTo(68.514f, 70.626f, 68.04f, 70.589f, 67.711f, 70.542f)
                    curveTo(67.382f, 70.5f, 67.203f, 70.467f, 67.208f, 70.453f)
                    curveTo(67.213f, 70.411f, 67.936f, 70.472f, 69.106f, 70.481f)
                    curveTo(70.276f, 70.491f, 71.893f, 70.425f, 73.659f, 70.158f)
                    curveTo(75.421f, 69.886f, 76.986f, 69.464f, 78.099f, 69.103f)
                    curveTo(79.213f, 68.737f, 79.889f, 68.465f, 79.903f, 68.503f)
                    close()
                }
                path(fill = SolidColor(Color(0xFFFAFAFA)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(80.081f, 74.995f)
                    curveTo(80.086f, 75.005f, 80.016f, 75.042f, 79.875f, 75.113f)
                    curveTo(79.71f, 75.192f, 79.508f, 75.286f, 79.269f, 75.399f)
                    curveTo(78.742f, 75.647f, 77.958f, 75.975f, 76.98f, 76.336f)
                    curveTo(76.003f, 76.706f, 74.814f, 77.086f, 73.48f, 77.452f)
                    curveTo(72.141f, 77.803f, 70.656f, 78.131f, 69.077f, 78.384f)
                    curveTo(67.499f, 78.624f, 65.981f, 78.759f, 64.6f, 78.83f)
                    curveTo(63.218f, 78.886f, 61.969f, 78.881f, 60.926f, 78.825f)
                    curveTo(59.883f, 78.774f, 59.037f, 78.699f, 58.459f, 78.624f)
                    curveTo(58.191f, 78.586f, 57.975f, 78.558f, 57.792f, 78.535f)
                    curveTo(57.641f, 78.511f, 57.562f, 78.492f, 57.562f, 78.488f)
                    curveTo(57.562f, 78.478f, 57.646f, 78.478f, 57.796f, 78.488f)
                    curveTo(57.98f, 78.502f, 58.201f, 78.516f, 58.468f, 78.53f)
                    curveTo(59.051f, 78.577f, 59.892f, 78.624f, 60.93f, 78.652f)
                    curveTo(61.973f, 78.685f, 63.214f, 78.671f, 64.586f, 78.605f)
                    curveTo(65.958f, 78.525f, 67.466f, 78.384f, 69.035f, 78.146f)
                    curveTo(70.604f, 77.897f, 72.085f, 77.578f, 73.414f, 77.241f)
                    curveTo(74.744f, 76.889f, 75.928f, 76.528f, 76.91f, 76.181f)
                    curveTo(77.892f, 75.839f, 78.681f, 75.539f, 79.221f, 75.319f)
                    curveTo(79.47f, 75.22f, 79.677f, 75.141f, 79.846f, 75.075f)
                    curveTo(79.997f, 75.014f, 80.077f, 74.991f, 80.081f, 74.995f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF232F51)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(37.96f, 176.804f)
                    lineTo(35.366f, 188.504f)
                    lineTo(30.64f, 197.167f)
                    curveTo(30.301f, 197.79f, 30.56f, 198.573f, 31.208f, 198.868f)
                    curveTo(31.57f, 199.032f, 31.988f, 199.018f, 32.336f, 198.817f)
                    curveTo(34.535f, 197.551f, 42.602f, 192.845f, 42.743f, 191.978f)
                    curveTo(42.907f, 190.974f, 46.3f, 179.818f, 46.3f, 179.818f)
                    lineTo(37.96f, 176.804f)
                    close()
                }
                path(fill = SolidColor(Color(0xFFffffff)), stroke = null, fillAlpha = 0.6f,
                        strokeAlpha = 0.6f, strokeLineWidth = 0.0f, strokeLineCap = Butt,
                        strokeLineJoin = Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(31.208f, 198.873f)
                    lineTo(42.935f, 191.199f)
                    lineTo(42.813f, 191.691f)
                    curveTo(42.71f, 192.118f, 42.456f, 192.493f, 42.099f, 192.751f)
                    curveTo(41.145f, 193.44f, 38.467f, 195.277f, 32.368f, 198.83f)
                    curveTo(32.011f, 199.041f, 31.574f, 199.055f, 31.208f, 198.873f)
                    close()
                }
                path(fill = SolidColor(Color(0xFFffffff)), stroke = null, fillAlpha = 0.6f,
                        strokeAlpha = 0.6f, strokeLineWidth = 0.0f, strokeLineCap = Butt,
                        strokeLineJoin = Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(41.465f, 186.858f)
                    curveTo(41.122f, 186.99f, 40.92f, 187.407f, 41.037f, 187.754f)
                    curveTo(41.155f, 188.101f, 41.578f, 188.312f, 41.921f, 188.185f)
                    curveTo(42.264f, 188.063f, 42.527f, 187.566f, 42.372f, 187.238f)
                    curveTo(42.217f, 186.905f, 41.705f, 186.685f, 41.404f, 186.891f)
                }
                path(fill = linearGradient(0.0f to Color(0xFF00BBAE), 1.0f to Color(0xFF006AAC),
                        start = Offset(33.0434f,193.539f), end = Offset(35.1536f,193.539f)), stroke
                        = null, strokeLineWidth = 0.0f, strokeLineCap = Butt, strokeLineJoin =
                        Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(35.122f, 194.443f)
                    curveTo(35.047f, 194.457f, 34.934f, 193.787f, 34.323f, 193.285f)
                    curveTo(33.726f, 192.769f, 33.04f, 192.774f, 33.045f, 192.699f)
                    curveTo(33.04f, 192.666f, 33.214f, 192.615f, 33.496f, 192.643f)
                    curveTo(33.778f, 192.671f, 34.163f, 192.802f, 34.497f, 193.083f)
                    curveTo(34.83f, 193.365f, 35.033f, 193.716f, 35.108f, 193.988f)
                    curveTo(35.178f, 194.265f, 35.155f, 194.438f, 35.122f, 194.443f)
                    close()
                }
                path(fill = linearGradient(0.0f to Color(0xFF00BBAE), 1.0f to Color(0xFF006AAC),
                        start = Offset(31.8959f,195.174f), end = Offset(33.9222f,195.174f)), stroke
                        = null, strokeLineWidth = 0.0f, strokeLineCap = Butt, strokeLineJoin =
                        Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(33.91f, 195.798f)
                    curveTo(33.844f, 195.835f, 33.562f, 195.366f, 32.998f, 195.043f)
                    curveTo(32.444f, 194.71f, 31.894f, 194.696f, 31.894f, 194.616f)
                    curveTo(31.875f, 194.551f, 32.495f, 194.438f, 33.13f, 194.813f)
                    curveTo(33.769f, 195.183f, 33.98f, 195.779f, 33.91f, 195.798f)
                    close()
                }
                path(fill = linearGradient(0.0f to Color(0xFF00BBAE), 1.0f to Color(0xFF006AAC),
                        start = Offset(34.2038f,191.771f), end = Offset(37.1691f,191.771f)), stroke
                        = null, strokeLineWidth = 0.0f, strokeLineCap = Butt, strokeLineJoin =
                        Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(37.161f, 192.821f)
                    curveTo(37.09f, 192.863f, 36.663f, 192.137f, 35.836f, 191.565f)
                    curveTo(35.018f, 190.979f, 34.187f, 190.815f, 34.205f, 190.74f)
                    curveTo(34.201f, 190.674f, 35.107f, 190.726f, 35.991f, 191.349f)
                    curveTo(36.874f, 191.968f, 37.227f, 192.802f, 37.161f, 192.821f)
                    close()
                }
                path(fill = linearGradient(0.0f to Color(0xFF00BBAE), 1.0f to Color(0xFF006AAC),
                        start = Offset(35.2375f,188.838f), end = Offset(38.2516f,188.838f)), stroke
                        = null, strokeLineWidth = 0.0f, strokeLineCap = Butt, strokeLineJoin =
                        Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(38.246f, 189.333f)
                    curveTo(38.199f, 189.394f, 37.621f, 188.968f, 36.79f, 188.733f)
                    curveTo(35.963f, 188.485f, 35.244f, 188.536f, 35.239f, 188.457f)
                    curveTo(35.22f, 188.391f, 35.977f, 188.218f, 36.865f, 188.48f)
                    curveTo(37.757f, 188.733f, 38.298f, 189.286f, 38.246f, 189.333f)
                    close()
                }
                path(fill = linearGradient(0.0f to Color(0xFF00BBAE), 1.0f to Color(0xFF006AAC),
                        start = Offset(35.9534f,185.428f), end = Offset(39.1007f,185.428f)), stroke
                        = null, strokeLineWidth = 0.0f, strokeLineCap = Butt, strokeLineJoin =
                        Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(39.097f, 185.916f)
                    curveTo(39.045f, 185.972f, 38.491f, 185.429f, 37.598f, 185.25f)
                    curveTo(36.705f, 185.054f, 35.982f, 185.335f, 35.953f, 185.26f)
                    curveTo(35.939f, 185.232f, 36.104f, 185.115f, 36.409f, 185.025f)
                    curveTo(36.715f, 184.936f, 37.166f, 184.894f, 37.65f, 184.993f)
                    curveTo(38.129f, 185.096f, 38.528f, 185.316f, 38.773f, 185.518f)
                    curveTo(39.017f, 185.724f, 39.12f, 185.893f, 39.097f, 185.916f)
                    close()
                }
                path(fill = linearGradient(0.0f to Color(0xFF00BBAE), 1.0f to Color(0xFF006AAC),
                        start = Offset(36.2607f,183.487f), end = Offset(39.8433f,183.487f)), stroke
                        = null, strokeLineWidth = 0.0f, strokeLineCap = Butt, strokeLineJoin =
                        Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(37.776f, 184.604f)
                    curveTo(37.762f, 184.599f, 37.771f, 184.44f, 37.87f, 184.158f)
                    curveTo(37.964f, 183.882f, 38.147f, 183.488f, 38.505f, 183.09f)
                    curveTo(38.683f, 182.902f, 38.885f, 182.658f, 39.247f, 182.583f)
                    curveTo(39.43f, 182.546f, 39.66f, 182.635f, 39.764f, 182.823f)
                    curveTo(39.862f, 183.005f, 39.862f, 183.207f, 39.82f, 183.394f)
                    curveTo(39.627f, 184.13f, 38.815f, 184.688f, 37.955f, 184.59f)
                    curveTo(37.095f, 184.477f, 36.456f, 183.816f, 36.282f, 183.094f)
                    curveTo(36.249f, 182.912f, 36.249f, 182.696f, 36.39f, 182.523f)
                    curveTo(36.526f, 182.344f, 36.785f, 182.326f, 36.945f, 182.396f)
                    curveTo(37.278f, 182.532f, 37.447f, 182.785f, 37.617f, 182.982f)
                    curveTo(37.941f, 183.404f, 38.105f, 183.802f, 38.19f, 184.079f)
                    curveTo(38.274f, 184.36f, 38.284f, 184.524f, 38.27f, 184.524f)
                    curveTo(38.223f, 184.543f, 38.114f, 183.882f, 37.471f, 183.094f)
                    curveTo(37.306f, 182.912f, 37.118f, 182.682f, 36.874f, 182.598f)
                    curveTo(36.757f, 182.555f, 36.639f, 182.579f, 36.573f, 182.668f)
                    curveTo(36.508f, 182.752f, 36.493f, 182.902f, 36.526f, 183.048f)
                    curveTo(36.677f, 183.638f, 37.264f, 184.243f, 37.992f, 184.327f)
                    curveTo(38.716f, 184.416f, 39.43f, 183.924f, 39.59f, 183.334f)
                    curveTo(39.627f, 183.193f, 39.623f, 183.038f, 39.566f, 182.935f)
                    curveTo(39.51f, 182.832f, 39.402f, 182.78f, 39.284f, 182.799f)
                    curveTo(39.035f, 182.837f, 38.824f, 183.043f, 38.65f, 183.216f)
                    curveTo(37.936f, 183.957f, 37.823f, 184.632f, 37.776f, 184.604f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF000000)), stroke = null, fillAlpha = 0.3f,
                        strokeAlpha = 0.3f, strokeLineWidth = 0.0f, strokeLineCap = Butt,
                        strokeLineJoin = Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(37.659f, 178.074f)
                    lineTo(37.095f, 180.76f)
                    lineTo(45.59f, 182.166f)
                    lineTo(46.299f, 179.823f)
                    lineTo(37.96f, 176.804f)
                    lineTo(37.659f, 178.074f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF232F51)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(75.909f, 181.135f)
                    lineTo(75.209f, 190.487f)
                    curveTo(75.209f, 190.487f, 84.498f, 194.34f, 84.54f, 196.079f)
                    lineTo(66.32f, 195.376f)
                    lineTo(66.813f, 180.76f)
                    lineTo(75.909f, 181.135f)
                    close()
                }
                path(fill = SolidColor(Color(0xFFffffff)), stroke = null, fillAlpha = 0.6f,
                        strokeAlpha = 0.6f, strokeLineWidth = 0.0f, strokeLineCap = Butt,
                        strokeLineJoin = Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(69.604f, 189.479f)
                    curveTo(69.247f, 189.563f, 68.989f, 189.948f, 69.055f, 190.304f)
                    curveTo(69.12f, 190.665f, 69.515f, 190.927f, 69.872f, 190.852f)
                    curveTo(70.229f, 190.777f, 70.553f, 190.323f, 70.445f, 189.971f)
                    curveTo(70.337f, 189.624f, 69.863f, 189.334f, 69.534f, 189.493f)
                }
                path(fill = SolidColor(Color(0xFFffffff)), stroke = null, fillAlpha = 0.6f,
                        strokeAlpha = 0.6f, strokeLineWidth = 0.0f, strokeLineCap = Butt,
                        strokeLineJoin = Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(66.32f, 195.371f)
                    lineTo(66.428f, 193.899f)
                    lineTo(83.859f, 195.127f)
                    curveTo(83.859f, 195.127f, 84.658f, 195.512f, 84.545f, 196.079f)
                    lineTo(66.32f, 195.371f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF000000)), stroke = null, fillAlpha = 0.3f,
                        strokeAlpha = 0.3f, strokeLineWidth = 0.0f, strokeLineCap = Butt,
                        strokeLineJoin = Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(75.769f, 182.987f)
                    lineTo(75.646f, 184.655f)
                    lineTo(66.696f, 184.224f)
                    lineTo(66.757f, 182.298f)
                    lineTo(75.769f, 182.987f)
                    close()
                }
                path(fill = linearGradient(0.0f to Color(0xFF00BBAE), 1.0f to Color(0xFF006AAC),
                        start = Offset(73.8085f,190.847f), end = Offset(75.5083f,190.847f)), stroke
                        = null, strokeLineWidth = 0.0f, strokeLineCap = Butt, strokeLineJoin =
                        Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(75.51f, 190.407f)
                    curveTo(75.505f, 190.496f, 75.059f, 190.519f, 74.608f, 190.786f)
                    curveTo(74.152f, 191.044f, 73.912f, 191.419f, 73.832f, 191.382f)
                    curveTo(73.753f, 191.363f, 73.903f, 190.838f, 74.453f, 190.519f)
                    curveTo(74.998f, 190.196f, 75.529f, 190.322f, 75.51f, 190.407f)
                    close()
                }
                path(fill = linearGradient(0.0f to Color(0xFF00BBAE), 1.0f to Color(0xFF006AAC),
                        start = Offset(76.1338f,191.916f), end = Offset(77.3888f,191.916f)), stroke
                        = null, strokeLineWidth = 0.0f, strokeLineCap = Butt, strokeLineJoin =
                        Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(77.389f, 191.269f)
                    curveTo(77.408f, 191.358f, 77.014f, 191.494f, 76.699f, 191.855f)
                    curveTo(76.37f, 192.212f, 76.281f, 192.615f, 76.191f, 192.605f)
                    curveTo(76.107f, 192.615f, 76.064f, 192.094f, 76.468f, 191.649f)
                    curveTo(76.863f, 191.199f, 77.389f, 191.185f, 77.389f, 191.269f)
                    close()
                }
                path(fill = linearGradient(0.0f to Color(0xFF00BBAE), 1.0f to Color(0xFF006AAC),
                        start = Offset(78.2905f,192.839f), end = Offset(79.1552f,192.839f)), stroke
                        = null, strokeLineWidth = 0.0f, strokeLineCap = Butt, strokeLineJoin =
                        Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(78.385f, 193.59f)
                    curveTo(78.305f, 193.604f, 78.198f, 193.158f, 78.447f, 192.68f)
                    curveTo(78.691f, 192.202f, 79.118f, 192.024f, 79.151f, 192.099f)
                    curveTo(79.198f, 192.174f, 78.916f, 192.422f, 78.719f, 192.821f)
                    curveTo(78.512f, 193.215f, 78.475f, 193.585f, 78.385f, 193.59f)
                    close()
                }
                path(fill = linearGradient(0.0f to Color(0xFF00BBAE), 1.0f to Color(0xFF006AAC),
                        start = Offset(73.5325f,188.108f), end = Offset(75.4341f,188.108f)), stroke
                        = null, strokeLineWidth = 0.0f, strokeLineCap = Butt, strokeLineJoin =
                        Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(75.43f, 188.255f)
                    curveTo(75.388f, 188.335f, 74.988f, 188.194f, 74.481f, 188.213f)
                    curveTo(73.978f, 188.218f, 73.579f, 188.373f, 73.536f, 188.293f)
                    curveTo(73.49f, 188.227f, 73.884f, 187.913f, 74.476f, 187.904f)
                    curveTo(75.068f, 187.89f, 75.477f, 188.19f, 75.43f, 188.255f)
                    close()
                }
                path(fill = linearGradient(0.0f to Color(0xFF00BBAE), 1.0f to Color(0xFF006AAC),
                        start = Offset(72.8073f,185.625f), end = Offset(75.5371f,185.625f)), stroke
                        = null, strokeLineWidth = 0.0f, strokeLineCap = Butt, strokeLineJoin =
                        Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(75.538f, 186.366f)
                    curveTo(75.538f, 186.404f, 75.186f, 186.446f, 74.627f, 186.324f)
                    curveTo(74.349f, 186.263f, 74.025f, 186.16f, 73.682f, 185.996f)
                    curveTo(73.513f, 185.912f, 73.339f, 185.813f, 73.17f, 185.696f)
                    curveTo(73.09f, 185.64f, 72.992f, 185.579f, 72.902f, 185.471f)
                    curveTo(72.808f, 185.373f, 72.761f, 185.138f, 72.879f, 184.998f)
                    curveTo(72.977f, 184.862f, 73.147f, 184.805f, 73.278f, 184.81f)
                    curveTo(73.414f, 184.81f, 73.522f, 184.843f, 73.616f, 184.871f)
                    curveTo(73.818f, 184.927f, 74.006f, 185.002f, 74.175f, 185.087f)
                    curveTo(74.519f, 185.26f, 74.791f, 185.471f, 74.998f, 185.668f)
                    curveTo(75.407f, 186.076f, 75.538f, 186.413f, 75.505f, 186.427f)
                    curveTo(75.463f, 186.455f, 75.27f, 186.169f, 74.852f, 185.832f)
                    curveTo(74.645f, 185.663f, 74.378f, 185.485f, 74.058f, 185.344f)
                    curveTo(73.898f, 185.274f, 73.725f, 185.213f, 73.541f, 185.162f)
                    curveTo(73.349f, 185.105f, 73.184f, 185.096f, 73.132f, 185.18f)
                    curveTo(73.114f, 185.213f, 73.109f, 185.227f, 73.147f, 185.284f)
                    curveTo(73.184f, 185.335f, 73.264f, 185.391f, 73.344f, 185.448f)
                    curveTo(73.499f, 185.56f, 73.659f, 185.654f, 73.814f, 185.743f)
                    curveTo(74.124f, 185.912f, 74.425f, 186.034f, 74.683f, 186.113f)
                    curveTo(75.2f, 186.282f, 75.543f, 186.319f, 75.538f, 186.366f)
                    close()
                }
                path(fill = linearGradient(0.0f to Color(0xFF00BBAE), 1.0f to Color(0xFF006AAC),
                        start = Offset(75.2097f,185.06f), end = Offset(76.6924f,185.06f)), stroke =
                        null, strokeLineWidth = 0.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                        strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(75.416f, 186.488f)
                    curveTo(75.383f, 186.502f, 75.219f, 186.174f, 75.209f, 185.597f)
                    curveTo(75.205f, 185.312f, 75.247f, 184.969f, 75.374f, 184.604f)
                    curveTo(75.435f, 184.426f, 75.519f, 184.238f, 75.628f, 184.06f)
                    curveTo(75.74f, 183.887f, 75.881f, 183.647f, 76.229f, 183.624f)
                    curveTo(76.407f, 183.624f, 76.553f, 183.769f, 76.6f, 183.891f)
                    curveTo(76.657f, 184.018f, 76.661f, 184.121f, 76.68f, 184.229f)
                    curveTo(76.703f, 184.444f, 76.694f, 184.651f, 76.657f, 184.843f)
                    curveTo(76.586f, 185.227f, 76.412f, 185.541f, 76.229f, 185.766f)
                    curveTo(75.853f, 186.226f, 75.477f, 186.315f, 75.472f, 186.287f)
                    curveTo(75.444f, 186.24f, 75.759f, 186.08f, 76.05f, 185.64f)
                    curveTo(76.191f, 185.424f, 76.323f, 185.133f, 76.375f, 184.796f)
                    curveTo(76.398f, 184.627f, 76.403f, 184.449f, 76.379f, 184.271f)
                    curveTo(76.36f, 184.079f, 76.29f, 183.919f, 76.219f, 183.933f)
                    curveTo(76.13f, 183.929f, 75.97f, 184.069f, 75.891f, 184.219f)
                    curveTo(75.792f, 184.379f, 75.712f, 184.543f, 75.646f, 184.702f)
                    curveTo(75.519f, 185.026f, 75.458f, 185.34f, 75.435f, 185.602f)
                    curveTo(75.383f, 186.141f, 75.463f, 186.479f, 75.416f, 186.488f)
                    close()
                }
                path(fill = linearGradient(0.0f to Color(0xFF00BBAE), 1.0f to Color(0xFF006AAC),
                        start = Offset(35.6868f,131.006f), end = Offset(83.9372f,131.006f)), stroke
                        = null, strokeLineWidth = 0.0f, strokeLineCap = Butt, strokeLineJoin =
                        Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(57.045f, 80.316f)
                    curveTo(57.045f, 80.316f, 54.151f, 80.442f, 52.248f, 90.272f)
                    curveTo(50.34f, 100.102f, 35.69f, 179.729f, 35.69f, 179.729f)
                    lineTo(46.304f, 179.823f)
                    lineTo(66.325f, 104.119f)
                    lineTo(64.995f, 182.166f)
                    lineTo(75.778f, 182.991f)
                    curveTo(75.778f, 182.991f, 83.869f, 96.652f, 83.944f, 91.627f)
                    curveTo(84.038f, 85.364f, 80.434f, 79.027f, 80.434f, 79.027f)
                    lineTo(57.045f, 80.316f)
                    close()
                }
                path(fill = linearGradient(0.0f to Color(0xFF00BBAE), 1.0f to Color(0xFF006AAC),
                        start = Offset(66.4635f,91.9909f), end = Offset(71.6332f,91.9909f)), stroke
                        = null, strokeLineWidth = 0.0f, strokeLineCap = Butt, strokeLineJoin =
                        Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(71.629f, 79.477f)
                    curveTo(71.695f, 79.491f, 70.595f, 85.106f, 69.172f, 92.016f)
                    curveTo(67.743f, 98.93f, 66.536f, 104.522f, 66.465f, 104.508f)
                    curveTo(66.399f, 104.494f, 67.499f, 98.878f, 68.923f, 91.969f)
                    curveTo(70.351f, 85.055f, 71.563f, 79.463f, 71.629f, 79.477f)
                    close()
                }
                path(fill = linearGradient(0.0f to Color(0xFF00BBAE), 1.0f to Color(0xFF006AAC),
                        start = Offset(72.4633f,81.6908f), end = Offset(74.1918f,81.6908f)), stroke
                        = null, strokeLineWidth = 0.0f, strokeLineCap = Butt, strokeLineJoin =
                        Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(73.283f, 80.878f)
                    curveTo(73.264f, 80.855f, 73.57f, 80.761f, 73.898f, 81.056f)
                    curveTo(74.049f, 81.197f, 74.199f, 81.431f, 74.19f, 81.731f)
                    curveTo(74.185f, 82.036f, 73.955f, 82.331f, 73.645f, 82.458f)
                    curveTo(73.339f, 82.589f, 72.968f, 82.547f, 72.743f, 82.341f)
                    curveTo(72.517f, 82.139f, 72.456f, 81.867f, 72.461f, 81.661f)
                    curveTo(72.479f, 81.22f, 72.757f, 81.066f, 72.761f, 81.094f)
                    curveTo(72.794f, 81.117f, 72.606f, 81.3f, 72.634f, 81.656f)
                    curveTo(72.649f, 81.825f, 72.724f, 82.031f, 72.893f, 82.167f)
                    curveTo(73.053f, 82.303f, 73.316f, 82.331f, 73.546f, 82.233f)
                    curveTo(73.781f, 82.134f, 73.941f, 81.928f, 73.955f, 81.722f)
                    curveTo(73.974f, 81.511f, 73.88f, 81.314f, 73.767f, 81.183f)
                    curveTo(73.541f, 80.911f, 73.278f, 80.92f, 73.283f, 80.878f)
                    close()
                }
                path(fill = linearGradient(0.0f to Color(0xFF00BBAE), 1.0f to Color(0xFF006AAC),
                        start = Offset(71.7273f,85.4943f), end = Offset(73.3545f,85.4943f)), stroke
                        = null, strokeLineWidth = 0.0f, strokeLineCap = Butt, strokeLineJoin =
                        Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(72.827f, 84.769f)
                    curveTo(72.822f, 84.741f, 73.128f, 84.778f, 73.292f, 85.167f)
                    curveTo(73.367f, 85.35f, 73.4f, 85.617f, 73.264f, 85.87f)
                    curveTo(73.132f, 86.133f, 72.799f, 86.288f, 72.484f, 86.259f)
                    curveTo(72.169f, 86.236f, 71.864f, 86.035f, 71.774f, 85.753f)
                    curveTo(71.68f, 85.481f, 71.756f, 85.224f, 71.854f, 85.05f)
                    curveTo(72.08f, 84.694f, 72.385f, 84.699f, 72.376f, 84.727f)
                    curveTo(72.39f, 84.764f, 72.15f, 84.825f, 72.009f, 85.13f)
                    curveTo(71.944f, 85.275f, 71.911f, 85.481f, 71.986f, 85.664f)
                    curveTo(72.056f, 85.847f, 72.263f, 85.983f, 72.498f, 86.002f)
                    curveTo(72.738f, 86.02f, 72.959f, 85.917f, 73.057f, 85.748f)
                    curveTo(73.16f, 85.58f, 73.16f, 85.373f, 73.118f, 85.214f)
                    curveTo(73.034f, 84.9f, 72.803f, 84.806f, 72.827f, 84.769f)
                    close()
                }
                path(fill = linearGradient(0.0f to Color(0xFF00BBAE), 1.0f to Color(0xFF006AAC),
                        start = Offset(52.6945f,84.3024f), end = Offset(62.6588f,84.3024f)), stroke
                        = null, strokeLineWidth = 0.0f, strokeLineCap = Butt, strokeLineJoin =
                        Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(62.655f, 80.977f)
                    curveTo(62.669f, 80.981f, 62.631f, 81.159f, 62.528f, 81.469f)
                    curveTo(62.425f, 81.778f, 62.246f, 82.219f, 61.969f, 82.734f)
                    curveTo(61.424f, 83.766f, 60.4f, 85.097f, 58.891f, 86.091f)
                    curveTo(57.378f, 87.084f, 55.753f, 87.497f, 54.588f, 87.591f)
                    curveTo(54.0f, 87.642f, 53.526f, 87.633f, 53.201f, 87.605f)
                    curveTo(52.877f, 87.577f, 52.699f, 87.544f, 52.699f, 87.525f)
                    curveTo(52.699f, 87.478f, 53.422f, 87.558f, 54.569f, 87.417f)
                    curveTo(55.71f, 87.281f, 57.284f, 86.85f, 58.755f, 85.884f)
                    curveTo(60.226f, 84.919f, 61.241f, 83.644f, 61.819f, 82.65f)
                    curveTo(62.401f, 81.656f, 62.612f, 80.963f, 62.655f, 80.977f)
                    close()
                }
                path(fill = linearGradient(0.0f to Color(0xFF00BBAE), 1.0f to Color(0xFF006AAC),
                        start = Offset(79.8571f,83.8205f), end = Offset(83.3057f,83.8205f)), stroke
                        = null, strokeLineWidth = 0.0f, strokeLineCap = Butt, strokeLineJoin =
                        Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(83.305f, 87.961f)
                    curveTo(83.295f, 87.975f, 83.183f, 87.905f, 82.985f, 87.769f)
                    curveTo(82.788f, 87.638f, 82.52f, 87.413f, 82.215f, 87.113f)
                    curveTo(81.604f, 86.513f, 80.88f, 85.542f, 80.406f, 84.319f)
                    curveTo(79.936f, 83.091f, 79.814f, 81.886f, 79.866f, 81.038f)
                    curveTo(79.889f, 80.611f, 79.936f, 80.269f, 79.992f, 80.035f)
                    curveTo(80.044f, 79.8f, 80.082f, 79.674f, 80.1f, 79.678f)
                    curveTo(80.147f, 79.688f, 80.044f, 80.203f, 80.039f, 81.042f)
                    curveTo(80.035f, 81.877f, 80.18f, 83.039f, 80.636f, 84.23f)
                    curveTo(81.101f, 85.421f, 81.773f, 86.377f, 82.342f, 86.996f)
                    curveTo(82.91f, 87.61f, 83.333f, 87.924f, 83.305f, 87.961f)
                    close()
                }
                path(fill = SolidColor(Color(0xFFFFBF9D)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(52.826f, 61.377f)
                    lineTo(57.538f, 61.635f)
                    curveTo(57.834f, 63.159f, 58.873f, 65.493f, 58.873f, 65.493f)
                    curveTo(62.669f, 58.921f, 65.61f, 50.113f, 66.338f, 46.579f)
                    curveTo(66.23f, 46.227f, 66.108f, 45.833f, 65.948f, 45.397f)
                    curveTo(65.765f, 44.891f, 65.92f, 43.94f, 66.009f, 43.405f)
                    curveTo(66.136f, 42.618f, 66.357f, 41.558f, 66.423f, 41.066f)
                    curveTo(66.625f, 39.571f, 66.865f, 39.336f, 67.391f, 39.505f)
                    curveTo(68.1f, 39.735f, 67.1f, 43.705f, 67.682f, 43.79f)
                    curveTo(67.997f, 43.836f, 68.378f, 42.477f, 68.49f, 41.361f)
                    curveTo(68.603f, 40.246f, 69.106f, 36.814f, 69.477f, 36.585f)
                    curveTo(70.017f, 36.247f, 70.492f, 36.782f, 70.356f, 37.893f)
                    curveTo(70.219f, 39.018f, 70.041f, 42.369f, 70.633f, 42.416f)
                    curveTo(71.215f, 42.463f, 71.704f, 36.772f, 71.704f, 36.772f)
                    curveTo(71.704f, 36.772f, 71.61f, 35.535f, 72.296f, 35.563f)
                    curveTo(73.532f, 35.614f, 72.564f, 41.615f, 72.461f, 42.187f)
                    curveTo(72.385f, 42.58f, 73.029f, 42.655f, 73.085f, 42.224f)
                    curveTo(73.142f, 41.783f, 73.442f, 36.533f, 74.697f, 36.576f)
                    curveTo(75.66f, 36.608f, 73.95f, 41.521f, 74.49f, 42.66f)
                    curveTo(75.031f, 43.799f, 75.406f, 38.793f, 76.327f, 38.863f)
                    curveTo(76.67f, 38.891f, 76.924f, 38.957f, 76.294f, 41.788f)
                    curveTo(75.956f, 43.312f, 74.96f, 46.11f, 74.091f, 48.426f)
                    curveTo(72.63f, 56.277f, 66.226f, 81.407f, 56.185f, 76.804f)
                    curveTo(50.566f, 74.23f, 47.944f, 61.49f, 47.944f, 61.49f)
                    lineTo(52.826f, 61.377f)
                    close()
                }
                path(fill = SolidColor(Color(0xFFFF9A6C)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(74.095f, 48.426f)
                    curveTo(74.091f, 48.426f, 74.105f, 48.365f, 74.142f, 48.253f)
                    curveTo(74.185f, 48.126f, 74.241f, 47.962f, 74.312f, 47.761f)
                    curveTo(74.467f, 47.306f, 74.687f, 46.673f, 74.969f, 45.867f)
                    curveTo(75.251f, 45.042f, 75.594f, 44.034f, 75.933f, 42.848f)
                    curveTo(76.106f, 42.257f, 76.247f, 41.615f, 76.384f, 40.926f)
                    curveTo(76.449f, 40.584f, 76.515f, 40.228f, 76.553f, 39.867f)
                    curveTo(76.572f, 39.684f, 76.59f, 39.501f, 76.576f, 39.323f)
                    curveTo(76.562f, 39.136f, 76.534f, 38.986f, 76.388f, 38.981f)
                    curveTo(76.233f, 38.939f, 76.135f, 39.065f, 76.026f, 39.225f)
                    curveTo(75.933f, 39.384f, 75.857f, 39.567f, 75.787f, 39.754f)
                    curveTo(75.651f, 40.129f, 75.547f, 40.532f, 75.444f, 40.945f)
                    curveTo(75.34f, 41.357f, 75.242f, 41.779f, 75.12f, 42.211f)
                    curveTo(75.045f, 42.426f, 75.012f, 42.637f, 74.833f, 42.871f)
                    curveTo(74.8f, 42.9f, 74.767f, 42.937f, 74.701f, 42.951f)
                    curveTo(74.626f, 42.97f, 74.575f, 42.942f, 74.523f, 42.909f)
                    curveTo(74.452f, 42.848f, 74.42f, 42.787f, 74.391f, 42.726f)
                    curveTo(74.34f, 42.604f, 74.302f, 42.482f, 74.288f, 42.361f)
                    curveTo(74.203f, 41.4f, 74.42f, 40.472f, 74.57f, 39.506f)
                    curveTo(74.655f, 39.023f, 74.739f, 38.54f, 74.805f, 38.048f)
                    curveTo(74.838f, 37.804f, 74.861f, 37.556f, 74.866f, 37.307f)
                    curveTo(74.875f, 37.082f, 74.833f, 36.773f, 74.734f, 36.731f)
                    curveTo(74.612f, 36.684f, 74.391f, 36.825f, 74.274f, 37.026f)
                    curveTo(74.142f, 37.228f, 74.044f, 37.462f, 73.959f, 37.706f)
                    curveTo(73.795f, 38.193f, 73.682f, 38.714f, 73.588f, 39.239f)
                    curveTo(73.499f, 39.764f, 73.424f, 40.303f, 73.367f, 40.847f)
                    curveTo(73.339f, 41.118f, 73.315f, 41.395f, 73.287f, 41.667f)
                    lineTo(73.25f, 42.084f)
                    curveTo(73.24f, 42.196f, 73.24f, 42.431f, 73.057f, 42.567f)
                    curveTo(72.893f, 42.693f, 72.667f, 42.703f, 72.489f, 42.59f)
                    curveTo(72.399f, 42.534f, 72.329f, 42.426f, 72.315f, 42.318f)
                    curveTo(72.305f, 42.262f, 72.315f, 42.215f, 72.319f, 42.164f)
                    lineTo(72.338f, 42.061f)
                    curveTo(72.381f, 41.784f, 72.423f, 41.503f, 72.465f, 41.222f)
                    curveTo(72.545f, 40.659f, 72.611f, 40.092f, 72.667f, 39.515f)
                    curveTo(72.719f, 38.943f, 72.761f, 38.362f, 72.771f, 37.781f)
                    curveTo(72.771f, 37.204f, 72.766f, 36.604f, 72.601f, 36.079f)
                    curveTo(72.545f, 35.92f, 72.46f, 35.775f, 72.357f, 35.732f)
                    curveTo(72.254f, 35.695f, 72.108f, 35.732f, 72.033f, 35.835f)
                    curveTo(71.868f, 36.07f, 71.84f, 36.454f, 71.854f, 36.764f)
                    verticalLineTo(36.778f)
                    verticalLineTo(36.787f)
                    curveTo(71.751f, 37.912f, 71.629f, 39.018f, 71.464f, 40.106f)
                    curveTo(71.38f, 40.65f, 71.29f, 41.189f, 71.154f, 41.718f)
                    curveTo(71.121f, 41.85f, 71.079f, 41.986f, 71.032f, 42.117f)
                    curveTo(70.966f, 42.248f, 70.952f, 42.384f, 70.759f, 42.529f)
                    curveTo(70.595f, 42.609f, 70.473f, 42.52f, 70.407f, 42.454f)
                    curveTo(70.337f, 42.379f, 70.309f, 42.309f, 70.276f, 42.239f)
                    curveTo(70.219f, 42.098f, 70.191f, 41.962f, 70.168f, 41.826f)
                    curveTo(70.017f, 40.748f, 70.078f, 39.707f, 70.139f, 38.69f)
                    curveTo(70.158f, 38.179f, 70.285f, 37.673f, 70.219f, 37.223f)
                    curveTo(70.186f, 37.003f, 70.102f, 36.778f, 69.951f, 36.679f)
                    curveTo(69.876f, 36.632f, 69.796f, 36.618f, 69.707f, 36.642f)
                    curveTo(69.66f, 36.656f, 69.618f, 36.67f, 69.576f, 36.698f)
                    curveTo(69.533f, 36.736f, 69.543f, 36.722f, 69.51f, 36.782f)
                    curveTo(69.322f, 37.172f, 69.232f, 37.668f, 69.129f, 38.114f)
                    curveTo(69.035f, 38.573f, 68.955f, 39.028f, 68.88f, 39.478f)
                    curveTo(68.805f, 39.928f, 68.744f, 40.373f, 68.683f, 40.809f)
                    curveTo(68.622f, 41.245f, 68.593f, 41.681f, 68.509f, 42.112f)
                    curveTo(68.429f, 42.539f, 68.33f, 42.951f, 68.175f, 43.35f)
                    curveTo(68.138f, 43.448f, 68.091f, 43.547f, 68.034f, 43.645f)
                    curveTo(67.969f, 43.734f, 67.931f, 43.851f, 67.743f, 43.908f)
                    curveTo(67.64f, 43.936f, 67.546f, 43.865f, 67.499f, 43.804f)
                    curveTo(67.456f, 43.743f, 67.438f, 43.682f, 67.424f, 43.626f)
                    curveTo(67.4f, 43.514f, 67.391f, 43.411f, 67.386f, 43.307f)
                    curveTo(67.377f, 42.492f, 67.499f, 41.742f, 67.536f, 41.02f)
                    curveTo(67.56f, 40.659f, 67.574f, 40.307f, 67.532f, 39.979f)
                    curveTo(67.508f, 39.825f, 67.461f, 39.646f, 67.372f, 39.609f)
                    curveTo(67.245f, 39.557f, 67.076f, 39.529f, 66.982f, 39.6f)
                    curveTo(66.771f, 39.754f, 66.695f, 40.101f, 66.625f, 40.382f)
                    curveTo(66.559f, 40.678f, 66.526f, 40.973f, 66.47f, 41.254f)
                    curveTo(66.272f, 42.389f, 66.056f, 43.359f, 65.958f, 44.156f)
                    curveTo(65.911f, 44.554f, 65.892f, 44.911f, 65.944f, 45.211f)
                    curveTo(66.009f, 45.501f, 66.108f, 45.759f, 66.164f, 45.961f)
                    curveTo(66.221f, 46.153f, 66.268f, 46.303f, 66.301f, 46.425f)
                    curveTo(66.329f, 46.528f, 66.343f, 46.584f, 66.338f, 46.584f)
                    curveTo(66.334f, 46.584f, 66.31f, 46.533f, 66.272f, 46.434f)
                    curveTo(66.23f, 46.317f, 66.174f, 46.167f, 66.108f, 45.979f)
                    curveTo(66.042f, 45.773f, 65.939f, 45.543f, 65.859f, 45.229f)
                    curveTo(65.798f, 44.915f, 65.807f, 44.55f, 65.845f, 44.146f)
                    curveTo(65.934f, 43.336f, 66.127f, 42.37f, 66.31f, 41.236f)
                    curveTo(66.362f, 40.954f, 66.39f, 40.654f, 66.456f, 40.35f)
                    curveTo(66.489f, 40.195f, 66.521f, 40.04f, 66.583f, 39.886f)
                    curveTo(66.644f, 39.736f, 66.709f, 39.562f, 66.883f, 39.445f)
                    curveTo(67.071f, 39.328f, 67.273f, 39.37f, 67.452f, 39.436f)
                    curveTo(67.677f, 39.562f, 67.687f, 39.778f, 67.724f, 39.946f)
                    curveTo(67.771f, 40.303f, 67.757f, 40.664f, 67.738f, 41.029f)
                    curveTo(67.705f, 41.756f, 67.588f, 42.52f, 67.607f, 43.293f)
                    curveTo(67.612f, 43.387f, 67.621f, 43.481f, 67.64f, 43.57f)
                    curveTo(67.659f, 43.664f, 67.71f, 43.696f, 67.696f, 43.682f)
                    curveTo(67.715f, 43.687f, 67.785f, 43.603f, 67.828f, 43.528f)
                    curveTo(67.875f, 43.443f, 67.912f, 43.354f, 67.95f, 43.265f)
                    curveTo(68.095f, 42.89f, 68.189f, 42.482f, 68.265f, 42.07f)
                    curveTo(68.344f, 41.657f, 68.373f, 41.226f, 68.429f, 40.781f)
                    curveTo(68.485f, 40.34f, 68.547f, 39.895f, 68.617f, 39.44f)
                    curveTo(68.688f, 38.986f, 68.767f, 38.526f, 68.861f, 38.062f)
                    curveTo(68.969f, 37.593f, 69.035f, 37.134f, 69.265f, 36.646f)
                    curveTo(69.28f, 36.6f, 69.378f, 36.487f, 69.435f, 36.464f)
                    curveTo(69.496f, 36.426f, 69.566f, 36.403f, 69.637f, 36.379f)
                    curveTo(69.782f, 36.342f, 69.961f, 36.361f, 70.097f, 36.45f)
                    curveTo(70.36f, 36.637f, 70.445f, 36.923f, 70.487f, 37.181f)
                    curveTo(70.529f, 37.443f, 70.515f, 37.71f, 70.482f, 37.964f)
                    curveTo(70.454f, 38.207f, 70.435f, 38.46f, 70.416f, 38.709f)
                    curveTo(70.36f, 39.712f, 70.304f, 40.762f, 70.449f, 41.775f)
                    curveTo(70.473f, 41.896f, 70.501f, 42.023f, 70.543f, 42.131f)
                    curveTo(70.567f, 42.182f, 70.59f, 42.234f, 70.614f, 42.257f)
                    curveTo(70.628f, 42.276f, 70.633f, 42.271f, 70.637f, 42.276f)
                    curveTo(70.628f, 42.389f, 70.637f, 42.3f, 70.633f, 42.323f)
                    curveTo(70.633f, 42.304f, 70.628f, 42.304f, 70.614f, 42.281f)
                    curveTo(70.637f, 42.262f, 70.717f, 42.131f, 70.755f, 42.018f)
                    curveTo(70.797f, 41.896f, 70.835f, 41.775f, 70.868f, 41.648f)
                    curveTo(70.999f, 41.137f, 71.088f, 40.603f, 71.168f, 40.064f)
                    curveTo(71.328f, 38.986f, 71.45f, 37.879f, 71.549f, 36.764f)
                    verticalLineTo(36.787f)
                    curveTo(71.544f, 36.417f, 71.535f, 36.032f, 71.793f, 35.653f)
                    curveTo(71.943f, 35.465f, 72.211f, 35.362f, 72.465f, 35.456f)
                    curveTo(72.714f, 35.573f, 72.808f, 35.793f, 72.879f, 35.981f)
                    curveTo(73.071f, 36.595f, 73.062f, 37.195f, 73.062f, 37.785f)
                    curveTo(73.052f, 38.376f, 73.01f, 38.962f, 72.958f, 39.543f)
                    curveTo(72.902f, 40.12f, 72.831f, 40.696f, 72.752f, 41.259f)
                    curveTo(72.709f, 41.54f, 72.662f, 41.826f, 72.62f, 42.103f)
                    curveTo(72.582f, 42.281f, 72.592f, 42.286f, 72.648f, 42.337f)
                    curveTo(72.705f, 42.379f, 72.817f, 42.375f, 72.874f, 42.328f)
                    curveTo(72.935f, 42.286f, 72.944f, 42.201f, 72.954f, 42.047f)
                    lineTo(72.991f, 41.629f)
                    curveTo(73.02f, 41.353f, 73.048f, 41.076f, 73.076f, 40.8f)
                    curveTo(73.137f, 40.251f, 73.212f, 39.707f, 73.306f, 39.173f)
                    curveTo(73.405f, 38.639f, 73.513f, 38.109f, 73.691f, 37.598f)
                    curveTo(73.781f, 37.345f, 73.884f, 37.092f, 74.039f, 36.857f)
                    curveTo(74.119f, 36.74f, 74.213f, 36.623f, 74.349f, 36.539f)
                    curveTo(74.49f, 36.459f, 74.641f, 36.389f, 74.847f, 36.468f)
                    curveTo(75.045f, 36.576f, 75.077f, 36.759f, 75.11f, 36.89f)
                    curveTo(75.129f, 37.031f, 75.138f, 37.167f, 75.134f, 37.298f)
                    curveTo(75.129f, 37.561f, 75.101f, 37.818f, 75.068f, 38.067f)
                    curveTo(75.002f, 38.568f, 74.913f, 39.056f, 74.828f, 39.539f)
                    curveTo(74.673f, 40.495f, 74.457f, 41.442f, 74.532f, 42.314f)
                    curveTo(74.542f, 42.422f, 74.575f, 42.52f, 74.608f, 42.604f)
                    curveTo(74.631f, 42.646f, 74.655f, 42.684f, 74.669f, 42.698f)
                    curveTo(74.669f, 42.693f, 74.664f, 42.689f, 74.659f, 42.722f)
                    curveTo(74.655f, 42.759f, 74.659f, 42.712f, 74.655f, 42.722f)
                    curveTo(74.636f, 42.693f, 74.65f, 42.703f, 74.659f, 42.689f)
                    curveTo(74.763f, 42.567f, 74.833f, 42.323f, 74.894f, 42.126f)
                    curveTo(75.021f, 41.709f, 75.12f, 41.287f, 75.228f, 40.875f)
                    curveTo(75.336f, 40.462f, 75.444f, 40.059f, 75.59f, 39.665f)
                    curveTo(75.66f, 39.468f, 75.74f, 39.281f, 75.853f, 39.098f)
                    curveTo(75.914f, 39.009f, 75.975f, 38.92f, 76.074f, 38.845f)
                    curveTo(76.116f, 38.807f, 76.182f, 38.779f, 76.243f, 38.761f)
                    curveTo(76.318f, 38.751f, 76.379f, 38.751f, 76.426f, 38.761f)
                    curveTo(76.539f, 38.765f, 76.694f, 38.864f, 76.731f, 38.981f)
                    curveTo(76.778f, 39.093f, 76.788f, 39.196f, 76.792f, 39.3f)
                    curveTo(76.802f, 39.501f, 76.778f, 39.689f, 76.759f, 39.876f)
                    curveTo(76.713f, 40.251f, 76.647f, 40.603f, 76.576f, 40.95f)
                    curveTo(76.435f, 41.639f, 76.285f, 42.29f, 76.102f, 42.881f)
                    curveTo(75.745f, 44.067f, 75.383f, 45.075f, 75.087f, 45.895f)
                    curveTo(74.786f, 46.715f, 74.547f, 47.348f, 74.377f, 47.775f)
                    curveTo(74.298f, 47.976f, 74.232f, 48.136f, 74.18f, 48.257f)
                    curveTo(74.124f, 48.37f, 74.1f, 48.426f, 74.095f, 48.426f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF00BBAE)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(47.893f, 61.922f)
                    curveTo(48.804f, 65.133f, 50.585f, 70.092f, 52.502f, 75.323f)
                    curveTo(52.779f, 76.078f, 53.831f, 76.13f, 54.179f, 75.403f)
                    lineTo(58.878f, 65.498f)
                    curveTo(58.878f, 65.498f, 57.745f, 57.361f, 57.844f, 57.647f)
                    curveTo(57.938f, 57.933f, 48.156f, 58.701f, 48.156f, 58.701f)
                    lineTo(47.893f, 61.922f)
                    close()
                }
                path(fill = SolidColor(Color(0xFFFAFAFA)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(58.469f, 60.323f)
                    curveTo(58.478f, 60.361f, 57.886f, 60.534f, 56.956f, 60.89f)
                    curveTo(56.026f, 61.242f, 54.771f, 61.805f, 53.47f, 62.587f)
                    curveTo(52.173f, 63.375f, 51.097f, 64.228f, 50.35f, 64.884f)
                    curveTo(49.607f, 65.545f, 49.18f, 65.991f, 49.147f, 65.962f)
                    curveTo(49.133f, 65.953f, 49.227f, 65.826f, 49.41f, 65.616f)
                    curveTo(49.589f, 65.4f, 49.87f, 65.104f, 50.228f, 64.758f)
                    curveTo(50.946f, 64.059f, 52.022f, 63.173f, 53.338f, 62.376f)
                    curveTo(54.653f, 61.584f, 55.941f, 61.041f, 56.89f, 60.726f)
                    curveTo(57.364f, 60.567f, 57.759f, 60.459f, 58.032f, 60.398f)
                    curveTo(58.309f, 60.333f, 58.464f, 60.309f, 58.469f, 60.323f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF1A2E35)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(60.329f, 62.794f)
                    curveTo(60.306f, 62.813f, 59.986f, 62.438f, 59.545f, 61.772f)
                    curveTo(59.108f, 61.106f, 58.563f, 60.145f, 58.093f, 59.02f)
                    curveTo(57.628f, 57.895f, 57.327f, 56.831f, 57.167f, 56.053f)
                    curveTo(57.008f, 55.275f, 56.965f, 54.783f, 56.993f, 54.778f)
                    curveTo(57.031f, 54.773f, 57.134f, 55.251f, 57.336f, 56.011f)
                    curveTo(57.539f, 56.775f, 57.863f, 57.816f, 58.318f, 58.927f)
                    curveTo(58.779f, 60.037f, 59.291f, 60.999f, 59.69f, 61.678f)
                    curveTo(60.095f, 62.362f, 60.362f, 62.775f, 60.329f, 62.794f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF1A2E35)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(63.463f, 18.918f)
                    curveTo(61.819f, 21.567f, 61.471f, 24.993f, 62.547f, 27.928f)
                    curveTo(63.031f, 29.245f, 63.792f, 30.543f, 63.656f, 31.94f)
                    curveTo(63.515f, 33.37f, 62.425f, 34.72f, 62.81f, 36.107f)
                    curveTo(63.035f, 36.923f, 63.74f, 37.532f, 64.013f, 38.339f)
                    curveTo(64.327f, 39.281f, 63.999f, 40.307f, 63.848f, 41.292f)
                    curveTo(63.463f, 43.8f, 64.266f, 46.322f, 64.468f, 48.853f)
                    curveTo(64.666f, 51.384f, 63.985f, 54.342f, 61.72f, 55.448f)
                    curveTo(61.034f, 55.786f, 60.132f, 55.879f, 59.563f, 55.368f)
                    curveTo(58.84f, 54.722f, 59.042f, 53.554f, 59.239f, 52.593f)
                    curveTo(61.227f, 43.007f, 55.086f, 33.665f, 56.49f, 23.976f)
                    curveTo(56.716f, 22.424f, 57.585f, 19.785f, 58.708f, 18.698f)
                    curveTo(59.831f, 17.61f, 62.927f, 17.742f, 63.989f, 18.89f)
                }
                path(fill = SolidColor(Color(0xFF375A64)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(54.005f, 46.706f)
                    curveTo(53.986f, 46.687f, 54.292f, 46.359f, 54.724f, 45.679f)
                    curveTo(55.156f, 45.004f, 55.697f, 43.95f, 55.979f, 42.543f)
                    curveTo(56.115f, 41.84f, 56.176f, 41.057f, 56.124f, 40.214f)
                    curveTo(56.087f, 39.37f, 55.969f, 38.465f, 56.026f, 37.49f)
                    curveTo(56.044f, 37.003f, 56.148f, 36.511f, 56.303f, 36.023f)
                    curveTo(56.472f, 35.54f, 56.693f, 35.071f, 56.961f, 34.617f)
                    curveTo(57.496f, 33.712f, 58.178f, 32.868f, 58.802f, 31.964f)
                    curveTo(59.423f, 31.064f, 59.939f, 30.112f, 60.109f, 29.109f)
                    curveTo(60.278f, 28.106f, 60.094f, 27.107f, 59.939f, 26.17f)
                    curveTo(59.775f, 25.232f, 59.658f, 24.309f, 59.728f, 23.446f)
                    curveTo(59.784f, 22.589f, 59.953f, 21.801f, 60.188f, 21.107f)
                    curveTo(60.658f, 19.72f, 61.358f, 18.74f, 61.884f, 18.126f)
                    curveTo(62.152f, 17.817f, 62.378f, 17.596f, 62.538f, 17.456f)
                    curveTo(62.697f, 17.315f, 62.787f, 17.245f, 62.791f, 17.25f)
                    curveTo(62.81f, 17.268f, 62.467f, 17.564f, 61.96f, 18.187f)
                    curveTo(61.462f, 18.81f, 60.799f, 19.795f, 60.357f, 21.159f)
                    curveTo(60.141f, 21.839f, 59.982f, 22.612f, 59.935f, 23.451f)
                    curveTo(59.878f, 24.29f, 59.996f, 25.181f, 60.165f, 26.123f)
                    curveTo(60.325f, 27.06f, 60.522f, 28.078f, 60.348f, 29.142f)
                    curveTo(60.174f, 30.201f, 59.634f, 31.19f, 59.004f, 32.1f)
                    curveTo(58.375f, 33.014f, 57.698f, 33.853f, 57.167f, 34.734f)
                    curveTo(56.904f, 35.175f, 56.688f, 35.629f, 56.524f, 36.093f)
                    curveTo(56.378f, 36.562f, 56.275f, 37.031f, 56.251f, 37.5f)
                    curveTo(56.19f, 38.442f, 56.298f, 39.342f, 56.326f, 40.195f)
                    curveTo(56.368f, 41.048f, 56.298f, 41.854f, 56.143f, 42.567f)
                    curveTo(55.833f, 43.996f, 55.255f, 45.056f, 54.799f, 45.722f)
                    curveTo(54.569f, 46.054f, 54.367f, 46.303f, 54.226f, 46.462f)
                    curveTo(54.09f, 46.631f, 54.015f, 46.711f, 54.005f, 46.706f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF375A64)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(49.452f, 47.418f)
                    curveTo(49.443f, 47.414f, 49.494f, 47.306f, 49.603f, 47.095f)
                    curveTo(49.72f, 46.87f, 49.875f, 46.57f, 50.068f, 46.195f)
                    curveTo(50.467f, 45.412f, 51.073f, 44.292f, 51.66f, 42.839f)
                    curveTo(52.238f, 41.395f, 52.816f, 39.581f, 52.831f, 37.518f)
                    curveTo(52.835f, 37.003f, 52.798f, 36.478f, 52.713f, 35.948f)
                    curveTo(52.628f, 35.418f, 52.506f, 34.874f, 52.426f, 34.307f)
                    curveTo(52.351f, 33.745f, 52.314f, 33.14f, 52.464f, 32.545f)
                    curveTo(52.614f, 31.949f, 52.99f, 31.42f, 53.441f, 31.007f)
                    curveTo(54.348f, 30.187f, 55.438f, 29.653f, 56.002f, 28.692f)
                    curveTo(56.58f, 27.749f, 56.707f, 26.639f, 56.852f, 25.621f)
                    curveTo(56.979f, 24.59f, 57.176f, 23.601f, 57.515f, 22.729f)
                    curveTo(57.839f, 21.853f, 58.276f, 21.093f, 58.732f, 20.446f)
                    curveTo(59.648f, 19.143f, 60.667f, 18.323f, 61.401f, 17.84f)
                    curveTo(61.772f, 17.596f, 62.068f, 17.427f, 62.279f, 17.329f)
                    curveTo(62.486f, 17.226f, 62.599f, 17.174f, 62.603f, 17.184f)
                    curveTo(62.617f, 17.207f, 62.171f, 17.418f, 61.457f, 17.92f)
                    curveTo(60.747f, 18.426f, 59.761f, 19.256f, 58.877f, 20.549f)
                    curveTo(58.44f, 21.191f, 58.018f, 21.946f, 57.708f, 22.804f)
                    curveTo(57.383f, 23.662f, 57.2f, 24.623f, 57.083f, 25.654f)
                    curveTo(56.942f, 26.676f, 56.824f, 27.81f, 56.213f, 28.823f)
                    curveTo(55.603f, 29.859f, 54.47f, 30.407f, 53.615f, 31.195f)
                    curveTo(53.183f, 31.588f, 52.845f, 32.067f, 52.708f, 32.61f)
                    curveTo(52.567f, 33.154f, 52.6f, 33.726f, 52.675f, 34.274f)
                    curveTo(52.751f, 34.828f, 52.873f, 35.371f, 52.957f, 35.915f)
                    curveTo(53.042f, 36.459f, 53.075f, 36.998f, 53.07f, 37.523f)
                    curveTo(53.037f, 39.632f, 52.436f, 41.46f, 51.83f, 42.909f)
                    curveTo(51.214f, 44.362f, 50.585f, 45.478f, 50.157f, 46.242f)
                    curveTo(49.946f, 46.607f, 49.777f, 46.898f, 49.65f, 47.118f)
                    curveTo(49.527f, 47.325f, 49.462f, 47.423f, 49.452f, 47.418f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF1A2E35)), stroke = SolidColor(Color(0xFF1A2E35)),
                        strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                        strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(162.098f, 35.892f)
                    curveTo(162.098f, 35.892f, 161.971f, 35.897f, 161.722f, 35.887f)
                    curveTo(161.473f, 35.878f, 161.107f, 35.859f, 160.623f, 35.892f)
                    curveTo(159.664f, 35.944f, 158.227f, 36.126f, 156.53f, 36.834f)
                    curveTo(154.853f, 37.542f, 152.894f, 38.789f, 151.334f, 40.875f)
                    curveTo(150.559f, 41.911f, 149.915f, 43.162f, 149.52f, 44.578f)
                    curveTo(149.14f, 45.994f, 149.013f, 47.569f, 149.191f, 49.186f)
                    curveTo(149.398f, 50.798f, 149.892f, 52.467f, 150.798f, 54.0f)
                    curveTo(151.249f, 54.764f, 151.78f, 55.505f, 152.433f, 56.161f)
                    curveTo(153.063f, 56.836f, 153.805f, 57.431f, 154.642f, 57.886f)
                    curveTo(155.478f, 58.336f, 156.432f, 58.622f, 157.414f, 58.584f)
                    curveTo(158.386f, 58.551f, 159.387f, 58.209f, 160.097f, 57.506f)
                    curveTo(160.811f, 56.803f, 161.168f, 55.772f, 161.168f, 54.745f)
                    curveTo(161.173f, 53.709f, 160.815f, 52.678f, 160.195f, 51.811f)
                    curveTo(158.95f, 50.062f, 156.935f, 48.886f, 154.792f, 48.253f)
                    curveTo(152.626f, 47.625f, 150.249f, 47.606f, 147.975f, 48.159f)
                    curveTo(146.842f, 48.454f, 145.733f, 48.886f, 144.69f, 49.462f)
                    curveTo(143.647f, 50.039f, 142.703f, 50.798f, 141.871f, 51.67f)
                    curveTo(141.674f, 51.9f, 141.448f, 52.106f, 141.275f, 52.35f)
                    lineTo(140.734f, 53.081f)
                    curveTo(140.419f, 53.602f, 140.095f, 54.117f, 139.851f, 54.68f)
                    curveTo(139.343f, 55.791f, 138.986f, 56.981f, 138.808f, 58.209f)
                    curveTo(138.484f, 60.661f, 138.784f, 63.188f, 139.776f, 65.395f)
                    curveTo(140.734f, 67.603f, 142.374f, 69.497f, 144.408f, 70.641f)
                    curveTo(145.423f, 71.208f, 146.551f, 71.574f, 147.693f, 71.634f)
                    curveTo(148.83f, 71.705f, 149.967f, 71.414f, 150.93f, 70.856f)
                    curveTo(151.893f, 70.303f, 152.701f, 69.473f, 153.119f, 68.475f)
                    curveTo(153.538f, 67.477f, 153.533f, 66.338f, 153.171f, 65.344f)
                    curveTo(152.814f, 64.341f, 152.147f, 63.483f, 151.367f, 62.798f)
                    curveTo(150.582f, 62.114f, 149.666f, 61.598f, 148.712f, 61.261f)
                    curveTo(146.776f, 60.628f, 144.714f, 60.666f, 142.867f, 61.158f)
                    curveTo(141.016f, 61.659f, 139.367f, 62.611f, 138.075f, 63.825f)
                    curveTo(136.783f, 65.048f, 135.876f, 66.544f, 135.326f, 68.072f)
                    curveTo(134.217f, 71.156f, 134.499f, 74.283f, 135.453f, 76.678f)
                    curveTo(136.43f, 79.088f, 137.986f, 80.808f, 139.409f, 81.942f)
                    curveTo(140.842f, 83.086f, 142.148f, 83.705f, 143.041f, 84.052f)
                    curveTo(143.492f, 84.225f, 143.84f, 84.347f, 144.08f, 84.413f)
                    curveTo(144.314f, 84.488f, 144.437f, 84.53f, 144.437f, 84.53f)
                    curveTo(144.437f, 84.53f, 144.314f, 84.502f, 144.075f, 84.436f)
                    curveTo(143.835f, 84.375f, 143.483f, 84.263f, 143.027f, 84.099f)
                    curveTo(142.12f, 83.761f, 140.805f, 83.156f, 139.353f, 82.017f)
                    curveTo(137.915f, 80.888f, 136.332f, 79.163f, 135.331f, 76.73f)
                    curveTo(134.354f, 74.316f, 134.058f, 71.147f, 135.167f, 68.016f)
                    curveTo(135.716f, 66.459f, 136.632f, 64.936f, 137.948f, 63.684f)
                    curveTo(139.264f, 62.447f, 140.936f, 61.472f, 142.82f, 60.956f)
                    curveTo(144.7f, 60.445f, 146.8f, 60.403f, 148.787f, 61.05f)
                    curveTo(149.769f, 61.397f, 150.709f, 61.922f, 151.517f, 62.63f)
                    curveTo(152.321f, 63.338f, 153.016f, 64.219f, 153.392f, 65.269f)
                    curveTo(153.772f, 66.305f, 153.787f, 67.514f, 153.34f, 68.569f)
                    curveTo(152.894f, 69.623f, 152.053f, 70.491f, 151.052f, 71.067f)
                    curveTo(150.051f, 71.644f, 148.867f, 71.949f, 147.679f, 71.878f)
                    curveTo(146.495f, 71.817f, 145.334f, 71.438f, 144.291f, 70.856f)
                    curveTo(142.2f, 69.684f, 140.527f, 67.748f, 139.545f, 65.498f)
                    curveTo(138.531f, 63.248f, 138.23f, 60.68f, 138.554f, 58.181f)
                    curveTo(138.737f, 56.93f, 139.099f, 55.715f, 139.621f, 54.581f)
                    curveTo(139.87f, 54.009f, 140.199f, 53.48f, 140.523f, 52.95f)
                    lineTo(141.077f, 52.205f)
                    curveTo(141.256f, 51.951f, 141.486f, 51.741f, 141.688f, 51.506f)
                    curveTo(142.538f, 50.616f, 143.506f, 49.847f, 144.568f, 49.256f)
                    curveTo(145.63f, 48.67f, 146.758f, 48.229f, 147.913f, 47.93f)
                    curveTo(150.23f, 47.367f, 152.65f, 47.386f, 154.858f, 48.033f)
                    curveTo(157.047f, 48.684f, 159.096f, 49.875f, 160.388f, 51.689f)
                    curveTo(161.027f, 52.589f, 161.407f, 53.672f, 161.398f, 54.755f)
                    curveTo(161.398f, 55.833f, 161.022f, 56.92f, 160.256f, 57.67f)
                    curveTo(159.495f, 58.425f, 158.438f, 58.777f, 157.428f, 58.809f)
                    curveTo(156.404f, 58.847f, 155.408f, 58.547f, 154.552f, 58.083f)
                    curveTo(153.693f, 57.614f, 152.936f, 57.005f, 152.297f, 56.315f)
                    curveTo(151.635f, 55.645f, 151.094f, 54.89f, 150.643f, 54.112f)
                    curveTo(149.727f, 52.551f, 149.229f, 50.859f, 149.027f, 49.223f)
                    curveTo(148.848f, 47.583f, 148.985f, 45.989f, 149.375f, 44.555f)
                    curveTo(149.779f, 43.12f, 150.441f, 41.859f, 151.231f, 40.809f)
                    curveTo(152.819f, 38.709f, 154.806f, 37.462f, 156.502f, 36.764f)
                    curveTo(158.217f, 36.065f, 159.664f, 35.901f, 160.628f, 35.864f)
                    curveTo(161.111f, 35.84f, 161.483f, 35.864f, 161.732f, 35.883f)
                    curveTo(161.971f, 35.878f, 162.098f, 35.892f, 162.098f, 35.892f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF00BBAE)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(178.129f, 83.813f)
                    horizontalLineTo(110.824f)
                    verticalLineTo(135.605f)
                    horizontalLineTo(178.129f)
                    verticalLineTo(83.813f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF00BBAE)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(110.824f, 83.812f)
                    lineTo(123.256f, 95.381f)
                    horizontalLineTo(189.42f)
                    lineTo(178.129f, 83.812f)
                    horizontalLineTo(110.824f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF00BBAE)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(110.824f, 83.813f)
                    curveTo(112.036f, 83.813f, 139.837f, 83.761f, 178.129f, 83.691f)
                    horizontalLineTo(178.181f)
                    lineTo(178.219f, 83.728f)
                    curveTo(181.822f, 87.422f, 185.567f, 91.256f, 189.396f, 95.175f)
                    curveTo(189.434f, 95.217f, 189.476f, 95.255f, 189.514f, 95.292f)
                    lineTo(189.72f, 95.503f)
                    horizontalLineTo(189.424f)
                    curveTo(162.295f, 95.484f, 138.216f, 95.466f, 123.261f, 95.452f)
                    horizontalLineTo(123.232f)
                    lineTo(123.209f, 95.433f)
                    curveTo(115.424f, 88.134f, 111.232f, 84.197f, 110.824f, 83.813f)
                    curveTo(111.237f, 84.192f, 115.461f, 88.092f, 123.308f, 95.325f)
                    lineTo(123.256f, 95.306f)
                    curveTo(138.211f, 95.297f, 162.291f, 95.278f, 189.42f, 95.255f)
                    lineTo(189.33f, 95.466f)
                    curveTo(189.293f, 95.428f, 189.255f, 95.386f, 189.213f, 95.349f)
                    curveTo(185.384f, 91.425f, 181.644f, 87.591f, 178.04f, 83.897f)
                    lineTo(178.129f, 83.934f)
                    curveTo(139.837f, 83.864f, 112.036f, 83.813f, 110.824f, 83.813f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF1A2E35)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(126.141f, 107.902f)
                    curveTo(125.023f, 107.902f, 124.008f, 108.905f, 123.984f, 110.016f)
                    curveTo(123.965f, 111.131f, 124.947f, 112.163f, 126.066f, 112.205f)
                    curveTo(127.179f, 112.247f, 128.236f, 111.286f, 128.297f, 110.17f)
                    curveTo(128.358f, 109.059f, 127.414f, 107.986f, 126.3f, 107.906f)
                }
                path(fill = SolidColor(Color(0x00000000)), stroke = SolidColor(Color(0xFF1A2E35)),
                        strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                        strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(126.141f, 107.902f)
                    curveTo(125.023f, 107.902f, 124.008f, 108.905f, 123.984f, 110.016f)
                    curveTo(123.965f, 111.131f, 124.947f, 112.163f, 126.066f, 112.205f)
                    curveTo(127.179f, 112.247f, 128.236f, 111.286f, 128.297f, 110.17f)
                    curveTo(128.358f, 109.059f, 127.414f, 107.986f, 126.3f, 107.906f)
                }
                path(fill = SolidColor(Color(0xFF1A2E35)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(151.047f, 107.902f)
                    curveTo(149.929f, 107.902f, 148.914f, 108.905f, 148.895f, 110.016f)
                    curveTo(148.876f, 111.131f, 149.858f, 112.163f, 150.977f, 112.205f)
                    curveTo(152.09f, 112.247f, 153.147f, 111.286f, 153.208f, 110.17f)
                    curveTo(153.27f, 109.055f, 152.325f, 107.986f, 151.212f, 107.906f)
                }
                path(fill = SolidColor(Color(0x00000000)), stroke = SolidColor(Color(0xFF1A2E35)),
                        strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                        strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(151.047f, 107.902f)
                    curveTo(149.929f, 107.902f, 148.914f, 108.905f, 148.895f, 110.016f)
                    curveTo(148.876f, 111.131f, 149.858f, 112.163f, 150.977f, 112.205f)
                    curveTo(152.09f, 112.247f, 153.147f, 111.286f, 153.208f, 110.17f)
                    curveTo(153.27f, 109.055f, 152.325f, 107.986f, 151.212f, 107.906f)
                }
                path(fill = SolidColor(Color(0xFF1A2E35)), stroke = SolidColor(Color(0xFF1A2E35)),
                        strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                        strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(151.207f, 127.332f)
                    curveTo(151.094f, 127.378f, 150.93f, 125.302f, 149.121f, 122.667f)
                    curveTo(148.233f, 121.36f, 146.903f, 119.977f, 145.137f, 118.852f)
                    curveTo(143.379f, 117.727f, 141.152f, 116.921f, 138.742f, 116.831f)
                    curveTo(136.332f, 116.789f, 134.062f, 117.474f, 132.249f, 118.5f)
                    curveTo(130.426f, 119.527f, 129.021f, 120.835f, 128.058f, 122.091f)
                    curveTo(126.108f, 124.622f, 125.826f, 126.689f, 125.718f, 126.633f)
                    curveTo(125.704f, 126.628f, 125.727f, 126.507f, 125.779f, 126.277f)
                    curveTo(125.845f, 126.052f, 125.892f, 125.7f, 126.056f, 125.278f)
                    curveTo(126.329f, 124.421f, 126.883f, 123.221f, 127.837f, 121.917f)
                    curveTo(128.786f, 120.614f, 130.205f, 119.25f, 132.07f, 118.181f)
                    curveTo(133.921f, 117.113f, 136.261f, 116.391f, 138.756f, 116.438f)
                    curveTo(141.246f, 116.527f, 143.544f, 117.375f, 145.334f, 118.542f)
                    curveTo(147.134f, 119.71f, 148.477f, 121.153f, 149.356f, 122.503f)
                    curveTo(150.239f, 123.858f, 150.728f, 125.086f, 150.953f, 125.958f)
                    curveTo(151.094f, 126.385f, 151.123f, 126.741f, 151.174f, 126.971f)
                    curveTo(151.207f, 127.205f, 151.221f, 127.332f, 151.207f, 127.332f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF000000)), stroke = null, fillAlpha = 0.5f,
                        strokeAlpha = 0.5f, strokeLineWidth = 0.0f, strokeLineCap = Butt,
                        strokeLineJoin = Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(110.974f, 84.384f)
                    lineTo(122.88f, 99.441f)
                    horizontalLineTo(178.129f)
                    verticalLineTo(95.381f)
                    horizontalLineTo(123.256f)
                    lineTo(110.974f, 84.384f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF1A2E35)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(164.856f, 135.483f)
                    curveTo(164.786f, 135.483f, 164.729f, 126.474f, 164.729f, 115.364f)
                    curveTo(164.729f, 104.25f, 164.786f, 95.246f, 164.856f, 95.246f)
                    curveTo(164.927f, 95.246f, 164.983f, 104.25f, 164.983f, 115.364f)
                    curveTo(164.983f, 126.479f, 164.927f, 135.483f, 164.856f, 135.483f)
                    close()
                }
                path(fill = SolidColor(Color(0xFFF5F5F5)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(175.428f, 123.746f)
                    horizontalLineTo(169.494f)
                    verticalLineTo(130.435f)
                    horizontalLineTo(175.428f)
                    verticalLineTo(123.746f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF000000)), stroke = null, fillAlpha = 0.5f,
                        strokeAlpha = 0.5f, strokeLineWidth = 0.0f, strokeLineCap = Butt,
                        strokeLineJoin = Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(110.824f, 83.813f)
                    lineTo(97.006f, 95.381f)
                    horizontalLineTo(110.824f)
                    verticalLineTo(83.813f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF000000)), stroke = null, fillAlpha = 0.4f,
                        strokeAlpha = 0.4f, strokeLineWidth = 0.0f, strokeLineCap = Butt,
                        strokeLineJoin = Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                    moveTo(110.824f, 83.813f)
                    lineTo(97.006f, 95.381f)
                    horizontalLineTo(110.824f)
                    verticalLineTo(83.813f)
                    close()
                }
                path(fill = SolidColor(Color(0xFFFF4F5B)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(180.112f, 135.605f)
                    horizontalLineTo(102.729f)
                    verticalLineTo(195.151f)
                    horizontalLineTo(180.112f)
                    verticalLineTo(135.605f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF232F51)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(180.112f, 135.605f)
                    horizontalLineTo(102.729f)
                    verticalLineTo(195.151f)
                    horizontalLineTo(180.112f)
                    verticalLineTo(135.605f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF1A2E35)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(160.726f, 194.729f)
                    curveTo(160.656f, 194.729f, 160.6f, 181.454f, 160.6f, 165.085f)
                    curveTo(160.6f, 148.711f, 160.656f, 135.441f, 160.726f, 135.441f)
                    curveTo(160.797f, 135.441f, 160.853f, 148.711f, 160.853f, 165.085f)
                    curveTo(160.853f, 181.458f, 160.797f, 194.729f, 160.726f, 194.729f)
                    close()
                }
                path(fill = SolidColor(Color(0xFFF5F5F5)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(173.553f, 181.599f)
                    horizontalLineTo(167.619f)
                    verticalLineTo(188.288f)
                    horizontalLineTo(173.553f)
                    verticalLineTo(181.599f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF415A91)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(135.129f, 135.755f)
                    horizontalLineTo(126.338f)
                    verticalLineTo(148.463f)
                    horizontalLineTo(135.129f)
                    verticalLineTo(135.755f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF415A91)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(135.129f, 182.27f)
                    horizontalLineTo(126.338f)
                    verticalLineTo(194.977f)
                    horizontalLineTo(135.129f)
                    verticalLineTo(182.27f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF1A2E35)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(179.44f, 135.605f)
                    curveTo(179.44f, 135.61f, 179.375f, 135.614f, 179.243f, 135.619f)
                    curveTo(179.102f, 135.624f, 178.91f, 135.628f, 178.661f, 135.633f)
                    curveTo(178.134f, 135.638f, 177.387f, 135.647f, 176.424f, 135.656f)
                    curveTo(174.47f, 135.666f, 171.665f, 135.68f, 168.206f, 135.694f)
                    curveTo(161.253f, 135.703f, 151.673f, 135.717f, 141.087f, 135.731f)
                    curveTo(130.496f, 135.717f, 120.916f, 135.703f, 113.963f, 135.694f)
                    curveTo(110.5f, 135.675f, 107.695f, 135.666f, 105.745f, 135.656f)
                    curveTo(104.786f, 135.647f, 104.035f, 135.638f, 103.508f, 135.633f)
                    curveTo(103.264f, 135.628f, 103.072f, 135.624f, 102.926f, 135.619f)
                    curveTo(102.794f, 135.614f, 102.729f, 135.61f, 102.729f, 135.605f)
                    curveTo(102.729f, 135.6f, 102.794f, 135.596f, 102.926f, 135.591f)
                    curveTo(103.067f, 135.586f, 103.259f, 135.581f, 103.508f, 135.577f)
                    curveTo(104.035f, 135.572f, 104.782f, 135.563f, 105.745f, 135.553f)
                    curveTo(107.7f, 135.544f, 110.504f, 135.53f, 113.963f, 135.511f)
                    curveTo(120.916f, 135.502f, 130.496f, 135.488f, 141.087f, 135.474f)
                    curveTo(151.673f, 135.488f, 161.253f, 135.502f, 168.206f, 135.511f)
                    curveTo(171.669f, 135.53f, 174.474f, 135.539f, 176.424f, 135.553f)
                    curveTo(177.383f, 135.563f, 178.134f, 135.572f, 178.661f, 135.577f)
                    curveTo(178.905f, 135.581f, 179.098f, 135.586f, 179.243f, 135.591f)
                    curveTo(179.375f, 135.596f, 179.44f, 135.6f, 179.44f, 135.605f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF00BBAE)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(87.51f, 30.675f)
                    curveTo(88.154f, 30.811f, 91.25f, 31.462f, 97.24f, 32.718f)
                    lineTo(97.179f, 32.676f)
                    curveTo(98.504f, 34.814f, 100.623f, 37.04f, 103.607f, 38.531f)
                    curveTo(105.106f, 39.243f, 106.774f, 39.825f, 108.587f, 40.087f)
                    curveTo(110.401f, 40.354f, 112.341f, 40.284f, 114.282f, 39.867f)
                    curveTo(118.149f, 39.047f, 122.029f, 36.614f, 124.435f, 32.826f)
                    curveTo(125.638f, 30.951f, 126.488f, 28.776f, 126.831f, 26.47f)
                    curveTo(127.165f, 24.164f, 126.986f, 21.745f, 126.286f, 19.439f)
                    curveTo(125.948f, 18.239f, 125.408f, 17.114f, 124.82f, 16.04f)
                    curveTo(124.491f, 15.525f, 124.172f, 15.0f, 123.81f, 14.512f)
                    lineTo(123.242f, 13.8f)
                    lineTo(122.96f, 13.448f)
                    lineTo(122.645f, 13.125f)
                    curveTo(120.996f, 11.367f, 118.985f, 10.026f, 116.847f, 9.168f)
                    curveTo(114.709f, 8.306f, 112.445f, 7.931f, 110.269f, 8.01f)
                    curveTo(108.089f, 8.085f, 106.003f, 8.606f, 104.156f, 9.445f)
                    curveTo(102.305f, 10.284f, 100.694f, 11.442f, 99.387f, 12.768f)
                    curveTo(98.081f, 14.095f, 97.062f, 15.571f, 96.324f, 17.072f)
                    curveTo(94.844f, 20.09f, 94.543f, 23.142f, 94.783f, 25.593f)
                    lineTo(94.821f, 25.514f)
                    curveTo(90.296f, 28.706f, 88.012f, 30.318f, 87.51f, 30.675f)
                    curveTo(88.022f, 30.328f, 90.334f, 28.757f, 94.919f, 25.654f)
                    lineTo(94.962f, 25.626f)
                    lineTo(94.957f, 25.575f)
                    curveTo(94.741f, 23.146f, 95.065f, 20.137f, 96.535f, 17.17f)
                    curveTo(97.273f, 15.693f, 98.283f, 14.245f, 99.575f, 12.946f)
                    curveTo(100.868f, 11.648f, 102.456f, 10.518f, 104.274f, 9.698f)
                    curveTo(106.092f, 8.878f, 108.145f, 8.371f, 110.283f, 8.306f)
                    curveTo(112.421f, 8.235f, 114.639f, 8.606f, 116.734f, 9.454f)
                    curveTo(118.83f, 10.298f, 120.798f, 11.615f, 122.415f, 13.335f)
                    lineTo(122.725f, 13.654f)
                    lineTo(123.002f, 14.001f)
                    lineTo(123.561f, 14.7f)
                    curveTo(123.914f, 15.178f, 124.228f, 15.693f, 124.553f, 16.195f)
                    curveTo(125.126f, 17.25f, 125.657f, 18.351f, 125.986f, 19.528f)
                    curveTo(126.672f, 21.787f, 126.85f, 24.159f, 126.526f, 26.418f)
                    curveTo(126.188f, 28.678f, 125.356f, 30.811f, 124.181f, 32.653f)
                    curveTo(121.823f, 36.37f, 118.026f, 38.765f, 114.225f, 39.586f)
                    curveTo(112.322f, 40.003f, 110.415f, 40.078f, 108.629f, 39.825f)
                    curveTo(106.844f, 39.572f, 105.2f, 39.009f, 103.719f, 38.315f)
                    curveTo(100.774f, 36.862f, 98.669f, 34.682f, 97.339f, 32.578f)
                    lineTo(97.315f, 32.545f)
                    lineTo(97.278f, 32.536f)
                    curveTo(91.264f, 31.392f, 88.154f, 30.801f, 87.51f, 30.675f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF00BBAE)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(112.271f, 16.537f)
                    lineTo(111.759f, 24.586f)
                    curveTo(111.717f, 25.242f, 111.19f, 25.767f, 110.532f, 25.804f)
                    curveTo(109.837f, 25.847f, 109.231f, 25.336f, 109.156f, 24.642f)
                    lineTo(108.258f, 16.551f)
                    curveTo(108.193f, 15.937f, 108.677f, 15.408f, 109.292f, 15.412f)
                    lineTo(111.261f, 15.44f)
                    curveTo(111.848f, 15.459f, 112.309f, 15.956f, 112.271f, 16.537f)
                    close()
                }
                path(fill = SolidColor(Color(0xFF00BBAE)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = NonZero) {
                    moveTo(108.394f, 30.337f)
                    curveTo(108.737f, 31.368f, 109.851f, 31.926f, 110.885f, 31.584f)
                    curveTo(111.918f, 31.242f, 112.477f, 30.131f, 112.134f, 29.1f)
                    curveTo(111.791f, 28.068f, 110.678f, 27.51f, 109.644f, 27.853f)
                    curveTo(108.611f, 28.195f, 108.051f, 29.306f, 108.394f, 30.337f)
                    close()
                }
            }
        }
        .build()
        return _imgNoData!!
    }

private var _imgNoData: ImageVector? = null
