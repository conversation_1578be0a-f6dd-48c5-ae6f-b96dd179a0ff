package vn.com.bidv.designsystem.component.dataentry

import IBGradient
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.datadisplay.badge.IBankBadgeLabel
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelColor
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelSize
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelType
import vn.com.bidv.designsystem.component.feedback.bottomsheet.IBankBottomSheet
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

@Composable
fun IBankSpecialDropdown(
    modifier: Modifier = Modifier.fillMaxWidth(),
    isExpand: Boolean = false,
    title: String = "",
    id: String = "",
    balance: String = "",
    subtitle: String = "",
    tag: String = "",
    imageResource: Int,
    iconEnd: Int? = null,
    onClick: () -> Unit = {}
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current
    val horizontalPadding = if (isExpand) IBSpacing.spacingS else IBSpacing.spacingM
    val iconEndFix = if (isExpand) R.drawable.circle_arrow_down else R.drawable.arrow_right
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(IBCornerRadius.cornerRadiusL))
            .wrapContentHeight()
            .background(brush = IBGradient.color_grd_card_primary)
            .clickable { onClick() }
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    start = horizontalPadding,
                    end = horizontalPadding,
                    top = IBSpacing.spacingS,
                    bottom = IBSpacing.spacingS
                )
        ) {
            if (!isExpand) {
                Text(
                    text = title,
                    style = typography.captionCaption_m,
                    color = colorScheme.contentOn_specialPrimary,
                    modifier = Modifier
                        .align(Alignment.Start)
                        .padding(bottom = IBSpacing.spacing2xs)
                )
            }
            Row(
                modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.Start
            ) {
                Box(
                    modifier = Modifier
                        .size(32.dp)
                        .clip(CircleShape)
                        .background(colorScheme.bgMainTertiary)
                        .border(
                            width = IBBorderDivider.borderDividerS,
                            color = colorScheme.borderMainPrimary,
                            shape = RoundedCornerShape(IBCornerRadius.cornerRadiusRound)
                        )
                ) {
                    Image(
                        modifier = Modifier.size(32.dp),
                        painter = painterResource(id = imageResource),
                        contentDescription = null
                    )
                }
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = IBSpacing.spacingXs)
                        .wrapContentHeight()
                ) {
                    Row(
                        horizontalArrangement = Arrangement.Start,
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = IBSpacing.spacing2xs)
                    ) {
                        Text(
                            text = id,
                            style = if (isExpand) typography.titleTitle_s else typography.bodyBody_s,
                            color = colorScheme.contentOn_specialPrimary,
                            modifier = Modifier.padding(end = IBSpacing.spacing2xs)
                        )
                        IBankBadgeLabel(
                            badgeColor = LabelColor.BRAND,
                            badgeSize = LabelSize.SM,
                            badgeType = LabelType.ROUNDED,
                            title = tag
                        )
                        Spacer(modifier = Modifier.weight(1f))
                        Icon(
                            modifier = Modifier
                                .size(24.dp),
                            imageVector = ImageVector.vectorResource(id = iconEnd ?: iconEndFix),
                            contentDescription = null,
                            tint = colorScheme.contentOn_specialPrimary
                        )
                    }
                    if (isExpand && subtitle.isNotBlank()) {
                        Text(
                            text = subtitle,
                            style = typography.bodyBody_s,
                            color = colorScheme.contentOn_specialSecondary,
                            modifier = Modifier
                                .align(Alignment.Start)
                                .padding(bottom = IBSpacing.spacingM)
                        )
                    }
                    Text(
                        text = balance,
                        style = typography.titleTitle_m,
                        color = colorScheme.contentOn_specialPrimary,
                        modifier = Modifier.align(Alignment.Start)
                    )
                }
            }
        }

        Icon(
            imageVector = ImageVector.vectorResource(id = R.drawable.bidv_flower_input_dropdown),
            modifier = Modifier.align(Alignment.TopEnd),
            contentDescription = "Account Icon",
            tint = Color.Unspecified
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Preview(showBackground = true)
@Composable
fun PreviewSpecialDropdown() {

    val sampleData1 = (0..3).map { "Tài khoản $it" to "**********$it" }
    val sampleData2 = (0..9).map { "Tài khoản $it" to "**********$it" }
    val sampleData3 = (0..21).map { "Tài khoản $it" to "**********$it" }

    var dataBottomSheet by remember { mutableStateOf(emptyList<Pair<String, String>>()) }
    var isLoading by remember { mutableStateOf(false) }
    var isLoading2 by remember { mutableStateOf(false) }
    var isLoading3 by remember { mutableStateOf(false) }
    var showSheet by remember { mutableStateOf(false) }

    LaunchedEffect(isLoading) {
        delay(2000L)
        dataBottomSheet = sampleData1
        isLoading = false
    }
    LaunchedEffect(isLoading2) {
        delay(2000L)
        dataBottomSheet = sampleData2
        isLoading2 = false
    }
    LaunchedEffect(isLoading3) {
        delay(2000L)
        dataBottomSheet = sampleData3
        isLoading3 = false
    }

    Column(
        Modifier.padding(IBSpacing.spacingM),
        verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingM)
    ) {
        IBankSpecialDropdown(
            title = "Chuyển từ",
            id = "***********",
            balance = "2,049,923,746 VND",
            subtitle = "Công ty TNHH An Bình",
            tag = "Mặc định",
            imageResource = R.mipmap.bidv,
            onClick = {
                showSheet = true
                isLoading = true
            }
        )
        IBankSpecialDropdown(
            Modifier,
            true,
            id = "0169 8433 001",
            balance = "2,049,923,745 VND",
            subtitle = "Công ty TNHH CMC",
            tag = "Thanh toán",
            imageResource = R.mipmap.vietcombank_priority,
            onClick = {
                showSheet = true
                isLoading2 = true
            }
        )
        IBankSpecialDropdown(
            Modifier.fillMaxWidth(0.7f),
            true,
            id = "0169 8433 001",
            balance = "2,049,923,745 VND",
            subtitle = "Công ty NHH An Bình",
            tag = "Tiêt kiệm",
            imageResource = R.drawable.icon_popup_error_64,
            iconEnd = R.drawable.arrow_top,
            onClick = {
                showSheet = true
                isLoading3 = true
            }
        )
    }

    if (showSheet) {
        IBankBottomSheet(
            title = "Số tài khoản",
            onDismiss = { showSheet = false },
            bottomSheetContent = {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(min = 100.dp)
                ) {
                    if (isLoading || isLoading2 || isLoading3) {
                        CircularProgressIndicator(modifier = Modifier.align(Alignment.Center))
                    } else {
                        LazyColumn(
                            modifier = Modifier.padding(IBSpacing.spacingM),
                            verticalArrangement = Arrangement.spacedBy(IBSpacing.spacing3xs)
                        ) {
                            items(
                                count = dataBottomSheet.size,
                            ) { index ->
                                val item = dataBottomSheet[index]
                                Row(modifier = Modifier.heightIn(min = 50.dp)) {
                                    Text(text = item.first, modifier = Modifier.weight(1f))
                                    Text(text = item.second)
                                }
                            }
                        }
                    }
                }
            },
        )
    }

}



