package vn.com.bidv.designsystem.component.datepicker

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.dataentry.IBDropdownStyle
import vn.com.bidv.designsystem.component.dataentry.IBFrameExtendState
import vn.com.bidv.designsystem.component.dataentry.IBFrameState
import vn.com.bidv.designsystem.component.dataentry.IBHintTextColor
import vn.com.bidv.designsystem.component.dataentry.IBankInputFrameBase
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

sealed class IBankInputDatePickerSize(
    val height: Dp,
) {
    class M : IBankInputDatePickerSize(height = 56.dp)
    class SM : IBankInputDatePickerSize(height = 36.dp)
}

@Composable
fun IBankInputDatePicker(
    modifier: Modifier = Modifier,
    size: IBankInputDatePickerSize = IBankInputDatePickerSize.M(),
    labelText: String,
    text: String,
    state: IBFrameState = IBFrameState.DEFAULT(LocalColorScheme.current),
    required: Boolean = false,
    iconEnd: Int = R.drawable.calendar_outline,
    onClickClear: () -> Unit = {},
    onClickEnd: () -> Unit = {},
    hintTextStart: String? = "",
    hintTextEnd: String? = "",
) {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current
    val hintTextColor = when (state) {
        is IBFrameState.ERROR -> IBHintTextColor.ERROR(colorScheme).hintTextColor
        else -> IBHintTextColor.DEFAULT(colorScheme).hintTextColor
    }

    Column(modifier = modifier.testTagIBank("IBankInputDatePicker_$labelText")) {
        IBankInputDatePickerBody(
            modifier = Modifier,
            size = size,
            labelText = labelText,
            text = text,
            state = state,
            required = required,
            iconEnd = iconEnd,
            onClickClear = onClickClear,
            onClickEnd = onClickEnd,
        )
        if (!hintTextStart.isNullOrEmpty() || !hintTextEnd.isNullOrEmpty()) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(
                        top = IBSpacing.spacing2xs, start = IBSpacing.spacing2xs
                    ), horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = hintTextStart ?: "",
                    style = typography.captionCaption_m,
                    color = hintTextColor,
                )
                Text(
                    modifier = Modifier.padding(end = IBSpacing.spacing2xs),
                    text = hintTextEnd ?: "",
                    style = typography.captionCaption_m,
                    color = hintTextColor,
                )
            }
        }
    }
}

@Composable
private fun IBankInputDatePickerBody(
    modifier: Modifier = Modifier,
    size: IBankInputDatePickerSize = IBankInputDatePickerSize.M(),
    labelText: String,
    text: String,
    state: IBFrameState = IBFrameState.DEFAULT(LocalColorScheme.current),
    required: Boolean = false,
    iconEnd: Int = R.drawable.calendar_outline,
    onClickClear: () -> Unit = {},
    onClickEnd: () -> Unit = {},
) = IBankInputFrameBase(
    modifier = Modifier
        .height(size.height)
        .clickable(
            interactionSource = remember { MutableInteractionSource() },
            indication = ripple(
                bounded = true,
                color = LocalColorScheme.current.bgSolidPrimary_press,
            ),
            onClick = onClickEnd
        )
        .then(modifier),
    state = state
) {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current
    val style = when (state) {
        is IBFrameState.DEFAULT -> IBDropdownStyle.DEFAULT(typography, colorScheme)
        is IBFrameExtendState.FILLED -> IBDropdownStyle.FILLED(typography, colorScheme)
        is IBFrameState.FOCUS -> IBDropdownStyle.FOCUS(typography, colorScheme)
        is IBFrameState.DISABLE -> IBDropdownStyle.DISABLE(typography, colorScheme)
        is IBFrameState.ERROR -> IBDropdownStyle.ERROR(typography, colorScheme)
        is IBFrameExtendState.FIXED -> IBDropdownStyle.FIXED(typography, colorScheme)
        is IBFrameExtendState.PRESSED -> IBDropdownStyle.DEFAULT(typography, colorScheme)
    }
    val labelColor = style.labelColor
    val iconEndTint = style.iconEndTint
    val showData = text.isNotBlank()
    val selectedTextColor = style.selectedTextColor
    Row(
        modifier = Modifier
            .align(Alignment.Center)
            .fillMaxWidth()
            .background(state.colorBackground)
            .padding(
                start = IBSpacing.spacingM,
                end = IBSpacing.spacingM,
                top = IBSpacing.spacingXs,
                bottom = IBSpacing.spacingXs
            ),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(modifier = Modifier.weight(1f), contentAlignment = Alignment.CenterStart) {
            when (size) {
                is IBankInputDatePickerSize.SM -> {
                    if (showData) {
                        Text(
                            modifier = Modifier
                                .fillMaxWidth(),
                            text = text,
                            style = typography.bodyBody_m,
                            color = selectedTextColor,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                    } else {
                        PlaceholderView(
                            modifier = Modifier
                                .fillMaxWidth(),
                            required = required,
                            placeHolderText = labelText,
                            placeHolderStyle = LocalTypography.current.captionCaption_m,
                            placeHolderColor = labelColor,
                        )
                    }
                }

                else -> {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .alpha(if (showData) 1f else 0f),
                    ) {
                        PlaceholderView(
                            modifier = Modifier.fillMaxWidth(),
                            required = required,
                            placeHolderText = labelText,
                            placeHolderStyle = LocalTypography.current.captionCaption_m,
                            placeHolderColor = labelColor,
                        )

                        Spacer(modifier = Modifier.height(IBSpacing.spacing3xs))
                        Text(
                            text = text,
                            style = typography.bodyBody_m,
                            color = selectedTextColor,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                    }

                    PlaceholderView(
                        modifier = Modifier
                            .fillMaxWidth()
                            .alpha(if (showData) 0f else 1f),
                        required = required,
                        placeHolderText = labelText,
                        placeHolderStyle = LocalTypography.current.bodyBody_m,
                        placeHolderColor = LocalColorScheme.current.contentPlaceholder,
                    )
                }
            }
        }


        if (showData) {
            Spacer(modifier = Modifier.width(IBSpacing.spacing2xs))
            IconButton(modifier = Modifier.size(IBSpacing.spacingM), onClick = onClickClear) {
                Icon(
                    imageVector = ImageVector.vectorResource(id = R.drawable.circle_close_outline),
                    contentDescription = "",
                    tint = colorScheme.contentMainTertiary,
                    modifier = Modifier
                        .clickable { onClickClear() }
                )
            }
        }

        Box(
            modifier = Modifier
                .padding(start = IBSpacing.spacingXs)
                .clickable { onClickEnd() },
        ) {
            Icon(
                modifier = Modifier.testTagIBank("IBankInputDatePicker_iconEnd").size(16.dp),
                tint = iconEndTint,
                imageVector = ImageVector.vectorResource(id = iconEnd),
                contentDescription = null,
            )
        }
    }
}

@Composable
private fun PlaceholderView(
    modifier: Modifier,
    required: Boolean,
    placeHolderText: String,
    placeHolderStyle: TextStyle,
    placeHolderColor: Color,
) {
    Row(
        modifier = modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            placeHolderText,
            style = placeHolderStyle,
            color = placeHolderColor,
        )
        if (required) {
            DotRequire()
        }
    }
}

@Composable
private fun DotRequire() {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current
    Text(
        "*",
        style = typography.captionCaption_m,
        color = colorScheme.contentNegativePrimary,
        modifier = Modifier.padding(start = IBSpacing.spacing3xs)
    )
}

@Preview
@Composable
private fun PreviewInputDatePicker() {
    val colorScheme = LocalColorScheme.current
    Spacer(Modifier.height(16.dp))
    Box(Modifier.padding(all = 12.dp)) {
        Column {
            IBankInputDatePicker(
                labelText = "Ngân hàng",
                required = false,
                text = "Ngân hàng TMCP Đầu tư và Phát triển Việt Nam (BIDV)",
                state = IBFrameState.DEFAULT(colorScheme),
                onClickClear = {}
            )
            Spacer(Modifier.height(24.dp))
            IBankInputDatePicker(
                labelText = "Label",
                required = true,
                text = "Ngân hàng TMCP Đầu tư và Phát triển Việt Nam (BIDV)",
                state = IBFrameExtendState.FILLED(colorScheme),
                onClickClear = {}
            )

            Spacer(Modifier.height(24.dp))
            IBankInputDatePicker(
                labelText = "Tên tài khoản",
                required = true,
                text = "",
                state = IBFrameState.ERROR(colorScheme),
                hintTextStart = "This is a hint text to help user",
                hintTextEnd = "200VND",
            )

            Spacer(Modifier.height(24.dp))
            IBankInputDatePicker(
                labelText = "Label",
                required = true,
                text = "",
                state = IBFrameState.DEFAULT(colorScheme)
            )

            Spacer(Modifier.height(24.dp))
            IBankInputDatePicker(
                labelText = "Label",
                required = true,
                text = "",
                state = IBFrameState.DEFAULT(colorScheme),
                size = IBankInputDatePickerSize.SM()
            )

            Spacer(Modifier.height(24.dp))
            IBankInputDatePicker(
                labelText = "Label",
                required = true,
                text = "25/02/2025",
                state = IBFrameState.DEFAULT(colorScheme),
                size = IBankInputDatePickerSize.SM()
            )
        }
    }
}

