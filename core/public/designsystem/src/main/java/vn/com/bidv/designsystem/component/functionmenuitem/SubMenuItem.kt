package vn.com.bidv.designsystem.component.functionmenuitem

import androidx.annotation.DrawableRes
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.R as RDesignSystem

@Composable
fun SubMenuItem(
    @DrawableRes leadingIcon: Int? = null,
    leadingModifier: Modifier = Modifier.size(IBSpacing.spacing2xl),
    content: String,
    styleContent: TextStyle = LocalTypography.current.bodyBody_m,
    colorContent: Color = LocalColorScheme.current.contentMainPrimary,
    showDivider: Boolean = true,
    onClick: () -> Unit = {},
    thumbContent: (@Composable () -> Unit)? = null,
) {
    Row(
        Modifier
            .fillMaxWidth()
            .height(IntrinsicSize.Min)
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = ripple(
                    bounded = true,
                    color = LocalColorScheme.current.bgSolidPrimary_press,
                ),
                onClick = onClick
            )
            .padding(
                top = IBSpacing.spacingS,
                start = IBSpacing.spacingM,
            ),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center,
    ) {
        leadingIcon?.let {
            Box(
                modifier = Modifier
                    .height(IntrinsicSize.Min)
                    .padding(bottom = IBSpacing.spacingS)
            ) {
                Icon(
                    painter = painterResource(id = leadingIcon),
                    contentDescription = null,
                    modifier = leadingModifier,
                    tint = LocalColorScheme.current.contentMainPrimary
                )
            }
            Spacer(modifier = Modifier.size(IBSpacing.spacingM))
        }
        Column(
            modifier = Modifier.fillMaxHeight()
        ) {
            Row(
                modifier = Modifier.padding(end = IBSpacing.spacingM),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center,
            ) {
                Text(
                    modifier = Modifier
                        .testTagIBank("SubMenuItem_content_$content")
                        .weight(1f),
                    text = content,
                    color = colorContent,
                    style = styleContent,
                    textAlign = TextAlign.Start
                )
                thumbContent?.let {
                    Spacer(modifier = Modifier.width(IBSpacing.spacingXs))
                    thumbContent()
                }
            }
            Spacer(modifier = Modifier.height(IBSpacing.spacingS))
            if (showDivider) {
                val pixelHeight = with(LocalDensity.current) { 1f / density }.dp
                Spacer(
                    Modifier
                        .fillMaxWidth()
                        .height(pixelHeight)
                        .background(LocalColorScheme.current.borderSolidPrimary)
                )
            }
        }
    }
}

@Preview
@Composable
fun SubMenuItemPreview() {
    SubMenuItem(
        leadingModifier = Modifier.size(IBSpacing.spacingM),
        leadingIcon = RDesignSystem.drawable.face_id,
        content = "Sử dụng Face ID để đăng nhập",
        styleContent = LocalTypography.current.bodyBody_m,
        colorContent = LocalColorScheme.current.contentMainPrimary,
        thumbContent = {
            Icon(
                painter = painterResource(id = RDesignSystem.drawable.arrow_right),
                contentDescription = null,
                modifier = Modifier.size(IBSpacing.spacingM),
                tint = Color.Unspecified
            )
        }
    )
}