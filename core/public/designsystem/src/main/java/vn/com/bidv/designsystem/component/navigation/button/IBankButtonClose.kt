package vn.com.bidv.designsystem.component.navigation.button

import androidx.compose.foundation.background
import androidx.compose.foundation.indication
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.NoRippleEffectView
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.utils.debounceClick

/**
 * Draft version of Close Buttons.
 * @param onClick called when this button is clicked.
 * @param isEnable controls the enabled state of this button. When `false`, this component will not
 *   respond to user input, and it will appear visually disabled and disabled to accessibility
 *   services.
 */

@Composable
fun IBankCloseButton(
    isEnable: Boolean = true,
    onClick: () -> Unit
) {

    val colorScheme = LocalColorScheme.current

    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()

    var backgroundColor by remember { mutableStateOf(Color.Transparent) }
    var iconColor by remember { mutableStateOf(Color.Transparent) }

    backgroundColor = if (isEnable) {
        if (isPressed) {
            colorScheme.bgMainTertiary_press
        } else {
            Color.Transparent
        }
    } else {
        Color.Transparent
    }

    iconColor =
        if (isEnable) {
            colorScheme.contentMainSecondary
        } else {
            colorScheme.contentDisablePrimary
        }

    NoRippleEffectView {
        Button(
            modifier =
            Modifier
                .size(40.dp)
                .indication(interactionSource, indication = null),
            onClick = debounceClick(onClick = onClick),
            colors = ButtonDefaults.buttonColors(Color.Transparent),
            shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL),
            interactionSource = interactionSource,
            contentPadding = PaddingValues(0.dp)
        ) {
            Row(
                modifier = Modifier
                    .size(24.dp)
                    .background(
                        color = backgroundColor,
                        shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL)
                    ),
                horizontalArrangement = Arrangement.Center
            ) {
                Icon(
                    imageVector = ImageVector.vectorResource(id = R.drawable.close),
                    contentDescription = null,
                    tint = iconColor,
                    modifier = Modifier
                        .size(13.dp)
                        .align(alignment = Alignment.CenterVertically)
                )
            }
        }
    }

}

@Composable
@Preview
fun ButtonCloseExample() {

    Column(
        modifier = Modifier
            .padding(20.dp),
        verticalArrangement = Arrangement.spacedBy(20.dp)
    ) {

        IBankCloseButton {}

        IBankCloseButton(
            isEnable = false,
        ) { }

    }

}

