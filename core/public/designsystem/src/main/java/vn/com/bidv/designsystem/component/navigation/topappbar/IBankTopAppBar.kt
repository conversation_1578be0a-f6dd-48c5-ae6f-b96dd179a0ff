package vn.com.bidv.designsystem.component.navigation.topappbar

import android.annotation.SuppressLint
import androidx.annotation.DrawableRes
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.utils.debounceClick

enum class TopAppBarType {
    Result,
    Title
}

data class TopAppBarConfig(
    val isShowTopAppBar: Boolean = true,
    val titleTopAppBar: String? = null,
    val titleTopAppBarColor: Color? = null,
    val isShowNavigationIcon: Boolean = true,
    val navigationIconColor: Color? = null,
    val iconLeading: Int = R.drawable.bidv_direct_logo,
    val onNavigationClick: (() -> Unit)? = null,
    val onHomeClick: (() -> Unit)? = null,
    val isShowActionItem: Boolean = true,
    val showHomeIcon: Boolean = true,
    val homeActionPosition: HomeActionPosition = HomeActionPosition.END,
    val actionItems: @Composable RowScope.() -> Unit = {}
)

@SuppressLint("ServiceCast")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun IBankTopAppBar(
    navController: NavController?,
    topAppBarType: TopAppBarType = TopAppBarType.Title,
    topAppBarConfig: TopAppBarConfig = TopAppBarConfig()
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current

    TopAppBar(
        modifier = Modifier.testTagIBank("IBankTopAppBar_${topAppBarConfig.titleTopAppBar}"),
        title = {
            topAppBarConfig.titleTopAppBar?.let {
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    textAlign = TextAlign.Start,
                    style = typography.titleTitle_m,
                    text = topAppBarConfig.titleTopAppBar,
                    color = topAppBarConfig.titleTopAppBarColor ?: colorScheme.contentMainPrimary,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )

            }
        },
        navigationIcon = {
            if (topAppBarConfig.isShowNavigationIcon) {
                Spacer(modifier = Modifier.size(IBSpacing.spacingS))
                when (topAppBarType) {
                    TopAppBarType.Title -> {
                        AppBarIconAction(
                            icon = R.drawable.arrow_left_outline,
                            tint = topAppBarConfig.navigationIconColor
                        ) {
                            if (topAppBarConfig.onNavigationClick == null)
                                navController?.popBackStack()
                            else
                                topAppBarConfig.onNavigationClick.invoke()
                        }
                    }

                    TopAppBarType.Result -> {
                        Icon(
                            modifier = Modifier
                                .defaultMinSize(139.dp, 28.dp)
                                .padding(start = IBSpacing.spacingM),
                            painter = painterResource(topAppBarConfig.iconLeading),
                            contentDescription = null,
                            tint = Color.Unspecified
                        )
                    }
                }
            }
        },
        actions = {
            TopAppBarActions(
                navController = navController,
                isShowHomeAction = topAppBarConfig.showHomeIcon,
                actions = topAppBarConfig.actionItems,
                onHomeClick = topAppBarConfig.onHomeClick,
                homeActionPosition = topAppBarConfig.homeActionPosition,
            )
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = Color.Transparent
        )
    )
}

enum class HomeActionPosition {
    START,
    END
}

@Composable
private fun TopAppBarActions(
    navController: NavController?,
    isShowHomeAction: Boolean = true,
    homeActionPosition: HomeActionPosition = HomeActionPosition.END,
    onHomeClick: (() -> Unit)? = null,
    actions: @Composable RowScope.() -> Unit
) {
    Row(
        modifier = Modifier.wrapContentSize(),
        horizontalArrangement = Arrangement.End,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        when (homeActionPosition) {
            HomeActionPosition.START -> {
                if (isShowHomeAction && navController?.previousBackStackEntry != null) {
                    GoHomeAction(navController, onHomeClick)
                }
                actions()
            }

            HomeActionPosition.END -> {
                actions()
                if (isShowHomeAction && navController?.previousBackStackEntry != null) {
                    GoHomeAction(navController, onHomeClick)
                }
            }
        }
        Spacer(modifier = Modifier.size(IBSpacing.spacingS))
    }

}

@Composable
fun GoHomeAction(navController: NavController?, onHomeClick: (() -> Unit)? = null) {
    // Hard code home route, because design system module not depend on SDKBase
    val homeRoute = "HomePageScreen"
    val homePageTabIndex = 0
    val homePageTabIndexKey = "homeTabIndex"
    AppBarIconAction(
        icon = R.drawable.home_outline,
    ) {
        if (onHomeClick != null) {
            onHomeClick()
        } else {
            // Before navigating to HomePageScreen, set the tabIndex value into the SavedStateHandle of HomePageScreen
            navController?.getBackStackEntry(homeRoute)?.savedStateHandle?.set(
                homePageTabIndexKey,
                homePageTabIndex,
            )

            // Navigate to HomePageScreen and preserve its previous state if available
            navController?.navigate(homeRoute) {
                popUpTo(homeRoute) {
                    saveState = true
                    inclusive = false
                }
                launchSingleTop = true
            }
        }
    }
}

@Composable
fun AppBarIconAction(
    @DrawableRes icon: Int,
    tint: Color? = null,
    enable: Boolean = true,
    onActionClick: () -> Unit
) {
    val context = LocalContext.current
    val iconName = runCatching { context.resources.getResourceEntryName(icon) }.getOrElse { "unknown" }
    IconButton(
        modifier = Modifier
            .testTagIBank("AppBarIconAction_$iconName")
            .width(40.dp)
            .fillMaxHeight(),
        enabled = enable,
        onClick = debounceClick(onClick = onActionClick)
    ) {
        Icon(
            modifier = Modifier.size(24.dp),
            painter = painterResource(icon),
            contentDescription = null,
            tint = tint ?: LocalColorScheme.current.contentMainSecondary
        )
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewTopAppBar() {
    Column {
        IBankTopAppBar(
            null,
            TopAppBarType.Title,
            TopAppBarConfig(
                titleTopAppBar = "Menu Titlte"
            )
        )

        IBankTopAppBar(
            null,
            TopAppBarType.Result
        )
    }
}