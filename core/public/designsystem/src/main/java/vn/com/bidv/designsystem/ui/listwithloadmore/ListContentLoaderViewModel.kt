package vn.com.bidv.designsystem.ui.listwithloadmore

import vn.com.bidv.common.patterns.mvi.BaseMviViewModel

abstract class ListContentLoaderViewModel<T>(
    reducer: ListContentLoaderReducer<T>
) : BaseMviViewModel<ListContentLoaderReducer.ListContentLoaderViewState<T>, ListContentLoaderReducer.ListContentLoaderViewEvent<T>, ListContentLoaderReducer.ListContentLoaderViewEffect>(
    initialState = ListContentLoaderReducer.ListContentLoaderViewState.ShowContent(),
    reducer = reducer
) {
    override fun handleEffect(
        sideEffect: ListContentLoaderReducer.ListContentLoaderViewEffect,
        onResult: (ListContentLoaderReducer.ListContentLoaderViewEvent<T>) -> Unit
    ) {
        if (sideEffect is ListContentLoaderReducer.ListContentLoaderViewEffect.GetData) {
            fetchData(
                pageIndex = sideEffect.pageIndex,
                pageSize = sideEffect.pageSize,
                onLoadSuccess = { data ->
                    onResult(
                        ListContentLoaderReducer.ListContentLoaderViewEvent.GetDataSuccess(items = data, isLastPage = data.size < sideEffect.pageSize)
                    )
                },
                onLoadFail = { errorMessage ->
                    onResult(
                        ListContentLoaderReducer.ListContentLoaderViewEvent.GetDataFail(errorMessage)
                    )
                }
            )
        }
    }

    abstract fun fetchData(
        pageIndex: Int,
        pageSize: Int,
        onLoadSuccess: (data: MutableList<T>) -> Unit,
        onLoadFail: (String?) -> Unit
    )
}
    