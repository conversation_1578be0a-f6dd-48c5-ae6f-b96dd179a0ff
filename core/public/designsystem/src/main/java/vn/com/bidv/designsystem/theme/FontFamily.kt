package vn.com.bidv.designsystem.theme

import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import vn.com.bidv.designsystem.R

val Roboto = FontFamily(
    Font(R.font.roboto_regular, FontWeight.Normal),
    <PERSON>ont(R.font.roboto_medium, FontWeight.Medium),
    <PERSON>ont(R.font.roboto_bold, FontWeight.Bold),
    <PERSON><PERSON>(R.font.roboto_light, FontWeight.Light),
    <PERSON><PERSON>(R.font.roboto_black, FontWeight.Black),
    <PERSON>ont(R.font.roboto_thin, FontWeight.Thin),
)