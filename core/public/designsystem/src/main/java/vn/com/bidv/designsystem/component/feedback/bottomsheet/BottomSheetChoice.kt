@file:JvmName("BottomSheetChoiceStateKt")

package vn.com.bidv.designsystem.component.feedback.bottomsheet

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.launch
import vn.com.bidv.designsystem.component.IBankLoaderIndicators
import vn.com.bidv.designsystem.theme.IBankSpacer
import vn.com.bidv.localization.getErrorMessage

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun <T> BottomSheetChoice(
    headerTitle: String? = null,
    bottomSheetState: BottomSheetChoiceState<T>,
    contentItem: @Composable (T) -> Unit,
    onDismiss: () -> Unit,
    onItemSelected: (T) -> Unit,
) {
    val scope = rememberCoroutineScope()
    val sheetState = rememberModalBottomSheetState()
    ModalBottomSheet(
        containerColor = Color.White,
        scrimColor = Color.Black.copy(alpha = 0.2f),
        onDismissRequest = {
            onDismiss()
        },
        sheetState = sheetState,
    ) {
        when (bottomSheetState) {
            is BottomSheetChoiceState.Loading -> {
                LoadingView()
            }

            is BottomSheetChoiceState.Error -> {
                ErrorView(bottomSheetState.message.getErrorMessage(LocalContext.current) )
            }

            is BottomSheetChoiceState.Success<T> -> {
                if (bottomSheetState.data.isNullOrEmpty()) {
                    EmptyContent()
                } else {
                    ViewListOption(
                        headerTitle = headerTitle,
                        listItem = bottomSheetState.data,
                        contentItem = contentItem,
                        onItemSelected = {
                            onItemSelected(it)
                            scope
                                .launch { sheetState.hide() }
                                .invokeOnCompletion {
                                    if (!sheetState.isVisible) {
                                        onDismiss()
                                    }
                                }
                        }
                    )
                }
            }
        }


    }
}


@Composable
private fun BaseBoxContent(
    modifier: Modifier = Modifier,
    contentAlignment: Alignment = Alignment.Center,
    content: @Composable () -> Unit
) {
    val contentHeight = LocalConfiguration.current.screenHeightDp.dp/3
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(contentHeight),
        contentAlignment = contentAlignment
    )  {
        content()
    }
}

@Composable
private fun LoadingView() {
    BaseBoxContent {
        IBankLoaderIndicators(
            backgroundColor = Color.Transparent,
            isParentFullSize = false
        )
    }
}


@Composable
private fun EmptyContent() {
    BaseBoxContent {
        Text(text = "No data")
    }
}

@Composable
private fun ErrorView(message: String) {
    BaseBoxContent {
        Text(text = message)
    }
}

@Composable
private fun <T> ViewListOption(
    headerTitle: String?,
    listItem: List<T>,
    contentItem: @Composable (T) -> Unit,
    onItemSelected: (T) -> Unit,
) {
    BaseBoxContent(
        contentAlignment = Alignment.TopStart
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(IBankSpacer.bottomSheetContentPadding)
        ) {
            Text(text = headerTitle ?: "")
            Spacer(modifier = Modifier.height(16.dp))
            LazyColumn {
                items(
                    items = listItem,
                ) { model ->
                    Box(modifier = Modifier
                        .fillMaxWidth()
                        .clickable {
                            onItemSelected(model)
                        }) {
                        contentItem(model)
                    }
                }
            }
        }
    }
}