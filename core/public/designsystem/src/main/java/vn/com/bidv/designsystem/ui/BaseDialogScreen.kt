package vn.com.bidv.designsystem.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import vn.com.bidv.common.patterns.mvi.BaseMviViewModel
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.common.ui.BaseMVIScreen
/**
 * A composable function that wraps around the BaseScreen to provide a base structure
 * for handling common MVI (Model-View-Intent) patterns in a Composable UI.
 *
 * @param UiState represents the View state.
 * @param UiEvent represents UI events that trigger state changes.
 * @param SideEffect represents side effects, such as navigation or showing a toast.
 * @param viewModel holds the current UI state, events, and side effects.
 * @param renderContent a composable function that renders the main content based on the current UI state.
 * @param handleSideEffect handles side effects emitted by the ViewModel, typically for one-time actions like navigation or showing messages.
 */
@Composable
fun <UiState : ViewState, UiEvent : ViewEvent, SideEffect : vn.com.bidv.common.patterns.mvi.SideEffect> BaseDialogScreen(
    viewModel: BaseMviViewModel<UiState, UiEvent, SideEffect>,
    handleSideEffect: ((SideEffect) -> Unit)? = null,
    renderContent: @Composable (UiState, (UiEvent) -> Unit) -> Unit,
) {
    BaseMVIScreen(
        viewModel = viewModel,
        renderContent = { uiState, onEvent ->
            Scaffold(
                contentWindowInsets = WindowInsets(0.dp, 0.dp, 0.dp, 0.dp),
                containerColor = Color.Transparent
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(it)
                        .padding(WindowInsets.navigationBars.asPaddingValues())
                ) {
                    renderContent(uiState, onEvent)
                }
            }
        },
        handleSideEffect = handleSideEffect
    )
}