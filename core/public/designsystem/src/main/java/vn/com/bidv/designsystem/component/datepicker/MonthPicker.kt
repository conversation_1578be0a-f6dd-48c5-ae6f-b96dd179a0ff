package vn.com.bidv.designsystem.component.datepicker

import androidx.annotation.IntRange
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.component.datepicker.model.DatePickerConfig
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import java.util.Calendar
import java.util.Locale
import vn.com.bidv.localization.R as RLocalization

@Composable
internal fun MonthPicker(
    modifier: Modifier,
    year: Int,
    @IntRange(from = -1L, to = 11L) monthSelected: Int,
    isContainedSelectedMonth: Boolean,
    datePickerConfig: DatePickerConfig,
    onMonthSelected: (Int) -> Unit,
) {
    val months = stringArrayResource(RLocalization.array.months)
    val currentMonth = Calendar.getInstance(Locale.getDefault())
    LazyVerticalGrid(
        columns = GridCells.Fixed(3),
        contentPadding = PaddingValues(8.dp),
        modifier = modifier.fillMaxWidth()
    ) {
        items(months.size) { index ->
            MonthItem(monthStr = months[index],
                isSelected = isContainedSelectedMonth && index == monthSelected,
                isCurrentMonth = isCurrentMonth(index, year, currentMonth),
                isPaddingMonth = isPaddingMonth(index, year, datePickerConfig),
                onClick = { onMonthSelected(index) } // Pass month index as 1-12
            )
        }
    }
}

private fun isCurrentMonth(monthIndex: Int, year: Int, currentMonth: Calendar): Boolean {
    return currentMonth.get(Calendar.MONTH) == monthIndex && currentMonth.get(Calendar.YEAR) == year
}

private fun isPaddingMonth(monthIndex: Int, year: Int, config: DatePickerConfig): Boolean {
    val calendar = Calendar.getInstance()

    val minDateCheck = config.minDate?.let { minDate ->
        calendar.time = minDate
        val minYear = calendar.get(Calendar.YEAR)
        val minMonth = calendar.get(Calendar.MONTH)
        year < minYear || (year == minYear && monthIndex < minMonth)
    } ?: false

    val maxDateCheck = config.maxDate?.let { maxDate ->
        calendar.time = maxDate
        val maxYear = calendar.get(Calendar.YEAR)
        val maxMonth = calendar.get(Calendar.MONTH)
        year > maxYear || (year == maxYear && monthIndex > maxMonth)
    } ?: false

    return minDateCheck || maxDateCheck
}

@Composable
private fun MonthItem(
    monthStr: String,
    isSelected: Boolean,
    isCurrentMonth: Boolean,
    isPaddingMonth: Boolean,
    onClick: () -> Unit,
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current
    val textAlpha =
        if (isPaddingMonth) 0.3f else 1f
    Box(contentAlignment = Alignment.Center,
        modifier = Modifier
            .padding(horizontal = 16.dp, vertical = 4.dp)
            .aspectRatio(2 / 1f)  // Ensures a square shape
            .fillMaxWidth(1f / 7)
            .border(
                width = 1.dp, color = when {
                    isCurrentMonth -> colorScheme.borderBrandQuaternary
                    else -> Color.Transparent
                }, shape = RoundedCornerShape(IBCornerRadius.cornerRadiusM)
            )
            .clickable(enabled = !isPaddingMonth) {
                onClick()
            }
            .alpha(textAlpha)
    ) {
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier
                .aspectRatio(2 / 1f) // Ensures a square shape
                .fillMaxWidth(1.5f / 7)
                .background(
                    color = Color.Transparent
                )
        ) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight()
                    .background(
                        color = when {
                            isSelected -> colorScheme.bgBrand_01Primary
                            else -> Color.Transparent
                        }, shape = RoundedCornerShape(IBCornerRadius.cornerRadiusM)
                    )
            ) {
                Text(
                    text = monthStr,
                    color = if (isSelected) colorScheme.contentOn_specialPrimary else colorScheme.contentMainPrimary,
                    style = if (isSelected) typography.titleTitle_s else typography.bodyBody_m,
                )
            }
        }
    }
}

@Preview
@Composable
fun PreviewMonthPicker() {
    Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
        MonthPicker(
            modifier = Modifier.padding(innerPadding),
            monthSelected = 6,
            year = 2024,
            isContainedSelectedMonth = false,
            datePickerConfig = DatePickerConfig.build {
                minDate = Calendar.getInstance(Locale.getDefault()).apply { set(2024, 5, 6) }.time
                maxDate = Calendar.getInstance(Locale.getDefault()).time
            }
        ) {

        }
    }
}