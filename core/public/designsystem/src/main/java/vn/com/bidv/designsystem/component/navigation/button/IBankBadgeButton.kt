package vn.com.bidv.designsystem.component.navigation.button

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.NoRippleEffectView
import vn.com.bidv.designsystem.component.datadisplay.badge.IBankBadgeLabel
import vn.com.bidv.designsystem.component.datadisplay.badge.IBankNotiBadge
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelColor
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelSize
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelType
import vn.com.bidv.designsystem.component.datadisplay.badge.NotiColor
import vn.com.bidv.designsystem.component.datadisplay.badge.NotiSize
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.utils.debounceClick

/**
 * Draft version of Badge Buttons with all type and size.
 * @param onClick called when this button is clicked.
 * @param modifier the custom [Modifier] to be applied to this button.
 * @param buttonType reuse the [NormalButtonType] to define the button style, only use one icon
 * between leading or trailing icon and leave text empty
 * @param badgeColor reuse the [LabelColor] to define the badge color
 * @param badgeNumber the number of badge, if it is negative, it will show a dot badge
 */

@Composable
fun IBankBadgeButton(
    modifier: Modifier,
    buttonType: NormalButtonType,
    badgeNumber: Int,
    badgeColor: LabelColor,
    icon: ImageVector? = null,
    onClick: () -> Unit
) {

    NoRippleEffectView {
        Box {
            IBankNormalButton(
                modifier = modifier,
                type = buttonType,
                size = NormalButtonSize.M(LocalTypography.current),
                text = null,
                leadingIcon = icon,
                onClick = debounceClick(onClick = onClick)
                )
            if (badgeNumber > 0) {
                IBankBadgeLabel(
                    modifier = Modifier.offset(x = 28.dp, y = (-8).dp),
                    title = badgeNumber.toString(),
                    badgeSize = LabelSize.SM,
                    badgeType = LabelType.PILL,
                    badgeColor = badgeColor,
                )
            } else if (badgeNumber == 0) {
                Box(modifier = Modifier)
            } else {
                IBankNotiBadge(
                    modifier = Modifier.offset(x = 34.dp),
                    title = "",
                    badgeSize = NotiSize.DOT,
                    badgeColor = NotiColor.RED
                )
            }
        }
    }
}

@Composable
@Preview
fun BadgeButtonExample() {

    Column(
        modifier = Modifier
            .padding(20.dp),
        verticalArrangement = Arrangement.spacedBy(20.dp)
    ) {
        IBankBadgeButton(
            modifier = Modifier,
            buttonType = NormalButtonType.SECONDARYCOLOR(
                LocalColorScheme.current
            ),
            icon = ImageVector.vectorResource(id = R.drawable.information_circle),
            badgeNumber = 2,
            badgeColor = LabelColor.BRAND
        ) { }
        IBankBadgeButton(
            modifier = Modifier,
            buttonType = NormalButtonType.SECONDARYCOLOR(
                LocalColorScheme.current
            ),
            icon = ImageVector.vectorResource(id = R.drawable.information_circle),
            badgeNumber = 2,
            badgeColor = LabelColor.INFO
        ) { }
        IBankBadgeButton(
            modifier = Modifier,
            buttonType = NormalButtonType.SECONDARYCOLOR(
                LocalColorScheme.current
            ),
            icon = ImageVector.vectorResource(id = R.drawable.information_circle),
            badgeNumber = 2,
            badgeColor = LabelColor.WARNING
        ) { }
        IBankBadgeButton(
            modifier = Modifier,
            buttonType = NormalButtonType.SECONDARYGRAY(
                LocalColorScheme.current
            ),
            icon = ImageVector.vectorResource(id = R.drawable.information_circle),
            badgeNumber = -1,
            badgeColor = LabelColor.BRAND
        ) { }
        IBankBadgeButton(
            modifier = Modifier,
            buttonType = NormalButtonType.SECONDARYGRAY(
                LocalColorScheme.current
            ),
            icon = ImageVector.vectorResource(id = R.drawable.information_circle),
            badgeNumber = 0,
            badgeColor = LabelColor.BRAND
        ) { }
    }

}

