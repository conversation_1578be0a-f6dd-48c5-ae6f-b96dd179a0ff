package vn.com.bidv.designsystem.component.datadisplay.avatar

import androidx.annotation.DrawableRes
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import vn.com.bidv.common.extenstion.isNotNullOrEmpty
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBShadow
import vn.com.bidv.designsystem.theme.IBShadow.dropShadow
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

/**
 * Draft version of Avatar/ Avatars Group with all type and size.
 * @param avatarInfo has 2 attributes :
 * name : only use when show avatar text, then please leave image empty.
 * imageUrl : only use when show avatar image, then please leave text empty.
 * if leave both attributes empty, it will show anonymous avatar image.
 */

@Composable
fun IBankAvatar(
    modifier: Modifier = Modifier,
    avatarInfo: AvatarInfo = AvatarInfo("", ""),
    @DrawableRes placeholderIcon: Int? = null
) {

    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current
    val borderColor = if (avatarInfo.name.isNotNullOrEmpty()) {
        colorScheme.borderMainPrimary
    } else {
        Color.Transparent
    }

    Surface(
        modifier = modifier
            .wrapContentWidth()
            .dropShadow(
                config = IBShadow.shadowsShadows0.copy(shape = RoundedCornerShape(IBCornerRadius.cornerRadiusRound))
            )
            .dropShadow(
                config = IBShadow.shadowsShadows1.copy(shape = RoundedCornerShape(IBCornerRadius.cornerRadiusRound))
            ),
        shape = CircleShape,
        border = BorderStroke(0.63.dp, borderColor),
    ) {
        if (avatarInfo.imageUrl.isNotNullOrEmpty()) {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(avatarInfo.imageUrl)
                    .crossfade(true)
                    .build(),
                contentDescription = null,
                placeholder = painterResource(id = R.drawable.anonymous_avatar),
                error = painterResource(id = R.drawable.anonymous_avatar),
                contentScale = ContentScale.Crop,
                modifier = modifier
            )
        } else {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(color = colorScheme.bgMainTertiary),
                contentAlignment = Alignment.Center
            ) {
                placeholderIcon?.let {
                    Icon(
                        modifier = Modifier.fillMaxSize(),
                        imageVector = ImageVector.vectorResource(id = it),
                        contentDescription = null,
                        tint = Color.Unspecified
                    )
                } ?: Text(
                    modifier = Modifier.fillMaxWidth(1f),
                    text = getNameIndicator(avatarInfo),
                    style = typography.labelLabel_xl,
                    color = colorScheme.contentMainTertiary,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

class AvatarInfo(
    val name: String? = null,
    val imageUrl: String? = null
)

private fun getNameIndicator(info: AvatarInfo): String {
    if (info.name.isNotNullOrEmpty()) {
        return info.name?.first().toString()
    }
    return ""
}

@Preview
@Composable
fun AvatarSample() {

    Row(
        modifier = Modifier
            .padding(5.dp),
        horizontalArrangement = Arrangement.spacedBy(10.dp)
    ) {
        IBankAvatar()
        IBankAvatar(
            Modifier,
            AvatarInfo(
                "Anton Bola",
                ""
            )
        )
        IBankAvatar(
            Modifier,
            AvatarInfo(
                "",
                "https://avatar.iran.liara.run/public/40"
            )
        )
        IBankAvatar(
            Modifier,
            AvatarInfo(
                "Cirdan Goo",
                "https://avatar.iran.liara.run/public/40"
            )
        )
        IBankAvatar(
            Modifier,
            AvatarInfo(
                "",
                "https://avatar.iran.liara.run/public/41"
            )
        )
        IBankAvatar(
            Modifier,
            AvatarInfo(
                "",
                "https://avatar.iran.liara.run/public/42"
            )
        )
    }

}
