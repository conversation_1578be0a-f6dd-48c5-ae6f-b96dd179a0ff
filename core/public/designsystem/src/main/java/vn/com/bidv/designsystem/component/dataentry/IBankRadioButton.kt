package vn.com.bidv.designsystem.component.dataentry

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme

@Composable
fun IBankRadioButtonWithText(
    modifier: Modifier = Modifier,
    checked: Boolean = false,
    enable: Boolean = true,
    textAlignLeft: Boolean = true,
    titleStyle: TextStyle? = null,
    title: String = "",
    content: String = "",
    onCheckedChange: ((Boolean) -> Unit)?,
) {
    Row(
        modifier = modifier.testTagIBank("IBankRadioButtonWithText_$title"),
    ) {
        if (title.isNotEmpty() || content.isNotEmpty()) {
            if (textAlignLeft) {
                Box(
                    Modifier.padding(end = IBSpacing.spacingXs)
                ) {
                    IBankTextAndTextSupport(
                        iBankTextAndTextSupportConfig = IBankTextAndTextSupportConfig(
                            title = title,
                            content = content,
                            textAlignLeft = true
                        )
                    )
                }
            }
        }

        IBankRadioButton(
            modifier = Modifier.padding(top = IBSpacing.spacing2xs),
            checked = checked,
            enable = enable,
        ) { // Default
            if (onCheckedChange != null) {
                onCheckedChange(it)
            }
            // On Switch Change
        }

        if (title.isNotEmpty() || content.isNotEmpty()) {
            if (!textAlignLeft) {
                Box(
                    Modifier.padding(start = IBSpacing.spacingXs)
                ) {
                    IBankTextAndTextSupport(
                        iBankTextAndTextSupportConfig = IBankTextAndTextSupportConfig(
                            title = title,
                            titleStyle = titleStyle ,
                            content = content,
                            textAlignLeft = false
                        )
                    )
                }
            }
        }
    }
}

@Composable
fun IBankRadioButton(
    modifier: Modifier = Modifier,
    checked: Boolean = false,
    enable: Boolean = true,
    onCheckedChange: ((Boolean) -> Unit)?,
) {
    val colorScheme = LocalColorScheme.current
    var colorBorder by remember { mutableStateOf(colorScheme.borderSolidSecondary) }
    var colorBackground by remember { mutableStateOf(Color.Transparent) }
    var colorDot by remember { mutableStateOf(colorScheme.contentOn_specialPrimary) }
    val interactionSource = remember { MutableInteractionSource() }

    if (enable) {
        if (checked) {
            colorBorder = colorScheme.bgBrand_01Primary
            colorBackground = colorScheme.bgBrand_01Primary
            colorDot = colorScheme.contentOn_specialPrimary
        } else {
            colorBorder = colorScheme.borderSolidSecondary
            colorBackground = Color.Transparent
            colorDot = Color.Transparent
        }
    } else {
        if (checked) {
            colorBorder = colorScheme.borderDisablePrimary
            colorBackground = colorScheme.bgDisablePrimary
            colorDot = colorScheme.borderMainPrimary
        } else {
            colorBorder = colorScheme.borderDisablePrimary
            colorBackground = colorScheme.bgDisablePrimary
            colorDot = colorScheme.bgDisablePrimary
        }
    }

    Box(
        modifier = modifier
            .clickable(
                interactionSource = interactionSource,
                indication = ripple(
                    radius = IBSpacing.spacingL,
                    bounded = false,
                    color = colorScheme.borderSolidSecondary_press
                )
            ) {
                if (onCheckedChange != null) {
                    onCheckedChange(!checked)
                }
            }
            .size(16.dp)
            .clip(RoundedCornerShape(IBCornerRadius.cornerRadiusRound))
            .background(colorBackground)
            .border(
                width = IBBorderDivider.borderDividerS,
                color = colorBorder,
                shape = RoundedCornerShape(IBCornerRadius.cornerRadiusRound)
            )
    ) {
        Box(
            modifier = Modifier
                .size(6.dp)
                .clip(RoundedCornerShape(IBCornerRadius.cornerRadiusRound))
                .background(colorDot)
                .align(Alignment.Center)
        )
    }
}

@Preview
@Composable
fun PreviewRadio() {
    Column(Modifier.background(LocalColorScheme.current.bgMainTertiary)) {
        IBankRadioButtonWithText(
            checked = true,
            enable = true,
            textAlignLeft = false,
            title = "Radio Button Text Right",
            content = "Save my login details for next time"
        ) {}
        Spacer(Modifier.height(12.dp))
        IBankRadioButtonWithText(
            checked = true,
            enable = true,
            textAlignLeft = true,
            title = "Radio Button Text Right",
            content = "Save my login details for next time"
        ) {}
        Spacer(Modifier.height(12.dp))
        IBankRadioButtonWithText(checked = true, enable = true) {}
        Spacer(Modifier.height(12.dp))
        IBankRadioButtonWithText(checked = false, enable = true) {}
        Spacer(Modifier.height(12.dp))
        IBankRadioButtonWithText(checked = true, enable = false) {}
        Spacer(Modifier.height(12.dp))
        IBankRadioButtonWithText(checked = false, enable = false) {}
    }

}