package vn.com.bidv.designsystem.component.feedback.bottomsheet

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.component.EmptyStateType
import vn.com.bidv.designsystem.component.IBankEmptyState
import vn.com.bidv.designsystem.component.IBankLoaderIndicators
import vn.com.bidv.designsystem.component.dataentry.AllowSpecificCharactersFilter
import vn.com.bidv.designsystem.component.dataentry.IBankFilterTextField
import vn.com.bidv.designsystem.component.dataentry.IBankInputSearchBase
import vn.com.bidv.designsystem.component.dataentry.RemoveVietnameseAccentFilter
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.localization.R as RLocalized

enum class SearchDialogState {
    IDE, LOADING, CONTENT, ERROR
}

data class SearchItem<T>(
    val data: T, var isSelected: Boolean = false
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun <T> IBankSearchDialog(
    title: String,
    state: SearchDialogState,
    listData: List<T>?,
    itemSelected: T?,
    searchFilter: (T, String) -> Boolean,
    showSearchBox: Boolean = false,
    listSearchFilterText: List<IBankFilterTextField> = listOf(
        RemoveVietnameseAccentFilter(),
        AllowSpecificCharactersFilter("., ")
    ),
    searchMaxLength: Int = 70,
    searchPlaceholder: String = stringResource(id = RLocalized.string.tim_kiem),
    autoSelectOneItemExist: Boolean = false,
    compareKey: (T) -> Any?,
    onRequestDismiss: (selected: T?) -> Unit,
    isClickable: (T) -> Boolean = { true },
    applyMinHeight: Boolean = true,
    emptyView: @Composable () -> Unit = {
        IBankEmptyState(
            modifier = Modifier.fillMaxSize(),
            supportingText = stringResource(RLocalized.string.khong_co_du_lieu)
        )
    },
    searchEmptyView: @Composable () -> Unit = {
        IBankEmptyState(
            emptyStateType = EmptyStateType.SearchNoResult,
            modifier = Modifier.fillMaxSize(),
            supportingText = stringResource(RLocalized.string.khong_tim_thay_ket_qua)
        )
    },
    errorView: @Composable () -> Unit = {
        IBankEmptyState(
            modifier = Modifier.fillMaxSize(),
            textButton = stringResource(RLocalized.string.co_loi_xay_ra_vui_long_thu_lai)
        )
    },
    itemContent: @Composable (index: Int, item: SearchItem<T>) -> Unit
) {
    val originData = listData?.map {
        SearchItem(it, itemSelected != null && compareKey(it) == compareKey(itemSelected))
    } ?: emptyList()
    val lazyListState = rememberLazyListState()
    IBankBottomSheet(
        title = title,
        onDismiss = { onRequestDismiss(null) },
        applyMinHeight = applyMinHeight
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
        ) {
            if (state == SearchDialogState.LOADING) {
                LoadingView()
            } else if (state == SearchDialogState.ERROR) {
                errorView()
            } else if (state == SearchDialogState.CONTENT) {
                if (originData.isEmpty()) {
                    emptyView()
                } else {
                    ContentView(
                        lazyListState = lazyListState,
                        listData = originData,
                        searchPlaceholder = searchPlaceholder,
                        showSearchBox = showSearchBox,
                        searchMaxLength = searchMaxLength,
                        autoSelectOneItemExist = autoSelectOneItemExist,
                        searchFilter = searchFilter,
                        onItemSelected = { item ->
                            onRequestDismiss(item)
                        },
                        listSearchFilterText = listSearchFilterText,
                        searchEmptyView = searchEmptyView,
                        itemContent = itemContent,
                        isClickable = isClickable
                    )
                }
            }
        }
    }
}

@Composable
private fun <T> ContentView(
    lazyListState: LazyListState,
    listData: List<SearchItem<T>>,
    searchPlaceholder: String,
    showSearchBox: Boolean,
    listSearchFilterText: List<IBankFilterTextField>,
    searchMaxLength: Int,
    autoSelectOneItemExist: Boolean,
    searchFilter: (T, String) -> Boolean,
    onItemSelected: (T) -> Unit,
    searchEmptyView: @Composable () -> Unit,
    isClickable: (T) -> Boolean = { true },
    itemContent: @Composable (index: Int, item: SearchItem<T>) -> Unit
) {
    var searchQuery by remember { mutableStateOf("") }
    var mDataSet by remember { mutableStateOf(listData) }
    Column {
        if (autoSelectOneItemExist && listData.size == 1) {
            onItemSelected(listData[0].data)
        } else {
            if (showSearchBox) {
                IBankInputSearchBase(
                    maxLength = searchMaxLength,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            horizontal = IBSpacing.spacingM, vertical = IBSpacing.spacingXs
                        ),
                    filters = listSearchFilterText,
                    placeHolderText = searchPlaceholder,
                    textValue = searchQuery,
                    onTextChange = { searchQuery = it },
                    onRequestChange = { txtSearch ->
                        mDataSet = if (txtSearch.isBlank()) {
                            listData
                        } else {
                            listData.filter { item ->
                                searchFilter(item.data, txtSearch)
                            }
                        }
                    },
                    onClickClear = { searchQuery = "" })
            }
            if (mDataSet.isEmpty()) {
                searchEmptyView()
            } else {
                LazyColumn(
                    state = lazyListState,
                    modifier = Modifier
                        .fillMaxWidth()
                        .wrapContentHeight(),
                    verticalArrangement = Arrangement.spacedBy(IBSpacing.spacing3xs),
                ) {
                    items(mDataSet.size) { index ->
                        val searchItem = mDataSet[index]
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .wrapContentHeight()
                                .padding(horizontal = if (isClickable(searchItem.data)) IBSpacing.spacingXs else 0.dp)
                                .clickable(enabled = isClickable(searchItem.data)) {
                                    searchItem.isSelected = true
                                    onItemSelected(searchItem.data)
                                }) {
                            itemContent(index, searchItem)
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun LoadingView() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .heightIn(LocalConfiguration.current.screenHeightDp.dp / 3),
        contentAlignment = Alignment.Center
    ) {
        IBankLoaderIndicators(
            backgroundColor = Color.Transparent, isParentFullSize = false
        )
    }
}

