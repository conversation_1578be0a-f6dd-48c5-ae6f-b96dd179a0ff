package vn.com.bidv.designsystem.component.dataentry

import androidx.annotation.DrawableRes
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.segmentcontrol.taglayout.IBankTagCornerStyle
import vn.com.bidv.designsystem.component.segmentcontrol.taglayout.IBankTagItem
import vn.com.bidv.designsystem.component.segmentcontrol.taglayout.IBankTagSize
import vn.com.bidv.designsystem.component.segmentcontrol.taglayout.TagItemData
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

sealed class IBankInputDropdownTypeData(
) {
    data class Select(val text: String) : IBankInputDropdownTypeData()
    data class Tags(val listItems: List<String>) : IBankInputDropdownTypeData()
}

@Composable
fun IBankInputDropdownBaseV2(
    modifier: Modifier = Modifier,
    labelText: String,
    required: Boolean = false,
    showIconInfo: Boolean = false,
    showClearButton: Boolean = true,
    typeData: IBankInputDropdownTypeData = IBankInputDropdownTypeData.Select(""),
    maxTagCount: Int = 2,
    iconStart: @Composable (() -> Unit)? = null, // Annotation @DrawableRes thêm vào đây
    @DrawableRes iconEnd: Int = R.drawable.arrow_bottom, // Annotation @DrawableRes thêm vào đây
    state: IBFrameState = IBFrameState.DEFAULT(LocalColorScheme.current),
    hintTextStart: String? = "",
    hintTextEnd: String? = "",
    onTagRemoved: ((Int) -> Unit)? = null,
    onClickClear: () -> Unit = {},
    onClickInfo: () -> Unit = {},
    onClickEnd: () -> Unit = {},
) {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current
    val hintTextColor = when (state) {
        is IBFrameState.ERROR -> IBHintTextColor.ERROR(colorScheme).hintTextColor
        else -> IBHintTextColor.DEFAULT(colorScheme).hintTextColor
    }
    Column(modifier = modifier) {
        IBankInputDropdownBody(
            modifier = Modifier,
            labelText = labelText,
            required = required,
            showIconInfo = showIconInfo,
            showClearButton = showClearButton,
            maxTagCount = maxTagCount,
            typeData = typeData,
            iconStart = iconStart,
            iconEnd = iconEnd,
            state = state,
            onTagRemoved = onTagRemoved,
            onClickClear = onClickClear,
            onClickInfo = onClickInfo,
            onClickEnd = onClickEnd
        )
        if (!hintTextStart.isNullOrEmpty() || !hintTextEnd.isNullOrEmpty()) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(
                        top = IBSpacing.spacing2xs, start = IBSpacing.spacing2xs
                    ), horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = hintTextStart ?: "",
                    style = typography.bodyBody_m,
                    color = hintTextColor,
                )
                Text(
                    modifier = Modifier.padding(end = IBSpacing.spacing2xs),
                    text = hintTextEnd ?: "",
                    style = typography.bodyBody_m,
                    color = hintTextColor,
                )
            }
        }
    }
}

@Composable
private fun IBankInputDropdownBody(
    modifier: Modifier = Modifier,
    labelText: String,
    required: Boolean = false,
    showIconInfo: Boolean = false,
    showClearButton: Boolean,
    typeData: IBankInputDropdownTypeData,
    maxTagCount: Int = 2,
    iconStart: @Composable() (() -> Unit)? = null,
    iconEnd: Int,
    state: IBFrameState = IBFrameState.DEFAULT(LocalColorScheme.current),
    onTagRemoved: ((Int) -> Unit)?,
    onClickClear: () -> Unit = {},
    onClickInfo: () -> Unit = {},
    onClickEnd: () -> Unit = {},
) = IBankInputFrameBase(
    modifier = Modifier.testTagIBank("IBankInputDropdownV2_$labelText")
        .heightIn(min = 56.dp)
        .clip(RoundedCornerShape(IBCornerRadius.cornerRadiusL))
        .clickable(
            interactionSource = remember { MutableInteractionSource() },
            indication = ripple(
                bounded = true,
                color = LocalColorScheme.current.bgSolidPrimary_press,
            ),
            onClick = onClickEnd,
            enabled = state !is IBFrameState.DISABLE,
        )
        .then(modifier),
    state = state
) {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current
    val style = when (state) {
        is IBFrameState.DEFAULT -> IBDropdownStyle.DEFAULT(typography, colorScheme)
        is IBFrameExtendState.FILLED -> IBDropdownStyle.FILLED(typography, colorScheme)
        is IBFrameState.FOCUS -> IBDropdownStyle.FOCUS(typography, colorScheme)
        is IBFrameState.DISABLE -> IBDropdownStyle.DISABLE(typography, colorScheme)
        is IBFrameState.ERROR -> IBDropdownStyle.ERROR(typography, colorScheme)
        is IBFrameExtendState.FIXED -> IBDropdownStyle.FIXED(typography, colorScheme)
        is IBFrameExtendState.PRESSED -> IBDropdownStyle.DEFAULT(typography, colorScheme)
    }
    val labelStyle = style.labelStyle
    val labelColor = style.labelColor
    val iconEndTint = style.iconEndTint
    val showData = when (typeData) {
        is IBankInputDropdownTypeData.Select -> typeData.text.isNotBlank()
        is IBankInputDropdownTypeData.Tags -> typeData.listItems.isNotEmpty()
    }
    val selectedTextColor = style.selectedTextColor
    val itemRemovable = style.itemRemovable
    Row(
        modifier = Modifier
            .align(Alignment.Center)
            .fillMaxWidth()
            .background(state.colorBackground)
            .padding(
                start = IBSpacing.spacingM,
                end = IBSpacing.spacingXs,
                top = IBSpacing.spacingXs,
                bottom = IBSpacing.spacingXs
            ),
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (iconStart != null) {
            Box(modifier = Modifier
                .size(32.dp)
                .clickable { onClickInfo() }
                .clip(CircleShape)
                .aspectRatio(1f)
                .border(
                    width = IBBorderDivider.borderDividerS,
                    color = colorScheme.borderMainPrimary,
                    shape = RoundedCornerShape(IBCornerRadius.cornerRadiusRound)
                ), contentAlignment = Alignment.Center) {
                iconStart()
            }
        }
        Box(modifier = Modifier.weight(1f), contentAlignment = Alignment.CenterStart) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        start = if (iconStart != null) IBSpacing.spacingXs else IBSpacing.spacingNone,
                    )
                    .alpha(if (showData) 1f else 0f),
            ) {
                PlaceholderView(
                    modifier = Modifier.fillMaxWidth(),
                    required = required,
                    showIconInfo = showIconInfo,
                    placeHolderText = labelText,
                    placeHolderStyle = LocalTypography.current.captionCaption_m,
                    placeHolderColor = labelColor,
                )

                Spacer(modifier = Modifier.height(IBSpacing.spacing3xs))

                if (typeData is IBankInputDropdownTypeData.Select) {
                    Text(
                        text = typeData.text,
                        style = typography.bodyBody_l,
                        color = selectedTextColor,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                } else if (typeData is IBankInputDropdownTypeData.Tags) {
                    TagsSection(
                        listItems = typeData.listItems,
                        itemRemovable = itemRemovable,
                        maxTagCount = maxTagCount,
                        onTagRemoved = onTagRemoved
                    )
                }

            }

            PlaceholderView(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        start = if (iconStart != null) IBSpacing.spacingXs else IBSpacing.spacingNone,
                    )
                    .alpha(if (showData) 0f else 1f),
                required = required,
                showIconInfo = showIconInfo,
                placeHolderText = labelText,
                placeHolderStyle = LocalTypography.current.bodyBody_l,
                placeHolderColor = LocalColorScheme.current.contentPlaceholder,
            )
        }
        if (showData && showClearButton) {
            Spacer(modifier = Modifier.width(IBSpacing.spacing2xs))
            IconButton(modifier = Modifier.size(IBSpacing.spacingL), onClick = onClickClear) {
                Icon(
                    imageVector = ImageVector.vectorResource(id = R.drawable.circle_close_outline),
                    contentDescription = "",
                    tint = colorScheme.contentMainTertiary,
                    modifier = Modifier
                        .clickable { onClickClear() }
                )
            }
        }

        Box(
            modifier = Modifier
                .clip(CircleShape)
                .size(32.dp)
                .clickable(
                    interactionSource = remember { MutableInteractionSource() },
                    indication = ripple(
                        bounded = true,
                        color = LocalColorScheme.current.bgSolidPrimary_press,
                    ),
                    onClick = onClickEnd,
                    enabled = state !is IBFrameState.DISABLE,
                ),
        ) {
            Icon(
                modifier = Modifier
                    .size(16.dp)
                    .align(Alignment.Center),
                tint = iconEndTint,
                imageVector = ImageVector.vectorResource(id = iconEnd),
                contentDescription = null,
            )
        }
    }
}

@Composable
private fun PlaceholderView(
    modifier: Modifier,
    required: Boolean,
    showIconInfo: Boolean,
    placeHolderText: String,
    placeHolderStyle: TextStyle,
    placeHolderColor: Color,
) {
    Row(
        modifier = modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            placeHolderText,
            style = placeHolderStyle,
            color = placeHolderColor,
        )
        if (required) {
            DotRequire()
        }
        if (showIconInfo) {
            Icon(
                imageVector = ImageVector.vectorResource(id = R.drawable.information_circle),
                modifier = Modifier
                    .size(16.dp)
                    .padding(IBSpacing.spacing3xs)
                    .align(Alignment.CenterVertically),
                tint = LocalColorScheme.current.contentMainPrimary,
                contentDescription = null,
            )
        }
    }
}

@Composable
private fun DotRequire() {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current
    Text(
        "*",
        style = typography.captionCaption_m,
        color = colorScheme.contentNegativePrimary,
        modifier = Modifier.padding(start = IBSpacing.spacing3xs)
    )
}

@Composable
private fun TagsSection(
    listItems: List<String> = emptyList(),
    itemRemovable: Boolean,
    maxTagCount: Int,
    onTagRemoved: ((Int) -> Unit)? = null
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current
    val showTags = if (listItems.size > maxTagCount) listItems.subList(0, maxTagCount) else listItems
    val tagItemList = showTags.mapIndexed { index, label ->
        TagItemData(
            data = label, label = label, hasCloseButton = itemRemovable
        )
    }
    val tagSize = IBankTagSize.SM(LocalColorScheme.current, LocalTypography.current)
    Row(
        modifier = Modifier
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
    ) {

        Row(modifier = Modifier.weight(1f, false),
            horizontalArrangement = Arrangement.spacedBy(IBSpacing.spacingXs)){
            tagItemList.forEach {
                IBankTagItem(
                    modifier = tagSize.tagModifier.weight(0.5f, false),
                    textModifier = Modifier.weight(1f, false),
                    tag = it,
                    cornerStyle = IBankTagCornerStyle.Rounded,
                    size = tagSize,
                    selectable = false,
                    onRemove = {
                        onTagRemoved?.invoke(tagItemList.indexOf(it))
                    }
                )
            }
        }

        if (listItems.size > maxTagCount) {
            Spacer(Modifier.width(IBSpacing.spacingXs))
            Box(
                modifier = Modifier
                    .border(
                        width = 1.dp,
                        color = colorScheme.borderMainPrimary,
                        shape = RoundedCornerShape(size = IBCornerRadius.cornerRadiusM)
                    )
                    .height(20.dp)
                    .background(
                        color = colorScheme.bgMainTertiary,
                        shape = RoundedCornerShape(size = IBCornerRadius.cornerRadiusM)
                    )
                    .padding(
                        start = IBSpacing.spacing2xs,
                        top = IBSpacing.spacing3xs,
                        end = IBSpacing.spacing2xs,
                        bottom = IBSpacing.spacing3xs
                    ),
            ){
                Text(
                    modifier = Modifier.align(Alignment.Center),
                    text = "+${listItems.size - maxTagCount}",
                    color = colorScheme.contentMainSecondary,
                    style = typography.bodyBody_s,
                    textAlign = TextAlign.Center,
                )
            }
        }
    }
}

@Preview
@Composable
fun PreviewInputDropdownBaseV2() {
    val colorScheme = LocalColorScheme.current
    Spacer(Modifier.height(16.dp))
    Box(Modifier.padding(all = 12.dp)) {
        Column {
            IBankInputDropdownBaseV2(
                labelText = "Ngân hàng",
                required = false,
                typeData = IBankInputDropdownTypeData.Select(text = "Ngân hàng TMCP Đầu tư và Phát triển Việt Nam (BIDV)"),
                showIconInfo = false,
                iconStart = {
                    Icon(
                        modifier = Modifier.size(32.dp),
                        imageVector = ImageVector.vectorResource(id = R.drawable.icon_popup_info_64),
                        contentDescription = null,
                        tint = Color.Unspecified,
                    )
                },
                iconEnd = R.drawable.arrow_bottom,
                hintTextStart = "This is a hint text to help user",
                hintTextEnd = "200VND",
                state = IBFrameState.DEFAULT(colorScheme),
            )
            Spacer(Modifier.height(24.dp))
            IBankInputDropdownBaseV2(
                labelText = "Label",
                required = true,
                typeData = IBankInputDropdownTypeData.Select(text = "Ngân hàng TMCP Đầu tư và Phát triển Việt Nam (BIDV)"),
                showIconInfo = false,
                iconStart = null,
                iconEnd = R.drawable.arrow_bottom,
                hintTextStart = "This is a hint text to help user",
                hintTextEnd = "",
                state = IBFrameExtendState.FILLED(colorScheme),
            )
            Spacer(Modifier.height(24.dp))
            IBankInputDropdownBaseV2(
                labelText = "Loại dịch vụ",
                required = true,
                typeData = IBankInputDropdownTypeData.Tags(
                    listOf(
                        "Một số dịch vụ khác tên rất là dàiMột số dịch vụ khác tên rất là dài",
                        "Một số dịch vụ khác tên rất là dài",
                        "Một số dịch vụ khác"
                    )
                ),
                showIconInfo = true,
                iconStart = {
                    Icon(
                        modifier = Modifier.size(32.dp),
                        imageVector = ImageVector.vectorResource(id = R.drawable.icon_popup_info_64),
                        contentDescription = null,
                        tint = Color.Unspecified,
                    )
                },
                iconEnd = R.drawable.arrow_bottom,
                hintTextStart = "This is a hint text to help user",
                hintTextEnd = "200VND",
                state = IBFrameState.FOCUS(colorScheme)
            )
            Spacer(Modifier.height(24.dp))
            IBankInputDropdownBaseV2(
                labelText = "Tên tài khoản",
                required = true,
                typeData = IBankInputDropdownTypeData.Tags(listOf("41252", "412451")),
                showIconInfo = false,
                iconStart = {
                    Icon(
                        modifier = Modifier.size(32.dp),
                        imageVector = ImageVector.vectorResource(id = R.drawable.icon_popup_info_64),
                        contentDescription = null,
                        tint = Color.Unspecified,
                    )
                },
                iconEnd = R.drawable.arrow_bottom,
                hintTextStart = "This is a hint text to help user",
                hintTextEnd = "200VND",
                state = IBFrameState.ERROR(colorScheme)
            )

            Spacer(Modifier.height(24.dp))
            IBankInputDropdownBaseV2(
                labelText = "Tên tài khoản",
                required = true,
                typeData = IBankInputDropdownTypeData.Select(""),
                showIconInfo = false,
                iconStart = {
                    Icon(
                        modifier = Modifier.size(32.dp),
                        imageVector = ImageVector.vectorResource(id = R.drawable.icon_popup_info_64),
                        contentDescription = null,
                        tint = Color.Unspecified,
                    )
                },
                iconEnd = R.drawable.arrow_bottom,
                hintTextStart = "This is a hint text to help user",
                hintTextEnd = "200VND",
                state = IBFrameState.ERROR(colorScheme)
            )

            Spacer(Modifier.height(24.dp))
            IBankInputDropdownBaseV2(
                labelText = "Label",
                required = true,
                typeData = IBankInputDropdownTypeData.Tags(listOf("Tiền điện", "Tiền nước", "Tiền xăng")),
                showIconInfo = true,
                iconStart = null,
                iconEnd = R.drawable.arrow_bottom,
                hintTextStart = "",
                hintTextEnd = "",
                state = IBFrameState.DISABLE(colorScheme)
            )
            Spacer(Modifier.height(24.dp))
            IBankInputDropdownBaseV2(
                labelText = "Label",
                required = true,
                typeData = IBankInputDropdownTypeData.Tags(listOf("Tiền điện", "Tiền nước")),
                showIconInfo = false,
                iconStart = null,
                iconEnd = R.drawable.arrow_bottom,
                hintTextStart = "This is a hint text to help user",
                hintTextEnd = "",
                state = IBFrameExtendState.FIXED(colorScheme)
            )

            Spacer(Modifier.height(24.dp))
            IBankInputDropdownBaseV2(
                labelText = "Label",
                required = true,
                showIconInfo = true,
                typeData = IBankInputDropdownTypeData.Select(text = ""),
                iconStart = null,
                iconEnd = R.drawable.arrow_bottom,
                hintTextStart = "",
                hintTextEnd = "",
                state = IBFrameState.DEFAULT(colorScheme)
            )
        }
    }
}

