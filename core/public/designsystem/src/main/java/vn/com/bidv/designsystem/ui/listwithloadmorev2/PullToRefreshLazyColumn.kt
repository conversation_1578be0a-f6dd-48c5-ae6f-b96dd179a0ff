package vn.com.bidv.designsystem.ui.listwithloadmorev2

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.FastOutLinearInEasing
import androidx.compose.animation.core.LinearOutSlowInEasing
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.pulltorefresh.pullToRefresh
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.times
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.ModelCheckAble
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.RuleFilters
import kotlin.math.max
import kotlin.math.min

/**
 * A composable that displays a list of items with pull-to-refresh and load-more functionality.
 *
 * @param viewModel The ViewModel that provides the data and handles events related to loading more content.
 * @param pullToRefreshConfig Configuration for the pull-to-refresh behavior.
 * @param loadMoreIndicator Composable to show a loading indicator at the bottom of the list when more data is being loaded.
 * @param onRetry Callback to be executed when a retry action is triggered on error.
 * @param errorView Composable to show an error message when there is a failure loading the data.
 * @param emptyView Composable to show when there are no items to display.
 * @param onRefresh Callback to be executed when a pull-to-refresh gesture is detected.
 * @param onLoadMoreData Callback to be executed when the end of the list is reached, triggering loading more data.
 * @param contentPadding Padding values to be applied to the content of the LazyColumn.
 * @param verticalArrangement Vertical arrangement of the items within the LazyColumn.
 * @param itemView Composable to display each individual item in the list.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun <T, Rule : RuleFilters> PullToRefreshLazyColumn(
    state: ListAutoLoadMoreReducer.ListAutoLoadMoreViewState<T, Rule>,
    pullToRefreshConfig: PullToRefreshConfig,
    errorView: @Composable (errorMessage: String?) -> Unit,
    emptyView: @Composable () -> Unit,
    loadingIndicator: @Composable () -> Unit,
    contentPadding: PaddingValues = PaddingValues(0.dp),
    verticalArrangement: Arrangement.Vertical = Arrangement.Top,
    onRefresh: () -> Unit,
    onLoadMoreData: () -> Unit,
    lazyListState: LazyListState,
    itemKey: (index: Int, T) -> Any = { index, _ -> index },
    userScrollEnabled: Boolean = true,
    modifier: Modifier = Modifier.fillMaxSize(),
    stickyView: (@Composable (T) -> Unit)? = null,
    itemView: @Composable (ModelCheckAble<T>) -> Unit,
) {
    var isScrolling by remember { mutableStateOf(false) }
    LaunchedEffect(lazyListState.isScrollInProgress) {
        if (lazyListState.isScrollInProgress) {
            isScrolling = true
        } else {
            delay(1500) // Chờ 1.5 giây
            isScrolling = false
        }
    }
    if (state.listItems.isEmpty() and state.isLastPage) {
        val pullToRefreshState = rememberPullToRefreshState()
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .pullToRefresh(
                        enabled = pullToRefreshConfig.enabled,
                        state = pullToRefreshState,
                        isRefreshing = state.isRefreshing,
                        onRefresh = onRefresh,
                    ),
            ) {
                val animatedHeight by animateDpAsState(
                    targetValue = (min(
                        1f,
                        pullToRefreshState.distanceFraction
                    ) * pullToRefreshConfig.loadingIndicatorMaxHeight),
                    animationSpec = tween(
                        durationMillis = pullToRefreshConfig.loadingAnimDurationMillis,
                        easing = LinearOutSlowInEasing
                    ),
                    label = ""
                )

                if (state.isRefreshing || pullToRefreshState.distanceFraction > 0f) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(animatedHeight),
                        contentAlignment = Alignment.Center
                    ) {
                        loadingIndicator()
                    }
                }

                LazyColumn(
                    state = lazyListState,
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = contentPadding,
                    verticalArrangement = verticalArrangement,
                    userScrollEnabled = userScrollEnabled
                ) {
                    item {
                        emptyView()
                    }
                }
            }
        }
    } else if (state.listItems.isEmpty() && state.isError) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            errorView(state.errorMessage)
        }
    } else {
        val pullToRefreshState = rememberPullToRefreshState()
        Box(modifier = modifier) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .pullToRefresh(
                        enabled = pullToRefreshConfig.enabled,
                        state = pullToRefreshState,
                        isRefreshing = state.isRefreshing,
                        onRefresh = onRefresh,
                    ),
            ) {
                val animatedHeight by animateDpAsState(
                    targetValue = (min(
                        1f,
                        pullToRefreshState.distanceFraction
                    ) * pullToRefreshConfig.loadingIndicatorMaxHeight),
                    animationSpec = tween(
                        durationMillis = pullToRefreshConfig.loadingAnimDurationMillis,
                        easing = LinearOutSlowInEasing
                    ),
                    label = ""
                )

                if (state.isRefreshing || pullToRefreshState.distanceFraction > 0f) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(animatedHeight),
                        contentAlignment = Alignment.Center
                    ) {
                        loadingIndicator()
                    }
                }

                LazyColumn(
                    state = lazyListState,
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = contentPadding,
                    verticalArrangement = verticalArrangement,
                    userScrollEnabled = userScrollEnabled
                ) {
                    listContent(
                        state = state,
                        itemKey = itemKey,
                        onLoadMoreData = onLoadMoreData,
                        errorView = errorView,
                        loadingIndicator = loadingIndicator,
                        stickyView = stickyView,
                        itemView = itemView,
                        lazyListState = lazyListState
                    )
                }
            }
            AnimatedVisibility(
                visible = isScrolling,
                modifier = Modifier.align(Alignment.CenterEnd),
                enter = fadeIn(),
                exit = fadeOut()
            ) {
                CustomScrollbar(
                    listState = lazyListState,
                    modifier = Modifier.align(Alignment.TopEnd)
                )
            }
        }
    }

    // Load more data when the screen is first launched
    LaunchedEffect(true) {
        if (state.listItems.isEmpty()) {
            onLoadMoreData()
        }
    }
}

@OptIn(ExperimentalFoundationApi::class)
private fun <T, Rule> LazyListScope.listContent(
    state: ListAutoLoadMoreReducer.ListAutoLoadMoreViewState<T, Rule>,
    itemKey: (index: Int, T) -> Any = { index, _ -> index },
    onLoadMoreData: () -> Unit,
    lazyListState: LazyListState? = null,
    errorView: @Composable (errorMessage: String?) -> Unit,
    loadingIndicator: @Composable () -> Unit,
    stickyView: (@Composable (T) -> Unit)? = null,
    itemView: @Composable (ModelCheckAble<T>) -> Unit,
) {
    if (state.listItems.firstOrNull()?.data is ListItemInterfaces) {
        // Case have sticky view
        val sections = state.listItems.groupBy { (it.data as? ListItemInterfaces)?.getGroupId() }
        sections.entries.forEachIndexed { entryIndex, entry ->
            val key = entry.key
            val value = entry.value
            stickyView?.let {
                stickyHeader(key) {
                    stickyView.invoke(value.first().data)
                }
            }
            items(
                count = value.size,
                key = { itemIndex ->
                    "$key -  ${itemKey.invoke(itemIndex, value[itemIndex].data)}"
                }
            ) { itemIndex ->
                if (entryIndex == sections.size - 1 && itemIndex == value.size - 1 && !state.isError) {
                    onLoadMoreData()
                }
                itemView(value[itemIndex])
            }
        }
    } else {
        // Case not have sticky view
        items(
            count = state.listItems.size,
            key = { index ->
                itemKey.invoke(index, state.listItems[index].data)
            }
        ) { index ->
            if (index == state.listItems.size - 1 && !state.isError) {
                onLoadMoreData()
            }
            itemView(state.listItems[index])
        }
    }

    if (!state.isLastPage) {
        if (state.isError) {
            item {
                errorView(state.errorMessage)
                LaunchedEffect(Unit) {
                    lazyListState?.animateScrollToItem(
                        lazyListState.layoutInfo.totalItemsCount - 1
                    )
                }
            }
        } else {
            if (!state.isRefreshing) {
                item {
                    loadingIndicator()
                }
            }
        }
    }
}

@Composable
fun CustomScrollbar(listState: LazyListState, modifier: Modifier) {
    // Tỷ lệ scroll
    val scrollRatio by remember {
        derivedStateOf {
            val totalItems = listState.layoutInfo.totalItemsCount
            val visibleItems = listState.layoutInfo.visibleItemsInfo
            val firstVisibleIndex = listState.firstVisibleItemIndex
            val offsetScrollRatio = visibleItems.getOrNull(firstVisibleIndex)?.size?.let {
                listState.firstVisibleItemScrollOffset.toFloat() / it
            } ?: 0f
            if (totalItems > 0) {
                (firstVisibleIndex.toFloat() + offsetScrollRatio) / (totalItems - visibleItems.size).coerceAtLeast(
                    1
                )
            } else 0f
        }

    }
    var scrollbarHeightRatio by remember { mutableFloatStateOf(0f) } // Giới hạn chiều cao

    LaunchedEffect(listState) {
        snapshotFlow {
            listState.layoutInfo.totalItemsCount
        }.collectLatest { totalItems ->
            val visibleItems = listState.layoutInfo.visibleItemsInfo.size
            // Chiều cao của thanh cuộn
            scrollbarHeightRatio = visibleItems.toFloat() / totalItems
        }
    }

    // Animate Scroll Ratio
    val animatedScrollRatio by animateFloatAsState(
        targetValue = scrollRatio,
        animationSpec = tween(durationMillis = 300, easing = FastOutLinearInEasing),
        label = "scrollbar animation"
    )

    if (scrollbarHeightRatio >= 1f) {
        return
    }

    Canvas(
        modifier = modifier
            .fillMaxHeight()
            .width(8.dp)
            .padding(end = 4.dp)
    ) {
        var scrollbarHeight = (scrollbarHeightRatio * size.height)
        scrollbarHeight = max(scrollbarHeight, 40f)

        drawRoundRect(
            color = Color.Gray.copy(alpha = 0.6f),
            topLeft = Offset(0f, animatedScrollRatio * (size.height - scrollbarHeight)),
            size = Size(size.width, scrollbarHeight),
            cornerRadius = CornerRadius(4.dp.toPx(), 4.dp.toPx())
        )
    }
}

