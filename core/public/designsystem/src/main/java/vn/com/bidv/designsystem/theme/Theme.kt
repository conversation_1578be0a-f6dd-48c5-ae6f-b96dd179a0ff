package vn.com.bidv.designsystem.theme

import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider

@Composable
fun IBankTheme(
    isPremium: Boolean = false,
    content: @Composable () -> Unit,
) {
    // Color scheme
    val colorScheme =
        if (isPremium) IBColorScheme.PremiumColorScheme else IBColorScheme.NormalColorScheme
    val typography =
        if (isPremium) IBTypography.PremiumTypography else IBTypography.NormalTypography

    // Composition locals
    CompositionLocalProvider(
        LocalColorScheme provides colorScheme,
        LocalTypography provides typography,
        content = content,
    )
}