package vn.com.bidv.designsystem.ui.listwithloadmorev2

import kotlinx.coroutines.Job
import vn.com.bidv.common.patterns.mvi.BaseMviViewModel
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.ModelCheckAble
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.RuleFilters

abstract class ListAutoLoadMoreViewModel<T, Rule : RuleFilters>(
    reducer: ListAutoLoadMoreReducer<T, Rule>,
    itemPerPage: Int = 20,
) : BaseMviViewModel<
        ListAutoLoadMoreReducer.ListAutoLoadMoreViewState<T, Rule>,
        ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent<T, Rule>,
        ListAutoLoadMoreReducer.ListAutoLoadMoreViewEffect<T, Rule>
        >(
    initialState = ListAutoLoadMoreReducer.ListAutoLoadMoreViewState(
        pageSize = itemPerPage
    ),
    reducer = reducer
) {
    private var job: Job? = null

    override fun handleEffect(
        sideEffect: ListAutoLoadMoreReducer.ListAutoLoadMoreViewEffect<T, Rule>,
        onResult: (ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent<T, Rule>) -> Unit,
    ) {
        when (sideEffect) {
            is ListAutoLoadMoreReducer.ListAutoLoadMoreViewEffect.GetData -> {
                job?.cancel()
                job = fetchData(
                    pageIndex = sideEffect.pageIndex,
                    pageSize = sideEffect.pageSize,
                    rule = sideEffect.rule,
                    onLoadSuccess = { data, totalItem ->
                        onResult(
                            ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent.GetDataSuccess(
                                items = data,
                                total = totalItem,
                                isLastPage = isLastPage(sideEffect, data),
                                listItemFiltered = filterLocal(
                                    sideEffect.searchKey,
                                    data.map { ModelCheckAble(it) })
                            )
                        )
                    },
                    onLoadFail = { errorMessage ->
                        onResult(
                            ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent.GetDataFail(
                                errorMessage ?: ""
                            )
                        )
                    }
                )
            }

            is ListAutoLoadMoreReducer.ListAutoLoadMoreViewEffect.FilterDataLocal -> {
                onResult(
                    ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent.FilterDataLocalSuccess(
                        listItemFiltered = filterLocal(sideEffect.searchKey, sideEffect.listItem)
                    )
                )
            }
        }
    }

    open fun isLastPage(
        sideEffect: ListAutoLoadMoreReducer.ListAutoLoadMoreViewEffect.GetData<Rule>,
        data: List<T>
    ): Boolean {
        return data.size < sideEffect.pageSize
    }

    protected abstract fun fetchData(
        pageIndex: Int,
        pageSize: Int,
        rule: Rule?,
        onLoadSuccess: (data: List<T>, total: Int?) -> Unit,
        onLoadFail: (String?) -> Unit,
    ): Job

    // Filter local data, inject usecase to viewmodel to filter data
    open fun filterLocal(key: String?, items: List<ModelCheckAble<T>>): List<ModelCheckAble<T>> {
        return items
    }
}
    