package vn.com.bidv.designsystem.component.dataentry

import androidx.annotation.DrawableRes
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBColorScheme
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBShadow
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.IBTypography
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

@Composable
fun IBankInputDropdownBase(
    modifier: Modifier = Modifier,
    labelText: String,
    required: Boolean = false,
    showIconInfo: Boolean = false,
    listItems: List<String> = emptyList(),
    itemMaxLine: Int = 1,
    iconStart: @Composable (() -> Unit)? = null, // Annotation @DrawableRes thêm vào đây
    @DrawableRes iconEnd: Int? = R.drawable.arrow_bottom, // Annotation @DrawableRes thêm vào đây
    state: IBFrameState = IBFrameState.DEFAULT(LocalColorScheme.current),
    hintTextStart: String? = "",
    hintTextEnd: String? = "",
    onClickInfo: () -> Unit = {},
    onClickEnd: () -> Unit = {},
) {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current
    val hintTextColor = when (state) {
        is IBFrameState.ERROR -> IBHintTextColor.ERROR(colorScheme).hintTextColor
        else -> IBHintTextColor.DEFAULT(colorScheme).hintTextColor
    }
    Column {
        IBankInputDropdownBody(
            modifier = modifier.testTagIBank("IBankInputDropdownBase_$labelText"),
            labelText = labelText,
            required = required,
            showIconInfo = showIconInfo,
            listItems = listItems,
            maxLine = itemMaxLine,
            iconStart = iconStart,
            iconEnd = iconEnd,
            state = state,
            onClickInfo = onClickInfo,
            onClickEnd = onClickEnd,
        )
        if (!hintTextStart.isNullOrEmpty() || !hintTextEnd.isNullOrEmpty()) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(
                        top = IBSpacing.spacing2xs, start = IBSpacing.spacing2xs
                    ), horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = hintTextStart ?: "",
                    style = typography.bodyBody_m,
                    color = hintTextColor,
                )
                Text(
                    modifier = Modifier.padding(end = IBSpacing.spacing2xs),
                    text = hintTextEnd ?: "",
                    style = typography.bodyBody_m,
                    color = hintTextColor,
                )
            }
        }
    }
}

@Composable
fun IBankInputDropdownBody(
    modifier: Modifier = Modifier,
    labelText: String,
    required: Boolean = false,
    showIconInfo: Boolean = false,
    listItems: List<String> = emptyList(),
    iconStart: @Composable (() -> Unit)? = null,
    iconEnd: Int?,
    maxLine: Int,
    state: IBFrameState = IBFrameState.DEFAULT(LocalColorScheme.current),
    onClickInfo: () -> Unit = {},
    onClickEnd: () -> Unit = {},
) = IBankInputFrameBase(state = state, bodyContent = {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current
    val style = when (state) {
        is IBFrameState.DEFAULT -> IBDropdownStyle.DEFAULT(typography, colorScheme)
        is IBFrameExtendState.FILLED -> IBDropdownStyle.FILLED(typography, colorScheme)
        is IBFrameState.FOCUS -> IBDropdownStyle.FOCUS(typography, colorScheme)
        is IBFrameState.DISABLE -> IBDropdownStyle.DISABLE(typography, colorScheme)
        is IBFrameState.ERROR -> IBDropdownStyle.ERROR(typography, colorScheme)
        is IBFrameExtendState.FIXED -> IBDropdownStyle.FIXED(typography, colorScheme)
        is IBFrameExtendState.PRESSED -> TODO()
    }
    val labelStyle = style.labelStyle
    val labelColor = style.labelColor
    val iconEndTint = style.iconEndTint
    val showData = listItems.isNotEmpty()
    val selectedTextColor = style.selectedTextColor
    val itemRemovable = style.itemRemovable
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(state.colorBackground)
            .wrapContentHeight()
            .padding(
                start = IBSpacing.spacingM,
                end = IBSpacing.spacingM,
                top = IBSpacing.spacingXs,
                bottom = IBSpacing.spacingXs
            ), verticalAlignment = Alignment.CenterVertically
    ) {
        if (iconStart != null) {
            Box(modifier = modifier
                .size(32.dp)
                .clickable { onClickInfo() }
                .clip(CircleShape)
                .aspectRatio(1f)
                .border(
                    width = IBBorderDivider.borderDividerS,
                    color = colorScheme.borderMainPrimary,
                    shape = RoundedCornerShape(IBCornerRadius.cornerRadiusRound)
                ), contentAlignment = Alignment.Center) {
                iconStart()
            }
        }
        Box(modifier = Modifier.weight(1f), contentAlignment = Alignment.CenterStart) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        start = if (iconStart != null) IBSpacing.spacingXs else IBSpacing.spacingNone,
                    )
                    .alpha(if (showData) 1f else 0f)
                    .clickable {
                        onClickEnd()
                    },
            ) {
                PlaceholderView(
                    modifier = Modifier.fillMaxWidth(),
                    required = required,
                    showIconInfo = showIconInfo,
                    placeHolderText = labelText,
                    placeHolderStyle = LocalTypography.current.captionCaption_m,
                    placeHolderColor = labelColor,
                )
                DropdownData(
                    maxLine = maxLine,
                    listItems = listItems,
                    itemRemovable = itemRemovable,
                    textColor = selectedTextColor,
                    typography = typography,
                    isErrorState = (state is IBFrameState.ERROR),
                )
            }

            PlaceholderView(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        start = if (iconStart != null) IBSpacing.spacingXs else IBSpacing.spacingNone,
                    )
                    .alpha(if (showData) 0f else 1f),
                required = required,
                showIconInfo = showIconInfo,
                placeHolderText = labelText,
                placeHolderStyle = LocalTypography.current.bodyBody_l,
                placeHolderColor = LocalColorScheme.current.contentPlaceholder,
            )
        }
        if (iconEnd != null) {
            Column(
                modifier = Modifier.testTagIBank("IBankInputDropdownBase_${labelText}_iconEnd")
                    .padding(start = IBSpacing.spacingXs)
                    .clickable { onClickEnd() },
                verticalArrangement = Arrangement.Center,
            ) {
                Icon(
                    modifier = Modifier.size(16.dp),
                    tint = iconEndTint,
                    imageVector = ImageVector.vectorResource(id = iconEnd),
                    contentDescription = null,
                )
            }
        }
    }
})

@Composable
private fun PlaceholderView(
    modifier: Modifier,
    required: Boolean,
    showIconInfo: Boolean,
    placeHolderText: String,
    placeHolderStyle: TextStyle,
    placeHolderColor: Color,
) {
    Row(
        modifier = modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            placeHolderText,
            style = placeHolderStyle,
            color = placeHolderColor,
        )
        if (required) {
            DotRequire()
        }
        if (showIconInfo) {
            Icon(
                imageVector = ImageVector.vectorResource(id = R.drawable.information_circle),
                modifier = Modifier
                    .size(16.dp)
                    .padding(IBSpacing.spacing3xs)
                    .align(Alignment.CenterVertically),
                tint = LocalColorScheme.current.contentMainPrimary,
                contentDescription = null,
            )
        }
    }
}

@Composable
private fun DotRequire() {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current
    Text(
        "*",
        style = typography.captionCaption_m,
        color = colorScheme.contentNegativePrimary,
        modifier = Modifier.padding(start = IBSpacing.spacing3xs)
    )
}

@Composable
fun DropdownData(
    listItems: List<String> = emptyList(),
    itemRemovable: Boolean,
    textColor: Color,
    typography: IBTypography,
    isErrorState: Boolean,
    maxLine: Int,
) {
    if (listItems.size <= 1) {
        // Display SELECTED item
        Text(
            text = listItems.firstOrNull() ?: "",
            style = typography.bodyBody_l,
            color = textColor,
            maxLines = maxLine,
            overflow = TextOverflow.Ellipsis
        )
    }
}

sealed class IBHintTextColor(
    val hintTextColor: Color
) {
    class DEFAULT(colorScheme: IBColorScheme) : IBHintTextColor(
        hintTextColor = colorScheme.contentMainTertiary
    )

    class ERROR(colorScheme: IBColorScheme) : IBHintTextColor(
        hintTextColor = colorScheme.contentNegativePrimary
    )
}

sealed class IBDropdownStyle(
    val labelStyle: TextStyle,
    val labelColor: Color,
    val iconEndTint: Color,
    val showData: Boolean = true,
    val selectedTextColor: Color,
    val itemRemovable: Boolean = true,
) {
    class DEFAULT(typography: IBTypography, colorScheme: IBColorScheme) : IBDropdownStyle(
        labelStyle = typography.bodyBody_m,
        labelColor = colorScheme.contentPlaceholder,
        iconEndTint = colorScheme.contentMainSecondary,
        showData = false,
        selectedTextColor = colorScheme.contentMainPrimary,
    )

    class FILLED(typography: IBTypography, colorScheme: IBColorScheme) : IBDropdownStyle(
        labelStyle = typography.captionCaption_m,
        labelColor = colorScheme.contentMainTertiary,
        iconEndTint = colorScheme.contentMainSecondary,
        showData = true,
        selectedTextColor = colorScheme.contentMainPrimary,
    )

    class FOCUS(typography: IBTypography, colorScheme: IBColorScheme) : IBDropdownStyle(
        labelStyle = typography.captionCaption_m,
        labelColor = colorScheme.contentMainTertiary,
        iconEndTint = colorScheme.contentMainSecondary,
        showData = true,
        selectedTextColor = colorScheme.contentMainPrimary,
    )

    class ERROR(typography: IBTypography, colorScheme: IBColorScheme) : IBDropdownStyle(
        labelStyle = typography.captionCaption_m,
        labelColor = colorScheme.contentNegativePrimary,
        iconEndTint = colorScheme.contentMainSecondary,
        selectedTextColor = colorScheme.contentMainPrimary,
        itemRemovable = false,
    )

    class DISABLE(typography: IBTypography, colorScheme: IBColorScheme) : IBDropdownStyle(
        labelStyle = typography.bodyBody_m,
        labelColor = colorScheme.contentMainTertiary,
        iconEndTint = colorScheme.contentDisablePrimary,
        showData = false,
        selectedTextColor = colorScheme.contentMainPrimary,
        itemRemovable = false,
    )

    class FIXED(typography: IBTypography, colorScheme: IBColorScheme) : IBDropdownStyle(
        labelStyle = typography.captionCaption_m,
        labelColor = colorScheme.contentMainTertiary,
        iconEndTint = colorScheme.contentDisablePrimary,
        showData = true,
        selectedTextColor = colorScheme.contentDisablePrimary,
        itemRemovable = false,
    )
}

sealed class IBFrameExtendState(
    colorBorder: Color,
    colorShadowUpper: IBShadow.ShadowConfig,
    colorShadowUnder: IBShadow.ShadowConfig,
    colorBackground: Color
) : IBFrameState(
    colorBorder = colorBorder,
    colorShadowUpper = colorShadowUpper,
    colorShadowUnder = colorShadowUnder,
    colorBackground = colorBackground
) {
    class FILLED(colorScheme: IBColorScheme) : IBFrameState(
        colorBorder = colorScheme.borderMainPrimary, colorShadowUnder = IBShadow.ShadowConfig(
            color = Color.Transparent, blur = 0.dp, offsetX = 0.dp, offsetY = 0.dp, spread = 0.dp
        ), colorShadowUpper = IBShadow.ShadowConfig(
            color = Color.Transparent, blur = 0.dp, offsetX = 0.dp, offsetY = 0.dp, spread = 0.dp
        ), colorBackground = colorScheme.bgMainTertiary
    )

    class FIXED(colorScheme: IBColorScheme) : IBFrameState(
        colorBorder = colorScheme.borderMainPrimary,
        colorShadowUnder = IBShadow.ShadowConfig(
            color = Color.Transparent, blur = 0.dp, offsetX = 0.dp, offsetY = 0.dp, spread = 0.dp
        ),
        colorShadowUpper = IBShadow.ShadowConfig(
            color = Color.Transparent, blur = 0.dp, offsetX = 0.dp, offsetY = 0.dp, spread = 0.dp
        ),
        colorBackground = colorScheme.bgDisablePrimary,
    )

    class PRESSED(colorScheme: IBColorScheme) : IBFrameState(
        colorBorder = colorScheme.borderMainPrimary_press,
        colorShadowUnder = IBShadow.ShadowConfig(
            color = Color.Transparent, blur = 0.dp, offsetX = 0.dp, offsetY = 0.dp, spread = 0.dp
        ),
        colorShadowUpper = IBShadow.ShadowConfig(
            color = Color.Transparent, blur = 0.dp, offsetX = 0.dp, offsetY = 0.dp, spread = 0.dp
        ),
        colorBackground = colorScheme.bgMainTertiary,
    )
}

@Preview
@Composable
fun PreviewInputDropdownBase() {
    val colorScheme = LocalColorScheme.current
    Spacer(Modifier.height(16.dp))
    Box(Modifier.padding(all = 12.dp)) {
        Column {
            IBankInputDropdownBase(
                labelText = "Ngân hàng",
                required = false,
                listItems = listOf("Ngân hàng TMCP Đầu tư và Phát triển Việt Nam (BIDV)"),
                itemMaxLine = Int.MAX_VALUE,
                showIconInfo = false,
                iconStart = {
                    Icon(
                        modifier = Modifier.size(32.dp),
                        imageVector = ImageVector.vectorResource(id = R.drawable.icon_popup_info_64),
                        contentDescription = null,
                        tint = Color.Unspecified,
                    )
                },
                iconEnd = R.drawable.arrow_bottom,
                hintTextStart = "This is a hint text to help user",
                hintTextEnd = "200VND",
                state = IBFrameState.DEFAULT(colorScheme),
            )
            Spacer(Modifier.height(24.dp))
            IBankInputDropdownBase(
                labelText = "Label",
                required = true,
                listItems = listOf("Ngân hàng TMCP Đầu tư và Phát triển Việt Nam (BIDV)"),
                showIconInfo = false,
                iconStart = null,
                iconEnd = R.drawable.arrow_bottom,
                hintTextStart = "This is a hint text to help user",
                hintTextEnd = "",
                state = IBFrameExtendState.FILLED(colorScheme),
            )
            Spacer(Modifier.height(24.dp))
            IBankInputDropdownBase(
                labelText = "Loại dịch vụ",
                required = true,
                listItems = listOf("Tiền điện", "Tiền nước"),
                showIconInfo = true,
                iconStart = {
                    Icon(
                        modifier = Modifier.size(32.dp),
                        imageVector = ImageVector.vectorResource(id = R.drawable.icon_popup_info_64),
                        contentDescription = null,
                        tint = Color.Unspecified,
                    )
                },
                iconEnd = R.drawable.arrow_bottom,
                hintTextStart = "This is a hint text to help user",
                hintTextEnd = "200VND",
                state = IBFrameState.FOCUS(colorScheme)
            )
            Spacer(Modifier.height(24.dp))
            IBankInputDropdownBase(
                labelText = "Tên tài khoản",
                required = true,
                listItems = listOf("41252", "412451"),
                showIconInfo = false,
                iconStart = {
                    Icon(
                        modifier = Modifier.size(32.dp),
                        imageVector = ImageVector.vectorResource(id = R.drawable.icon_popup_info_64),
                        contentDescription = null,
                        tint = Color.Unspecified,
                    )
                },
                iconEnd = R.drawable.arrow_bottom,
                hintTextStart = "This is a hint text to help user",
                hintTextEnd = "200VND",
                state = IBFrameState.ERROR(colorScheme)
            )

            Spacer(Modifier.height(24.dp))
            IBankInputDropdownBase(
                labelText = "Tên tài khoản",
                required = true,
                listItems = listOf(),
                showIconInfo = false,
                iconStart = {
                    Icon(
                        modifier = Modifier.size(32.dp),
                        imageVector = ImageVector.vectorResource(id = R.drawable.icon_popup_info_64),
                        contentDescription = null,
                        tint = Color.Unspecified,
                    )
                },
                iconEnd = R.drawable.arrow_bottom,
                hintTextStart = "This is a hint text to help user",
                hintTextEnd = "200VND",
                state = IBFrameState.ERROR(colorScheme)
            )

            Spacer(Modifier.height(24.dp))
            IBankInputDropdownBase(
                labelText = "Label",
                required = true,
                listItems = listOf("abc", "def"),
                showIconInfo = true,
                iconStart = null,
                iconEnd = R.drawable.arrow_bottom,
                hintTextStart = "",
                hintTextEnd = "",
                state = IBFrameState.DISABLE(colorScheme)
            )
            Spacer(Modifier.height(24.dp))
            IBankInputDropdownBase(
                labelText = "Label",
                required = true,
                listItems = listOf("123414", "42313"),
                showIconInfo = false,
                iconStart = null,
                iconEnd = R.drawable.arrow_bottom,
                hintTextStart = "This is a hint text to help user",
                hintTextEnd = "",
                state = IBFrameExtendState.FIXED(colorScheme)
            )

            Spacer(Modifier.height(24.dp))
            IBankInputDropdownBase(
                labelText = "Label",
                required = true,
                showIconInfo = true,
                iconStart = null,
                iconEnd = R.drawable.arrow_bottom,
                hintTextStart = "",
                hintTextEnd = "",
                state = IBFrameState.DEFAULT(colorScheme)
            )
        }
    }
}

