package vn.com.bidv.designsystem.component.keyboard

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBShadow
import vn.com.bidv.designsystem.theme.IBShadow.dropShadow
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import java.security.SecureRandom

@Composable
fun NumpadKeyboard(
    modifier: Modifier = Modifier,
    type: NumpadType = NumpadType.NORMAL,
    onKeyClick: (KeyInput) -> Unit
) {
    val localScheme = LocalColorScheme.current
    val numbers =
        if (type == NumpadType.SHUFFLED) {
            (0..9).toList()
                .shuffled(SecureRandom())
        } else {
            (1..9).toList() + 0
        }
    val keys = numbers
        .map { KeyInput.Number(it.toString()) }
        .toMutableList<KeyInput>()
        .apply {
            add(size - 1, KeyInput.Empty)
            add(KeyInput.Delete)
        }

    Box(
        modifier = modifier
            .testTagIBank("NumpadKeyboard")
            .dropShadow(
                config = IBShadow.shadowsShadowm1.copy(shape = RectangleShape)
            )
            .dropShadow(
                config = IBShadow.shadowsShadowm1.copy(shape = RectangleShape)
            )
    ) {
        LazyVerticalGrid(
            columns = GridCells.Fixed(3),
            modifier = Modifier
                .fillMaxWidth()
                .background(localScheme.bgMainTertiary)
                .padding(
                    IBSpacing.spacingM
                ),
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
        ) {
            items(keys.size) { index ->
                val key = keys[index]
                Box(
                    modifier = Modifier
                        .aspectRatio(110 / 60f),
                ) {
                    when (key) {
                        is KeyInput.Number -> {
                            NumberButton(
                                modifier = Modifier.fillMaxSize(),
                                numberText = key.value
                            ) {
                                onKeyClick(key)
                            }

                        }

                        is KeyInput.Delete -> {
                            Box(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .clickable {
                                        onKeyClick(key)
                                    },
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    imageVector = ImageVector.vectorResource(
                                        id = R.drawable.delete_button
                                    ),
                                    contentDescription = null,
                                    tint = localScheme.contentMainSecondary,
                                    modifier = Modifier
                                        .testTagIBank("NumpadKeyboard_icon_delete")
                                        .size(IBSpacing.spacing2xl)
                                )
                            }
                        }

                        KeyInput.Empty -> {
                            Box(
                                modifier = Modifier.fillMaxSize()
                            )
                        }
                    }
                }

            }
        }
    }
}

@Composable
fun NumberButton(
    modifier: Modifier,
    numberText: String,
    onKeyClick: () -> Unit
) {
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    val localScheme = LocalColorScheme.current

    val color =
        if (isPressed) localScheme.bgMainTertiary_press else localScheme.bgMainTertiary
    Button(
        onClick = onKeyClick,
        interactionSource = interactionSource,
        modifier = modifier,
        colors = ButtonDefaults.buttonColors(color),
        border = BorderStroke(1.dp, localScheme.borderMainSecondary),
        shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL),
        contentPadding = PaddingValues(vertical = IBSpacing.spacing2xs),
    ) {
        val numberIcon = getNumberIcon(numberText)
        Icon(
            imageVector = ImageVector.vectorResource(id = numberIcon),
            contentDescription = null,
            tint = localScheme.contentMainSecondary,
            modifier = Modifier
                .size(IBSpacing.spacing3xl)
        )
    }
}

private fun getNumberIcon(numberText: String): Int {
    return when (numberText) {
        NUMBER_0 -> R.drawable.number_0
        NUMBER_1 -> R.drawable.number_1
        NUMBER_2 -> R.drawable.number_2
        NUMBER_3 -> R.drawable.number_3
        NUMBER_4 -> R.drawable.number_4
        NUMBER_5 -> R.drawable.number_5
        NUMBER_6 -> R.drawable.number_6
        NUMBER_7 -> R.drawable.number_7
        NUMBER_8 -> R.drawable.number_8
        NUMBER_9 -> R.drawable.number_9
        else -> R.drawable.number_0
    }
}

@Preview
@Composable
fun PreviewNumpadKeyboard() {

    var showNumpad by remember { mutableStateOf(false) }

    Box {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .align(Alignment.BottomCenter),
            verticalArrangement = Arrangement.Bottom
        ) {
            Button(onClick = { showNumpad = !showNumpad }) {
                Text(text = if (showNumpad) "Hide Numpad" else "Show Numpad")
            }

            if (showNumpad) {
                NumpadKeyboard(onKeyClick = { key -> println("Key clicked: $key") })
            }
        }
    }
}

enum class NumpadType {
    NORMAL,
    SHUFFLED
}

sealed class KeyInput {
    data class Number(val value: String) : KeyInput()
    data object Delete : KeyInput()
    data object Empty : KeyInput()
}

private const val NUMBER_0 = "0"
private const val NUMBER_1 = "1"
private const val NUMBER_2 = "2"
private const val NUMBER_3 = "3"
private const val NUMBER_4 = "4"
private const val NUMBER_5 = "5"
private const val NUMBER_6 = "6"
private const val NUMBER_7 = "7"
private const val NUMBER_8 = "8"
private const val NUMBER_9 = "9"

