package vn.com.bidv.designsystem.ui.listwithloadmorev2

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import vn.com.bidv.common.extenstion.isNotNullOrEmpty
import vn.com.bidv.common.ui.BaseMVIScreen
import vn.com.bidv.designsystem.component.IBankEmptyState
import vn.com.bidv.designsystem.component.IBankLoaderIndicatorsImpl
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.ModelCheckAble
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.RuleFilters
import vn.com.bidv.localization.R

@Composable
fun <T, Rule : RuleFilters> ListAutoLoadMore(
    viewModel: ListAutoLoadMoreViewModel<T, Rule>,
    pullToRefreshConfig: PullToRefreshConfig = PullToRefreshConfig(),
    loadingView: @Composable () -> Unit = {
        IBankLoaderIndicatorsImpl.Default()
    },
    errorView: @Composable (errorMessage: String?) -> Unit = { errorMessage ->
        IBankEmptyState(
            modifier = Modifier.fillMaxSize(),
            supportingText = if (errorMessage.isNotNullOrEmpty())
                errorMessage else stringResource(id = R.string.co_loi_xay_ra_trong_qua_trinh_xu_ly_vui_long_thu_lai),
            textButton = stringResource(id = R.string.retry),
            leadingIconButton = ImageVector.vectorResource(id = vn.com.bidv.designsystem.R.drawable.refresh_outline),
            onClickButton = { viewModel.sendEvent(ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent.Retry) }
        )
    },
    emptyView: @Composable () -> Unit = {
        IBankEmptyState()
    },
    registerOnEvent: ((
        onEvent: (ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent<T, Rule>) -> Unit,
    ) -> Unit)? = null,
    onStateChange: ((state: ListAutoLoadMoreReducer.ListAutoLoadMoreViewState<T, Rule>) -> Unit)? = null,
    onScrollStateChange: ((LazyListState) -> Unit)? = null,
    listState: LazyListState = rememberLazyListState(),
    itemKey: (index: Int, T) -> Any = { index, _ -> index },
    userScrollEnabled: Boolean = true,
    modifier: Modifier = Modifier.fillMaxSize(),
    contentPadding: PaddingValues = PaddingValues(0.dp),
    verticalArrangement: Arrangement.Vertical = Arrangement.Top,
    stickyView: (@Composable (T) -> Unit)? = null,
    defaultRuleFilter: Rule? = null,
    itemView: @Composable (ModelCheckAble<T>) -> Unit,
) {
    BaseMVIScreen(
        viewModel = viewModel,
        renderContent = { viewState, onEvent ->
            LaunchedEffect(true) {
                defaultRuleFilter?.let {
                    onEvent(
                        ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent.UpdateDefaultRuleFilters(
                            defaultRuleFilter
                        )
                    )
                }
            }

            PullToRefreshLazyColumn(
                state = viewState,
                pullToRefreshConfig = pullToRefreshConfig,
                errorView = errorView,
                emptyView = emptyView,
                stickyView = stickyView,
                itemView = itemView,
                loadingIndicator = loadingView,
                onRefresh = {
                    onEvent(ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent.RefreshData)
                },
                onLoadMoreData = {
                    onEvent(ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent.LoadMoreData)
                },
                lazyListState = listState,
                contentPadding = contentPadding,
                itemKey = itemKey,
                userScrollEnabled = userScrollEnabled,
                modifier = modifier,
                verticalArrangement = verticalArrangement
            )
            LaunchedEffect(viewState) {
                onStateChange?.invoke(viewState)
            }
            LaunchedEffect(true) {
                registerOnEvent?.invoke(onEvent)
            }
            LaunchedEffect(listState) {
                snapshotFlow { listState.firstVisibleItemIndex }
                    .distinctUntilChanged()
                    .collectLatest {
                        onScrollStateChange?.invoke(listState)
                    }
            }
        },
        handleSideEffect = {
            //Do Nothing
        }
    )
}
