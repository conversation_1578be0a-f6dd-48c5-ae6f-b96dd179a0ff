package vn.com.bidv.designsystem.ui

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import vn.com.bidv.common.patterns.mvi.BaseState
import vn.com.bidv.designsystem.component.IBankEmptyState
import vn.com.bidv.designsystem.component.IBankLoaderIndicators
import vn.com.bidv.localization.R

@Composable
fun CommonScreen(
    baseState: BaseState,
    onInitScreen: @Composable () -> Unit = { },
    loadingView: @Composable () -> Unit = { IBankLoaderIndicators() },
    onRetryWhenError: () -> Unit = { },
    errorView: @Composable (errorMessage: String?) -> Unit = { errorMessage ->
        IBankEmptyState(
            modifier = Modifier.fillMaxSize(),
            supportingText = errorMessage,
            textButton = stringResource(id = R.string.retry),
            onClickButton = onRetryWhenError
        )
    }
) {
    when (baseState) {
        is BaseState.InitScreen -> {
            onInitScreen()
        }

        is BaseState.Loading -> {
            loadingView()
        }

        is BaseState.Error -> {
            errorView(baseState.errorMessage)
        }
    }
}
