package vn.com.bidv.designsystem.component.feedback.modalconfirm

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import vn.com.bidv.common.extenstion.isNotNull
import vn.com.bidv.common.extenstion.isNotNullOrEmpty
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.navigation.button.IBankNormalButton
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonType
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.localization.R as RLocalization

data class DialogButtonInfo(
    val label: String,
    val isDismissRequest: Boolean = true,
    val isEnable: Boolean = true,
    val onClick: (() -> Unit) = {},
)

@Composable
fun IBankModalConfirm(
    modifier: Modifier = Modifier,
    modalConfirmType: ModalConfirmType = ModalConfirmType.Info,
    title: String = stringResource(RLocalization.string.thong_bao),
    supportingText: String? = null,
    mainContent: String? = null,
    annotatedSupportingText: AnnotatedString? = null,
    contentView: @Composable (() -> Unit)? = null,
    isHtmlContent: Boolean = false,
    dialogProperties: DialogProperties = DialogProperties(
        dismissOnBackPress = false,
        dismissOnClickOutside = false
    ),
    webViewSettings: WebViewSettings? = null,
    listDialogButtonInfo: List<DialogButtonInfo> = emptyList(),
    isShowIconClose: Boolean = true,
    onDismissRequest: () -> Unit = { },
) {

    Dialog(
        onDismissRequest = {
            onDismissRequest()
        },
        properties = dialogProperties
    ) {
        ModalContent(
            modifier = modifier,
            modalConfirmType = modalConfirmType,
            title = title,
            supportingText = supportingText,
            mainContent = mainContent,
            annotatedSupportingText = annotatedSupportingText,
            contentView = contentView,
            isHtmlContent = isHtmlContent,
            listDialogButtonInfo = listDialogButtonInfo,
            webViewSettings = webViewSettings,
            isShowIconClose = isShowIconClose,
            onDismissRequest = {
                onDismissRequest()
            },
        )
    }
}

@Composable
private fun ModalContent(
    modifier: Modifier,
    modalConfirmType: ModalConfirmType,
    title: String,
    supportingText: String?,
    mainContent: String?,
    annotatedSupportingText: AnnotatedString?,
    contentView: @Composable (() -> Unit)? = null,
    isHtmlContent: Boolean = false,
    webViewSettings: WebViewSettings?,
    listDialogButtonInfo: List<DialogButtonInfo> = emptyList(),
    isShowIconClose: Boolean = true,
    onDismissRequest: () -> Unit,
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current
    val screenHeight = LocalConfiguration.current.screenHeightDp.dp

    Box(
        modifier = modifier.testTagIBank("IBankModalConfirm_$title").heightIn(max = screenHeight * 0.9606f),
        contentAlignment = Alignment.TopEnd
    ) {
        Surface(
            shape = RoundedCornerShape(IBSpacing.spacingL),
            color = colorScheme.contentOn_specialPrimary
        ) {
            Column(
                modifier = Modifier
                    .wrapContentSize()
                    .padding(
                        start = IBSpacing.spacingL,
                        top = IBSpacing.spacingM,
                        bottom = IBSpacing.spacingL,
                        end = IBSpacing.spacingL
                    ),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    modifier = Modifier.padding(IBSpacing.spacing2xs),
                    painter = painterResource(id = modalConfirmType.icon),
                    contentDescription = null,
                    tint = Color.Unspecified
                )

                Spacer(modifier = Modifier.height(IBSpacing.spacingXs))

                Text(
                    text = title,
                    style = typography.titleTitle_m,
                    modifier = Modifier
                        .padding(horizontal = IBSpacing.spacing2xl),
                    textAlign = TextAlign.Center
                )

                Column(
                    modifier = Modifier.weight(1f, fill = false)
                ) {

                    if (mainContent.isNotNullOrEmpty()) {
                        Column {
                            Spacer(modifier = Modifier.height(IBSpacing.spacingS))
                            Text(
                                text = mainContent.orEmpty(),
                                style = typography.headlineHeadline_s,
                                textAlign = TextAlign.Center
                            )
                        }
                    }

                    if (annotatedSupportingText != null && annotatedSupportingText.text.isNotEmpty()) {
                        Column {
                            Spacer(modifier = Modifier.height(IBSpacing.spacingXs))
                            Text(
                                text = annotatedSupportingText,
                                textAlign = TextAlign.Center
                            )
                        }
                    }

                    if (supportingText.isNotNullOrEmpty()) {
                        Column(modifier = modifier.testTagIBank("IBankModalConfirm_supportingText")) {
                            Spacer(modifier = Modifier.height(IBSpacing.spacingXs))
                            if (isHtmlContent) {
                                DisplayHtmlContent(
                                    htmlContent = supportingText.orEmpty(),
                                    webViewSettings = webViewSettings
                                )
                            } else {
                                Text(
                                    text = supportingText.orEmpty(),
                                    style = typography.bodyBody_m,
                                    textAlign = TextAlign.Center
                                )
                            }
                        }
                    }

                    if (contentView.isNotNull()) {
                        contentView?.invoke()
                    }

                }

                Spacer(modifier = Modifier.height(IBSpacing.spacing2xl))

                ActionButtons(
                    listDialogButtonInfo = listDialogButtonInfo,
                    onDismissRequest
                )
            }
        }

        if (isShowIconClose) {
            IconButton(
                modifier = Modifier.padding(
                    top = IBSpacing.spacingXs,
                    end = IBSpacing.spacingXs
                ).testTagIBank("IBankModalConfirm_closeIcon"),
                onClick = onDismissRequest
            ) {
                Icon(
                    painter = painterResource(R.drawable.modal_confirm_close),
                    contentDescription = null
                )
            }
        }
    }
}

@Composable
private fun ActionButtons(
    listDialogButtonInfo: List<DialogButtonInfo>,
    onDismissRequest: () -> Unit,
) {
    if (listDialogButtonInfo.size == 2) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(
                space = IBSpacing.spacingM,
                Alignment.CenterHorizontally
            ),
            verticalAlignment = Alignment.CenterVertically
        ) {
            listDialogButtonInfo[1].let {
                IBankNormalButton(
                    modifier = Modifier.weight(1f),
                    type = NormalButtonType.SECONDARYGRAY(LocalColorScheme.current),
                    text = it.label,
                    onClick = {
                        if (it.isDismissRequest) {
                            onDismissRequest()
                        }
                        it.onClick()
                    },
                )
            }

            listDialogButtonInfo.first().let {
                IBankNormalButton(
                    modifier = Modifier.weight(1f),
                    text = it.label,
                    onClick = {
                        if (it.isDismissRequest) {
                            onDismissRequest()
                        }
                        it.onClick()
                    },
                )
            }
        }
    } else {
        Column(
            modifier = Modifier.fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(
                space = IBSpacing.spacingM,
                Alignment.CenterVertically
            )
        ) {
            listDialogButtonInfo.firstOrNull()?.let {
                IBankNormalButton(
                    modifier = Modifier.fillMaxWidth(),
                    text = it.label,
                    onClick = {
                        if (it.isDismissRequest) {
                            onDismissRequest()
                        }
                        it.onClick()
                    },
                )
            }
            listDialogButtonInfo.drop(1).forEach {
                IBankNormalButton(
                    modifier = Modifier.fillMaxWidth(),
                    type = NormalButtonType.SECONDARYGRAY(LocalColorScheme.current),
                    text = it.label,
                    onClick = {
                        if (it.isDismissRequest) {
                            onDismissRequest()
                        }
                        it.onClick()
                    },
                )
            }
        }
    }

}

enum class ModalConfirmType(val icon: Int) {
    Success(R.drawable.icon_popup_success_64),
    Warning(R.drawable.icon_popup_warning_64),
    Info(R.drawable.icon_popup_info_64),
    Question(R.drawable.icon_popup_question_64),
    Error(R.drawable.icon_popup_error_64),
    Delete(R.drawable.icon_popup_delete_64),

}

@Preview
@Composable
fun Preview() {
//    IBankModalConfirm(
//        modalConfirmType = ModalConfirmType.Warning,
//        title = "Tài khoản đã được kích hoạt trên thiết bị khác",
//        supportingText = "Quý khách có đồng ý tiếp tục cài đặt ứng dụng trên thiết bị hiện tại không?",
//        listDialogButtonInfo = listOf(
//            DialogButtonInfo(
//                label = "Đồng ý 1",
//                onClick = {}
//            ),
//            DialogButtonInfo(
//                label = "Hủy",
//                onClick = {}
//            )
//        )
//    )

//    IBankModalConfirm(
//        modalConfirmType = ModalConfirmType.Warning,
//        title = "Tài khoản đã được kích hoạt trên thiết bị khác",
//        supportingText = "Quý khách có đồng ý tiếp tục cài đặt ứng dụng trên thiết bị hiện tại không?",
//        listDialogButtonInfo = listOf(
//            DialogButtonInfo(
//                label = "Đồng ýs",
//                onClick = {}
//            ),
//        )
//    )
//
    IBankModalConfirm(
        modalConfirmType = ModalConfirmType.Warning,
        title = "Tài khoản đã được kích hoạt trên thiết bị khác",
        supportingText = "Quý khách có đồng ý tiếp tục cài đặt ứng dụng trên thiết bị hiện tại không?",
        listDialogButtonInfo = listOf(
            DialogButtonInfo(
                label = "Đồng ý",
                onClick = {}
            ),
            DialogButtonInfo(
                label = "Để sau",
                onClick = {}
            ),
            DialogButtonInfo(
                label = "Hủy",
                onClick = {}
            )
        )
    )

}
