package vn.com.bidv.designsystem.component.bottomNavigation

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.IBankSpacer
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

@Composable
fun IBankBottomNavItem(
    itemData: BottomNavItemData,
    iconSize: Dp = 24.dp,
    modifier: Modifier,
    spacingBetweenIconAndText: Dp = 4.dp,
) {

    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current

    Box(
        modifier = modifier
        .padding(horizontal = IBankSpacer.paddingContentAndContent)
        .fillMaxSize()
        .clickable(
            indication = null,
            interactionSource = remember { MutableInteractionSource() }
        ) {
            itemData.onClick()
        }) {

        if (itemData.selected) {
            Box(
                modifier = Modifier
                    .size(width = IBSpacing.spacingM, height = IBSpacing.spacing2xs)
                    .clip(
                        RoundedCornerShape(
                            bottomStart = IBSpacing.spacing2xs,
                            bottomEnd = IBSpacing.spacing2xs
                        )
                    )
                    .background(colorSchema.contentBrand_01Primary)
                    .align(Alignment.TopCenter)
            )
        }

        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.align(Alignment.Center)
        ) {

            Icon(
                painter = if (itemData.selected) painterResource(id = itemData.drawableResIdSelection) else painterResource(id = itemData.drawableResIdUnSelection),
                contentDescription = null,
                modifier = Modifier.size(iconSize),
                tint = if (itemData.selected) Color.Unspecified else colorSchema.contentMainPrimary
            )

            Spacer(modifier = Modifier.height(spacingBetweenIconAndText))

            Text(
                text = itemData.label,
                color = if (itemData.selected) colorSchema.contentBrand_01Primary else colorSchema.contentMainTertiary,
                style = if (itemData.selected) typography.labelLabel_m else typography.bodyBody_s
            )

        }
    }
}