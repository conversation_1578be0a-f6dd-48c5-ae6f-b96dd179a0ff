package vn.com.bidv.designsystem.component.datepicker

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.component.datepicker.model.DatePickerConfig
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import java.util.Calendar
import java.util.Locale

@Composable
internal fun YearPicker(
    modifier: Modifier,
    startYear: Int,
    selected: Int,
    datePickerConfig: DatePickerConfig,
    onSelected: (year: Int) -> Unit,
) {
    val calendar = Calendar.getInstance()
    val currentYear = calendar.get(Calendar.YEAR)
    var yearsSelected by remember { mutableIntStateOf(selected) }
    val years = (startYear until startYear + 15).toList() // 15 years in a grid

    LazyVerticalGrid(
        columns = GridCells.Fixed(3),
        contentPadding = PaddingValues(8.dp),
        modifier = modifier.fillMaxWidth()
    ) {
        items(years.size) { index ->
            val year = years[index]
            YearItem(
                year = year,
                isSelected = year == yearsSelected,
                isCurrentYear = year == currentYear,
                isPaddingYear = isPaddingYear(year, datePickerConfig),
            ) {
                yearsSelected = year
                onSelected(yearsSelected)
            }
        }
    }
}

private fun isPaddingYear(year: Int, config: DatePickerConfig): Boolean {
    val calendar = Calendar.getInstance()

    val minYear = config.minDate?.let {
        calendar.time = it
        calendar.get(Calendar.YEAR)
    }

    val maxYear = config.maxDate?.let {
        calendar.time = it
        calendar.get(Calendar.YEAR)
    }

    return (minYear != null && year < minYear) || (maxYear != null && year > maxYear)
}

@Composable
private fun YearItem(
    year: Int,
    isSelected: Boolean,
    isCurrentYear: Boolean,
    isPaddingYear: Boolean,
    onClick: () -> Unit,
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current

    val textAlpha =
        if (isPaddingYear) 0.3f else 1f
    Box(contentAlignment = Alignment.Center,
        modifier = Modifier
            .padding(horizontal = 16.dp, vertical = 4.dp)
            .aspectRatio(2 / 1f)  // Ensures a square shape
            .fillMaxWidth(1f / 7)
            .border(
                width = 1.dp, color = when {
                    isCurrentYear -> colorScheme.borderBrandQuaternary
                    else -> Color.Transparent
                }, shape = RoundedCornerShape(IBCornerRadius.cornerRadiusM)
            )
            .clickable(enabled = !isPaddingYear) { onClick() }
            .alpha(textAlpha)
    ) {
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier
                .aspectRatio(2 / 1f) // Ensures a square shape
                .fillMaxWidth(1.5f / 7)
                .background(
                    color = Color.Transparent
                )
        ) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight()
                    .background(
                        color = when {
                            isSelected -> colorScheme.bgBrand_01Primary
                            else -> Color.Transparent
                        }, shape = RoundedCornerShape(IBCornerRadius.cornerRadiusM)
                    )
            ) {
                Text(
                    text = year.toString(),
                    color = if (isSelected) colorScheme.contentOn_specialPrimary else colorScheme.contentMainPrimary,
                    style = if (isSelected) typography.titleTitle_s else typography.bodyBody_m,
                )
            }
        }
    }
}

@Preview
@Composable
fun PreviewYearPicker() {
    Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
        YearPicker(
            modifier = Modifier.padding(innerPadding),
            startYear = 2015,
            selected = 2025,
            datePickerConfig = DatePickerConfig.build {
                minDate = Calendar.getInstance(Locale.getDefault()).apply { set(2024, 5, 6) }.time
                maxDate = Calendar.getInstance(Locale.getDefault()).time
            }
        ) {}
    }
}