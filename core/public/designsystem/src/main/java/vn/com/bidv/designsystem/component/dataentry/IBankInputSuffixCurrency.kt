package vn.com.bidv.designsystem.component.dataentry

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.painter.Painter
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

@Composable
fun IBankInputSuffixCurrency(
    iconRight: Painter,
    labelTail: String,
    showIcon: Boolean = false,
    onIconTailClick: () -> Unit? = {}
) {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current

    Box(Modifier.testTagIBank("IBankInputSuffixCurrency_$labelTail").clickable {
        onIconTailClick()
    }) {
        Row(Modifier.align(Alignment.BottomEnd), verticalAlignment = Alignment.CenterVertically) {

            Text(
                style = typography.bodyBody_l, color = colorScheme.contentMainPrimary, text =
                labelTail
            )

            if (showIcon) {
                Image(
                    iconRight,
                    contentDescription = "",
                    modifier = Modifier.padding(IBSpacing.spacingXs)
                )
            }
        }
    }
}