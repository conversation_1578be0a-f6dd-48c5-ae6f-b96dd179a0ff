package vn.com.bidv.sdkbase.ui

import androidx.compose.runtime.Immutable
import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import javax.inject.Inject

@HiltViewModel
class EmptyViewModel @Inject constructor() :
    ViewModelIBankBase<EmptyReducer.EmptyUiState, EmptyReducer.EmptyUiEvent, EmptyReducer.EmptySideEffect>(
        initialState = EmptyReducer.EmptyUiState.Init,
        reducer = EmptyReducer()
    )

class EmptyReducer :
    Reducer<EmptyReducer.EmptyUiState, EmptyReducer.EmptyUiEvent, EmptyReducer.EmptySideEffect> {
    @Immutable
    sealed class EmptyUiState : ViewState {
        data object Init : EmptyUiState()
    }

    @Immutable
    sealed class EmptyUiEvent : ViewEvent

    @Immutable
    sealed class EmptySideEffect : SideEffect

    override fun reduce(
        previousState: EmptyUiState,
        event: EmptyUiEvent
    ): Pair<EmptyUiState, EmptySideEffect?> {
        return previousState to null
    }
}