package vn.com.bidv.sdkbase.utils.exts

import android.annotation.SuppressLint
import vn.com.bidv.log.BLogUtil
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.concurrent.TimeUnit

fun Date.dateToString(
    pattern: String = SdkBaseConstants.DateTimeConstants.FORMAT_DD_MM_YYYY,
    locale: Locale = Locale.getDefault()
): String {
    return try {
        val formatter = SimpleDateFormat(pattern, locale)
        formatter.format(this)
    } catch (e: Exception) {
        ""
    }
}

fun String.toDate(
    pattern: String = SdkBaseConstants.DateTimeConstants.FORMAT_DD_MM_YYYY,
    locale: Locale = Locale.getDefault()
): Date? {
    return try {
        val formatter = SimpleDateFormat(pattern, locale)
        formatter.parse(this)
    } catch (e: Exception) {
        null
    }
}

fun Date.formatDateToString(format: String): String {
    val outputFormat = SimpleDateFormat(format, Locale.ENGLISH)
    return outputFormat.format(this)
}

fun calculateDateRange(
    startDate: Date?, endDate: Date?, unit: ChronoUnit = ChronoUnit.DAYS
): Long {
    val startLocalDate = startDate?.toLocalDate()
    val endLocalDate = endDate?.toLocalDate()
    return unit.between(startLocalDate, endLocalDate)
}

private fun Date.toLocalDate(): LocalDate {
    return this.toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
}

fun getStartOfOneYearAgo(): Date {
    return Calendar.getInstance().apply {
        add(Calendar.YEAR, -1)
        add(Calendar.MONTH, Calendar.JANUARY)
        add(Calendar.DAY_OF_MONTH, 1)
        add(Calendar.HOUR_OF_DAY, 0)
        add(Calendar.MINUTE, 0)
        add(Calendar.SECOND, 0)
        add(Calendar.MILLISECOND, 0)
    }.time
}

fun getStartOfYear(): Date {
    return Calendar.getInstance().apply {
        add(Calendar.MONTH, Calendar.JANUARY)
        add(Calendar.DAY_OF_MONTH, 1)
        add(Calendar.HOUR_OF_DAY, 0)
        add(Calendar.MINUTE, 0)
        add(Calendar.SECOND, 0)
        add(Calendar.MILLISECOND, 0)
    }.time
}

fun getCurrentDate(pattern: String = SdkBaseConstants.DateTimeConstants.FORMAT_DD_MM_YYYY): String {
    val currentDateTime = LocalDateTime.now()
    val formatter =
        DateTimeFormatter.ofPattern(pattern)
    return currentDateTime.format(formatter)
}

fun String.getDateAfter(
    count: Int,
    unit: Int = Calendar.DAY_OF_YEAR,
    pattern: String = SdkBaseConstants.DateTimeConstants.FORMAT_DD_MM_YYYY
): Date? {
    return try {
        val formatter = SimpleDateFormat(pattern, Locale.getDefault())
        val date = formatter.parse(this) ?: return null
        val calendar = Calendar.getInstance()
        calendar.time = date
        calendar.add(unit, count)
        calendar.time
    } catch (e: Exception) {
        null
    }
}

fun Date.getDateBefore(count: Int, unit: Int = Calendar.DAY_OF_YEAR): Date? {
    return try {
        val calendar = Calendar.getInstance()
        calendar.time = this
        calendar.add(unit, -count)
        calendar.time
    } catch (e: Exception) {
        null
    }
}

@SuppressLint("SimpleDateFormat")
fun convertDateToServerFormat(inputDate: String): String {
    return try {
        val inputFormat =
            SimpleDateFormat(SdkBaseConstants.DateTimeConstants.FORMAT_DD_MM_YYYY)
        val outputFormat =
            SimpleDateFormat(SdkBaseConstants.DateTimeConstants.FORMAT_YYYY_MM_DD)
        val date: Date? = inputFormat.parse(inputDate)
        if (date != null) {
            outputFormat.format(date)
        } else {
            ""
        }
    } catch (e: Exception) {
        ""
    }
}

@SuppressLint("SimpleDateFormat")
fun convertDateToClientFormat(inputDate: String): String {
    return try {
        val inputFormat =
            SimpleDateFormat(SdkBaseConstants.DateTimeConstants.FORMAT_YYYY_MM_DD)
        val outputFormat =
            SimpleDateFormat(SdkBaseConstants.DateTimeConstants.FORMAT_DD_MM_YYYY)
        val date: Date? = inputFormat.parse(inputDate)
        if (date != null) {
            outputFormat.format(date)
        } else {
            ""
        }
    } catch (e: Exception) {
        ""
    }
}

@SuppressLint("SimpleDateFormat")
fun String.toFormattedDate(
    inputPattern: String = SdkBaseConstants.DateTimeConstants.FORMAT_YYYY_MM_DD,
    outputPattern: String = SdkBaseConstants.DateTimeConstants.FORMAT_DD_MM_YYYY
): String {
    return try {
        val inputFormat = SimpleDateFormat(inputPattern)
        inputFormat.isLenient = false // if input not is inputPattern
        val outputFormat = SimpleDateFormat(outputPattern)
        val date: Date? = inputFormat.parse(this)
        date?.let { outputFormat.format(it) } ?: ""
    } catch (e: Exception) {
        ""
    }
}

/*
Input = "2024-11-29T13:56:50.977561"
Output: "2024-11-29 13:56:50"
*/
fun String.toDateWithSeconds(
    inputPatterns: List<String> = listOf(
        SdkBaseConstants.DateTimeConstants.INPUT_FORMAT_DD_MM_YYYY_HH_MM_SS,
        SdkBaseConstants.DateTimeConstants.INPUT_FORMAT_DD_MM_YYYY_HH_MM_SS_SSSSSS,
        SdkBaseConstants.DateTimeConstants.INPUT_FORMAT_DD_MM_YYYY_HH_MM_SS_SSSSSSSSS
    ),
    outputPattern: String = SdkBaseConstants.DateTimeConstants.FORMAT_DD_MM_YYYY_HH_MM_SS,
): String {
    val outputFormatter = DateTimeFormatter.ofPattern(outputPattern)
    for (pattern in inputPatterns) {
        try {
            val inputFormatter = DateTimeFormatter.ofPattern(pattern)
            val localDateTime = LocalDateTime.parse(this, inputFormatter)
            return localDateTime.format(outputFormatter)
        } catch (e: Exception) {
            BLogUtil.e("Input format mismatch")
        }
    }
    return ""
}

/*
Input = "2024-11-29T13:56:50.977561"/"2024-11-29T13:56:50.97756"
Output: "2024-11-29 13:56:50"
*/
fun String.toDateWithSecondsByFormatters(
    inputFormatters: List<DateTimeFormatter> = listOf(
        DateTimeFormatter.ISO_DATE_TIME,
    ),
    outputPattern: String = SdkBaseConstants.DateTimeConstants.FORMAT_DD_MM_YYYY_HH_MM_SS,
): String {
    val outputFormatter = DateTimeFormatter.ofPattern(outputPattern)
    for (inputFormatter in inputFormatters) {
        try {
            val localDateTime = LocalDateTime.parse(this, inputFormatter)
            return localDateTime.format(outputFormatter)
        } catch (e: Exception) {
            BLogUtil.e("Input format mismatch")
        }
    }
    return ""
}

fun isToday(
    dateString: String,
    pattern: String = SdkBaseConstants.DateTimeConstants.FORMAT_YYYY_MM_DD
): Boolean {
    try {
        val formatter = SimpleDateFormat(pattern, Locale.getDefault())
        val inputDate = formatter.parse(dateString)
        val today = Calendar.getInstance()
        val inputCal = Calendar.getInstance().apply {
            if (inputDate != null) {
                time = inputDate
            }
        }
        return today[Calendar.YEAR] == inputCal[Calendar.YEAR] &&
                today[Calendar.DAY_OF_YEAR] == inputCal[Calendar.DAY_OF_YEAR]
    } catch (ex: Exception) {
        return false
    }
}

fun isSameDate(
    dateStr1: String?,
    dateStr2: String?,
    pattern: String = SdkBaseConstants.DateTimeConstants.FORMAT_YYYY_MM_DD
): Boolean {
    return try {
        val formatter = SimpleDateFormat(pattern, Locale.getDefault())
        val date1 = formatter.parse(dateStr1 ?: "")
        val date2 = formatter.parse(dateStr2 ?: "")
        if (date1 == null || date2 == null) return false
        val cal1 = Calendar.getInstance().apply { time = date1 }
        val cal2 = Calendar.getInstance().apply { time = date2 }

        return cal1[Calendar.YEAR] == cal2[Calendar.YEAR] &&
                cal1[Calendar.DAY_OF_YEAR] == cal2[Calendar.DAY_OF_YEAR]
    } catch (ex: Exception) {
        false
    }
}

fun daysBetween(
    fromDate: String,
    toDate: String,
    pattern: String = SdkBaseConstants.DateTimeConstants.FORMAT_YYYY_MM_DD
): Long {
    return try {
        val format = SimpleDateFormat(pattern, Locale.getDefault())
        val date1 = format.parse(fromDate) ?: Date()
        val date2 = format.parse(toDate) ?: Date()

        val diffMillis = kotlin.math.abs(date2.time - date1.time)
        TimeUnit.DAYS.convert(diffMillis, TimeUnit.MILLISECONDS)
    } catch (ex: Exception) {
        0L
    }
}

fun isCompareTwoDate(
    fromDate: String?,
    toDate: String?,
    pattern: String = SdkBaseConstants.DateTimeConstants.FORMAT_YYYY_MM_DD
): Boolean {
    return try {
        if (fromDate.isNullOrBlank() || toDate.isNullOrBlank()) return false
        val format = SimpleDateFormat(pattern, Locale.getDefault())
        val date1 = format.parse(fromDate) ?: Date()
        val date2 = format.parse(toDate) ?: Date()

        date1.time <= date2.time
    } catch (ex: Exception) {
        false
    }
}


