package vn.com.bidv.sdkbase.component.listWithLoadMore

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import vn.com.bidv.sdkbase.domain.DomainResult

class LoadMoreListInterfaceImp<T>(
    val loadMore: () -> DomainResult<List<T>>,
    val canLoadMore: (DomainResult<List<T>>) -> Boolean
) : LoadMoreListInterface<T> {
    private val _loadMoreState = MutableStateFlow<LoadMoreListState<T>>(LoadMoreListState.Loading())
    override val loadMoreState: StateFlow<LoadMoreListState<T>> = _loadMoreState.asStateFlow()

    override fun loadMoreData() {
        when (val domainResult: DomainResult<List<T>> = loadMore()) {
            is DomainResult.Error -> {
                _loadMoreState.value = LoadMoreListState.Error(
                    domainResult.errorMessage,
                    _loadMoreState.value.listDataLoaded
                )
            }

            is DomainResult.Success -> {
                val newListData = mutableListOf<T>().apply {
                    addAll(_loadMoreState.value.listDataLoaded ?: listOf())
                    addAll(domainResult.data ?: listOf())
                }.toList()
                if (canLoadMore(domainResult)) {
                    _loadMoreState.value = LoadMoreListState.Loading(newListData)
                } else {
                    _loadMoreState.value = LoadMoreListState.Complete(newListData)
                }
            }
        }
    }

    override fun retry() {
        _loadMoreState.value = LoadMoreListState.Loading(_loadMoreState.value.listDataLoaded)
        loadMoreData()
    }
}