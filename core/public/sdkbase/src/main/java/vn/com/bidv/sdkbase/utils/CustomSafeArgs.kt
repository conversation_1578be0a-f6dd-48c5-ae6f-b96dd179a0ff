package vn.com.bidv.sdkbase.utils

import android.net.Uri
import android.os.Bundle
import android.util.Base64
import androidx.navigation.NavType
import kotlinx.serialization.KSerializer
import kotlinx.serialization.json.Json

object CustomSafeArgs {

    // Generic NavType for primitive types
    class PrimitiveNavType<T>(
        private val getFromBundle: (Bundle, String) -> T?,
        private val putToBundle: (Bundle, String, T) -> Unit,
        private val parseValueFromString: (String) -> T
    ) : NavType<T>(isNullableAllowed = true) {
        override fun get(bundle: Bundle, key: String): T? {
            return getFromBundle(bundle, key)
        }

        override fun parseValue(value: String): T {
            return parseValueFromString(value)
        }

        override fun serializeAsValue(value: T): String {
            return value.toString()
        }

        override fun put(bundle: Bundle, key: String, value: T) {
            putToBundle(bundle, key, value)
        }
    }

    // Generic NavType for serializable types
    class SerializableNavType<T>(
        private val serializer: KSerializer<T>
    ) : NavType<T>(isNullableAllowed = true) {
        override fun get(bundle: Bundle, key: String): T? {
            return bundle.getString(key)?.let { decode(it) }
        }

        override fun parseValue(value: String): T {
            return decode(value)
        }

        override fun serializeAsValue(value: T): String {
            return encode(value)
        }

        override fun put(bundle: Bundle, key: String, value: T) {
            bundle.putString(key, encode(value))
        }

        private fun encode(value: T): String {
            val json = Json.encodeToString(serializer, value)
            return Base64.encodeToString(json.toByteArray(), Base64.URL_SAFE or Base64.NO_WRAP)
        }

        private fun decode(value: String): T {
            val json = String(Base64.decode(value, Base64.URL_SAFE))
            return Json.decodeFromString(serializer, json)
        }
    }

    class ListSerializableNavType<T>(
        private val serializer: KSerializer<List<T>>
    ) : NavType<List<T>>(isNullableAllowed = true) {

        override fun get(bundle: Bundle, key: String): List<T>? {
            return bundle.getString(key)?.let { Json.decodeFromString(serializer, it) }
        }

        override fun parseValue(value: String): List<T> {
            return Json.decodeFromString(serializer, Uri.decode(value))
        }

        override fun serializeAsValue(value: List<T>): String {
            return Uri.encode(Json.encodeToString(serializer, value))
        }

        override fun put(bundle: Bundle, key: String, value: List<T>) {
            bundle.putString(key, Json.encodeToString(serializer, value))
        }
    }

    // Defined instances for primitive types
    val BooleanType = PrimitiveNavType(
        getFromBundle = { bundle, key -> bundle.getBoolean(key) },
        putToBundle = { bundle, key, value -> bundle.putBoolean(key, value) },
        parseValueFromString = { value -> value.toBoolean() }
    )

    val StringType = PrimitiveNavType(
        getFromBundle = { bundle, key -> bundle.getString(key) },
        putToBundle = { bundle, key, value -> bundle.putString(key, value) },
        parseValueFromString = { value -> value }
    )

    val IntType = PrimitiveNavType(
        getFromBundle = { bundle, key -> bundle.getInt(key) },
        putToBundle = { bundle, key, value -> bundle.putInt(key, value) },
        parseValueFromString = { value -> value.toInt() }
    )
}

/*
Example:
    typeMap = mapOf(
        typeOf<AccountDMO>() to SerializableNavType(AccountDMO.serializer()), /*For Object*/
        typeOf<List<BillDMO>>() to SerializableNavType(ListSerializer(BillDMO.serializer())), /*For List<Object>*/
        typeOf<String>() to CustomSafeArgs.StringType, /*For String*/
        typeOf<Boolean>() to CustomSafeArgs.BooleanType, /*For Boolean*/
    )
*/
