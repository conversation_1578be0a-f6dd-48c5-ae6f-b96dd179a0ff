package vn.com.bidv.sdkbase.ui

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.launch
import vn.com.bidv.designsystem.component.feedback.snackbar.IBankSnackBarInfo
import vn.com.bidv.sdkbase.data.LocalRepository
import vn.com.bidv.sdkbase.data.ShareDataDTO
import javax.inject.Inject

@HiltViewModel
class EmptyShareDataViewModel @Inject constructor(private val localRepository: LocalRepository) :
    ViewModel() {
    val data: Flow<ShareDataDTO> = localRepository.shareData

    fun showSnackBar(iBankSnackBarInfo: IBankSnackBarInfo) {
        viewModelScope.launch {
            localRepository.showSnackBar(iBankSnackBarInfo)
        }
    }
}