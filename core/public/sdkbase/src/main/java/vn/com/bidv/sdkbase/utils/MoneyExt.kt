package vn.com.bidv.sdkbase.utils

import androidx.compose.ui.text.intl.Locale
import androidx.compose.ui.text.toUpperCase
import vn.com.bidv.log.BLogUtil
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.util.Locale.US
import kotlin.math.min

val ASIA_CCY = arrayOf(
    SdkBaseConstants.MoneyCurrencyConstants.VND,
    SdkBaseConstants.MoneyCurrencyConstants.KRW,
    SdkBaseConstants.MoneyCurrencyConstants.JPY,
    SdkBaseConstants.MoneyCurrencyConstants.CLP
)
val CUR_ASIA_PATTERN = "#,###"
val CUR_OTHER_PATTERN = "#,###.##################"
val DEFAULT_MAX_TOTAL_DIGITS = 19

fun String?.formatMoney(currCode: String?, isShowCurrCode: Boolean = true): String {
    val currency = if (isShowCurrCode) currCode.orEmpty().trim() else ""
    val isAsiaFormat = (ASIA_CCY.contains(currCode.orEmpty().trim().toUpperCase(Locale.current)))
    val defaultZero = if (isAsiaFormat) {
        "0 $currency".trim()
    } else {
        "0.00 $currency".trim()
    }
    val formatter: DecimalFormat =
        if (isAsiaFormat) {
            DecimalFormat("#,###", DecimalFormatSymbols(US))
        } else {
            DecimalFormat("#,##0.00", DecimalFormatSymbols(US))
        }
    return try {
        val numberAmount = this?.toDoubleOrNull() ?: 0.0
        if (this.isNullOrBlank() || numberAmount == 0.0) {
            defaultZero
        } else {
            formatter.format(numberAmount).trim() + " $currency"
        }
    } catch (e: Exception) {
        BLogUtil.logException(e)
        defaultZero
    }
}

fun String?.formatAmount(
    currCode: String?,
    useAsiaFormat: Boolean = false,
    isShowCurrCode: Boolean = false,
    isDisplayZeroIfNull: Boolean = false,
    maxTotalDigits: Int = DEFAULT_MAX_TOTAL_DIGITS
): String {
    val currency = currCode.orEmpty().trim()
    val currencyDisplay = if (isShowCurrCode) currency else ""
    val isAsiaFormat = useAsiaFormat || (ASIA_CCY.contains(currency.toUpperCase(Locale.current)))
    val defaultZero = if (isDisplayZeroIfNull) "0" else ""

    val formatter: DecimalFormat =
        if (isAsiaFormat) {
            DecimalFormat(CUR_ASIA_PATTERN, DecimalFormatSymbols(US))
        } else {
            DecimalFormat(CUR_OTHER_PATTERN, DecimalFormatSymbols(US))
        }

    return try {
        val amount = this?.run {
            val dotIndex = indexOfLast { it == '.' }
            when {
                dotIndex < 0 || dotIndex >= maxTotalDigits -> substring(
                    0, min(maxTotalDigits, length)
                )

                else -> substring(0, min(maxTotalDigits + 1, length))
            }.replace(Regex("\\.0+$"), "").toBigDecimalOrNull()
        }
        val amountDisplay = amount?.let {
            formatter.format(it)
        } ?: defaultZero

        if (isShowCurrCode) "$amountDisplay $currencyDisplay".trim() else amountDisplay
    } catch (e: Exception) {
        BLogUtil.logException(e)
        if (isShowCurrCode) "$defaultZero $currencyDisplay".trim() else defaultZero
    }
}

fun String?.formatPercent(): String {
    val number = this?.toFloatOrNull() ?: 0f
    return if (number > 0) {
        val decimalFormat = DecimalFormat("0.00", DecimalFormatSymbols(US))
        decimalFormat.format(number)
    } else {
        "0"
    }
}
