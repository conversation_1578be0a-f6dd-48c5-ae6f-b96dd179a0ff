package vn.com.bidv.sdkbase.navigation

import androidx.compose.runtime.compositionLocalOf
import androidx.navigation.NavController

const val LOGIN_SCREEN = "LOGIN_SCREEN"

sealed class IBankMainRouting(val route: String, val deepLinkPattern: String) {

    sealed class AuthRoutes {
        data object AuthMainRoute : IBankMainRouting(
            route = "auth_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/login"
        )

        data object ReActiveSmartOtpRoute : IBankMainRouting(
            route = "re_active_smart_otp_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/re_active_smart_otp_route"
        ) {
            val routeWithArgument = "$route?$ARG_TRANS_AUTH={$ARG_TRANS_AUTH}"
        }

        data object ManageApprovalRequestsRoute : IBankMainRouting(
            route = "manage_approval_requests_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/manage_approval_requests_route"
        )

        data object ManageQuestionsRoute : IBankMainRouting(
            route = "manage_question_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/manage_question_route"
        )

        data object PositiveChangePwRoute : IBankMainRouting(
            route = "positive_change_pw_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/positive_change_pw_route"
        )

        data object ChangePinRoute : IBankMainRouting(
            route = "change_pin_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/change_pin_route"
        )

        data object TurnOnBiometricRoute : IBankMainRouting(
            route = "turn_on_biometric_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/turn_on_biometric"
        ) {
            val routeWithArgument = "$route?$ARG_TRANS_AUTH={$ARG_TRANS_AUTH}"
        }

        data object SettingLanguageRoute : IBankMainRouting(
            route = "setting_language_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/setting_language"
        )

        data object UserInfoRoute : IBankMainRouting(
            route = "user_info_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/user_info"
        )

        data object ActiveSmartOtpRoute: IBankMainRouting(
            route = "active_smart_otp_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/active_smart_otp_route"
        ) {
            val routeWithArgument = "$route?$ARG_TRANS_AUTH={$ARG_TRANS_AUTH}"
        }

        data object ConvertActiveSmartOtpRoute: IBankMainRouting(
            route = "convert_active_smart_otp_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/convert_active_smart_otp_route"
        )

        companion object {
            val routes = listOf(ManageApprovalRequestsRoute)
            const val ARG_TRANS_AUTH = "ARG_TRANS_AUTH"
        }

    }

    sealed class HomeRoute {
        data object HomeMainRoute : IBankMainRouting(
            route = "home_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/home"
        )

        data object SettingSecureInfoRoute : IBankMainRouting(
            route = "setting_secure_info_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/home"
        )

        data object AvatarPickerRoute : IBankMainRouting(
            route = "avatar_picker_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/avatar-picker"
        )

        companion object {
            val routes = listOf(HomeMainRoute)
        }
    }

    sealed class POCRoute {
        data object POCMainRoute : IBankMainRouting(
            route = "poc_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/poc"
        )
    }

    sealed class NotifyRoute {
        data object NotifyMainRoute : IBankMainRouting(
            route = "notify_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/notify"
        ) {
            val routeWithArgument = "$route?$REDIRECT_ID={$REDIRECT_ID}&$DISPLAY_TAB={$DISPLAY_TAB}"
        }

        data object NotifySettingMainRoute : IBankMainRouting(
            route = "notify_setting_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/notify_setting"
        )

        companion object {
            val routes = listOf(
                NotifyMainRoute,
                NotifySettingMainRoute
            )
            const val REDIRECT_ID = "REDIRECT_ID"
            const val DISPLAY_TAB = "DISPLAY_TAB"
        }
    }

    sealed class InquiryRoute {
        data object InquiryMainRoute : IBankMainRouting(
            route = "inquiry_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/inquiry"
        )

        data object PaymentAccountList : IBankMainRouting(
            route = "payment_account_list_screen",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/inquiry"
        )

        data object DepositAccountList : IBankMainRouting(
            route = "deposit_account_list_screen",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/inquiry"
        )

        data object LoanAccountList : IBankMainRouting(
            route = "loan_account_list_screen",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/inquiry"
        )

        data object GuaranteeAccountList : IBankMainRouting(
            route = "guarantee_account_list_screen",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/inquiry"
        )

        data object PaymentAccountDetail : IBankMainRouting(
            route = "payment_account_detail",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/inquiry"
        ) {
            val routeWithArgument = "$route?$ACCOUNT_NO={$ACCOUNT_NO}?$OPENED_DATE={$OPENED_DATE}"
        }

        companion object {
            val routes = listOf(InquiryMainRoute)
            const val ACCOUNT_NO = "accountNo"
            const val OPENED_DATE = "opened_date"
        }
    }

    sealed class StatementRoute {

        object Routes {
            const val STATEMENT_MANAGER_ROUTE = "statement_manager_route"
            const val STATEMENT_PERIODIC_ROUTE = "statement_periodic_route"
        }

        data object StatementMainRoute : IBankMainRouting(
            route = "statement_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/statement"
        )

        // Sao kê và Xác nhận của ngân hàng
        data object StatementManagerRoute : IBankMainRouting(
            route = Routes.STATEMENT_MANAGER_ROUTE,
            deepLinkPattern = "ibank://vn.com.bidv.ibank/statement"
        )

        // Dịch vụ đăng ký in sao kê định kỳ
        data object StatementPeriodicRoute : IBankMainRouting(
            route = Routes.STATEMENT_PERIODIC_ROUTE,
            deepLinkPattern = "ibank://vn.com.bidv.ibank/statement"
        )

        companion object {
            val routes = listOf(StatementMainRoute)
        }
    }

    sealed class DepositRoute {
        data object DepositMainRoute : IBankMainRouting(
            route = "deposit_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/deposit"
        )

        data object DepositWithDrawalListRoute : IBankMainRouting(
            route = "WithdrawalListScreen",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/deposit"
        ) {
            val routeWithArgument = "$route?$TAB_ID={$TAB_ID}?${STATUS_TRANSACTION}={${STATUS_TRANSACTION}}"
        }

        data object DepositListRoute : IBankMainRouting(
            route = "DepositListScreen",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/deposit"
        ) {
            val routeWithArgument = "$route?$TAB_ID={$TAB_ID}?${STATUS_TRANSACTION}={${STATUS_TRANSACTION}}"
        }

        data object DepositCreateTransFlowRoute : IBankMainRouting(
            route = "DEPOSIT_CREATE_TRANS_FLOW_ROUTE",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/deposit"
        )

        data object WithdrawalCreateTransFlowRoute : IBankMainRouting(
            route = "WITHDRAWAL_CREATE_TRANS_FLOW_ROUTE",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/deposit"
        )
        data object TermWithdrawalCreateDetailDeepLinkRoute : IBankMainRouting(
            route = "CreateCreditTransRoute",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/deposit"
        ) {
            val routeWithArgument =
                "$route?$accountNo={$accountNo}"
        }

        companion object {
            val routes = listOf(DepositMainRoute)
            const val accountNo = "accountNo"
            const val TAB_ID = "tabId"
            const val STATUS_TRANSACTION = "statusTransaction"
        }
    }

    sealed class TransferRoute {
        data object TransferMainRoute : IBankMainRouting(
            route = "transfer_route", deepLinkPattern = "ibank://vn.com.bidv.ibank/transfer"
        ) {
            val routeWithArgument = "$route?$tabId={$tabId}?$STATUS_TRANSACTION={$STATUS_TRANSACTION}"
        }

        data object BeneficiaryList : IBankMainRouting(
            route = "beneficiary_list",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/beneficiary_list"
        )

        data object CreateTransferRoute : IBankMainRouting(
            route = "create_transfer_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/transfer/create"
        ) {
            val routeWithArgument = "$route?$ARG_CONTACT={$ARG_CONTACT}"
        }

        data object InitTransferRoute : IBankMainRouting(
            route = "init_transfer_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/transfer/init"
        ) {
            val routeWithArgument =
                "$route?$ARG_TEMPLATE={$ARG_TEMPLATE}&$ARG_DATA_QR={$ARG_DATA_QR}"
        }

        companion object {
            val routes = listOf(TransferMainRoute, CreateTransferRoute, BeneficiaryList)
            const val ARG_CONTACT = "contact"
            const val ARG_TEMPLATE = "template"
            const val ARG_DATA_QR = "data_qr"
            const val tabId = "tabId"
            const val STATUS_TRANSACTION = "statusTransaction"
        }
    }

    sealed class CommonRoute {
        data object CommonMainRoute : IBankMainRouting(
            route = "common_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/common"
        )

        data object VerifyTransactionRoute : IBankMainRouting(
            route = "verify_transaction_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/common/verify_transaction"
        ) {
            val routeWithArgument = "$route?$ARG_TRANS_AUTH={$ARG_TRANS_AUTH}"
        }

        data object VerifyByTypeTransactionRoute : IBankMainRouting(
            route = "verify_by_type_transaction_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/common/verify_by_type_transaction"
        ) {
            val routeWithArgument = "$route?$DATA_OBJECT_INIT={$DATA_OBJECT_INIT}&$TYPE={$TYPE}&$ADDITIONAL_DATA={$ADDITIONAL_DATA}"
        }

        data object CommonActionHistoryRoute : IBankMainRouting(
            route = "common_action_history_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/common_action_history_route"
        ) {
            val routeWithArgument = "$route?$TXN_ID={$TXN_ID}&$TXN_CODE={$TXN_CODE}"
        }

        companion object {
            val routes = listOf(CommonMainRoute, VerifyTransactionRoute, CommonActionHistoryRoute)
            const val ARG_TRANS_AUTH = "ARG_TRANS_AUTH"
            const val TXN_ID = "TXN_ID"
            const val TXN_CODE = "TXN_CODE"
            const val TYPE = "TYPE"
            const val ADDITIONAL_DATA = "ADDITIONAL_DATA"
            const val DATA_OBJECT_INIT = "DATA_OBJECT_INIT"
        }
    }

    sealed class PaymentRecallRoute {
        data object TransferRecallRoute : IBankMainRouting(
            route = "payment_recall_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/payment_recall"
        ) {
            val routeWithArgument =
                "$route?$tabId={$tabId}?$STATUS_TRANSACTION={$STATUS_TRANSACTION}?$PRODUCT_CODE={$PRODUCT_CODE}"
        }

        companion object {
            val routes = listOf(TransferRecallRoute)
            const val tabId = "tabId"
            const val STATUS_TRANSACTION = "statusTransaction"
            const val PRODUCT_CODE = "productCode"
        }
    }

    sealed class PaymentInquiryRoute {
        data object PaymentInquiryScreenRoute : IBankMainRouting(
            route = "payment_inquiry_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/payment_inquiry"
        ) {
            val routeWithArgument = "$route?$ROUTE_ID={$ROUTE_ID}?${STATUS_TRANSACTION}={${STATUS_TRANSACTION}}\""
        }

        companion object {
            val routes = listOf(PaymentInquiryScreenRoute)
            const val ROUTE_ID = "routeId"
            const val STATUS_TRANSACTION = "statusTransaction"
        }
    }

    sealed class RevokeTransactionRoute {

        object Routes {
            const val MAKER_TRANSACTION_MANAGER_ROUTE = "maker_transaction_manager_screen"
            const val CHECKER_TRANSACTION_MANAGER_ROUTE = "checker_transaction_manager_screen"
        }

        data object RevokeTransactionMainRoute : IBankMainRouting(
            route = "revoke_transaction_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/revoke_transaction"
        )

        data object MakerTransactionManagerRoute : IBankMainRouting(
            route = Routes.MAKER_TRANSACTION_MANAGER_ROUTE,
            deepLinkPattern = "ibank://vn.com.bidv.ibank/revoke_transaction"
        )

        data object CheckerTransactionManagerRoute : IBankMainRouting(
            route = Routes.CHECKER_TRANSACTION_MANAGER_ROUTE,
            deepLinkPattern = "ibank://vn.com.bidv.ibank/revoke_transaction"
        )

        data object RevokeListTransactionRoute : IBankMainRouting(
            "maker_transaction_manager_screen_deep_link",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/revoke_transaction"
        ) {
            val routeWithArgument = "$route?$TAB_ID={$TAB_ID}&$STATUS_TRANSACTION={$STATUS_TRANSACTION}"
        }

        companion object {
            val routes = listOf(RevokeTransactionMainRoute)
            const val TRANS_ID = "transId"
            const val TAB_ID = "tabId"
            const val STATUS_TRANSACTION = "statusTransaction"
        }
    }

    sealed class TransactionReportRoute {
        data object TransactionReportMainRoute : IBankMainRouting(
            route = "transaction_report_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/transaction_report"
        ){
            val routeWithArgument = "$route?$ROUTE_ID={$ROUTE_ID}"
        }

        companion object {
            val routes = listOf(TransactionReportMainRoute)
            const val ROUTE_ID = "routeId"
        }
    }

    sealed class TransactionApprovalRoute {
        data object TransactionApprovalMainRoute : IBankMainRouting(
            route = "transaction_approval_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/transaction_approval"
        ) {
            val routeWithArgument = "$route?$ROUTE_ID={$ROUTE_ID}"
        }

        companion object {
            val routes = listOf(TransactionApprovalMainRoute)
            const val ROUTE_ID = "routeId"
        }
    }

    sealed class TemplateTransactionRoute {
        data object TemplateTransactionMainRoute : IBankMainRouting(
            route = "template_transaction_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/template_transaction"
        ) {
            val routeWithArgument = "$route?$ROUTE_ID={$ROUTE_ID}"
        }

        companion object {
            val routes = listOf(TemplateTransactionMainRoute)
            const val ROUTE_ID = "routeId"
        }
    }

    sealed class LoanRoute {
        data object LoanRouteMainRoute : IBankMainRouting(
            route = "loan_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/loan"
        )

        companion object {
            val routes = listOf(LoanRouteMainRoute)
        }
    }

    sealed class GovernmentServiceRoute {
        data object GovernmentServiceMainRoute : IBankMainRouting(
            route = "government_service",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/government_service"
        )

        companion object {
            val routes = listOf(GovernmentServiceMainRoute)
        }
    }

    sealed class UtilitiesRoute {
        data object ExchangeRateRoute : IBankMainRouting(
            route = "exchange_rate_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/exchange_rate"
        )

        data object FeeInquiryRoute : IBankMainRouting(
            route = "fee_inquiry_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/fee_inquiry"
        )

        data object FeeInquiryDetailRoute : IBankMainRouting(
            route = "fee_inquiry_detail_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/fee_inquiry_detail"
        ) {
            val routeWithArgument = "$route?$ARG_FEE_SCHEDULED_DETAIL={$ARG_FEE_SCHEDULED_DETAIL}"
        }

        data object DepositInterestRateRoute : IBankMainRouting(
            route = "deposit_interest_rate_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/deposit_interest_rate"
        )

        companion object {
            val routes = listOf(
                ExchangeRateRoute,
                FeeInquiryRoute,
                DepositInterestRateRoute
            )
            const val ARG_FEE_SCHEDULED_DETAIL = "ARG_FEE_SCHEDULED_DETAIL"
        }
    }

    sealed class PaymentBulkRoute {
        data object ManageRoute : IBankMainRouting(
            route = "payment_bulk_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/payment_bulk"
        ) {
            val routeWithArgument = "$route?$STATUS_TRANSACTION={$STATUS_TRANSACTION}"
        }

        companion object {
            val routes = listOf(ManageRoute)
            const val STATUS_TRANSACTION = "statusTransaction"
        }
    }

    sealed class PaymentSalaryRoute {
        data object ManageRoute : IBankMainRouting(
            route = "payment_salary_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/payment_salary"
        ) {
            val routeWithArgument = "$route?$STATUS_TRANSACTION={$STATUS_TRANSACTION}"
        }

        companion object {
            val routes = listOf(ManageRoute)
            const val STATUS_TRANSACTION = "statusTransaction"
        }
    }

    sealed class ForeignExchangeRoute {
        data object ManageForeignExchangeRoute : IBankMainRouting(
            route = "manage_foreign_exchange_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/manage_foreign_exchange_route"
        )

        data object SellForeignExchangeRoute : IBankMainRouting(
            route = "sell_foreign_exchange_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/sell_foreign_exchange_route"
        ) {
            val routeWithArgument = "$route?$PRE_INIT_DATA={$PRE_INIT_DATA}"
        }

        data object BuyForeignExchangeRoute : IBankMainRouting(
            route = "buy_foreign_exchange_route",
            deepLinkPattern = "ibank://vn.com.bidv.ibank/buy_foreign_exchange_route"
        ) {
            val routeWithArgument = "$route?$PRE_INIT_DATA={$PRE_INIT_DATA}"
        }

        companion object {
            val routes = listOf(
                ManageForeignExchangeRoute,
                SellForeignExchangeRoute,
                BuyForeignExchangeRoute
            )
            const val PRE_INIT_DATA = "PRE_INIT_DATA"
        }
    }

    companion object {
        val allRoutes: List<String> = listOf(
            AuthRoutes.routes,
            HomeRoute.routes,
            NotifyRoute.routes,
            CNRRoute.routes,
            InquiryRoute.routes,
            DepositRoute.routes,
            TransferRoute.routes,
            PaymentRecallRoute.routes,
            PaymentInquiryRoute.routes,
            RevokeTransactionRoute.routes,
            TransactionReportRoute.routes,
            TransactionApprovalRoute.routes,
            CommonRoute.routes,
            TemplateTransactionRoute.routes,
            UtilitiesRoute.routes,
            StatementRoute.routes,
            PaymentBulkRoute.routes,
            PaymentSalaryRoute.routes,
            LoanRoute.routes,
            ForeignExchangeRoute.routes,
            GovernmentServiceRoute.routes,
            TransferInternationalRoute.routes
        ).flatten().map {
            it.route
        }
    }
}

val LocalIBankNavController =
    compositionLocalOf<NavController> { error("No iBank NavController provided") }