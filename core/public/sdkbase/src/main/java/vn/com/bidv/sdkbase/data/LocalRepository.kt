package vn.com.bidv.sdkbase.data

import androidx.compose.ui.unit.IntOffset
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import vn.com.bidv.designsystem.component.feedback.snackbar.IBankSnackBarInfo
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject

@ActivityRetainedScoped
class LocalRepository @Inject constructor() {
    private val _loadingCount = MutableStateFlow(0)
    val loadingCount: StateFlow<Int> = _loadingCount.asStateFlow()

    private val _initDataCount = MutableStateFlow(0)
    val initDataCount: StateFlow<Int> = _initDataCount.asStateFlow()

    private val _errorMessage by lazy { Channel<String>() }
    val errorMessage: Flow<String> by lazy { _errorMessage.receiveAsFlow() }

    private val _sessionExpiredMessage by lazy { Channel<String>() }
    val sessionExpiredMessage: Flow<String> by lazy { _sessionExpiredMessage.receiveAsFlow() }

    private val _snackBarInfo by lazy { Channel<IBankSnackBarInfo>() }
    val snackBarInfo: Flow<IBankSnackBarInfo> by lazy { _snackBarInfo.receiveAsFlow() }
    private val _snackBarOffset = MutableStateFlow<IntOffset>(IntOffset.Zero)
    val snackBarOffset: StateFlow<IntOffset> = _snackBarOffset.asStateFlow()

    private val _notificationModel = MutableSharedFlow<NotificationModel>(replay = 1)
    val notificationModel: SharedFlow<NotificationModel> = _notificationModel.asSharedFlow()

    private val _shareData by lazy { Channel<ShareDataDTO>() }
    val shareData: Flow<ShareDataDTO> by lazy { _shareData.receiveAsFlow() }

    private val _shareDataList = mutableListOf<Map<String, Channel<ShareDataDTO>>>()
    private val shareDataList: List<Map<String, Flow<ShareDataDTO>>>
        get() {
            return _shareDataList.map {
                it.mapValues { (_, channel) -> channel.receiveAsFlow() }
            }
        }

    private val requestReloadData = mutableMapOf<String, MutableStateFlow<ReloadDataDTO>>()

    private val _isLoginSuccess = MutableStateFlow(false)

    private val _isPopupLoaded = AtomicBoolean(false)

    private val _transactionData = MutableStateFlow<String?>(null)
    val transactionData: StateFlow<String?> = _transactionData.asStateFlow()

    private var _isNeedToCheckAppVersion: Boolean = true
    val isNeedToCheckAppVersion: Boolean
        get() = _isNeedToCheckAppVersion

    private var _isNeedToSyncTime = MutableStateFlow(true)
    val isNeedToSyncTime: StateFlow<Boolean> = _isNeedToSyncTime.asStateFlow()


    suspend fun requestReload(key: ReloadKey) {
        val item = requestReloadData[key.genKey()]
        if (item != null) {
            item.emit(ReloadDataDTO(key, System.currentTimeMillis()))
        } else {
            requestReloadData[key.genKey()] =
                MutableStateFlow(ReloadDataDTO(key, System.currentTimeMillis()))
        }
    }

    fun subscribeReloadData(key: ReloadKey): StateFlow<ReloadDataDTO> {
        val reloadDataFlow = requestReloadData[key.genKey()]
        val newFlow = MutableStateFlow(ReloadDataDTO(key, 0))
        if (reloadDataFlow == null) {
            requestReloadData[key.genKey()] = newFlow
        }
        return reloadDataFlow ?: newFlow
    }

    fun subscribeShareData(key: String): Flow<ShareDataDTO> {
        if (_shareDataList.any { it.containsKey(key) }) {
            return shareDataList.first { it.containsKey(key) }.values.first()
        }
        val channel = Channel<ShareDataDTO>()
        _shareDataList.add(mapOf(key to channel))
        return shareDataList.first { it.containsKey(key) }.values.first()
    }

    fun unsubscribeShareData(key: String) {
        _shareDataList.removeAll { it.containsKey(key) }
    }

    suspend fun shareDataTo(key: String, data: ShareDataDTO) {
        _shareDataList.find { it.containsKey(key) }?.values?.firstOrNull()?.send(data)
    }

    fun showLoading() {
        _loadingCount.value += 1
    }

    fun hideLoading() {
        _loadingCount.value -= 1
    }

    fun hideAllLoading() {
        _loadingCount.value = 0
    }

    fun setInitDataCount(count: Int) {
        _initDataCount.value = count
    }

    fun decreaseInitDataCount() {
        _initDataCount.value -= 1
    }

    suspend fun showPopupError(errorMessage: String) {
        _errorMessage.send(errorMessage)
    }

    suspend fun setSessionExpired(message: String) {
        _sessionExpiredMessage.send(message)
    }

    suspend fun showSnackBar(info: IBankSnackBarInfo) {
        _snackBarInfo.send(info)
    }

    fun setSnackBarOffset(offset: IntOffset) {
        _snackBarOffset.value = offset
    }

    suspend fun shareData(data: ShareDataDTO) {
        _shareData.send(data)
    }

    fun setLoginStatus(isSuccess: Boolean) {
        _isLoginSuccess.value = isSuccess
    }

    fun isLoginSuccess() = _isLoginSuccess.value

    fun setPopupLoadedStatus(isLoaded: Boolean) {
        _isPopupLoaded.set(isLoaded)
    }

    fun isPopupLoaded() = _isPopupLoaded.get()

    suspend fun pushNotification(model: NotificationModel?) {
        model?.let {
            _notificationModel.emit(model)
        } ?: _notificationModel.resetReplayCache()
    }

    fun pushDataToCreateTransaction(data: String?) {
        _transactionData.value = data
    }

    fun clearTransactionData() {
        _transactionData.value = null
    }

    fun updateNeedToCheckAppVersion(isCheckVersion: Boolean) {
        _isNeedToCheckAppVersion = isCheckVersion
    }

    fun updateNeedToSyncTime(isNeedToSyncTime: Boolean) {
        _isNeedToSyncTime.value = isNeedToSyncTime
    }

}