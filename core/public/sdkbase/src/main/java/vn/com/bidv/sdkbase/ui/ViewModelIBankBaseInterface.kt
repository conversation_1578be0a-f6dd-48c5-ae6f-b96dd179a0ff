package vn.com.bidv.sdkbase.ui

import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import vn.com.bidv.common.extenstion.isNotNullOrEmpty
import vn.com.bidv.designsystem.component.feedback.snackbar.IBankSnackBarInfo
import vn.com.bidv.localization.R
import vn.com.bidv.log.BLogUtil
import vn.com.bidv.network.NetworkStatusCode
import vn.com.bidv.sdkbase.data.LocalRepository
import vn.com.bidv.sdkbase.data.ReloadDataDTO
import vn.com.bidv.sdkbase.data.ReloadKey
import vn.com.bidv.sdkbase.data.ShareDataDTO
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.ResourceProvider

interface ViewModelIBankBaseInterface {

    val viewModelScopes: CoroutineScope
    val ioDispatcher: CoroutineDispatcher
    val localRepository: LocalRepository
    val resourceProvider: ResourceProvider
    val listSubscribedKey: MutableList<String>
    val reloadDataCache: MutableMap<String, ReloadDataDTO>

    @Deprecated(
        message = "Use callDomain instead",
        replaceWith = ReplaceWith("callDomain")
    )
    fun launch(
        showLoadingIndicator: Boolean = true,
        block: suspend CoroutineScope.(
            onGeneralError: (errorMessage: String?) -> Unit,
        ) -> Unit
    ): Job {
        return viewModelScopes.launch(ioDispatcher) {
            if (showLoadingIndicator) {
                localRepository.showLoading()
            }
            try {
                block.invoke(viewModelScopes) {
                    showPopupError(it)
                }
            } catch (exception: Exception) {
                BLogUtil.logException(exception)
                showPopupError()
            } finally {
                if (showLoadingIndicator) {
                    localRepository.hideLoading()
                }
            }
        }
    }


    /**
     * Executes a domain call within a coroutine scope, handling success and error cases.
     *
     * @param dispatcherCallBlock The dispatcher to use for the domain call block.
     * @param dispatcherCallBack The dispatcher to use for the success and error callbacks.
     * @param showLoadingIndicator Whether to show a loading indicator during the call.
     * @param isListenAllError Whether to listen for all errors.
     * @param isListenSessionExpired Whether to listen for session expired errors.
     * @param listErrorCodeListen A list of error codes to listen for.
     * @param onFail A callback to be invoked on failure with the error result
     * @param onSuccess A callback to be invoked on success with the success result.
     * @param callBlock The suspend function representing the domain call.
     * @return A Job representing the coroutine.
     */
    fun <T> callDomain(
        dispatcherCallBlock: CoroutineDispatcher = ioDispatcher,
        dispatcherCallBack: CoroutineDispatcher = Dispatchers.Main,
        showLoadingIndicator: Boolean = true,
        isListenAllError: Boolean = false,
        isListenSessionExpired: Boolean = false,
        listErrorCodeListen: List<String> = emptyList(),
        onFail: (DomainResult.Error?) -> Unit = { error ->
            showPopupError(error?.errorMessage)
        },
        onSuccess: (DomainResult.Success<T>) -> Unit = {},
        callBlock: suspend CoroutineScope.() -> DomainResult<T>,
    ): Job {
        return viewModelScopes.launch(dispatcherCallBlock) {
            if (showLoadingIndicator) {
                localRepository.showLoading()
            }
            try {
                when (val domainResult = callBlock.invoke(viewModelScopes)) {
                    is DomainResult.Success -> {
                        withContext(dispatcherCallBack) {
                            onSuccess(domainResult)
                        }
                    }

                    is DomainResult.Error -> {
                        val domainResultMapped = mapperDomainResultError(domainResult)
                        if (domainResultMapped.isSessionExpired() && !isListenSessionExpired) {
                            localRepository.setSessionExpired(
                                domainResultMapped.errorMessage ?: resourceProvider.getString(
                                    R.string.phien_dang_nhap_het_hieu_luc_quy_khach_vui_long_thuc_hien_dang_nhap_lai
                                )
                            )
                        } else if (isListenAllError || listErrorCodeListen.contains(
                                domainResultMapped.errorCode
                            )
                        ) {
                            withContext(dispatcherCallBack) {
                                onFail(domainResultMapped)
                            }
                        } else {
                            withContext(dispatcherCallBack) {
                                onFail(null)
                            }
                            showPopupError(domainResultMapped.errorMessage)
                        }
                    }
                }
            } catch (exception: Exception) {
                BLogUtil.logException(exception)
                onFail(DomainResult.Error(NetworkStatusCode.UNDEFINE))
            } finally {
                if (showLoadingIndicator) {
                    localRepository.hideLoading()
                }
            }
        }
    }

    //https://bidv-vn.atlassian.net/wiki/spaces/IBANK2/pages/*********/25.+Danh+m+c+c+c+m+l+i+httpCode+c+th+l+i+k+t+n+i+chung+c+n+client+c+u+h+nh+n+i+dung#1.-C%C3%A1c-case-Utilities-c%E1%BA%A5u-h%C3%ACnh-chung-to%C3%A0n-app
    fun mapperDomainResultError(domainResult: DomainResult.Error): DomainResult.Error {
        val message = when {
            domainResult.errorCode == NetworkStatusCode.CONNECT_TIME_OUT -> resourceProvider.getString(R.string.co_loi_xay_ra_trong_qua_trinh_xu_ly_vui_long_thu_lai)
            domainResult.errorCode == NetworkStatusCode.UNKNOWN_HOST -> resourceProvider.getString(R.string.quy_khach_vui_long_kiem_tra_ket_noi_internetwifi)
            domainResult.errorCode == NetworkStatusCode.NO_CONNECTION -> resourceProvider.getString(R.string.quy_khach_vui_long_kiem_tra_ket_noi_internetwifi)
            domainResult.errorCode == NetworkStatusCode.UNDEFINE -> resourceProvider.getString(R.string.co_loi_xay_ra_trong_qua_trinh_xu_ly_vui_long_thu_lai)
            domainResult.errorCode == "IM0307" -> resourceProvider.getString(R.string.phien_dang_nhap_het_hieu_luc_do_nguoi_dung_da_dang_nhap_tren_thiet_bi_khac_quy_khach_vui_long_thuc_hien_dang_nhap_lai)
            domainResult.isHttpError("403") -> resourceProvider.getString(R.string.quy_khach_chua_duoc_phan_quyen_su_dung_chuc_nang_vui_long_kiem_tra_lai)
            domainResult.isHttpError("401") -> resourceProvider.getString(R.string.phien_dang_nhap_het_hieu_luc_quy_khach_vui_long_thuc_hien_dang_nhap_lai)
            domainResult.isHttpError("400") -> resourceProvider.getString(R.string.du_lieu_dau_vao_khong_hop_le)
            domainResult.isHttpError("404") -> resourceProvider.getString(R.string.du_lieu_dau_vao_khong_hop_le)
            domainResult.isHttpError("405") -> resourceProvider.getString(R.string.du_lieu_dau_vao_khong_hop_le_405)
            domainResult.isHttpError("412") -> resourceProvider.getString(R.string.du_lieu_dau_vao_khong_hop_le_412)
            domainResult.isHttpError("413") -> resourceProvider.getString(R.string.du_lieu_dau_vao_khong_hop_le_413)
            domainResult.isHttpError("429") -> resourceProvider.getString(R.string.co_loi_xay_ra_trong_qua_trinh_xu_ly_vui_long_thu_lai_429)
            domainResult.isHttpError("500") -> resourceProvider.getString(R.string.co_loi_xay_ra_trong_qua_trinh_xu_ly_vui_long_thu_lai)
            domainResult.isHttpError("503") -> resourceProvider.getString(R.string.co_loi_xay_ra_trong_qua_trinh_xu_ly_vui_long_thu_lai_503)
            else ->  {
                domainResult.errorMessage.takeIf { domainResult.errorMessage.isNotNullOrEmpty() }
                    ?: (resourceProvider.getString(R.string.co_loi_xay_ra_vui_long_thu_lai) + ". (${domainResult.errorCode})")
            }
        }
        return DomainResult.Error(
            errorCode = domainResult.errorCode,
            errorMessage = message,
            listClientErrorCode = domainResult.listClientErrorCode,
            data = domainResult.data,
            status = domainResult.status
        )
    }

    fun showPopupError(errorMessage: String? = null) {
        viewModelScopes.launch {
            localRepository.showPopupError(
                errorMessage
                    ?: resourceProvider.getString(
                        R.string.co_loi_xay_ra_trong_qua_trinh_xu_ly_vui_long_thu_lai
                    ),
            )
        }
    }

    fun showSnackBar(iBankSnackBarInfo: IBankSnackBarInfo) {
        viewModelScopes.launch {
            localRepository.showSnackBar(iBankSnackBarInfo)
        }
    }

    fun subscribeShareData(key: String): Flow<ShareDataDTO> {
        if (!listSubscribedKey.contains(key)) {
            listSubscribedKey.add(key)
        }
        return localRepository.subscribeShareData(key)
    }

    fun requestReloadData(key: ReloadKey) = viewModelScopes.launch {
        localRepository.requestReload(key)
    }

    fun subscribeReloadData(key: ReloadKey): Flow<ReloadDataDTO> {
        return localRepository.subscribeReloadData(key)
    }

    fun checkReloadData(dataUpdate: ReloadDataDTO): Boolean {
        val key = dataUpdate.key.genKey()
        val cacheValue = reloadDataCache[key]
        if (cacheValue == null) {
            reloadDataCache[key] = ReloadDataDTO(dataUpdate.key, System.currentTimeMillis())
        } else {
            if (cacheValue.timeLoad < dataUpdate.timeLoad) {
                reloadDataCache[key] = dataUpdate
                return true
            }
        }
        return false
    }

    fun onViewModelCleared() {
        listSubscribedKey.forEach {
            localRepository.unsubscribeShareData(it)
        }
    }
}