package vn.com.bidv.sdkbase.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.component.screen.IBankScreen
import vn.com.bidv.sdkbase.navigation.LocalIBankNavController
import vn.com.bidv.designsystem.component.navigation.button.IBankNormalButton

@Composable
fun DefaultNoRouteScreen() {
    val navController = LocalIBankNavController.current
    IBankScreen {
        Column(
            modifier = Modifier
                .fillMaxSize(),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(text = stringResource(R.string.under_development))
            IBankNormalButton(
                onClick = {
                    navController.popBackStack()
                },
                text = stringResource(R.string.close)
            )
        }
    }
}