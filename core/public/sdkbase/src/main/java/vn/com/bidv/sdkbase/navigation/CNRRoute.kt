package vn.com.bidv.sdkbase.navigation

sealed class CNRRoute {
    data object CNRMainRoute : IBankMainRouting(
        route = "cnr_route",
        deepLinkPattern = "ibank://vn.com.bidv.ibank/cnr"
    )

    data object CreateInvoiceScreenRoute : IBankMainRouting(
        route = "create_invoice_screen_route",
        deepLinkPattern = "ibank://vn.com.bidv.ibank/create_invoice"
    )

    data object InvoiceAndTopUpScreenRoute : IBankMainRouting(
        route = "invoice_and_topup_screen_route",
        deepLinkPattern = "ibank://vn.com.bidv.ibank/invoice_and_topup"
    ) {
        val routeWithArgument =
            "$route?$TAB_ID={$TAB_ID}?$STATUS_TRANSACTION={$STATUS_TRANSACTION}"
    }

    companion object {
        val routes = listOf(CNRMainRoute)
        const val TAB_ID = "tabId"
        const val STATUS_TRANSACTION = "statusTransaction"
    }
}