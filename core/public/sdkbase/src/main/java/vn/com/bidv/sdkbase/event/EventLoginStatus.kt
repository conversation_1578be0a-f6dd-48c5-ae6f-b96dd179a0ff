package vn.com.bidv.sdkbase.event

sealed class EventLoginStatus(val status: String) {
    data object Login : EventLoginStatus("true")
    data object Logout : EventLoginStatus("false")
    data object InLoginProcess : EventLoginStatus("InLoginProcess")

    companion object {
        fun fromString(status: String): EventLoginStatus {
            return when (status) {
                Login.status -> Login
                Logout.status -> Logout
                InLoginProcess.status -> InLoginProcess
                else -> Logout
            }
        }
    }
}