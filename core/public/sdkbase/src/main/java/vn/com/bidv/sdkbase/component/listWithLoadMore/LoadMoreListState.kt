package vn.com.bidv.sdkbase.component.listWithLoadMore

sealed class LoadMoreListState<T>(val listDataLoaded: List<T>) {
    data class Loading<T>(val listData: List<T>? = null)
        : LoadMoreListState<T>(listData ?: listOf())
    data class Error<T>(val message: String?, val listData: List<T>? = null)
        : LoadMoreListState<T>(listData ?: listOf())
    data class Complete<T>(val data: List<T>?)
        : LoadMoreListState<T>(data ?: listOf())
}