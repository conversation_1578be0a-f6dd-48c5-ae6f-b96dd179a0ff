package vn.com.bidv.sdkbase.navigation

import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController

class FeatureGraphBuilderDefault : FeatureGraphBuilder {
    override fun buildGraph(
        navGraphBuilder: NavGraphBuilder,
        navController: NavHostController,
        registeredRoutes: (args: List<String>) -> Unit
    ) = Unit
}

interface FeatureGraphBuilder {
    fun buildGraph(
        navGraphBuilder: NavGraphBuilder,
        navController: NavHostController,
        registeredRoutes: (args: List<String>) -> Unit
    )
}