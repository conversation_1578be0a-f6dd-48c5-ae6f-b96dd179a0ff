package vn.com.bidv.sdkbase.navigation

sealed class TransferInternationalRoute {
    data object MainRoute : IBankMainRouting(
        route = "transfer_international",
        deepLinkPattern = "ibank://vn.com.bidv.ibank/transfer_international"
    )

    data object InitRoute : IBankMainRouting(
        route = "init_international",
        deepLinkPattern = "ibank://vn.com.bidv.ibank/transfer_international/init"
    )

    data object ManageRoute: IBankMainRouting(
        route = "manage_international",
        deepLinkPattern = "ibank://vn.com.bidv.ibank/transfer_international/manage"
    )

    companion object {
        val routes = listOf(
            MainRoute,
            InitRoute,
            ManageRoute
        )
    }
}