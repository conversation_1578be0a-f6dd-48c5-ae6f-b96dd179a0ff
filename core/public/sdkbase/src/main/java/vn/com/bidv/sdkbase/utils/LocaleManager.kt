package vn.com.bidv.sdkbase.utils

import android.app.Activity
import android.content.Context
import android.content.res.Resources
import android.os.Build
import vn.com.bidv.common.sharePreference.IKey
import vn.com.bidv.common.sharePreference.Storage
import java.util.Locale
import vn.com.bidv.designsystem.R as RDesignSystem
import vn.com.bidv.localization.R as RLocalization

object LocaleManager {

    fun getListItemLanguage() = listOf(
        ItemLanguage(icon = RDesignSystem.drawable.icon_vietnam, language = RLocalization.string.language_vietnamese, localeDef = LocaleDef.VIE, apiDefLang = ApiDefLang.VIETNAM),
        ItemLanguage(icon = RDesignSystem.drawable.icon_english, language = RLocalization.string.language_english, localeDef = LocaleDef.ENGLISH, apiDefLang = ApiDefLang.ENGLISH),
        ItemLanguage(icon = RDesignSystem.drawable.icon_korean, language = RLocalization.string.language_korean, localeDef = LocaleDef.KOREAN, apiDefLang = ApiDefLang.KOREAN),
        ItemLanguage(icon = RDesignSystem.drawable.icon_japan, language = RLocalization.string.language_japanese, localeDef = LocaleDef.JAPAN, apiDefLang = ApiDefLang.JAPAN),
        ItemLanguage(icon = RDesignSystem.drawable.icon_china, language = RLocalization.string.language_simplified_chinese, localeDef = LocaleDef.CHINA, apiDefLang = ApiDefLang.CHINA),
        ItemLanguage(icon = RDesignSystem.drawable.icon_china_tradition, language = RLocalization.string.language_traditional_chinese, localeDef = LocaleDef.TAIWAN, apiDefLang = ApiDefLang.TAIWAN),
    )

    fun setLocale(context: Context): Context {
        return updateResources(context, getLanguagePref())
    }

    fun setNewLocale(context: Context, language: String): Context {
        setLanguagePref(language)
        return updateResources(context, language)
    }

    fun getCurrentLanguage(context: Context): ItemLanguage {
        val curLang = getLanguagePref()
        return getListItemLanguage().singleOrNull { item -> item.localeDef == curLang } ?: getCurrentLocate(context)
    }

    fun getCurrentLocateForApi(): String {
        return getLanguagePref()?.let {
            getListItemLanguage().singleOrNull { item -> item.localeDef == it }?.apiDefLang
        } ?: ApiDefLang.VIETNAM
    }

    fun checkNeedResetLocate(act: Activity) {
        val curLang = getLanguagePref()
        if (getAppLocale(act.resources).language != curLang
            || getAppLocale(act.applicationContext.resources).language != curLang){
            setLocale(act)
            setLocale(act.applicationContext)
        }
    }

    private fun setLanguagePref(language: String) {
        Storage.put(LanguageKey.LANGUAGE_KEY, language)
    }

    private fun updateResources(ctx: Context, userSelectedLanguage: String?): Context {
        var context = ctx
        val curLang = getAppLocale(context.resources).toString()
        val lang = if (userSelectedLanguage.isNullOrEmpty()) {
            val mLang = getListItemLanguage().singleOrNull {
                curLang.contains(it.localeDef)
            }
            mLang?.localeDef ?: LocaleDef.VIE
        } else userSelectedLanguage
        val myLocale = when(lang) {
            LocaleDef.CHINA -> Locale.SIMPLIFIED_CHINESE
            LocaleDef.TAIWAN -> Locale.TRADITIONAL_CHINESE
            else -> Locale(lang)
        }

        val dm = context.resources.displayMetrics
        val conf = context.resources.configuration
        conf.setLocale(myLocale)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            context = context.createConfigurationContext(conf)
        } else {
            @Suppress("DEPRECATION")
            context.resources.updateConfiguration(conf, dm)
        }
        ResourceProvider.initialize(context)
        return context
    }

    private fun getAppLocale(res: Resources): Locale {
        val config = res.configuration
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            config.locales[0]
        } else {
            @Suppress("DEPRECATION")
            config.locale
        }
    }

    private fun getCurrentLocate(context: Context): ItemLanguage {
        val currentSystemLanguage = when (getAppLocale(context.resources)) {
            Locale.SIMPLIFIED_CHINESE -> LocaleDef.CHINA
            Locale.TRADITIONAL_CHINESE -> LocaleDef.TAIWAN
            else -> getAppLocale(context.resources).language
        }
        return  getListItemLanguage().firstOrNull {
            currentSystemLanguage.equals(it.localeDef, true)
        } ?: getListItemLanguage().first()
    }

    private fun getLanguagePref(): String? {
        return Storage.get(LanguageKey.LANGUAGE_KEY)
    }

}

enum class LanguageKey(
    override val key: String,
    override val defValue: String? = null
): IKey {
    LANGUAGE_KEY("language_key");

    override val prefix: String = "Language"
}