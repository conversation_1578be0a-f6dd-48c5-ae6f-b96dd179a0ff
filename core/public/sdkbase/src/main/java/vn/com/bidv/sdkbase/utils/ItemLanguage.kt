package vn.com.bidv.sdkbase.utils

import androidx.annotation.DrawableRes
import androidx.annotation.StringDef
import androidx.annotation.StringRes

data class ItemLanguage(
    @DrawableRes val icon: Int,
    @StringRes val language: Int,
    @LocaleDef val localeDef: String,
    @ApiDefLang val apiDefLang: String,
    var isSelected: Boolean = false
) {
    fun mapToShortedName(): String {
        return when (localeDef) {
            LocaleDef.VIE -> "VIE"
            LocaleDef.ENGLISH -> "ENG"
            LocaleDef.JAPAN -> "JPN"
            LocaleDef.CHINA -> "SIM"
            LocaleDef.TAIWAN -> "TRA"
            LocaleDef.KOREAN -> "KOR"
            else -> LocaleDef.VIE
        }
    }
}

@kotlin.annotation.Retention(AnnotationRetention.SOURCE)
@StringDef(ApiDefLang.VIETNAM, ApiDefLang.ENGLISH, ApiDefLang.CHINA, ApiDefLang.JAPAN, ApiDefLang.TAIWAN, ApiDefLang.KOREAN)
annotation class ApiDefLang {
    companion object {
        const val VIETNAM = "vi-vn"
        const val ENGLISH = "en-us"
        const val JAPAN = "ja-jp"
        const val CHINA = "zh-cn"
        const val TAIWAN = "zh-tw"
        const val KOREAN = "ko-kr"
    }
}

@kotlin.annotation.Retention(AnnotationRetention.SOURCE)
@StringDef(LocaleDef.VIE, LocaleDef.ENGLISH, LocaleDef.JAPAN, LocaleDef.CHINA, LocaleDef.TAIWAN, LocaleDef.KOREAN)
annotation class LocaleDef {
    companion object {
        const val VIE = "vi"
        const val ENGLISH = "en"
        const val JAPAN = "ja"
        const val CHINA = "zh_CN"
        const val TAIWAN = "zh_TW"
        const val KOREAN = "ko"
    }
}
