package vn.com.bidv.sdkbase.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import androidx.activity.ComponentActivity
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.core.content.ContextCompat.startActivity
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.PermissionStatus
import com.google.accompanist.permissions.rememberPermissionState
import com.google.accompanist.permissions.shouldShowRationale
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume
import kotlin.random.Random

object PermissionUtils {

    @OptIn(ExperimentalPermissionsApi::class)
    @Composable
    fun checkPermissionAndDoSomeThing(
        context: Context,
        permission: String,
        onPermissionGranted: () -> Unit,
        onPermissionDenied: (isPermanentlyDenied: Boolean) -> Unit = {}
    ) {
        var isLaunchPermissionRequest by remember { mutableStateOf(false) }
        val permissionState = rememberPermissionState(permission) {
            isLaunchPermissionRequest = true
        }
        val scope = rememberCoroutineScope()
        LaunchedEffect(Random.nextInt()) {
            if (!isLaunchPermissionRequest) {
                permissionState.launchPermissionRequest()
            }
        }
        when (permissionState.status) {
            is PermissionStatus.Granted -> {
                onPermissionGranted()
            }

            is PermissionStatus.Denied -> {
                if (isLaunchPermissionRequest) {
                    if (permissionState.status.shouldShowRationale) {
                        LaunchedEffect(Random.nextInt()) {
                            scope.launch {
                                val intent = Intent(
                                    Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                                    Uri.fromParts("package", context.packageName, null)
                                )
                                startActivity(context, intent, null)
                            }
                        }
                    } else {
                        onPermissionDenied(permissionState.status.shouldShowRationale)
                    }
                }
            }
        }

    }

    suspend fun requestPermission(context: Context, permission: String): Boolean {
        return suspendCancellableCoroutine { continuation ->
            val launcher = ActivityResultContracts.RequestPermission()
            val activity = context as? ComponentActivity
                ?: throw IllegalArgumentException("Context must be a ComponentActivity")

            val resultLauncher = activity.activityResultRegistry.register(
                "permission_${permission}_${Random.nextInt()}",
                launcher
            ) { isGranted ->
                continuation.resume(isGranted)
            }

            resultLauncher.launch(permission)
            continuation.invokeOnCancellation {
                resultLauncher.unregister()
            }
        }
    }
}