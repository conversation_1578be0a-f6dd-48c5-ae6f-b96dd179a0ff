package vn.com.bidv.sdkbase.component.screen

import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import vn.com.bidv.common.ui.IBankScreenState
import vn.com.bidv.sdkbase.component.BaseViewModel

abstract class IBankScreenWithLoadingViewModel<T> : BaseViewModel() {
    private val _screenState = MutableStateFlow<IBankScreenState<T>>(IBankScreenState.Initial)
    val screenState = _screenState.asStateFlow()

    protected fun setScreenState(state: IBankScreenState<T>) {
        _screenState.value = state
    }

    abstract fun loadScreenData()

    private val exceptionHandler = CoroutineExceptionHandler { coroutineContext, throwable ->
        setScreenState(IBankScreenState.Error("", "1000"))
    }

    private val mainScope = viewModelScope.plus(exceptionHandler)

    fun launch(block: suspend CoroutineScope.() -> Unit): Job {
        setScreenState(IBankScreenState.Loading())
        return mainScope.launch(Dispatchers.IO) {
            try {
                block.invoke(mainScope)
            } finally {
//                setScreenState(IBankScreenState.Initial)
            }
            return@launch
        }
    }


}
