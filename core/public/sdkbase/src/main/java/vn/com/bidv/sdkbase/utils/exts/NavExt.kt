package vn.com.bidv.sdkbase.utils.exts

import androidx.navigation.NavHostController
import androidx.navigation.NavOptionsBuilder
import androidx.navigation.navOptions

fun NavHostController.navigateWithArgument(
    route: String,
    listData : List<Pair<String, String?>>,
    builder: NavOptionsBuilder.() -> Unit = {}
) {
    val routeBuilder = StringBuilder(route)
    listData.forEach {
        if (!routeBuilder.contains("?")) {
            routeBuilder.append("?")
        }
        if (it.first.isNotBlank() && !it.second.isNullOrEmpty())
        routeBuilder.append("${it.first}=${it.second}&")
    }
    if (routeBuilder.endsWith("&")) {
        routeBuilder.deleteCharAt(routeBuilder.length - 1)
    }
    navigate(routeBuilder.toString(), builder)
}