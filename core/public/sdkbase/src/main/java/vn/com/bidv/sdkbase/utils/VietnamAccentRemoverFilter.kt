package vn.com.bidv.sdkbase.utils

import vn.com.bidv.designsystem.component.dataentry.IBankFilterTextField

/**
 * A filter that removes Vietnamese accents from the input string while preserving the original diacritic marks as separate characters.
 *
 * @property maxLength The maximum length of the resulting string after processing.
 * Example: "việt nam" > "vieetj nam"
 */
class VietnamAccentRemoverFilter(private val maxLength: Int = Int.MAX_VALUE) : IBankFilterTextField {
    override fun apply(input: String): String {
        return VNCharacterUtil.removeAccentWithSubChar(input, maxLength)
    }
}

/**
 * A filter that removes Vietnamese accents from the input string while preserving the original diacritic marks as separate characters.
 *
 * Example: "việt nam" > "viet nam"
 */
class VietnameseAccentRemoverFilter : IBankFilterTextField {
    override fun apply(input: String): String {
        return VNCharacterUtil.removeAccent(input)
    }
}