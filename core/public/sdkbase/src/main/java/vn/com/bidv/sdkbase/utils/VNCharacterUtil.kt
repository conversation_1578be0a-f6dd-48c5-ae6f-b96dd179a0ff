package vn.com.bidv.sdkbase.utils

object VNCharacterUtil {
    const val textAndNumber = "qwertyuiopasdfghjklzxcvbnmQWERTYUIOPASDFGHJKLZXCVBNM1234567890"

    const val textNumberAndSpace = "$textAndNumber "

    // Mang cac ky tu goc co dau (đã sắp sêp để có thể sử dụng Arrays.binarySearch)
    val SOURCE_CHARACTERS = listOf(
        'À', 'Á', 'Â', 'Ã', 'È', 'É',
        'Ê', 'Ì', 'Í', 'Ò', 'Ó', 'Ô', 'Õ', 'Ù', 'Ú', 'Ý', 'à', 'á', 'â',
        'ã', 'è', 'é', 'ê', 'ì', 'í', 'ò', 'ó', 'ô', 'õ', 'ù', 'ú', 'ý',
        'Ă', 'ă', 'Đ', 'đ', 'Ĩ', 'ĩ', 'Ũ', 'ũ', 'Ơ', 'ơ', 'Ư', 'ư', 'Ạ',
        'ạ', 'Ả', 'ả', 'Ấ', 'ấ', 'Ầ', 'ầ', 'Ẩ', 'ẩ', 'Ẫ', 'ẫ', 'Ậ', 'ậ',
        'Ắ', 'ắ', 'Ằ', 'ằ', 'Ẳ', 'ẳ', 'Ẵ', 'ẵ', 'Ặ', 'ặ', 'Ẹ', 'ẹ', 'Ẻ',
        'ẻ', 'Ẽ', 'ẽ', 'Ế', 'ế', 'Ề', 'ề', 'Ể', 'ể', 'Ễ', 'ễ', 'Ệ', 'ệ',
        'Ỉ', 'ỉ', 'Ị', 'ị', 'Ọ', 'ọ', 'Ỏ', 'ỏ', 'Ố', 'ố', 'Ồ', 'ồ', 'Ổ',
        'ổ', 'Ỗ', 'ỗ', 'Ộ', 'ộ', 'Ớ', 'ớ', 'Ờ', 'ờ', 'Ở', 'ở', 'Ỡ', 'ỡ',
        'Ợ', 'ợ', 'Ụ', 'ụ', 'Ủ', 'ủ', 'Ứ', 'ứ', 'Ừ', 'ừ', 'Ử', 'ử', 'Ữ',
        'ữ', 'Ự', 'ự', 'Ỳ', 'ỳ', 'Ỵ', 'ỵ', 'Ỷ', 'ỷ', 'Ỹ', 'ỹ'
    )

    // Mang cac ky tu thay the khong dau
    val DESTINATION_CHARACTERS = listOf(
        'A', 'A', 'A', 'A', 'E',
        'E', 'E', 'I', 'I', 'O', 'O', 'O', 'O', 'U', 'U', 'Y', 'a', 'a',
        'a', 'a', 'e', 'e', 'e', 'i', 'i', 'o', 'o', 'o', 'o', 'u', 'u',
        'y', 'A', 'a', 'D', 'd', 'I', 'i', 'U', 'u', 'O', 'o', 'U', 'u',
        'A', 'a', 'A', 'a', 'A', 'a', 'A', 'a', 'A', 'a', 'A', 'a', 'A',
        'a', 'A', 'a', 'A', 'a', 'A', 'a', 'A', 'a', 'A', 'a', 'E', 'e',
        'E', 'e', 'E', 'e', 'E', 'e', 'E', 'e', 'E', 'e', 'E', 'e', 'E',
        'e', 'I', 'i', 'I', 'i', 'O', 'o', 'O', 'o', 'O', 'o', 'O', 'o',
        'O', 'o', 'O', 'o', 'O', 'o', 'O', 'o', 'O', 'o', 'O', 'o', 'O',
        'o', 'O', 'o', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u',
        'U', 'u', 'U', 'u', 'Y', 'y', 'Y', 'y', 'Y', 'y', 'Y', 'y'
    )

    val SOURCE_STRINGS_ = listOf(
        "AF", "AS", "AA", "AX", "EF", "ES",
        "EE", "IF", "IS", "OF", "OS", "OO", "OX", "UF", "US", "YS", "af", "as", "aa",
        "ax", "ef", "es", "ee", "if", "is", "of", "os", "oo", "ox", "uf", "us", "ys",
        "AW", "aw", "DD", "dd", "IX", "ix", "UX", "ux", "OW", "ow", "UW", "uw", "AJ",
        "aj", "AR", "ar", "AAS", "aas", "AAF", "aaf", "AAR", "aar", "AAX", "aax", "AAJ", "aaj",
        "AWS", "aws", "AWF", "awf", "AWR", "awr", "AWX", "awx", "AWF", "awf", "EJ", "ej", "ER",
        "er", "EX", "ex", "EES", "ees", "EEF", "eef", "EER", "eer", "EEX", "eex", "EEJ", "eej",
        "IR", "ir", "IJ", "ij", "OJ", "oj", "OR", "or", "OOS", "oos", "OOF", "oof", "OOR",
        "oor", "OOX", "oox", "OOJ", "ooj", "OWS", "ows", "OWF", "owf", "OWR", "owr", "OWX", "owx",
        "OWJ", "owj", "UJ", "uj", "UR", "ur", "UWS", "uws", "UWF", "uwf", "UWR", "uwr", "UWX",
        "uwx", "UWJ", "uwj", "YF", "yf", "YJ", "yj", "YR", "yr", "YX", "yx"
    )

    /**
     * Bo dau 1 ky tu
     *
     * @param ch
     * @return
     */
    fun removeAccent(ch: Char): Char {
        val index: Int = SOURCE_CHARACTERS.binarySearch(ch)
        return if (index >= 0) {
            DESTINATION_CHARACTERS[index]
        } else ch
    }

    /**
     * Bo dau 1 chuoi
     *
     * @param s
     * @return
     */
    fun removeAccent(s: String?): String {
        if (s.isNullOrEmpty()) {
            return ""
        }
        return s.map { removeAccent(it) }.joinToString("")
    }

    fun removeAccentWithSubChar(ch: Char): String {
        val index: Int = SOURCE_CHARACTERS.binarySearch(ch)
        return if (index >= 0) {
            SOURCE_STRINGS_[index]
        } else ch.toString()
    }

    // Thêm maxLength để xử lý lỗi
    // Sau khi sử dụng removeAccentWithSubChar -> số lượng kí tự trong string có thể lớn hơn max length đã được set trong edt
    // thì chỉ được phép lấy từ 0 -> max length
    fun removeAccentWithSubChar(s: String?, maxLength: Int): String {
        if (s.isNullOrEmpty()) {
            return ""
        }
        return s.map { removeAccentWithSubChar(it) }.joinToString("").take(maxLength)
    }

    fun removeSpecialChar(source: String, listCharAllow: String): String {
        return source.filter { it in listCharAllow }
    }
}